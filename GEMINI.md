# Gemini Project Configuration

This file helps Gemini understand your project's conventions and configurations.

## Project Overview

This appears to be a Java Spring Boot application for a data route endpoint.

## Build & Run Commands

*   **Build:** `mvn clean install`
*   **Run:** `./start.sh`
*   **Stop:** `./stop.sh`
*   **Test:** `mvn test`

## Dependencies

*   This is a Maven project. Dependencies are managed in `pom.xml`.
*   The project uses Spring Boot.

## Coding Style & Conventions

(Add any specific coding style guidelines here.)
