# 目标要求

* 你是java领域专家，擅长postgresql、flyway、jpa和hibernate，可以生成对应建表schema的sql文件以及CRUD操作的Entity.java、Repository.java文件。
* 根据 __技术方案文档__ 中的“业务实体”章节帮我建表，字段id、created_by、created_time、updated_by、updated_time、ext默认添加,并参考以下建表语句：

```postgresql
create table t_user
(
    id          varchar(36)            not null
        constraint t_user_pk primary key,
    account     varchar(32)            not null,
    real_name   varchar(50)            not null,
    password    varchar(255)           not null,
    phone       varchar(50),
    email       varchar(255),
    deleted     bool      default false,
    enabled     bool      default true,
    ext         json      DEFAULT '{}' NOT NULL,
    create_time timestamp default current_timestamp,
    update_time timestamp default current_timestamp
);

comment on table t_user is '用户表';
comment on column t_user.id is '主键';
comment on column t_user.account is '账号';
comment on column t_user.real_name is '真实姓名';
comment on column t_user.password is '密码';
comment on column t_user.phone is '手机号';
comment on column t_user.email is '邮箱';
comment on column t_user.ext is '扩展字段';
comment on column t_user.enabled is '是否启用';
```

* “业务实体”中会标注表属于哪个schema
  * 如果未指定则默认为data_route_public,则脚本存放路径为src/main/resources/db/migration/public。
  * 如果是租户库，则脚本存放路径为src/main/resources/db/migration/tenant。

# 输出

1. 对应目录的 *.sql文件，其中文件名称规则为：YYMMDD_XX__表名.sql，XX为序号，从01开始，每次自增1，表名使用下划线命名法，比如t_user.sql对应的文件名为250718_01__t_user.sql，比如：[V250718_01__add_negotiate.sql](../src/main/resources/db/migration/public/V250718_01__add_negotiate.sql)
2. User.java

```java
package com.ailpha.ailand.dataroute.endpoint.user.domain;

import com.ailpha.ailand.dataroute.endpoint.user.enums.IdentityAuthStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Entity
@Table(name = "t_user")
public class User {
    @Id
    String id;
    String account;
    @Column(name = "real_name")
    String realName;
    String password;
    String phone;
    String email;
    Boolean enabled;
    Boolean deleted;
    @Column(name = "create_time")
    Date createTime;
    @Column(name = "update_time")
    Date updateTime;

    @Column(name = "company_id")
    Long companyId;

    @Embedded
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "ext")
    Ext ext;

    @Data
    @Embeddable
    public static class Ext {
        // 经办人身份验证是否通过
        Boolean reviewPass;
    }

    @Data
    @Embeddable
    public static class DelegateInfo {
        // 经办人相关信息
        @Schema(description = "经办人姓名", example = "李四")
        private String delegateName;
    }
}
```

2. UserRepository.java

```java
package com.ailpha.ailand.dataroute.endpoint.user.repository;

import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

public interface UserRepository extends JpaRepository<User, String>, QuerydslPredicateExecutor<User> {
    User findFirstByAccountOrderByCreateTimeDesc(String account);
}
```

# 要求：

1. 根据<目标需求>严格按照上下文要求生成完整的目标代码，不要遗漏服务代码，也不要只给部分示例
2. 严格参照上面样例，禁止简化生成的内容。
3. 生成的文件路径需符合 __工程结构文档__ 中的路径规范，确保代码组织清晰。
4. 给出文件的名称。