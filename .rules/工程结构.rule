data-route-endpoint/                                    # 数据路由端点服务，主要包含数据资产管理和交易业务逻辑
├── src/                                              # 源代码目录
│   ├── main/                                         # 主要源代码
│   │   ├── java/                                     # Java源代码
│   │   │   ├── com/
│   │   │   │   └── ailpha/
│   │   │   │       └── ailand/
│   │   │   │           └── dataroute/
│   │   │   │               └── endpoint/
│   │   │   │                   ├── base/            # 基础组件和工具类
│   │   │   │                   ├── blockchain/      # 区块链相关模块
│   │   │   │                   │   ├── domain/      # 区块链实体类
│   │   │   │                   │   └── repository/  # 区块链数据访问层
│   │   │   │                   ├── common/          # 公共配置和通用组件
│   │   │   │                   │   ├── aspect/      # AOP切面
│   │   │   │                   │   ├── config/      # 配置类
│   │   │   │                   │   ├── csrf/        # CSRF防护
│   │   │   │                   │   ├── enums/       # 公共枚举
│   │   │   │                   │   ├── exception/   # 异常处理
│   │   │   │                   │   ├── filters/     # 过滤器
│   │   │   │                   │   ├── interceptor/ # 拦截器
│   │   │   │                   │   ├── log/         # 日志相关
│   │   │   │                   │   ├── manager/     # 管理器
│   │   │   │                   │   ├── pk/          # 主键相关
│   │   │   │                   │   ├── request/     # 公共请求对象
│   │   │   │                   │   ├── rest/        # REST相关
│   │   │   │                   │   ├── restclient/  # REST客户端
│   │   │   │                   │   ├── service/     # 公共服务
│   │   │   │                   │   ├── tuple/       # 元组工具
│   │   │   │                   │   └── utils/       # 工具类
│   │   │   │                   ├── configuration/   # Spring配置类
│   │   │   │                   ├── entity/          # 通用实体类
│   │   │   │                   ├── repository/      # 通用数据访问层
│   │   │   │                   ├── util/            # 工具类
│   │   │   │                   ├── user/            # 用户管理模块
│   │   │   │                   │   ├── cache/       # 用户缓存配置
│   │   │   │                   │   ├── contants/    # 用户相关常量
│   │   │   │                   │   ├── controller/  # 用户接口控制器
│   │   │   │                   │   ├── domain/      # 用户实体类
│   │   │   │                   │   ├── enums/       # 用户相关枚举
│   │   │   │                   │   ├── mapstruct/   # 用户对象转换器
│   │   │   │                   │   ├── remote/      # 用户远程服务调用
│   │   │   │                   │   │   ├── request/ # 远程调用请求对象
│   │   │   │                   │   │   └── response/# 远程调用响应对象
│   │   │   │                   │   ├── repository/  # 用户数据访问层
│   │   │   │                   │   ├── schedule/    # 用户相关定时任务
│   │   │   │                   │   ├── security/    # 用户安全配置
│   │   │   │                   │   ├── service/     # 用户业务逻辑层
│   │   │   │                   │   └── vo/          # 用户值对象
│   │   │   │                   │       ├── login/   # 登录相关VO
│   │   │   │                   │       └── loginSetting/ # 登录设置VO
│   │   │   │                   ├── dataasset/       # 数据资产管理模块
│   │   │   │                   │   ├── cache/       # 数据资产缓存配置
│   │   │   │                   │   ├── controller/  # 数据资产接口控制器
│   │   │   │                   │   ├── deliver/     # 数据资产交付相关
│   │   │   │                   │   ├── domain/      # 数据资产实体类
│   │   │   │                   │   │   └── update/  # 数据更新相关实体
│   │   │   │                   │   ├── enums/       # 数据资产相关枚举
│   │   │   │                   │   ├── mapper/      # 数据映射器
│   │   │   │                   │   ├── repository/  # 数据资产数据访问层
│   │   │   │                   │   ├── request/     # 数据资产请求对象
│   │   │   │                   │   ├── schedule/    # 数据资产定时任务
│   │   │   │                   │   │   └── quartz/  # Quartz定时任务
│   │   │   │                   │   ├── service/     # 数据资产业务逻辑层
│   │   │   │                   │   │   └── impl/    # 业务逻辑实现类
│   │   │   │                   │   └── vo/          # 数据资产值对象
│   │   │   │                   ├── company/         # 公司管理模块
│   │   │   │                   │   ├── controller/  # 公司接口控制器
│   │   │   │                   │   ├── domain/      # 公司实体类
│   │   │   │                   │   ├── dto/         # 公司数据传输对象
│   │   │   │                   │   ├── mapstruct/   # 公司对象转换器
│   │   │   │                   │   ├── remote/      # 公司远程服务调用
│   │   │   │                   │   ├── repository/  # 公司数据访问层
│   │   │   │                   │   └── service/     # 公司业务逻辑层
│   │   │   │                   ├── connector/       # 连接器模块
│   │   │   │                   ├── dataAssetApproval/ # 数据资产审批模块
│   │   │   │                   ├── dataInvoiceStorage/ # 数据发票存储模块
│   │   │   │                   ├── dataprope/       # 数据属性模块
│   │   │   │                   ├── deliveryScene/   # 交付场景模块
│   │   │   │                   ├── demand/          # 需求管理模块
│   │   │   │                   ├── dict/            # 字典管理模块
│   │   │   │                   ├── hengnao/         # 恒脑相关模块
│   │   │   │                   ├── home/            # 首页模块
│   │   │   │                   ├── license/         # 许可证模块
│   │   │   │                   ├── logo/            # 标志管理模块
│   │   │   │                   │   ├── controller/  # 标志接口控制器
│   │   │   │                   │   ├── entity/      # 标志实体类
│   │   │   │                   │   ├── repository/  # 标志数据访问层
│   │   │   │                   │   ├── request/     # 标志请求对象
│   │   │   │                   │   └── service/     # 标志业务逻辑层
│   │   │   │                   ├── messageNotice/   # 消息通知模块
│   │   │   │                   ├── network/         # 网络管理模块
│   │   │   │                   │   ├── domain/      # 网络实体类
│   │   │   │                   │   ├── repository/  # 网络数据访问层
│   │   │   │                   │   └── service/     # 网络业务逻辑层
│   │   │   │                   ├── node/            # 节点管理模块
│   │   │   │                   │   ├── controller/  # 节点接口控制器
│   │   │   │                   │   ├── domain/      # 节点实体类
│   │   │   │                   │   ├── dto/         # 节点数据传输对象
│   │   │   │                   │   ├── repository/  # 节点数据访问层
│   │   │   │                   │   └── service/     # 节点业务逻辑层
│   │   │   │                   ├── openapi/         # 开放API模块
│   │   │   │                   │   └── agent/       # 代理相关
│   │   │   │                   ├── order/           # 订单管理模块
│   │   │   │                   │   ├── constants/   # 订单常量
│   │   │   │                   │   ├── controller/  # 订单接口控制器
│   │   │   │                   │   ├── domain/      # 订单实体类
│   │   │   │                   │   ├── mapstruct/   # 订单对象转换器
│   │   │   │                   │   ├── remote/      # 订单远程服务调用
│   │   │   │                   │   ├── repository/  # 订单数据访问层
│   │   │   │                   │   ├── schedule/    # 订单定时任务
│   │   │   │                   │   ├── service/     # 订单业务逻辑层
│   │   │   │                   │   ├── util/        # 订单工具类
│   │   │   │                   │   └── vo/          # 订单值对象
│   │   │   │                   ├── plugin/          # 插件管理模块
│   │   │   │                   │   ├── aop/         # AOP切面
│   │   │   │                   │   ├── common/      # 公共组件
│   │   │   │                   │   ├── controller/  # 插件接口控制器
│   │   │   │                   │   ├── converter/   # 转换器
│   │   │   │                   │   ├── service/     # 插件业务逻辑层
│   │   │   │                   │   ├── strategy/    # 策略模式
│   │   │   │                   │   └── vo/          # 插件值对象
│   │   │   │                   ├── restclient/      # REST客户端模块
│   │   │   │                   ├── servicenode/     # 服务节点模块
│   │   │   │                   │   ├── controller/  # 服务节点接口控制器
│   │   │   │                   │   └── remote/      # 服务节点远程调用
│   │   │   │                   ├── sse/             # 服务器推送事件模块
│   │   │   │                   ├── statistics/      # 统计分析模块
│   │   │   │                   ├── tenant/          # 租户管理模块
│   │   │   │                   │   ├── config/      # 租户配置
│   │   │   │                   │   ├── contants/    # 租户常量
│   │   │   │                   │   ├── context/     # 租户上下文
│   │   │   │                   │   ├── entity/      # 租户实体类
│   │   │   │                   │   ├── interceptor/ # 租户拦截器
│   │   │   │                   │   ├── repository/  # 租户数据访问层
│   │   │   │                   │   ├── resolver/    # 租户解析器
│   │   │   │                   │   └── service/     # 租户业务逻辑层
│   │   │   │                   ├── third/           # 第三方集成模块
│   │   │   │                   │   ├── aigate/      # AI网关集成
│   │   │   │                   │   ├── apigate/     # API网关集成
│   │   │   │                   │   ├── config/      # 第三方配置
│   │   │   │                   │   ├── constants/   # 第三方常量
│   │   │   │                   │   ├── hub/         # 数据中心集成
│   │   │   │                   │   ├── input/       # 输入处理
│   │   │   │                   │   ├── mapper/      # 数据映射
│   │   │   │                   │   ├── mapstruct/   # 对象转换器
│   │   │   │                   │   ├── minio/       # MinIO集成
│   │   │   │                   │   ├── output/      # 输出处理
│   │   │   │                   │   ├── request/     # 第三方请求对象
│   │   │   │                   │   ├── response/    # 第三方响应对象
│   │   │   │                   │   ├── service/     # 第三方服务
│   │   │   │                   │   └── util/        # 第三方工具类
│   │   │   │                   └── upgrade/         # 升级管理模块
│   │   │   │                       ├── controller/  # 升级接口控制器
│   │   │   │                       ├── entity/      # 升级实体类
│   │   │   │                       ├── mapper/      # 升级数据映射
│   │   │   │                       ├── repository/  # 升级数据访问层
│   │   │   │                       ├── service/     # 升级业务逻辑层
│   │   │   │                       └── vo/          # 升级值对象
│   │   │   ├── okhttp3/                              # OkHttp3相关扩展
│   │   │   │   └── logging/                          # HTTP请求日志
│   │   │   └── org/                                  # 第三方库扩展
│   │   │       ├── flywaydb/                         # Flyway数据库迁移扩展
│   │   │       └── springframework/                  # Spring框架扩展
│   │   └── resources/                                # 资源文件目录
│   │       ├── conf/                                 # 配置文件
│   │       ├── datasource-template/                  # 数据源模板
│   │       ├── db/                                   # 数据库相关
│   │       │   └── migration/                        # 数据库迁移脚本
│   │       ├── dict/                                 # 字典数据
│   │       └── fonts/                                # 字体文件
│   └── test/                                         # 测试代码
│       ├── java/                                     # Java测试代码
│       │   └── com/
│       │       └── ailpha/                           # 测试包结构
│       └── resources/                                # 测试资源文件
├── logs/                                             # 日志文件目录
├── 技术方案/                                          # 技术方案文档目录
├── assembly.xml                                      # Maven打包配置
├── logback.xml                                       # 日志配置文件
├── pom.xml                                           # Maven项目配置文件
├── start.sh                                          # 启动脚本
├── stop.sh                                           # 停止脚本
├── upgrade.sh                                        # 升级脚本
├── rsa_private_encrypt.sh                            # RSA私钥加密脚本
├── rsa_public_decrypt.sh                             # RSA公钥解密脚本
└── UPGRADE_README.md                                 # 升级说明文档
