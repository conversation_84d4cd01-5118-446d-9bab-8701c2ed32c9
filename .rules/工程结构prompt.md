帮我生成当前工程的文件夹目录树并用*中文注释*，生成结果以*树形结构*输出：

1. 排除target目录和.git目录，其他目录保留。
2. 使用最高效的方式列出每个工程模块的文件夹目录树，直到叶节点文件夹，但又不能包含具体文件。
3. 生成结果整合到一个”工程结构.rule“文件中，并放在data-route-endpoint/.rules合适目录下。

---

下面是其中一个模块输出格式示例：

```plain
data-route-endpoint/                            # 服务层模块，主要包含业务逻辑和服务配置。
├── src
│   ├── main
|   |   ├── java/            
|   |   |  ├── com/
|   |   |    └── ailpha/
|   |   |      └──ailand/
│   │   │           ├── dataroute
│   │   │           │ └── endpoint
│   │   │           │       ├── common            # 公共配置
│   │   │           │       ├── user              # 用户模块
│   │   │           │       │   ├── cache         # 缓存类
│   │   │           │       │   ├── contants      # 静态常量类
│   │   │           │       │   ├── controller	  # 接口类
│   │   │           │       │   ├── domain        # 实体类
│   │   │           │       │   ├── enums         # 枚举类
│   │   │           │       │   ├── mapstruct	  # mapstruct转换类
│   │   │           │       │   ├── remote        # 第三方远程调用类
│   │   │           │       │   ├── repository	  # 数据访问曾
│   │   │           │       │   ├── schedule	  # 定时任务类
│   │   │           │       │   ├── security      # 安全配置
│   │   │           │       │   ├── service       # 业务逻辑层
│   │   │           │       │   └── vo            # 值对象

```





