# CLAUDE_CN.md

本文件为 Claude Code (claude.ai/code) 在处理此代码库时提供中文指导。

## 构建和开发命令

### 项目构建
```bash
# 使用 Maven 构建项目
mvn clean compile

# 打包应用程序
mvn clean package

# 使用特定配置文件运行（dev 为默认配置）
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 测试
```bash
# 运行测试（当前配置中已跳过）
mvn test

# 安全依赖检查
mvn dependency-check:check
```

### 运行应用程序
```bash
# 开发模式
./start.sh --spring.profiles.active=dev

# 停止应用程序
./stop.sh
```

## 代码架构

### 核心包结构
- `com.ailpha.ailand.dataroute.endpoint` - 主应用程序包
- `base/` - 基础能力管理和 API 网关响应
- `common/` - 横切关注点（配置、过滤器、拦截器、工具类）
- `dataasset/` - 数据资产管理（交付、产品、资源）
- `connector/` - 路由器和连接管理
- `openapi/` - 外部 API 平台集成
- `company/` - 公司/组织管理
- `user/` - 用户认证和管理

### 关键技术栈
- **Spring Boot 3.3.10** 基于 Java 21
- **Spring Security** 用于认证/授权
- **Spring Data JPA** 配合 QueryDSL 进行数据库操作
- **Retrofit** 用于 HTTP 客户端集成
- **Knife4j** 用于 API 文档
- **Flyway** 用于数据库迁移
- **MapStruct** 用于对象映射
- **Lombok** 用于减少样板代码

### 数据库配置
- 主数据库：HighgoDB（PostgreSQL 兼容）
- 连接池：HikariCP
- 模式管理：Flyway 迁移文件位于 `src/main/resources/db/migration/`
- 多租户支持，使用独立模式

### 安全架构
- Spring Security 配合自定义认证提供者
- JWT/会话基础认证
- CSRF 保护，包含白名单 API
- 基于角色的访问控制（RBAC）
- 外部平台的 API 密钥/密码认证

### 核心领域模型
- **DataAsset** - 核心数据资产管理
- **Company** - 组织/租户管理
- **DataProduct** - 已发布的数据产品
- **DataResource** - 原始数据资源
- **DeliveryScene** - 数据交付场景

### 配置文件
- `dev` - 开发环境（默认）
- `test` - 测试环境
- `prod` - 生产环境

### 外部集成
- **MinIO** - 文件存储和管理
- **Router Agent** - 数据路由服务
- **IAM Server** - 身份和访问管理
- **License Server** - 许可证验证

### 常见开发模式
- 使用 MapStruct 进行 DTO/Entity 转换
- QueryDSL 进行类型安全的数据库查询
- Retrofit 进行外部服务调用
- Spring Security 进行端点保护
- 自定义切面用于日志记录和监控

## 环境变量和配置
- 数据库凭据使用 Jasypt 加密
- 特定配置文件位于 `application-{profile}.yaml`
- 默认加密密码：`ailand@VFR_`
- 通过自定义 KeyStore 配置进行密钥管理

## 文件上传支持

### 当前实现
项目支持多种文件上传方式：

1. **MultipartFile 上传**（传统方式）
   ```java
   @PostMapping("/upload")
   public SuccessResponse<UploadResponse> upload(MultipartFile file) {
       // 处理文件上传
   }
   ```

2. **请求体中的 Base64 文件内容**（新增支持）
   ```java
   @PostMapping("init/entity")
   public SuccessResponse<String> init(@RequestParam("type") String type, 
                                      @RequestParam("id") String id,
                                      @RequestBody IdentityFileDTO request) {
       // 支持在 JSON 中包含 Base64 编码的文件内容
   }
   ```

### IdentityFileDTO 结构
```java
public class IdentityFileDTO {
    String crt;              // 证书内容
    String entityId;         // 实体ID
    String nodeId;           // 节点ID
    String functionNodeUrl;  // 功能节点URL
    String fileContent;      // Base64 编码的文件内容
    String fileName;         // 文件名
}
```

### 使用方式

#### 方式1：直接传递 JSON 数据
```json
{
    "crt": "-----BEGIN CERTIFICATE-----...",
    "entityId": "entity123",
    "nodeId": "node456",
    "functionNodeUrl": "http://example.com"
}
```

#### 方式2：传递 Base64 编码的文件
```json
{
    "fileContent": "eyJjcnQiOiIuLi4iLCJlbnRpdHlJZCI6Ii4uLiJ9",
    "fileName": "identity.json"
}
```

当 `type=file` 且提供了 `fileContent` 时，系统会：
1. 解码 Base64 内容
2. 解析为 JSON 格式
3. 映射到 IdentityFileDTO 对象

这种方式特别适合：
- 前端 JavaScript 应用
- 移动应用
- 需要在 JSON API 中传递文件内容的场景
- 避免 multipart/form-data 复杂性的情况


