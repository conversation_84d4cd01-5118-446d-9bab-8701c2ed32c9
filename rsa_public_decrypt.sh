#!/bin/bash

# ==================== 可配置参数 ====================
# 需根据实际环境修改以下路径
ENCRYPT_FILE="./encrypt.txt"      # 待解密的加密文件路径（默认当前目录encrypt.txt）
OUTPUT_FILE="./decrypted.txt"     # 解密结果输出文件路径（默认当前目录decrypted.txt）
PUBLIC_KEY_FILE="./rsaPublicKey.txt"  # 公钥文件路径（默认当前目录rsaPublicKey.txt）
# ====================================================

# 清理临时文件的函数（确保退出时执行）
cleanup() {
    rm -f "$TEMP_PEM" "$ENCRYPT_BIN" "$DECRYPT_TEMP"
}
trap cleanup EXIT

# 定义临时文件
TEMP_PEM=$(mktemp)          # 带PEM头的公钥临时文件
ENCRYPT_BIN=$(mktemp)       # Base64解码后的二进制加密数据
DECRYPT_TEMP=$(mktemp)      # 解密结果临时文件

# 检查公钥文件是否存在
if [ ! -f "$PUBLIC_KEY_FILE" ]; then
    echo "错误: 公钥文件 $PUBLIC_KEY_FILE 不存在"
    exit 1
fi

# 检查加密文件是否存在
if [ ! -f "$ENCRYPT_FILE" ]; then
    echo "错误: 加密文件 $ENCRYPT_FILE 不存在"
    exit 1
fi

# 修复公钥PEM格式（添加头尾）
{
    echo "-----BEGIN PUBLIC KEY-----"
    cat "$PUBLIC_KEY_FILE"
    echo "-----END PUBLIC KEY-----"
} > "$TEMP_PEM" || { echo "错误: 公钥文件读取失败"; exit 1; }

# 将Base64加密文件解码为二进制数据
base64 -d "$ENCRYPT_FILE" > "$ENCRYPT_BIN" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: 加密文件 $ENCRYPT_FILE 不是有效的Base64编码"
    exit 1
fi

# 执行RSA公钥解密（验证签名模式）
if ! openssl rsautl -verify -pubin -inkey "$TEMP_PEM" -in "$ENCRYPT_BIN" -out "$DECRYPT_TEMP"; then
    echo "解密失败，可能原因："
    echo "1. 公钥格式不正确（非RSA公钥）"
    echo "2. 加密数据与公钥不匹配"
    echo "3. openssl工具异常"
    exit 1
fi

# 获取解密结果（处理可能的二进制内容）
decrypt_result=$(cat "$DECRYPT_TEMP")

# 输出到控制台
echo "解密结果: "
echo "$decrypt_result"

# 输出到文件（自动创建目录）
output_dir=$(dirname "$OUTPUT_FILE")
if [ ! -d "$output_dir" ]; then
    mkdir -p "$output_dir" || { echo "错误: 输出目录 $output_dir 创建失败"; exit 1; }
fi
echo "$decrypt_result" > "$OUTPUT_FILE" || { echo "错误: 输出文件 $OUTPUT_FILE 写入失败"; exit 1; }
echo "已保存到文件: $OUTPUT_FILE"
    
