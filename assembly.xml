<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 http://maven.apache.org/xsd/assembly-2.2.0.xsd">
    <id>release</id>
    <formats>
        <format>tar.gz</format>
    </formats>
    <dependencySets>
        <dependencySet>
            <outputDirectory>lib</outputDirectory>
        </dependencySet>
    </dependencySets>

    <fileSets>
        <fileSet>
            <directory>${project.basedir}/</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*.sh</include>
                <include>logback.xml</include>
            </includes>
            <directoryMode>777</directoryMode>
            <fileMode>755</fileMode>
            <filtered>true</filtered>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <!--        <fileSet>
                    <directory>${project.build.directory}/</directory>
                    <outputDirectory>/</outputDirectory>
                    <includes>
                        <include>*.jar</include>
                    </includes>
                </fileSet>-->
        <fileSet>
            <directory>${project.basedir}/src/main/resources</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>*.sh</include>
                <include>docker/**</include>
                <include>logback.xml</include>
                <include>data/**</include>
<!--                <include>db/**</include>-->
                <include>plugin/**</include>
                <include>job/**</include>
                <include>conf/**</include>
                <include>dict/**</include>
            </includes>
            <directoryMode>777</directoryMode>
            <fileMode>755</fileMode>
            <filtered>true</filtered>
            <lineEnding>unix</lineEnding>
        </fileSet>
        <fileSet>
            <directory>${project.basedir}/src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>application.yaml</include>
                <include>application.properties</include>
                <include>application-${app.env}.properties</include>
                <include>application-${app.env}.yaml</include>
            </includes>
            <filtered>false</filtered>
        </fileSet>
    </fileSets>
</assembly>