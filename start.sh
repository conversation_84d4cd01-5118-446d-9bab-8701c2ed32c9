#!/bin/bash

export JAVA_HOME=/data/apps/endpoint/data-route/jdk-21.0.5
export CLASSPATH=.:$JAVA_HOME/lib:/lib
export PATH=$JAVA_HOME/bin:$PATH

option=

for arg in "$@"
do
   key=${arg%%=*}
   value=${arg#*=}
   case $key in
   --JAVA_OPTS)
      JAVA_OPTS=$value
      ;;
   --JMX_REMOTE_OPTS)
      JMX_REMOTE_OPTS=$value
      ;;
   *)
      option="$option $key=$value"
      ;;
   esac
done

cd `dirname -- $0`

workspace=$(pwd)

pid=$(ps -ef | grep "${workspace}.*${app.mainClass-short}" | grep java | grep -v "grep"| awk '{print $2}')

if [ -n "$pid" ];then
    echo "程序进程已存在，将不会执行启动操作"
    exit 0
fi

#CLASSPATH="./${app.name}.jar:${workspace}"
CLASSPATH=".:${workspace}"
for file in "${workspace}"/lib/*.jar;
do
   CLASSPATH="$CLASSPATH":"$file"
done

JAVA_OPTS="-Dfile.encoding=UTF-8 -Djava.net.preferIPv4Stack=true ${JAVA_OPTS}"
JAVA_OPTS="-Dcollector.home=${workspace} ${JAVA_OPTS}"

arch=$(uname -i)

cpus=$(grep -c processor /proc/cpuinfo)
threads=$(((cpus + 3) / 4))

tenuringThreshold=0

if [ ${threads} -gt 12 ]; then
   tenuringThreshold=12
else
   tenuringThreshold=${threads}
fi

# G1
# export MEM=1g  # used for java heap size
GC_OPTS="-XX:+UseG1GC -XX:ParallelGCThreads=${threads} -XX:ConcGCThreads=${threads} -XX:InitiatingHeapOccupancyPercent=45 -XX:NewRatio=2 -XX:SurvivorRatio=8 -XX:MaxTenuringThreshold=${tenuringThreshold} -XX:-UseAdaptiveSizePolicy"
JAVA_OPTS="${JAVA_OPTS} ${GC_OPTS}"

# 根据实际需要增减
memMax=`free -g|awk 'NR==2' |awk '{print$2}'`
JAVA_OPTS="${JAVA_OPTS} -Xms512m -Xmx$(((${memMax} + 3) / 4))g"

# GC_LOGS="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${workspace}/logs/${app.name}_%p.hprof -XX:+PrintGCDetails -Xloggc:${workspace}/logs/${app.name}-gc.log -XX:+PrintGCTimeStamps"
# JAVA_OPTS="${JAVA_OPTS} ${GC_LOGS}"

JMX_OPTS="-Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=17387 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.authenticate=false -Djava.net.preferIPv4Stack=true -Djava.rmi.server.hostname=0.0.0.0"
# 需要查看JMX数据时放开
# JAVA_OPTS="${JAVA_OPTS} ${JMX_OPTS}"

JMX_REMOTE_OPTS=" -Xdebug -Xrunjdwp:transport=dt_socket,address=*:8756,server=y,suspend=n "
# 需要远程调试时放开
# JAVA_OPTS="${JAVA_OPTS} ${JMX_REMOTE_OPTS}"
##<<<

# 用于开发环境流水线填充ip
if [ "${app.env}" = "dev" ]; then
    ip_address=$(ifconfig ens192 | grep 'inet ' | awk '{print $2}')
    option="$option --local.host.ip=$ip_address"
fi

# jasypt password config
JAVA_JASYPT_OPTS="-Djasypt.encryptor.password=ailand@VFR_"
JAVA_OPTS="${JAVA_OPTS} ${JAVA_JASYPT_OPTS}"

echo "等待启动 ${app.mainClass}..."
echo "java $JAVA_OPTS ${app.mainClass} --spring.profiles.active=${app.env} $option"
nohup java "--add-exports=java.base/sun.security.x509=ALL-UNNAMED" "--add-exports=java.security.jgss/sun.security.krb5.internal.ktab=ALL-UNNAMED" "-Dailand.crypt.aes.vipara=s5YHnw8z#U]kB;.0" "-Dailand.crypt.aes.key=SkWrZ;9A#jHc(N_6" "-Dailand.aes.iv=dbappsecurityflk" "-Dailand.sign.aes.key=abcdef0123456789" "-Dailand.sign.aes.iv=0123456789abcdef" $JAVA_OPTS -classpath "$CLASSPATH" ${app.mainClass} --spring.profiles.active=${app.env} $option > /dev/null 2>&1 &

echo "${app.mainClass} started..."
