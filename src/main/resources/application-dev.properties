knife4j.enable=true
knife4j.production=false
springdoc.api-docs.enabled=true
local.host.ip=127.0.0.1
# å½åç»ç«¯å¯¹å¤ip å¯¹åºå¹³å° 210ã211
ailand.endpoint.ip=https://${local.host.ip}:4443
data-route.aigate.base-url=https://${local.host.ip}:6443
data-route.aigate.access-key-id=ceb9d55f8011b7ec072930ba7426f65d
data-route.aigate.access-key-secret=f63f1b13603f7afa01f54ed8e39865f096453be7dfa7e323872f78795aaa04fc
# APIç½å³
data-route.gateway.manager-base-url=http://${local.host.ip}:7443
# AiSort
data-route.aisort.base-url=https://${local.host.ip}:8081
ailand.file-storage.base-path=/data/apps/disk-data/data-route
logging.level.root=INFO
#logging.level.org.springframework=INFO
logging.level.io.lettuce=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.io.netty=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.github.lianjiatech=ERROR
logging.level.org.flywaydb=ERROR
#logging.level.org.springframework.web.client=DEBUG
#logging.level.org.apache.hc.client5.http=DEBUG
logging.level.com.ailpha.ailand.dataroute.endpoint=trace
#logging.level.com.github.lianjiatech.retrofit=debug
#retrofit.global-log.log-level=debug
#retrofit.global-log.log-strategy=body
ailand.flyway.enabled=false
spring.data.redis.host=${local.host.ip}
ailand.iam-server.base-url=https://${local.host.ip}:2443
server.servlet.session.timeout=12h
ailand.asset-file.keep=true
ailand.iam-server.external-url=https://${local.host.ip}:2443
ailand.deploy.mode=share
