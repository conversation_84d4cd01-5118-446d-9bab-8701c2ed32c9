### äº¤ææå¹³å°ç 
trader.platform-code=TRADER_PLATFORM_CODE
### æ°ç±å¨ç»ç«¯å¯¹å¤ipï¼ååï¼
ailand.endpoint.ip=https://EXTERNAL_ENDPOINT_HOST:4443



data-route.aigate.access-key-id=AIGATE_ACCESS_KEY_ID
data-route.aigate.access-key-secret=AIGATE_ACCESS_KEY_SECRET

ailand.iam-server.base-url=https://INTERNAL_ENDPOINT_HOST:2443
ailand.file-storage.base-path=FILE_STORAGE_BASE_PATH

logging.level.root=DEBUG
#logging.level.org.springframework=INFO
logging.level.io.lettuce=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.io.netty=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.github.lianjiatech=ERROR
logging.level.org.flywaydb=ERROR
#logging.level.org.springframework.web.client=DEBUG
logging.level.org.apache.hc.client5.http=ERROR
logging.level.com.ailpha.ailand.dataroute.endpoint=trace
#logging.level.com.github.lianjiatech.retrofit=debug
#retrofit.global-log.log-level=debug
#retrofit.global-log.log-strategy=body

ailand.iam-server.external-url=https://IAM_EXTERNAL_URL:2443

ailand.internal.ip=INTERNAL_ENDPOINT_HOST