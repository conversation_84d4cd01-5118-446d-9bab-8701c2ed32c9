<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <property resource="application.properties" />

    <property name="APP_NAME" value="${spring.application.name}"/>
    <property name="LOG_DIR" value="${logging.file.path}"/>

    <property name="maxHistory" value="7"/>
    <property name="maxSize" value="100MB"/>

    <appender class="ch.qos.logback.core.ConsoleAppender" name="STDOUT">
        <encoder>
            <pattern>%blue(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %magenta([%thread]) %cyan(%logger{36} - [%M:%L]) - %msg%n</pattern>
        </encoder>
    </appender>

    <appender class="ch.qos.logback.core.rolling.RollingFileAppender" name="FILE">
        <File>${LOG_DIR}/${APP_NAME}.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_DIR}/${APP_NAME}.log.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
            <maxFileSize>${maxSize}</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>