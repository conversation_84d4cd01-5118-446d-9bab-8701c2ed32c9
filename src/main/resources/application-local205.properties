spring.application.name=data-route-endpoint
ailand.application.version=1.0.0
server.port=8080

local.host.ip=***********

# å½åç»ç«¯å¯¹å¤ip å¯¹åºå¹³å° 210ã211
ailand.endpoint.ip=https://${local.host.ip}:4443

spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
#https://scc.das-security.cn/#/docs/specifications?position=das-rest-sdk%2525E6%25258E%2525A5%2525E5%252585%2525A5%2525E6%25258C%252587%2525E5%25258D%252597
#é»è®¤trueï¼å¼å¯å¨å±æ¦æªRestfulApiExceptionå¼å¸¸:RestfulApiExceptionHandler
das.rest.enable=true
#é»è®¤falseï¼å¼å¯apiç»è®¡
das.rest.scan.enable=false
#é»è®¤false ï¼å¼å¯ ResponseBodyAdvice å¿«éè§£æååºï¼è¯¦è§5.3
das.rest.response.advice.enable=false

data-route.aigate.access-key-id=ceb9d55f8011b7ec072930ba7426f65d
data-route.aigate.access-key-secret=f63f1b13603f7afa01f54ed8e39865f096453be7dfa7e323872f78795aaa04fc

ailand.license-server.base-url=http://***********:9876
ailand.internal.ip=***********
ailand.asset-file.keep=true
ailand.file-storage.base-path=/mnt/d/disk-data/206

logging.level.root=INFO
#logging.level.org.springframework=INFO
logging.level.io.lettuce=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.io.netty=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.github.lianjiatech=ERROR
logging.level.org.flywaydb=ERROR
#logging.level.org.springframework.web.client=DEBUG
#logging.level.org.apache.hc.client5.http=DEBUG
logging.level.com.ailpha.ailand.dataroute.endpoint=trace
logging.level.com.ailpha.ailand.dataroute.endpoint.common.interceptor=trace
logging.level.com.github.lianjiatech.retrofit=debug
retrofit.global-log.log-level=debug
retrofit.global-log.log-strategy=body

ailand.flyway.enabled=false
spring.data.redis.host=${local.host.ip}
ailand.iam-server.base-url=https://${local.host.ip}:443
#server.servlet.session.timeout=1m
#spring.session.timeout=60
logging.level.org.springframework.session=DEBUG

spring.datasource.url=jdbc:highgo://${local.host.ip}:5866/highgo?currentSchema=data_route_public
spring.datasource.username=highgo
spring.datasource.password=O%0baNnBg7fITxlE
ailand.useCaptcha=false
needDecryptPwd=false
checkLicense=false

# application.properties
logging.level.org.ehcache=DEBUG
logging.level.net.sf.ehcache=DEBUG
ailand.deploy.mode=share

# Spring Session JDBCéç½®
spring.session.jdbc.initialize-schema=always
spring.session.jdbc.schema=classpath:org/springframework/session/jdbc/schema-postgresql.sql
spring.session.jdbc.table-name=SPRING_SESSION
server.servlet.session.timeout=30m

# å¤ç¹ç»å½éç½®
ailand.security.login.maximum-sessions=5
ailand.security.login.exception-if-maximum-exceeded=false

upgrade.md5.skip-check=true