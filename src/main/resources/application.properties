spring.application.name=data-route-endpoint
ailand.application.version=1.0.0
server.port=8080
spring.profiles.active=dev

spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true

ailand.file-storage.base-path=/data/apps/data-route-endpoint/data
ailand.asset-file.keep=true
#https://scc.das-security.cn/#/docs/specifications?position=das-rest-sdk%2525E6%25258E%2525A5%2525E5%252585%2525A5%2525E6%25258C%252587%2525E5%25258D%252597
#é»è®¤trueï¼å¼å¯å¨å±æ¦æªRestfulApiExceptionå¼å¸¸:RestfulApiExceptionHandler
das.rest.enable=true
#é»è®¤falseï¼å¼å¯apiç»è®¡
das.rest.scan.enable=false
#é»è®¤false ï¼å¼å¯ ResponseBodyAdvice å¿«éè§£æååºï¼è¯¦è§5.3
das.rest.response.advice.enable=false
trader.platform-code=1858337670212923394
logging.level.root=INFO
logging.file.path=logs
logging.level.io.swagger=ERROR
logging.level.io.netty=ERROR
logging.level.springfox=ERROR
logging.level.Validator=ERROR
logging.level.org.hibernate=ERROR
logging.level.javax.activation=ERROR
logging.level.com.dbapp.licence=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.org.apache.tomcat=ERROR
logging.level.org.apache.coyote=ERROR
logging.level.org.elasticsearch=ERROR
logging.level.jdk.event.security=ERROR
logging.level.org.springframework=ERROR
logging.level.org.apache.catalina=ERROR
logging.level.ch.qos.logback=ERROR
logging.level.org.apache.hc.client5=ERROR
logging.level.com.github.lianjiatech.retrofit=ERROR
logging.level.org.springframework.boot.autoconfigure=ERROR
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

# Spring Session JDBCéç½®
spring.session.jdbc.initialize-schema=always
spring.session.jdbc.schema=classpath:org/springframework/session/jdbc/schema-postgresql.sql
spring.session.jdbc.table-name=SPRING_SESSION
server.servlet.session.timeout=30m

# å¤ç¹ç»å½éç½®
ailand.security.login.maximum-sessions=5
ailand.security.login.exception-if-maximum-exceeded=false
ailand.internal.ip=127.0.0.1

upgrade.md5.skip-check=false
ailand.endpoint.prefix.url=${ailand.endpoint.ip}/_data-route