{"core": {"container": {"trace": {"enable": "false"}, "job": {"reportInterval": 10000, "id": ""}, "taskGroup": {"channel": 1}}, "transport": {"exchanger": {"class": "com.ailpha.ailand.collecotr.core.transport.exchanger.BufferedRecordExchanger", "bufferSize": 32}, "channel": {"byteCapacity": 67108864, "flowControlInterval": 20, "class": "com.ailpha.ailand.collecotr.core.transport.channel.memory.MemoryChannel", "speed": {"byte": -1, "record": -1}, "capacity": 512}}, "statistics": {"collector": {"plugin": {"taskClass": "com.ailpha.ailand.collecotr.core.statistics.plugin.task.StdoutPluginCollector", "maxDirtyNumber": 10}}}}, "entry": {"jvm": "-Xms1G -Xmx1G"}, "common": {"column": {"dateFormat": "yyyy-MM-dd", "datetimeFormat": "yyyy-MM-dd HH:mm:ss", "timeFormat": "HH:mm:ss", "extraFormats": ["yyyyMMdd"], "timeZone": "GMT+8", "encoding": "utf-8"}}, "plugin": {"reader": {"serverfilepathreader": {"name": "serverfilepathreader", "class": "com.ailpha.ailand.plugin.reader.serverfilepath.ServerFilePathReader", "description": "useScene: test. mechanism: use ailand-data-collector framework to transport data from hdfs. warn: The more you know about the data, the less problems you encounter.", "developer": "ah"}}, "writer": {"filepathwriter": {"name": "filepathwriter", "class": "com.ailpha.ailand.collector.plugin.writer.filepathwriter.FilePathWriter", "description": "useScene: test. mechanism: use ailand-data-collector framework to transport data to txt file. warn: The more you know about the data, the less problems you encounter.", "developer": "ah"}}}, "job": {"content": [{"reader": {"name": "serverfilepathreader", "parameter": {"path": "", "column": [], "fileType": "csv", "encoding": "UTF-8", "fieldDelimiter": ","}}, "writer": {"name": "filepathwriter", "parameter": {"path": "", "fileName": "", "writeMode": "", "fieldDelimiter": "", "dateFormat": "", "sdk-params": ""}}}], "setting": {"errorLimit": {"record": 0, "percentage": 0.02}, "speed": {"byte": 10485760}}, "callbackUrl": ""}}