{"mysql": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:mysql://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "oracle": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:oracle:thin:@", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "hdfs": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "URL", "code": "jdbcUrl", "type": "text", "prev": "hdfs://", "tooltip": true, "require": true}], "hive": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:hive2://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "odps": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:odps:", "tooltip": true, "require": true}, {"name": "access_id", "code": "access_id", "type": "text", "require": true}, {"name": "access_key", "code": "access_key", "type": "password", "require": true}, {"name": "project_name", "code": "project_name", "type": "text", "require": true}], "postgresql": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:postgresql://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "gaussdb200": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:postgresql://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "dm": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:dm://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "kingbase": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "JDBC URL", "code": "jdbcUrl", "type": "text", "prev": "jdbc:postgresql://", "tooltip": true, "require": true}, {"name": "用户名", "code": "username", "type": "text", "require": true}, {"name": "密码", "code": "password", "type": "password", "require": true}], "serverfile": [{"name": "数据源名称", "code": "name", "type": "text", "require": true}, {"name": "数据源描述", "code": "desc", "type": "text", "require": false}, {"name": "前置机", "code": "executorNo", "type": "select", "require": true, "desc": "前置机下拉单选，保存前置机 id"}, {"name": "数据目录", "code": "jdbcUrl", "type": "text", "require": true}]}