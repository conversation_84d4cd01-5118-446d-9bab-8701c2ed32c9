ailand:
  security:
    white-apis:
      - /node/simple/page
      - /login
      - /captcha
      - /user/companyAdmin/register
      - /router/**
      # 前置机数据集任务执行完毕回调接口
      - /callback/executor/*
      # /data-asset/download/* 这部分路由minio accessKey进行鉴权
      - /data-asset/download/**
      - /sse/receiveDataRouteMessage
      - /swagger-ui/**
      - /v3/**
      - /third/download/solution/**
      - /third/api-gateway/log-report
      - /third/contract/execute/callback
      - /third/offline/order
      - /third/userinfo
      - /third/userinfo/**
      - /third/data-asset/**
      - /swagger-ui.html
      - /doc.html
      - /webjars/**
      - /favicon.ico
      # appKey appSecret 生成查看,已在代码中控制仅本地127.0.0.1可访问
      - /magic/app/**
      # 开放接口获取token,鉴权逻辑在 OpenAPITokenController
      - /openapi/token
      - /callback/mpc/**
      - /callback/tee/**
    csrf-white-apis:
      - /login
      - /router/**
      - /callback/executor/*
      - /data-asset/download/**
      - /dict/codes
      - /sse/receiveDataRouteMessage
      - /swagger-ui/**
      - /v3/**
      - /third/download/solution/**
      - /third/api-gateway/log-report
      - /third/contract/execute/callback
      - /third/offline/order
      - /third/userinfo
      - /third/userinfo/**
      - /third/data-asset/**
      - /swagger-ui.html
      - /doc.html
      - /webjars/**
      - /favicon.ico
      - /magic/app/**
      - /openapi/token
      - /callback/mpc/**
      - /callback/tee/**
      - /demand/download/solutionFile
  license-server:
    base-url: http://**************:9876
spring:
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
    org.hibernate.orm.jdbc.bind: TRACE  #