CREATE TABLE "upgrade_package_info"
(
    "id"          varchar(100) NOT NULL PRIMARY KEY,
    "name"        varchar(100),
    "module"      varchar(100),
    "version"     varchar(100),
    "description" varchar(1000),
    "md5"         varchar(100),
    "source"      varchar(100),
    "file_path"   varchar(100),
    "upload_time" timestamp(6),
    "create_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON COLUMN "upgrade_package_info"."id" IS 'ID';

COMMENT ON COLUMN "upgrade_package_info"."name" IS '名称';

COMMENT ON COLUMN "upgrade_package_info"."module" IS '模块：DATA_ROUTE（连接器）';

COMMENT ON COLUMN "upgrade_package_info"."version" IS '版本';

COMMENT ON COLUMN "upgrade_package_info"."description" IS '描述';

COMMENT ON COLUMN "upgrade_package_info"."md5" IS 'MD5';

COMMENT ON COLUMN "upgrade_package_info"."source" IS '来源：DATA_ROUTE（连接器）';

COMMENT ON COLUMN "upgrade_package_info"."file_path" IS '文件路径';

COMMENT ON COLUMN "upgrade_package_info"."upload_time" IS '上传时间';
