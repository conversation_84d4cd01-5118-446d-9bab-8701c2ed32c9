CREATE TABLE if not exists t_blockchain_plugin_api_detail
(
    id          BIGINT       NOT NULL GENERATED ALWAYS AS IDENTITY,
    url         VARCHAR(255) NOT NULL,
    method      VARCHAR(10)  NOT NULL,
    params      JSON,
    body        TEXT,
    body_type   VARCHAR(20),
    headers     JSO<PERSON>,
    plugin_id   BIGINT,
    ext         TEXT,
    type        VARCHAR(50),
    up_data_field        VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON COLUMN t_blockchain_plugin_api_detail.up_data_field IS 'body 为 json 时指定该字段作为上传数据字段';

CREATE TABLE if not exists t_blockchain_plugin_detail
(
    id          BIGINT       NOT NULL GENERATED ALWAYS AS IDENTITY,
    name        VARCHAR(200) NOT NULL,
    description VARCHAR(200),
    ext         TEXT,
    create_time TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    type        VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    module_type VARCHAR(200) NOT NULL,
    status      INTEGER      NOT NULL DEFAULT 1
);

COMMENT
ON COLUMN  t_blockchain_plugin_detail.status IS '0-未启用，1-启用，-1-删除';