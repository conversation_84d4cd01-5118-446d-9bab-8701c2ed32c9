
CREATE TABLE t_negotiate_transfer (
      id VARCHAR(64) PRIMARY KEY,
      node_id VARCHAR(64),
      trading_strategy_code VARCHAR(100),
      trading_strategy_name VARCHAR(100),
      ctrl_instruction_id VARCHAR(100),
      transaction_execution_strategy json,
      transfer_mode VARCHAR(6),
      extend json,
      create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_negotiate_transfer IS '协商传输表';

-- 为字段添加注释（如果上面字段级COMMENT不支持，可以使用以下方式）
COMMENT ON COLUMN t_negotiate_transfer.id IS '主键ID';
COMMENT ON COLUMN t_negotiate_transfer.node_id IS '业务节点ID';
COMMENT ON COLUMN t_negotiate_transfer.trading_strategy_code IS '交易合约标识';
COMMENT ON COLUMN t_negotiate_transfer.trading_strategy_name IS '交易合约名称';
COMMENT ON COLUMN t_negotiate_transfer.ctrl_instruction_id IS '控制指令编号';
COMMENT ON COLUMN t_negotiate_transfer.transaction_execution_strategy IS '交易合约控制指令';
COMMENT ON COLUMN t_negotiate_transfer.transfer_mode IS '传输模式：当前仅支持【主动拉取】推送-1 拉取-2';

