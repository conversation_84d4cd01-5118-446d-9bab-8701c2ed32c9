
CREATE TABLE if not exists t_local_product_ref (
                                                   id varchar(64)  NOT NULL,
    asset_id varchar(64) ,
    product_platform_id varchar(64),
    company_id varchar(100) ,
    extend text ,
    create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT t_local_product_ref_pkey PRIMARY KEY (id)
    );