CREATE TABLE "upgrade_task_info"
(
    "id"                varchar(100) NOT NULL PRIMARY KEY,
    "before_package_id" varchar(100),
    "before_version"    varchar(100),
    "after_package_id"  varchar(100),
    "after_version"     varchar(100),
    "md5"               varchar(100),
    "module"            varchar(100),
    "source"            varchar(100),
    "upgrade_time"      timestamp(6),
    "status"            varchar(100),
    "log"               text,
    "create_time"       timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_time"       timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON COLUMN "upgrade_task_info"."id" IS 'ID';

COMMENT ON COLUMN "upgrade_task_info"."before_package_id" IS '升级前包ID';

COMMENT ON COLUMN "upgrade_task_info"."before_version" IS '升级前版本';

COMMENT ON COLUMN "upgrade_task_info"."after_package_id" IS '升级后包ID';

COMMENT ON COLUMN "upgrade_task_info"."after_version" IS '升级后版本';

COMMENT ON COLUMN "upgrade_task_info"."md5" IS 'MD5';

COMMENT ON COLUMN "upgrade_task_info"."module" IS '模块：DATA_ROUTE（连接器）';

COMMENT ON COLUMN "upgrade_task_info"."source" IS '来源：DATA_ROUTE（连接器）';

COMMENT ON COLUMN "upgrade_task_info"."upgrade_time" IS '升级时间';

COMMENT ON COLUMN "upgrade_task_info"."status" IS '升级状态：WAIT（待升级）UPGRADING（升级中）SUCCESS（升级成功）FAILURE（升级失败）INVALID（已失效）';

COMMENT ON COLUMN "upgrade_task_info"."log" IS '日志';