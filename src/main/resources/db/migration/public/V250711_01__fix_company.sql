drop table if exists t_company;
-- 简化表结构

CREATE TABLE if not exists t_company
(
    id           bigint primary key,
    node_id      varchar(36) null,
    company_id   varchar(36) null,
    created_by   VARCHAR(36),                         -- 创建人
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_by   VARCHAR(36),                         -- 更新人
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    deleted      BOOLEAN   DEFAULT FALSE,             -- 是否删除
    ext          text      default '{}'
)