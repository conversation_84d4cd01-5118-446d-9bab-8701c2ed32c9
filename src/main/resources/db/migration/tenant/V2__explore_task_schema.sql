CREATE TABLE IF NOT EXISTS t_heng_nao_agent_task
(
    id          BIGSERIAL PRIMARY KEY,                          -- 任务主键
    task_id     VARCHAR(64),                                    -- 恒脑任务ID
    task_type   VARCHAR(64),                                    -- 恒脑任务ID
    user_id     varchar(36) NOT NULL,                           -- 用户ID
    file_name   VARCHAR(255),                                   -- 上传文件名
    status      INT         NOT NULL,                           -- 任务状态（1-执行中，2-成功，3-取消，4-失败）
    result_json TEXT,                                           -- 任务结果（JSON）
    fail_reason VARCHAR(1024),                                  -- 失败原因
    ext         json                 default '{}'::json,
    created_at  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at  TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

COMMENT ON TABLE t_heng_nao_agent_task IS '恒脑智能体异步任务记录';
COMMENT ON COLUMN t_heng_nao_agent_task.id IS '任务主键';
COMMENT ON COLUMN t_heng_nao_agent_task.task_id IS '恒脑任务ID';
COMMENT ON COLUMN t_heng_nao_agent_task.task_type IS '恒脑任务类型';
COMMENT ON COLUMN t_heng_nao_agent_task.user_id IS '用户ID';
COMMENT ON COLUMN t_heng_nao_agent_task.file_name IS '上传文件名';
COMMENT ON COLUMN t_heng_nao_agent_task.status IS '任务状态（1-执行中，2-成功，3-取消，4-失败）';
COMMENT ON COLUMN t_heng_nao_agent_task.result_json IS '任务结果（JSON）';
COMMENT ON COLUMN t_heng_nao_agent_task.fail_reason IS '失败原因';
COMMENT ON COLUMN t_heng_nao_agent_task.created_at IS '创建时间';
COMMENT ON COLUMN t_heng_nao_agent_task.updated_at IS '更新时间';