-- 订单表迁移
CREATE TABLE dr_order_approval_record (
      id varchar(64)  NOT NULL,
      asset_id varchar(64) ,
      asset_name varchar(100) ,
      delivery_mode varchar(100) ,
      beneficiary_id varchar(64) ,
      beneficiary_username varchar(100) ,
      beneficiary_router_id varchar(100) ,
      beneficiary_enterprise_name varchar(100) ,
      beneficiary_enterprise_property varchar(100) ,
      approver_id varchar(64) ,
      approver_username varchar(100) ,
      approver_router_id varchar(100) ,
      approver_enterprise_name varchar(100) ,
      charging_way varchar(100) ,
      metering_way varchar(100) ,
      allowance int8,
      successful_usage int8,
      unsuccessful_usage int8,
      status varchar(100) ,
      extend json ,
      expire_date timestamp(6),
      pull_time timestamp(6),
      create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      type varchar(255) ,
      approve_time timestamp(6),
      CONSTRAINT dr_order_approval_record_pkey PRIMARY KEY (id)
)
;

COMMENT ON COLUMN dr_order_approval_record.id IS '订单编号';

COMMENT ON COLUMN dr_order_approval_record.asset_id IS '资产ID';

COMMENT ON COLUMN dr_order_approval_record.asset_name IS '资产名称';

COMMENT ON COLUMN dr_order_approval_record.delivery_mode IS '交付方式';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_id IS '获益人用户ID';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_username IS '获益人用户名';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_router_id IS '获益方数由器ID';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_enterprise_name IS '获益方企业名称';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_enterprise_property IS '获益人方企业性质';

COMMENT ON COLUMN dr_order_approval_record.approver_id IS '审批人用户ID';

COMMENT ON COLUMN dr_order_approval_record.approver_username IS '审批人用户名';

COMMENT ON COLUMN dr_order_approval_record.approver_router_id IS '审批方数由器ID';

COMMENT ON COLUMN dr_order_approval_record.approver_enterprise_name IS '审批方企业名称';

COMMENT ON COLUMN dr_order_approval_record.charging_way IS '计费方式：预付费、后付费';

COMMENT ON COLUMN dr_order_approval_record.metering_way IS '计量方式：按次、按时间';

COMMENT ON COLUMN dr_order_approval_record.allowance IS '使用次数上限';

COMMENT ON COLUMN dr_order_approval_record.successful_usage IS '成功使用次数';

COMMENT ON COLUMN dr_order_approval_record.unsuccessful_usage IS '失败使用次数';

COMMENT ON COLUMN dr_order_approval_record.status IS '状态';

COMMENT ON COLUMN dr_order_approval_record.expire_date IS '有效期';

COMMENT ON COLUMN dr_order_approval_record.type IS '资产类型';

COMMENT ON COLUMN dr_order_approval_record.approve_time IS '审批通过时间';


CREATE TABLE dr_scene_asset (
        id varchar(64)  NOT NULL,
        delivery_scene_id varchar(64) ,
        data_asset_id varchar(255) ,
        api_id varchar(255) ,
        order_id varchar(255) ,
        api_key varchar(255) ,
        ext json,
        CONSTRAINT dr_scene_asset_pkey PRIMARY KEY (id)
)
;


CREATE UNIQUE INDEX api_id_key ON dr_scene_asset USING btree (
    api_id  pg_catalog.text_ops ASC NULLS LAST
    );

COMMENT ON COLUMN dr_scene_asset.delivery_scene_id IS '场景id';

COMMENT ON COLUMN dr_scene_asset.data_asset_id IS '资产id';

COMMENT ON COLUMN dr_scene_asset.api_id IS 'api id';

COMMENT ON COLUMN dr_scene_asset.order_id IS '订单id';

COMMENT ON COLUMN dr_scene_asset.api_key IS 'apiKey';



CREATE TABLE dr_delivery_scene (
       id varchar(64)  NOT NULL,
       scene_status varchar(64) ,
       delivery_type varchar(255) ,
       digital_scene_id varchar(255) ,
       digital_scene_name varchar(255) ,
       ext json,
       create_user varchar(255) ,
       create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
       CONSTRAINT dr_delivery_scene_pkey PRIMARY KEY (id)
)
;


COMMENT ON COLUMN dr_delivery_scene.scene_status IS '状态';

COMMENT ON COLUMN dr_delivery_scene.delivery_type IS '交付方式';

COMMENT ON COLUMN dr_delivery_scene.digital_scene_id IS '数字证书 —— 合规场景id';

COMMENT ON COLUMN dr_delivery_scene.digital_scene_name IS '数字证书 —— 合规场景名称 冗余存储';

COMMENT ON COLUMN dr_delivery_scene.ext IS '扩展';

COMMENT ON COLUMN dr_delivery_scene.create_user IS '创建人';

COMMENT ON COLUMN dr_delivery_scene.create_time IS '创建时间';


CREATE TABLE dr_asset_beneficiary_rel (
      id varchar(64)  NOT NULL,
      asset_id varchar(64) ,
      beneficiary_id varchar(64) ,
      order_id varchar(100) ,
      extend text,
      create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT dr_asset_beneficiary_rel_pkey PRIMARY KEY (id)
)
;


COMMENT ON COLUMN dr_asset_beneficiary_rel.id IS 'ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.asset_id IS '资产ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.beneficiary_id IS '获益人用户ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.order_id IS '订单ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.extend IS '扩展字段json（api鉴权key等信息）';