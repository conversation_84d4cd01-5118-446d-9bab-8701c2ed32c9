create table if not exists t_role
(
    id          varchar(36) not null primary key,
    name        varchar(32) not null,
    description varchar(255)
);

comment on column t_role.id is '主键';
comment on column t_role.name is '角色名称';
comment on column t_role.description is '描述';

insert into t_role(id, name, description)
values ('1', 'SUPER_ADMIN', '系统管理员');
insert into t_role(id, name, description)
values ('2', 'TRADER', '交易方');
insert into t_role(id, name, description)
values ('3', 'COMPANY_ADMIN', '交易方');

create table if not exists t_menu
(
    id        varchar(36) not null primary key,
    name      varchar(32) not null,
    parent_id varchar(32) default '0'
);
comment on column t_menu.id is '主键';
comment on column t_menu.name is '菜单名称';
comment on column t_menu.parent_id is '父菜单ID';

insert into t_menu(id, name, parent_id)
values ('RouterRegister', '连接器入网', '0');
insert into t_menu(id, name, parent_id)
values ('User<PERSON>anager', '用户管理', '0');
insert into t_menu(id, name, parent_id)
values ('DataInvoiceStorage', '数据发票存证', '0');
insert into t_menu(id, name, parent_id)
values ('BI', '大屏监控', '0');
insert into t_menu(id, name, parent_id)
values ('DataAssetManager', '数据资产登记', '0');
insert into t_menu(id, name, parent_id)
values ('UseSceneManager', '交付场景管理', '0');
insert into t_menu(id, name, parent_id)
values ('OrderManager', '订单管理', '0');
insert into t_menu(id, name, parent_id)
values ('PersonalCenter', '个人中心', '0');
insert into t_menu(id, name, parent_id)
values ('PersonalUserInfo', '个人信息', '0');
insert into t_menu(id, name, parent_id)
values ('PersonalLogs', '操作日志', '0');
insert into t_menu(id, name, parent_id)
values ('MessageNotice', '消息通知', '0');
insert into t_menu(id, name, parent_id)
values ('OrderBuyer', '订单-买方', '0');
insert into t_menu(id, name, parent_id)
values ('OrderSeller', '订单-卖方', '0');
insert into t_menu(id, name, parent_id)
values ('LicenseManager', 'license', '0');
insert into t_menu(id, name, parent_id)
values ('PluginManage', '插件管理中心', '0');
insert into t_menu(id, name, parent_id)
values ('BaseManager', '基础能力管理', '0');
insert into t_menu(id, name, parent_id)
values ('MPC', 'MPC多方安全计算', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('TEE', 'TEE可信执行环境', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('API_GATE', '网关', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('TRADE_PLATFORM', '交易平台', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('DATA_INVOICE', '数据发票', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('AUDIT_LOG', '日志审计', 'BaseManager');
insert into t_menu(id, name, parent_id)
values ('CompanyRegister', '企业认证', '0');
insert into t_menu(id, name, parent_id)
values ('DataResourceRegistApproval', '数据资源登记审批', 'DataAssetApproval');
insert into t_menu(id, name, parent_id)
values ('DataProductPublishApproval', '数据产品上架审批', 'DataAssetApproval');
insert into t_menu(id, name, parent_id)
values ('DataProductRegsitApproval', '数据产品登记审批', 'DataAssetApproval');
insert into t_menu(id, name, parent_id)
values ('DelegateInfo', '身份实名认证信息', 'PersonalCenter');

create table if not exists t_user_role
(
    id      varchar(36) primary key not null,
    user_id varchar(32)             not null,
    role_id varchar(32)             not null
);
comment on column t_user_role.id is '主键';
comment on column t_user_role.role_id is '角色ID';
comment on column t_user_role.user_id is '用户ID';

create table if not exists t_permission
(
    id      varchar(36) primary key not null,
    menu_id varchar(32)             not null,
    role_id varchar(32)             not null
);
comment on column t_permission.id is '主键';
comment on column t_permission.role_id is '角色ID';
comment on column t_permission.menu_id is '菜单ID';

insert into t_permission
values ('e8f178dea57d11efa4c60242ac110015', 'LicenseManager', 'SUPER_ADMIN');
-- insert into t_permission
-- values ('e8f178dea57d11efa4c60242ac110025', 'PluginManage', 'SUPER_ADMIN');
insert into t_permission
values ('e8f178dea57d11efa4c60242ac110045', 'RouterRegister', 'SUPER_ADMIN');
insert into t_permission
values ('e8f26b40a57d11efa4c80242ac110065', 'DataInvoiceStorage', 'COMPANY_ADMIN');
insert into t_permission
values ('e8f2d5fea57d11efa4c90242ac110007', 'PersonalCenter', 'SUPER_ADMIN');
insert into t_permission
values ('e8f2d5fea57d11efa4c90242ac110008', 'PersonalCenter', 'COMPANY_ADMIN');
insert into t_permission
values ('e8f2d6fea57d11efa4c90242ac110009', 'PersonalUserInfo', 'SUPER_ADMIN');
insert into t_permission
values ('e8f2d6fea57d11efa4c90242ac11015', 'PersonalUserInfo', 'COMPANY_ADMIN');
insert into t_permission
values ('e8f2d7fea57d11efa4c90242ac110005', 'PersonalLogs', 'COMPANY_ADMIN');
insert into t_permission
values ('e8f2d7fea57d11efa4c90242ac110015', 'PersonalLogs', 'SUPER_ADMIN');
insert into t_permission
values ('e8f38476a57d11efa4cb0242ac110005', 'DataAssetManager', 'TRADER');
insert into t_permission
values ('e8f3d822a57d11efa4cc0242ac110005', 'UseSceneManager', 'TRADER');
insert into t_permission
values ('e8f3d812a57d11efa4cc0242ac110005', 'OrderManager', 'TRADER');
insert into t_permission
values ('f8f3d812a57d11efa4cc0242ac110005', 'PersonalCenter', 'TRADER');
insert into t_permission
values ('g8f3d812a57d11efa4cc0242ac110005', 'PersonalUserInfo', 'TRADER');
insert into t_permission
values ('h8f3d812a57d11efa4cc0242ac110005', 'PersonalLogs', 'TRADER');
insert into t_permission
values ('j8f3d812a57d11efa4cc0242ac110005', 'MessageNotice', 'TRADER');
insert into t_permission
values ('k8f3d812a57d11efa4cc0242ac110005', 'OrderBuyer', 'TRADER');
insert into t_permission
values ('l8f3d812a57d11efa4cc0242ac110005', 'OrderSeller', 'TRADER');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e6g7', 'DataAssetApproval', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e6g8', 'BaseManager', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e6g9', 'MPC', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e6g1', 'API_GATE', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e611', 'TRADE_PLATFORM', 'SUPER_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e621', 'DATA_INVOICE', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e631', 'AUDIT_LOG', 'SUPER_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e1g7', 'DataProductRegsitApproval', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e2g7', 'DataProductPublishApproval', 'COMPANY_ADMIN');
insert into t_permission
values ('3f2d9e8e8f8e4a8e9b9ba2c3d4f5e3g7', 'DataResourceRegistApproval', 'COMPANY_ADMIN');
insert into t_permission
values ('3f1d9e8e8f8e4a8e9b9ba2c3d4f5e3g7', 'DelegateInfo', 'TRADER');


CREATE TABLE if not exists t_sse_message_record
(
    "id"               BIGINT NOT NULL PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    "data_id"          varchar(36),
    "message"          text,
    "trader_role_type" varchar(50),
    "user_id"          varchar(36),
    "type"             varchar(255),
    "create_time"      timestamp,
    "send_time"        timestamp,
    "update_time"      timestamp,
    "read_status"      varchar(255) COLLATE "pg_catalog"."default"
);

create table t_log
(
    id          varchar(36) not null primary key,
    user_id     varchar(32) not null,
    op_type     varchar(32) not null,
    op_module   varchar(32) not null,
    description varchar(50),
    message     varchar(50),
    success     bool,
    username    varchar(50),
    create_time timestamp,
    ip          varchar(50)
);
comment on column t_log.id is '主键';
comment on column t_log.user_id is '用户ID';
comment on column t_log.op_type is '操作类型';
comment on column t_log.op_module is '操作模块';
comment on column t_log.description is '描述';
comment on column t_log.message is '消息';
comment on column t_log.success is '结果';
comment on column t_log.username is '用户名称';
comment on column t_log.create_time is '创建时间';
comment on column t_log.ip is 'ip';

CREATE TABLE IF NOT EXISTS t_plugin_api_type
(
    "id"          BIGINT       NOT NULL PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    "name"        varchar(64)  NOT NULL,
    "mark"        varchar(128) NOT NULL,
    "type"        varchar(32)  NOT NULL,
    "create_time" timestamp(6)
);

COMMENT ON COLUMN t_plugin_api_type.id IS '主键';

COMMENT ON COLUMN t_plugin_api_type.name IS '接口名称';

COMMENT ON COLUMN t_plugin_api_type.mark IS '接口标记';

COMMENT ON COLUMN t_plugin_api_type.type IS '插件类型';

COMMENT ON TABLE t_plugin_api_type IS '插件接口清单';


INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('数商上报', 'business_reporting', 'EXCHANGE', now());
INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('新增产品数据', 'save_data_asset', 'EXCHANGE', now());
INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('编辑产品数据', 'update_data_asset', 'EXCHANGE', now());
INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('产品数据上下架', 'publish_status', 'EXCHANGE', now());
INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('交易登记', 'transaction_register', 'CERTIFICATE', now());
INSERT INTO t_plugin_api_type ("name", "mark", "type", "create_time")
VALUES ('交付登记', 'delivery_register', 'CERTIFICATE', now());


CREATE TABLE t_plugin_api_detail
(
    "id"          BIGINT      NOT NULL PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    "plugin_id"   BIGINT      NOT NULL,
    "api_mark"    varchar(32) NOT NULL,
    "api_name"    varchar(32),
    "api_url"     varchar(512),
    "create_user" varchar(32),
    "create_time" timestamp(6),
    "update_time" timestamp(6),
    "enabled"     bool
)
;

COMMENT ON COLUMN t_plugin_api_detail."id" IS '主键di';

COMMENT ON COLUMN t_plugin_api_detail."plugin_id" IS '插件id';

COMMENT ON COLUMN t_plugin_api_detail."api_mark" IS '接口标识';

COMMENT ON COLUMN t_plugin_api_detail."api_name" IS '接口名称';

COMMENT ON COLUMN t_plugin_api_detail."api_url" IS '接口地址';

COMMENT ON COLUMN t_plugin_api_detail."enabled" IS '是否启用';

COMMENT ON TABLE t_plugin_api_detail IS '插件接口关联表';


CREATE TABLE t_plugin_detail
(
    "id"               BIGINT       NOT NULL PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    "name"             varchar(128) NOT NULL,
    "type"             varchar(32)  NOT NULL,
    "domain"           varchar(255),
    "encrypt_type"     varchar(32),
    "status"           bool         NOT NULL,
    "create_user"      varchar(32)  NOT NULL,
    "create_time"      timestamp(6),
    "update_time"      timestamp(6),
    "plug_credentials" text
)
;

COMMENT ON COLUMN t_plugin_detail."id" IS '主键';

COMMENT ON COLUMN t_plugin_detail."name" IS '插件名称';

COMMENT ON COLUMN t_plugin_detail."type" IS '插件类型plug_type';

COMMENT ON COLUMN t_plugin_detail."domain" IS '对接域名';

COMMENT ON COLUMN t_plugin_detail."encrypt_type" IS '插件加密方式plug_encrypt_type';

COMMENT ON COLUMN t_plugin_detail."status" IS '插件状态';

COMMENT ON COLUMN t_plugin_detail."plug_credentials" IS '插件凭证';

COMMENT ON TABLE t_plugin_detail IS '插件详情表';

create table t_user
(
    id          varchar(36)            not null
        constraint t_user_pk primary key,
    account     varchar(32)            not null,
    real_name   varchar(50)            not null,
    password    varchar(255)           not null,
    phone       varchar(50),
    email       varchar(255),
    deleted     bool      default false,
    enabled     bool      default true,
    ext         json      DEFAULT '{}' NOT NULL,
    create_time timestamp default current_timestamp,
    update_time timestamp default current_timestamp
);

comment on table t_user is '用户表';

comment on column t_user.id is '主键';

comment on column t_user.account is '账号';

comment on column t_user.real_name is '真实姓名';

comment on column t_user.password is '密码';

comment on column t_user.phone is '手机号';

comment on column t_user.email is '邮箱';

comment on column t_user.ext is '扩展字段';
comment on column t_user.enabled is '是否启用';

create table t_access_key
(
    id         integer generated always as identity,
    app_id     varchar(36),
    app_name   varchar(36),
    app_secret varchar(50)
);

comment on column t_access_key.id is 'id';
comment on column t_access_key.app_id is 'app_id';
comment on column t_access_key.app_name is 'app_name 用以标识客户端';
comment on column t_access_key.app_secret is 'app_secret';

insert into t_menu(id, name, parent_id)
values ('Configuration', '系统配置', '0');
insert into t_permission
values ('9bfa34a6afe14aac8beb095e0f207be9', 'Configuration', 'COMPANY_ADMIN');

insert into t_menu(id, name, parent_id)
values ('BaseDataProbe', '基础能力管理-数据探查', 'BaseManager');
insert into t_permission
values ('69fd18ea410f4a198d213b6d516aaaf0', 'BaseDataProbe', 'SUPER_ADMIN');

insert into t_menu(id, name, parent_id)
values ('Demand', '需求大厅', '0');
insert into t_menu(id, name, parent_id)
values ('DemandMap', '需求地图', 'Demand');
insert into t_menu(id, name, parent_id)
values ('DemandManage', '需求发布管理', 'Demand');
insert into t_menu(id, name, parent_id)
values ('DemandNegotiation', '需求磋商', 'Demand');
insert into t_menu(id, name, parent_id)
values ('DemandReview', '需求审核', '0');
insert into t_menu(id, name, parent_id)
values ('DataProductMap', '产品地图', '0');
insert into t_permission
values ('l8f3d812a57d11efa4cc0242acaa0005', 'DataProductMap', 'TRADER');

CREATE TABLE t_data_resource
(
    id                        varchar                               NOT NULL,
    data_resource_platform_id varchar                               NULL,
    platform_id               varchar                               NOT NULL,
    platform_type             integer DEFAULT 0                     NOT NULL,
    data_resource_name        varchar                               NOT NULL,
    description               varchar                               NULL,
    provider                  json    DEFAULT '{}'                  NOT NULL,
    industry                  varchar                               NULL,
    source_type               varchar                               NULL,
    capacity                  integer                               NULL,
    sensitive_level           varchar                               NULL,
    update_frequency          varchar                               NULL,
    item_status               varchar DEFAULT 'item_status1'        NOT NULL,
    push_status               varchar DEFAULT 'pushstatus_not_push' NOT NULL,
    data_ext                  json    DEFAULT '{}'                  NOT NULL,
    delivery_ext              json    DEFAULT '{}'                  NOT NULL,
    user_id                   varchar(32)                           not null,
    username                  varchar(50),
    create_time               TIMESTAMP                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time               TIMESTAMP,
    is_delete                 bool    DEFAULT FALSE,
    CONSTRAINT t_data_resource_pk PRIMARY KEY (id)
);

COMMENT ON COLUMN t_data_resource.id IS '数据资产唯一标识（当前连接器）';
COMMENT ON COLUMN t_data_resource.data_resource_platform_id IS '数据资产唯一标识（数由空间）';
COMMENT ON COLUMN t_data_resource.platform_id IS '连接器ID';
COMMENT ON COLUMN t_data_resource.platform_type IS '连接器类型: 0 标准型 1 全功能型';
COMMENT ON COLUMN t_data_resource.data_resource_name IS '数据资源名称';
COMMENT ON COLUMN t_data_resource.description IS '数据资源描述';
COMMENT ON COLUMN t_data_resource.provider IS '资源提供方信息';
COMMENT ON COLUMN t_data_resource.industry IS '行业分类';
COMMENT ON COLUMN t_data_resource.source_type IS '数据接入方式,可用值:API,DATABASE,FILE';
COMMENT ON COLUMN t_data_resource.capacity IS '数据容量: MB';
COMMENT ON COLUMN t_data_resource.sensitive_level IS '敏感等级';
COMMENT ON COLUMN t_data_resource.update_frequency IS '更新频率';
COMMENT ON COLUMN t_data_resource.item_status IS '审核状态 item_status1 item_status2 item_status3';
COMMENT ON COLUMN t_data_resource.push_status IS '上下线状态 pushstatus_pushed 已发布 pushstatus_not_push 未发布';
COMMENT ON COLUMN t_data_resource.data_ext IS '数据资源扩展信息: dataCoverage 数据覆盖范围；dataCoverageTimeStart 数据覆盖周期开始时间；dataCoverageTimeEnd 数据覆盖周期结束时间；apiQueryWay (接入方式为API)API查询方式,可用值:REALTIME,OFFLINE；dataType 数据类型(接入方式为FILE),可用值:STRUCTURED,UNSTRUCTURED,MODEL 等信息';
COMMENT ON COLUMN t_data_resource.delivery_ext IS 'deliveryModes 交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC；交付信息扩展字段 MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）；debugDataSource 调试数据来源,可用值:generateFromExecutor,sourceSampling,extraUpload,none';

CREATE TABLE t_data_product
(
    id                       varchar                               NOT NULL,
    data_product_platform_id varchar                               NULL,
    platform_id              varchar                               NOT NULL,
    platform_type            integer DEFAULT 0                     NOT NULL,
    data_product_name        varchar                               NOT NULL,
    description              varchar                               NULL,
    provider                 json    DEFAULT '{}'                  NOT NULL,
    industry                 varchar                               NULL,
    source_type              varchar                               NULL,
    capacity                 integer                               NULL,
    sensitive_level          varchar                               NULL,
    update_frequency         varchar                               NULL,
    item_status              varchar DEFAULT 'item_status1'        NOT NULL,
    push_status              varchar DEFAULT 'pushstatus_not_push' NOT NULL,
    data_ext                 json    DEFAULT '{}'                  NOT NULL,
    delivery_ext             json    DEFAULT '{}'                  NOT NULL,
    user_id                  varchar(32)                           not null,
    username                 varchar(50),
    create_time              TIMESTAMP                             NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time              TIMESTAMP,
    is_delete                bool    DEFAULT FALSE,
    CONSTRAINT t_data_product_pk PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN t_data_product.id IS '数据资产唯一标识（当前连接器）';
COMMENT ON COLUMN t_data_product.data_product_platform_id IS '数据资产唯一标识（数由空间）';
COMMENT ON COLUMN t_data_product.platform_id IS '连接器ID';
COMMENT ON COLUMN t_data_product.platform_type IS '连接器类型: 0 标准型 1 全功能型';
COMMENT ON COLUMN t_data_product.data_product_name IS '数据产品名称';
COMMENT ON COLUMN t_data_product.description IS '数据产品描述';
COMMENT ON COLUMN t_data_product.provider IS '资源提供方信息';
COMMENT ON COLUMN t_data_product.industry IS '行业分类';
COMMENT ON COLUMN t_data_product.source_type IS '数据接入方式,可用值:API,DATABASE,FILE';
COMMENT ON COLUMN t_data_product.capacity IS '数据容量: MB';
COMMENT ON COLUMN t_data_product.sensitive_level IS '敏感等级';
COMMENT ON COLUMN t_data_product.update_frequency IS '更新频率';
COMMENT ON COLUMN t_data_product.item_status IS '审核状态 item_status1 item_status2 item_status3';
COMMENT ON COLUMN t_data_product.push_status IS '上下线状态 pushstatus_pushed 已发布 pushstatus_not_push 未发布';
COMMENT ON COLUMN t_data_product.data_ext IS '数据产品扩展信息: dataCoverage 数据覆盖范围；dataCoverageTimeStart 数据覆盖周期开始时间；dataCoverageTimeEnd 数据覆盖周期结束时间；apiQueryWay (接入方式为API)API查询方式,可用值:REALTIME,OFFLINE；dataType 数据类型(接入方式为FILE),可用值:STRUCTURED,UNSTRUCTURED,MODEL；product_from 产品来源: 平台生成(MPC) 外部生成 等信息';
COMMENT ON COLUMN t_data_product.delivery_ext IS '交付信息扩展字段 deliveryModes 交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC；MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）；debugDataSource 调试数据来源,可用值:generateFromExecutor,sourceSampling,extraUpload,none';

CREATE TABLE "statistic_delivery"
(
    "id"            BIGINT NOT NULL GENERATED ALWAYS AS IDENTITY,
    "delivery_id"   varchar(100),
    "delivery_mode" varchar(100),
    "creator_id"    varchar(100),
    "create_time"   timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_time"   timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "statistic_delivery_pkey" PRIMARY KEY ("id")
);

COMMENT ON COLUMN "statistic_delivery"."id" IS 'ID';

COMMENT ON COLUMN "statistic_delivery"."delivery_id" IS '交付ID';

COMMENT ON COLUMN "statistic_delivery"."delivery_mode" IS '交付方式';

COMMENT ON COLUMN "statistic_delivery"."creator_id" IS '创建人用户ID';

insert into t_menu(id, name, parent_id)
values ('Home', '首页', '0');

insert into t_menu(id, name, parent_id)
values ('DataAssetManage', '数据资产管理', '0');
insert into t_menu(id, name, parent_id)
values ('DataMarket', '数据集市', 'DataAssetManage');
insert into t_menu(id, name, parent_id)
values ('DataProbe', '数据探查', 'DataAssetManage');
insert into t_menu(id, name, parent_id)
values ('DataSourceEnter', '数据资源录入', 'DataAssetManage');
insert into t_menu(id, name, parent_id)
values ('DataProductEnter', '数据产品录入', 'DataAssetManage');
insert into t_menu(id, name, parent_id)
values ('DATABASE', '数据库网关', 'DataAssetManage');

insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3731', 'DataAssetManage', 'TRADER');
insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3732', 'DataMarket', 'TRADER');
insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3733', 'DataProbe', 'TRADER');
insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3734', 'DataSourceEnter', 'TRADER');
insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3735', 'DataProductEnter', 'TRADER');
insert into t_permission
values ('1ee942433beb4cda841aa385eaaa3736', 'DATABASE', 'SUPER_ADMIN');

CREATE TABLE SPRING_SESSION
(
    PRIMARY_ID            CHAR(36) NOT NULL,
    SESSION_ID            CHAR(36) NOT NULL,
    CREATION_TIME         BIGINT   NOT NULL,
    LAST_ACCESS_TIME      BIGINT   NOT NULL,
    MAX_INACTIVE_INTERVAL INT      NOT NULL,
    EXPIRY_TIME           BIGINT   NOT NULL,
    PRINCIPAL_NAME        VARCHAR(100),
    CONSTRAINT SPRING_SESSION_PK PRIMARY KEY (PRIMARY_ID)
);

CREATE UNIQUE INDEX SPRING_SESSION_IX1 ON SPRING_SESSION (SESSION_ID);
CREATE INDEX SPRING_SESSION_IX2 ON SPRING_SESSION (EXPIRY_TIME);
CREATE INDEX SPRING_SESSION_IX3 ON SPRING_SESSION (PRINCIPAL_NAME);

CREATE TABLE SPRING_SESSION_ATTRIBUTES
(
    SESSION_PRIMARY_ID CHAR(36)     NOT NULL,
    ATTRIBUTE_NAME     VARCHAR(200) NOT NULL,
    ATTRIBUTE_BYTES    BYTEA        NOT NULL,
    CONSTRAINT SPRING_SESSION_ATTRIBUTES_PK PRIMARY KEY (SESSION_PRIMARY_ID, ATTRIBUTE_NAME),
    CONSTRAINT SPRING_SESSION_ATTRIBUTES_FK FOREIGN KEY (SESSION_PRIMARY_ID) REFERENCES SPRING_SESSION (PRIMARY_ID) ON DELETE CASCADE
);


alter table t_sse_message_record
    add ext text default '{}';

comment
    on column t_sse_message_record.ext is '扩展字段';

-- 添加组织ID字段
ALTER TABLE t_user
    ADD COLUMN company_id bigint;

-- 添加字段注释
COMMENT
    ON COLUMN t_user.company_id IS '企业ID';


CREATE TABLE IF NOT EXISTS t_custom_logo
(
    id          SERIAL PRIMARY KEY,
    config_ext  json      NOT NULL DEFAULT '{}'::json,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time timestamp
);

COMMENT ON TABLE t_custom_logo IS 'Logo配置表';
COMMENT ON COLUMN t_custom_logo.id IS '主键ID';
COMMENT ON COLUMN t_custom_logo.create_time IS '创建时间';
COMMENT ON COLUMN t_custom_logo.update_time IS '更新时间';

insert into t_menu(id, name, parent_id)
values ('ServiceNode', '业务节点申请', '0');

CREATE TABLE t_company
(
    id                              bigint primary key,
    node_id                         varchar(36) not null,
    company_id                      varchar(36) not null,
    business_license                VARCHAR(500),                                                                                           -- 主键
    status                          varchar(20) not null check (status in
                                                                ('INIT', 'NOT_REVIEW', 'REVIEW_PASS', 'REVIEW_REFUSED',
                                                                 'DISABLE'))                                     default 'NOT_REVIEW',
    access_type                     VARCHAR(20) NOT NULL CHECK (access_type IN ('LEGAL_PERSON', 'AGENT_PERSON')) DEFAULT 'LEGAL_PERSON',    -- 接入主体类型

    -- 法人相关字段
    organization_name               VARCHAR(255),                                                                                           -- 组织名称
    credit_code                     VARCHAR(20),                                                                                            -- 统一社会信用代码
    legal_representative_name       VARCHAR(100),                                                                                           -- 法定代表人或负责人姓名
    legal_representative_id_type    VARCHAR(50),                                                                                            -- 法定代表人证件类型
    legal_representative_id_number  VARCHAR(20),                                                                                            -- 法定代表人证件号码
    legal_representative_id_expiry  DATE,                                                                                                   -- 法定代表人证件有效期
    legal_representative_auth_level VARCHAR(20),                                                                                            -- 法定代表人实名认证等级
    auth_method                     VARCHAR(50),                                                                                            -- 实名认证方式
    registration_address            VARCHAR(500),                                                                                           -- 注册地址
    industry_type                   VARCHAR(100),                                                                                           -- 行业类型
    business_start_date             DATE,                                                                                                   -- 经营期限起始
    business_end_date               DATE,                                                                                                   -- 经营期限截止

    -- 经办人相关字段
    delegate_name                   VARCHAR(100),                                                                                           -- 委办人姓名
    delegate_id_type                VARCHAR(50),                                                                                            -- 委办人证件类型
    delegate_id_number              VARCHAR(20),                                                                                            -- 委办人证件号码
    delegate_id_expiry              DATE,                                                                                                   -- 委办人证件有效期
    delegate_institution            VARCHAR(255),                                                                                           -- 委办人所属委办机构
    delegate_institution_code       VARCHAR(20),                                                                                            -- 委办机构统一社会信用代码
    delegate_contact                VARCHAR(50),                                                                                            -- 委办人联系方式
    delegate_email                  VARCHAR(100),                                                                                           -- 委办人电子邮箱
    delegate_auth_level             VARCHAR(20),                                                                                            -- 委办人实名认证等级
    delegate_auth_method            VARCHAR(50),                                                                                            -- 实名认证方式
    delegate_registration_address   VARCHAR(500),                                                                                           -- 委办人注册地址
    delegate_industry_type          VARCHAR(100),                                                                                           -- 委办人行业类型
    delegate_task_scope             TEXT,                                                                                                   -- 委办任务范围
    delegate_authorization_start    DATE,                                                                                                   -- 委办授权期限起始
    delegate_authorization_end      DATE,                                                                                                   -- 委办授权期限截止
    delegate_remarks                TEXT,                                                                                                   -- 委办人备注

    -- 接入主体有效期
    is_permanent                    BOOLEAN     NOT NULL                                                         DEFAULT FALSE,             -- 是否永久有效
    validity_end_date               DATE,
    -- 添加审核相关字段
    review_user_id                  VARCHAR(36),                                                                                            -- 审核人ID
    review_time                     TIMESTAMP,                                                                                              -- 审核时间
    review_remarks                  TEXT,                                                                                                   -- 审核备注
    refuse_reason                   TEXT,                                                                                                   -- 拒绝原因

-- 添加基础字段
    created_by                      VARCHAR(36),                                                                                            -- 创建人
    created_time                    TIMESTAMP                                                                    DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_by                      VARCHAR(36),                                                                                            -- 更新人
    updated_time                    TIMESTAMP                                                                    DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    deleted                         BOOLEAN                                                                      DEFAULT FALSE,             -- 是否删除                                                                                                   -- 指定结束日期
    service_node_id                 bigint      null,
    ext                             text                                                                         default ''
        CONSTRAINT chk_validity CHECK (
            (is_permanent = TRUE AND validity_end_date IS NULL) OR
            (is_permanent = FALSE AND validity_end_date IS NOT NULL)
            )
);

-- 主键字段
COMMENT ON COLUMN t_company.id IS '主键ID，自动生成的唯一标识';
COMMENT ON COLUMN t_company.business_license IS '营业执照';
-- 接入主体类型
COMMENT ON COLUMN t_company.access_type IS '接入主体类型，单选值：法人、经办人，默认值：法人';

-- 法人相关字段
COMMENT ON COLUMN t_company.organization_name IS '组织名称（法人类型时必填）';
COMMENT ON COLUMN t_company.credit_code IS '统一社会信用代码（法人类型时必填）';
COMMENT ON COLUMN t_company.legal_representative_name IS '法定代表人或负责人姓名（法人类型时必填）';
COMMENT ON COLUMN t_company.legal_representative_id_type IS '法定代表人证件类型（法人类型时必填）';
COMMENT ON COLUMN t_company.legal_representative_id_number IS '法定代表人证件号码（法人类型时必填）';
COMMENT ON COLUMN t_company.legal_representative_id_expiry IS '法定代表人证件有效期';
COMMENT ON COLUMN t_company.legal_representative_auth_level IS '法定代表人实名认证等级';
COMMENT ON COLUMN t_company.auth_method IS '实名认证方式';
COMMENT ON COLUMN t_company.registration_address IS '注册地址';
COMMENT ON COLUMN t_company.industry_type IS '行业类型';
COMMENT ON COLUMN t_company.business_start_date IS '经营期限起始日期';
COMMENT ON COLUMN t_company.business_end_date IS '经营期限截止日期';

-- 经办人相关字段
COMMENT ON COLUMN t_company.delegate_name IS '委办人姓名（经办人类型时必填）';
COMMENT ON COLUMN t_company.delegate_id_type IS '委办人证件类型（经办人类型时必填）';
COMMENT ON COLUMN t_company.delegate_id_number IS '委办人证件号码（经办人类型时必填）';
COMMENT ON COLUMN t_company.delegate_id_expiry IS '委办人证件有效期';
COMMENT ON COLUMN t_company.delegate_institution IS '委办人所属委办机构（经办人类型时必填）';
COMMENT ON COLUMN t_company.delegate_institution_code IS '委办机构统一社会信用代码（经办人类型时必填）';
COMMENT ON COLUMN t_company.delegate_contact IS '委办人联系方式';
COMMENT ON COLUMN t_company.delegate_email IS '委办人电子邮箱';
COMMENT ON COLUMN t_company.delegate_auth_level IS '委办人实名认证等级';
COMMENT ON COLUMN t_company.delegate_auth_method IS '委办人实名认证方式';
COMMENT ON COLUMN t_company.delegate_registration_address IS '委办人注册地址';
COMMENT ON COLUMN t_company.delegate_industry_type IS '委办人行业类型';
COMMENT ON COLUMN t_company.delegate_task_scope IS '委办任务范围';
COMMENT ON COLUMN t_company.delegate_authorization_start IS '委办授权期限起始日期';
COMMENT ON COLUMN t_company.delegate_authorization_end IS '委办授权期限截止日期';
COMMENT ON COLUMN t_company.delegate_remarks IS '委办人备注信息';

-- 有效期相关字段
COMMENT ON COLUMN t_company.is_permanent IS '是否永久有效，布尔值：TRUE（永久有效）、FALSE（非永久有效）';
COMMENT ON COLUMN t_company.validity_end_date IS '接入主体有效期的指定结束日期（当 is_permanent 为 FALSE 时必填）';
COMMENT ON COLUMN t_company.review_user_id IS '审核人ID';
COMMENT ON COLUMN t_company.review_time IS '审核时间';
COMMENT ON COLUMN t_company.review_remarks IS '审核备注';
COMMENT ON COLUMN t_company.refuse_reason IS '拒绝原因';
COMMENT ON COLUMN t_company.created_by IS '创建人';
COMMENT ON COLUMN t_company.created_time IS '创建时间';
COMMENT ON COLUMN t_company.updated_by IS '更新人';
COMMENT ON COLUMN t_company.updated_time IS '更新时间';
COMMENT ON COLUMN t_company.deleted IS '是否删除';

COMMENT on COLUMN t_company.company_id is '企业唯一标识';
COMMENT on COLUMN t_company.service_node_id is '功能节点ID';
COMMENT on COLUMN t_company.ext is '扩展字段';

-- 在t_company表定义中添加缺失的字段（在service_node_id字段之前添加）
ALTER TABLE t_company
    ADD COLUMN auth_date            VARCHAR(255),
    ADD COLUMN identity_status      VARCHAR(255),
    ADD COLUMN registration_date    VARCHAR(255),
    ADD COLUMN registered_capital   VARCHAR(255),
    ADD COLUMN business_scope       TEXT,
    ADD COLUMN authorization_letter VARCHAR(500);

-- 添加字段注释
COMMENT ON COLUMN t_company.auth_date IS '认证时间';
COMMENT ON COLUMN t_company.identity_status IS '身份状态';
COMMENT ON COLUMN t_company.registration_date IS '注册日期';
COMMENT ON COLUMN t_company.registered_capital IS '注册金额';
COMMENT ON COLUMN t_company.business_scope IS '经营范围';
COMMENT ON COLUMN t_company.authorization_letter IS '授权书';


-- 订单表迁移


CREATE TABLE dr_order_approval_record (
      id varchar(64)  NOT NULL,
      asset_id varchar(64) ,
      asset_name varchar(100) ,
      delivery_mode varchar(100) ,
      beneficiary_id varchar(64) ,
      beneficiary_username varchar(100) ,
      beneficiary_router_id varchar(100) ,
      beneficiary_enterprise_name varchar(100) ,
      beneficiary_enterprise_property varchar(100) ,
      approver_id varchar(64) ,
      approver_username varchar(100) ,
      approver_router_id varchar(100) ,
      approver_enterprise_name varchar(100) ,
      charging_way varchar(100) ,
      metering_way varchar(100) ,
      allowance int8,
      successful_usage int8,
      unsuccessful_usage int8,
      status varchar(100) ,
      extend text ,
      expire_date timestamp(6),
      create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      type varchar(255) ,
      approve_time timestamp(6),
      CONSTRAINT dr_order_approval_record_pkey PRIMARY KEY (id)
)
;


COMMENT ON COLUMN dr_order_approval_record.id IS '订单编号';

COMMENT ON COLUMN dr_order_approval_record.asset_id IS '资产ID';

COMMENT ON COLUMN dr_order_approval_record.asset_name IS '资产名称';

COMMENT ON COLUMN dr_order_approval_record.delivery_mode IS '交付方式';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_id IS '获益人用户ID';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_username IS '获益人用户名';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_router_id IS '获益方数由器ID';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_enterprise_name IS '获益方企业名称';

COMMENT ON COLUMN dr_order_approval_record.beneficiary_enterprise_property IS '获益人方企业性质';

COMMENT ON COLUMN dr_order_approval_record.approver_id IS '审批人用户ID';

COMMENT ON COLUMN dr_order_approval_record.approver_username IS '审批人用户名';

COMMENT ON COLUMN dr_order_approval_record.approver_router_id IS '审批方数由器ID';

COMMENT ON COLUMN dr_order_approval_record.approver_enterprise_name IS '审批方企业名称';

COMMENT ON COLUMN dr_order_approval_record.charging_way IS '计费方式：预付费、后付费';

COMMENT ON COLUMN dr_order_approval_record.metering_way IS '计量方式：按次、按时间';

COMMENT ON COLUMN dr_order_approval_record.allowance IS '使用次数上限';

COMMENT ON COLUMN dr_order_approval_record.successful_usage IS '成功使用次数';

COMMENT ON COLUMN dr_order_approval_record.unsuccessful_usage IS '失败使用次数';

COMMENT ON COLUMN dr_order_approval_record.status IS '状态';

COMMENT ON COLUMN dr_order_approval_record.expire_date IS '有效期';

COMMENT ON COLUMN dr_order_approval_record.type IS '资产类型';

COMMENT ON COLUMN dr_order_approval_record.approve_time IS '审批通过时间';


CREATE TABLE dr_scene_asset (
        id varchar(64)  NOT NULL,
        delivery_scene_id varchar(64) ,
        data_asset_id varchar(255) ,
        api_id varchar(255) ,
        order_id varchar(255) ,
        api_key varchar(255) ,
        ext text  DEFAULT '{}'::text,
        CONSTRAINT dr_scene_asset_pkey PRIMARY KEY (id)
)
;


CREATE UNIQUE INDEX api_id_key ON dr_scene_asset USING btree (
    api_id  pg_catalog.text_ops ASC NULLS LAST
    );

COMMENT ON COLUMN dr_scene_asset.delivery_scene_id IS '场景id';

COMMENT ON COLUMN dr_scene_asset.data_asset_id IS '资产id';

COMMENT ON COLUMN dr_scene_asset.api_id IS 'api id';

COMMENT ON COLUMN dr_scene_asset.order_id IS '订单id';

COMMENT ON COLUMN dr_scene_asset.api_key IS 'apiKey';



CREATE TABLE dr_delivery_scene (
       id varchar(64)  NOT NULL,
       scene_status varchar(64) ,
       delivery_type varchar(255) ,
       digital_scene_id varchar(255) ,
       digital_scene_name varchar(255) ,
       ext text  DEFAULT '{}'::text,
       create_user varchar(255) ,
       create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
       router_id varchar(64)  NOT NULL DEFAULT ''::character varying,
       CONSTRAINT dr_delivery_scene_pkey PRIMARY KEY (id)
)
;


COMMENT ON COLUMN dr_delivery_scene.scene_status IS '状态';

COMMENT ON COLUMN dr_delivery_scene.delivery_type IS '交付方式';

COMMENT ON COLUMN dr_delivery_scene.digital_scene_id IS '数字证书 —— 合规场景id';

COMMENT ON COLUMN dr_delivery_scene.digital_scene_name IS '数字证书 —— 合规场景名称 冗余存储';

COMMENT ON COLUMN dr_delivery_scene.ext IS '扩展';

COMMENT ON COLUMN dr_delivery_scene.create_user IS '创建人';

COMMENT ON COLUMN dr_delivery_scene.create_time IS '创建时间';

COMMENT ON COLUMN dr_delivery_scene.router_id IS '数由器ID';



CREATE TABLE dr_asset_beneficiary_rel (
      id varchar(64)  NOT NULL,
      asset_id varchar(64) ,
      beneficiary_id varchar(64) ,
      order_id varchar(100) ,
      extend text ,
      create_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      update_time timestamp(6) DEFAULT CURRENT_TIMESTAMP,
      CONSTRAINT dr_asset_beneficiary_rel_pkey PRIMARY KEY (id)
)
;


COMMENT ON COLUMN dr_asset_beneficiary_rel.id IS 'ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.asset_id IS '资产ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.beneficiary_id IS '获益人用户ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.order_id IS '订单ID';

COMMENT ON COLUMN dr_asset_beneficiary_rel.extend IS '扩展字段json（api鉴权key等信息）';

delete from t_permission where id ='l8f3d812a57d11efa4cc0242ac110005';