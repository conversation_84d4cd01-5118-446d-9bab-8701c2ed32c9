CREATE TABLE "service_node_info"
(
    "id"                        varchar(100) NOT NULL PRIMARY KEY,
    "entry_name"                varchar(100),
    "service_node_id"           varchar(100),
    "type"                      varchar(100),
    "type_description"          varchar(100),
    "ip"                        varchar(100),
    "domain_name"               varchar(100),
    "api_url"                   varchar(100),
    "introduction"              text,
    "version"                   varchar(100),
    "reserve_notes"             varchar(100),
    "enterprise_name"           varchar(100),
    "enterprise_identity_id"    varchar(100),
    "process_status"            varchar(100),
    "process_time"              timestamp(6),
    "extend"                    text,
    "create_time"               timestamp(6) DEFAULT CURRENT_TIMESTAMP,
    "update_time"               timestamp(6) DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON COLUMN "service_node_info"."id" IS 'ID';

COMMENT ON COLUMN "service_node_info"."entry_name" IS '业务节点登记名称';

COMMENT ON COLUMN "service_node_info"."service_node_id" IS '业务节点标识编码';

COMMENT ON COLUMN "service_node_info"."type" IS '业务功能类型：1－应用侧基础设施，2－数据交易类，3－数据开发利用类，4－公共数据授权运营平台类，5－公共服务平台类';

COMMENT ON COLUMN "service_node_info"."type_description" IS '业务功能类型描述';

COMMENT ON COLUMN "service_node_info"."ip" IS '业务节点IP地址';

COMMENT ON COLUMN "service_node_info"."domain_name" IS '业务节点域名';

COMMENT ON COLUMN "service_node_info"."api_url" IS '业务节点接口地址';

COMMENT ON COLUMN "service_node_info"."introduction" IS '业务功能简介';

COMMENT ON COLUMN "service_node_info"."version" IS '业务节点版本';

COMMENT ON COLUMN "service_node_info"."reserve_notes" IS '备注';

COMMENT ON COLUMN "service_node_info"."enterprise_name" IS '所属法人或其他组织名称';

COMMENT ON COLUMN "service_node_info"."enterprise_identity_id" IS '所属法人或其他组织身份标识码';

COMMENT ON COLUMN "service_node_info"."process_status" IS '状态：APPLY（待审批）APPROVED（已通过）REJECTED（已拒绝）';

COMMENT ON COLUMN "service_node_info"."process_time" IS '审核时间';

COMMENT ON COLUMN "service_node_info"."extend" IS '扩展字段';