-- 创建数据接入任务表
CREATE TABLE IF NOT EXISTS t_data_update_task
(
    id                              varchar(255) NOT NULL,
    data_product_id                 varchar(255) NOT NULL,
    data_update_type                varchar(255) NOT NULL, -- ONCE:单次, SCHEDULED:定时, MANUAL:手动
    cron_expression                 varchar(255),
    last_task_status                varchar(255), -- CREATED:已创建, SYNCING:同步中, SUCCESS:处理成功, FAILED:处理失败
    create_time                     timestamp DEFAULT CURRENT_TIMESTAMP,
    update_time                     timestamp DEFAULT CURRENT_TIMESTAMP,
    ext                             TEXT,
    base_info                       json DEFAULT '{}',
    latest_task_logId               varchar(255),
    task_log_num                    integer DEFAULT 0,
    CONSTRAINT t_access_data_task_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE t_data_update_task IS '数据接入任务表';
COMMENT ON COLUMN t_data_update_task.id IS '任务ID';
COMMENT ON COLUMN t_data_update_task.data_product_id IS '关联的数据产品ID';
COMMENT ON COLUMN t_data_update_task.data_update_type IS '任务调度方式：ONCE-单次,SCHEDULED-定时,MANUAL-手动';
COMMENT ON COLUMN t_data_update_task.cron_expression IS 'Cron表达式';
COMMENT ON COLUMN t_data_update_task.last_task_status IS '最近一次任务执行状态：CREATED-已创建,SYNCING-同步中,SUCCESS-处理成功,FAILED-处理失败';
COMMENT ON COLUMN t_data_update_task.create_time IS '创建时间';
COMMENT ON COLUMN t_data_update_task.update_time IS '更新时间';
COMMENT ON COLUMN t_data_update_task.ext IS '扩展字段';
COMMENT ON COLUMN t_data_update_task.base_info IS '任务基本信息json';
COMMENT ON COLUMN t_data_update_task.latest_task_logId IS '最近一次的任务ID';
COMMENT ON COLUMN t_data_update_task.task_log_num IS '任务执行次数';

-- 创建数据接入任务历史表
CREATE TABLE IF NOT EXISTS t_data_update_task_log (
    id                  varchar(255) NOT NULL,
    task_id             varchar(255) NOT NULL,
    task_status         varchar(255) NOT NULL, -- CREATED:已创建, SYNCING:同步中, SUCCESS:处理成功, FAILED:处理失败
    log                 text,
    create_time         timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    start_time          timestamp,
    end_time            timestamp,
    ext json            DEFAULT '{}',
    data_update_type    varchar(255) NOT NULL,
    CONSTRAINT t_access_data_task_log_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE t_data_update_task_log IS '数据接入任务历史表';
COMMENT ON COLUMN t_data_update_task_log.id IS '历史记录ID';
COMMENT ON COLUMN t_data_update_task_log.task_id IS '关联的任务ID';
COMMENT ON COLUMN t_data_update_task_log.task_status IS '任务状态：CREATED-已创建,SYNCING-同步中,SUCCESS-处理成功,FAILED-处理失败';
COMMENT ON COLUMN t_data_update_task_log.log IS '任务日志';
COMMENT ON COLUMN t_data_update_task_log.create_time IS '创建时间';
COMMENT ON COLUMN t_data_update_task_log.start_time IS '任务开始时间';
COMMENT ON COLUMN t_data_update_task_log.end_time IS '任务结束时间';
COMMENT ON COLUMN t_data_update_task_log.ext IS '扩展字段';
COMMENT ON COLUMN t_data_update_task_log.data_update_type IS '更新方式';