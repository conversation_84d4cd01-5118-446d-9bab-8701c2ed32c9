server:
  servlet:
    session:
      timeout: 60m
# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
    enabled: false
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.ailpha.ailand.dataroute.endpoint
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: false
  setting:
    language: zh_cn
  production: true
spring:
  profiles:
    active: dev
  flyway:
    baseline-description: 连接器SQL基线
    enabled: false
  datasource:
    driver-class-name: com.highgo.jdbc.Driver
    url: *********************************************************************
    username: highgo
    password: O%0baNnBg7fITxlE
  jackson:
    deserialization:
      #     accept-empty-string-as-null-object 数翰接口出错时返回体中的data字段为空字符串，导致反序列化失败报错不正确
      accept-empty-string-as-null-object: true
ailand:
  deploy:
    mode: standalone # 默认为独享模式，即只允许存在一个企业
  security:
    login:
      exception-if-maximum-exceeded: true
      maximum-sessions: -1
    white-apis:
      - /login
      - /user/companyAdmin/register
      - /captcha
      - /router/publicKey
      # 前置机数据集任务执行完毕回调接口
      - /callback/executor/*
      # /data-asset/download/* 这部分路由minio accessKey进行鉴权
      - /data-asset/download/**
      - /sse/receiveDataRouteMessage
      - /third/download/solution/**
      - /third/api-gateway/log-report
      - /third/contract/execute/callback
      - /third/offline/order
      - /third/userinfo
      - /third/userinfo/**
      - /third/data-asset/**
      - /favicon.ico
      # appKey appSecret 生成查看,已在代码中控制仅本地127.0.0.1可访问
      - /magic/app/**
      # 开放接口获取token,鉴权逻辑在 OpenAPITokenController
      - /openapi/token
      - /third/ConnectorIdentityVerify
      - /third/ConnectorIdentityVerifyNonce
      - /callback/mpc/**
      - /callback/tee/**
      - /logo/customLogo
      - /data-asset/api/**
      - /node/simple/page
      - /third/generateKey
      - /third/orderDelivery
      - /third/companyInfo
      - /third/transfer/process
      - /third/user/preview/authorizationLetter
      - /company/init/csr
      - /company/init/entity
      - /company/upload/crt
      - /transfer/data/**
    csrf-white-apis:
      - /login
      - /captcha
      - /router/**
      - /callback/executor/*
      - /data-asset/download/**
      - /dict/codes
      - /sse/receiveDataRouteMessage
      - /third/download/solution/**
      - /third/api-gateway/log-report
      - /third/contract/execute/callback
      - /third/offline/order
      - /third/userinfo
      - /third/userinfo/**
      - /third/data-asset/**
      - /favicon.ico
      - /magic/app/**
      - /openapi/token
      - /callback/mpc/**
      - /callback/tee/**
      - /demand/download/solutionFile
      - /logo/customLogo
      - /data-asset/api/**
      - /node/simple/page
      - /third/generateKey
      - /third/orderDelivery
      - /third/companyInfo
      - /third/transfer/process
      - /transfer/data/**
  key-pair:
    enable: true
    alias: data-route
    store-path: ${ailand.file-storage.base-path}
    store-type: PKCS12
    key-password: ENC(ePjNNtouLd62XKcTdU6x0BeckxBajoCBer3idA7Rrn4gm+irUai4wc9UO7IljVId)
    key-store-password: ENC(ePjNNtouLd62XKcTdU6x0BeckxBajoCBer3idA7Rrn4gm+irUai4wc9UO7IljVId)
  file-storage:
    license-file-suffix:
      - json
    logo-file-suffix:
      - jpg
      - jpeg
      - png
      - bmp
      - gif
      - svg
      - ai
      - JPG
      - JPEG
      - PNG
      - BMP
      - GIF
      - SVG
      - AI
    logo-base-path: /data/files/public
  license-server:
    base-url: http://127.0.0.1:9876
  agent:
    base-url: http://127.0.0.1:63200
  heng-nao:
    enable: true
    app-key: hengnaoLFMRS9fMivNApUQxNdOp
    app-secret: 46wdbvdtuorkwmp07k8gaykeoqael09a
    base-url: https://www.das-ai.com
    data-product-explore-agent-id: ef0a5f84-625d-4414-a8cc-19ea3c55b1fa
    data-resource-explore-agent-id: 6e1cb479-cc07-48a2-b568-82575d2df6ab
  executor-server:
    port: 6112
  new-lic:
    product-name: AiLand安全岛隐私计算平台
    local-cache-dir: ${ailand.file-storage.base-path}/license/cpi/
    additional: 'not top say'
  iam-server:
    base-url: https://10.50.3.209
    client-id: ZWUPZID2U1
    client-secret: C99XGC3NYK
  version: 1.0.0
  license-messages:
    expired-official: "您的许可证已过期，升级功能已被锁定，您无法享受产品技术支持、故障维修等售后服务！请联系空间管理员延期许可！"
    expired-non-official: "您的许可证已过期，平台服务已锁定！请联系空间管理员延期许可！"
    will-expire-official: "您的许可证将于%s天后过期，升级功能将被锁定，您将无法享受产品技术支持、故障维修等售后服务！请联系空间管理员延期许可！"
    will-expire-non-official: "您的许可证将于%s天后过期，平台服务将被锁定，到期后您将无法再使用平台！请联系空间管理员延期许可！"
    days-threshold: 90
  model:
    supported-file-suffix:
      - .tar.gz
      - .zip
      - .pkl
      - .ckpt
      - .pt
      - .pth
      - .safetensors
      - .gguf
      - .onnx
das:
  rest:
    response:
      advice:
        enable: true
    scan:
      enable: true

data-route:
  endpoint:
    appKey: SYQZD202412311030
    appSecret: c0e45fb4-f4fd-4d9a-bb90-c5d655c1ad2c
  gateway:
    access-key-id: pGLx26m0ehJpUHbJrpTIAIbj
    access-key-secret: R9GzRCIHDNJcgimHPHwNfmx_wZT3WpzO6Ah3JQHEPsykIUaiI4Y-r2BUVCEUSxZZiym3XtY14lnDVMvVWH51GLvwsMBWoCmg3MB5
    manager-base-url: https://127.0.0.1:7443
    server-base-url: https://127.0.0.1:10443
  #    server-base-url: http://127.0.0.1:10080
  aigate:
    base-url: https://127.0.0.1:6443
  #    enabled: true
  aisort:
    base-url: https://127.0.0.1:8081
  minio:
    endpoint: https://127.0.0.1:9000
    tls-enabled: false
    root-user: ailand
    root-password: 2wsxVFR_