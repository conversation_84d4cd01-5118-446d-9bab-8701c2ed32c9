package com.ailpha.ailand.collector.api.source;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @Author: jackie.liu
 * @Date: 2021/6/7 1:41 下午
 */
@Getter
public enum DataCollectSourceTypeEnum {
    SQOOP("sqoop", 1, "SQOOP"),
    HDFS("hdfs", 2, "HDFS"),
    HIVE("hive", 3, "HIVE"),
    MYSQL("mysql", 4, "MYSQL"),
    TEXT("text", 5, "TEXT"),
    STREAM("stream", 6, "STREAM"),
    OR<PERSON><PERSON>("oracle", 7, "ORACLE"),
    ODPS("odps", 8, "ODPS"),
    POSTGRESQL("postgresql", 9, "POSTGRESQL"),
    GAUSSDB200("gaussdb200", 10, "GAUSSDB200"),
    DM("dm", 11, "DM"),
    HTTP("http", 12, "HTTP"),
    SERVERFILE("serverfile", 13, "SERVERFILE"),
    SERVERFILEPATH("serverfilepath", 14, "SERVERFILEPATH"),

    ;

    private final String type;
    private final int code;
    private final String desc;

    DataCollectSourceTypeEnum(String type, int code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public String getType() {
        return this.type;
    }

    @JsonCreator
    public static DataCollectSourceTypeEnum getByType(String type) {
        // 此类型直接使用 pg reader连接
        if ("kingbase".equals(type)) return POSTGRESQL;
        DataCollectSourceTypeEnum[] var1 = values();
        for (DataCollectSourceTypeEnum item : var1) {
            if (Objects.equals(item.getType(), type)) {
                return item;
            }
        }
        return null;
    }

    public static DataCollectSourceTypeEnum getByCode(int code) {
        DataCollectSourceTypeEnum[] var1 = values();
        for (DataCollectSourceTypeEnum item : var1) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

}
