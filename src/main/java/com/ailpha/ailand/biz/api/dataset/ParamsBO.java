package com.ailpha.ailand.biz.api.dataset;

import com.ailpha.ailand.biz.api.constants.DataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * 2023/3/6
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ParamsBO {

    @NotEmpty(message = "键名不能为空")
    @Schema(description = "键")
    String key;

    @Schema(description = "值")
    String value;

    @Schema(description = "数据类型")
    DataTypeEnum dataType;

    @Schema(description = "描述")
    @Size(max = 50, message = "描述最大长度50")
    String desc;
}
