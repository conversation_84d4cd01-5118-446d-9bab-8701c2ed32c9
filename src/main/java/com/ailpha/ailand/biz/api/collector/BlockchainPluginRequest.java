package com.ailpha.ailand.biz.api.collector;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * @author: yuwenping
 * @date: 2025/5/8 18:42
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BlockchainPluginRequest implements Serializable {
    @Schema(description = "插件名")
    private String name;

    @Schema(description = "插件配置描述")
    private String description;

    @Schema(description = "插件模块")
    private String moduleType;

    @Schema(description = "插件文件路径 groovy, jar 文件的地址")
    private String pluginContent;

    @Schema(description = "插件类型，API；GROOVY；JAR")
    private BlockchainPluginTypeEnum type;

    @Schema(description = "插件类型=API；api 信息")
    private List<BlockchainApiImportTestVO> apiList;
}
