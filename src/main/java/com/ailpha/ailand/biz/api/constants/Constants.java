package com.ailpha.ailand.biz.api.constants;

/**
 * <AUTHOR>
 * 2025/2/24
 */
public class Constants {

    /**
     * public绝对路径
     */
    public static final String PUBLIC_PATH = "/data/apps/file/data-route/public";

    /**
     * nginx访问轮径
     */
    public static final String NGINX_PUBLIC_PATH = "/_data-route/public";

    /**
     * DATA_PATH绝对路径
     */
    public static final String DATA_PATH = "/data/apps/file/data-route";

    /**
     * 系统配置文件名称
     */
    public static final String CONFIG_FILE_NAME = "config.info";

    /**
     * 升级包相对路径
     */
    public static final String PACKAGE_PATH = "PACKAGE";

    /**
     * RSA公钥
     */
    public static final String RSA_PUBLIC_KEY = "rsaPublicKey.txt";

    /**
     * RSA私钥
     */
    public static final String RSA_PRIVATE_KEY = "rsaPrivateKey.txt";

    /**
     * RSA私钥加密脚本
     */
    public static final String RSA_PRIVATE_ENCRYPT_SH = "rsa_private_encrypt.sh";

    /**
     * RSA公钥解密脚本
     */
    public static final String RSA_PUBLIC_DECRYPT_SH = "rsa_public_decrypt.sh";

    /**
     * 当前版本信息
     */
    public static final String VERSION_INFO = "version_info.json";

    /**
     * 升级包工作目录
     */
    public static final String UPGRADE_PACKAGE = "upgrade-package";

    /**
     * 升级脚本名称
     */
    public static final String UPGRADE_SH = "upgrade.sh";

    /**
     * 升级包说明
     */
    public static final String UPGRADE_README_MD = "UPGRADE_README.md";
}
