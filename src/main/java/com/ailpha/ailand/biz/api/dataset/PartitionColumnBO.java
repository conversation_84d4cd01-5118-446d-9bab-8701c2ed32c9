package com.ailpha.ailand.biz.api.dataset;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/12/13 16:04
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PartitionColumnBO {

    @Pattern(regexp = "^\\w{0,32}$", message = "字段名必须只能包含字母数字或者下划线")
    @Schema(description = "字段名")
    String column;

    @Schema(description = "字段值")
    String columnValue;

    @Schema(description = "是否选中")
    Boolean selected;

}
