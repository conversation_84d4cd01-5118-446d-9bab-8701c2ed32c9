package com.ailpha.ailand.biz.api.collector;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * @author: yuwenping
 * @date: 2025/5/8 18:42
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BlockchainPluginPageRequest extends Page implements Serializable {
    @Schema(description = "插件名")
    private String name;

    @Schema(description = "插件模块")
    private String moduleType;

    @Schema(description = "插件类型，API；GROOVY；JAR")
    private BlockchainPluginTypeEnum type;

    @Schema(description = "状态 1：启用 0：关闭 -1：删除")
    private Integer status;
}
