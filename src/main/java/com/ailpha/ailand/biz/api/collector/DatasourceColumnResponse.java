package com.ailpha.ailand.biz.api.collector;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/13 15:35
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasourceColumnResponse {
    List<ColumnDefinition> columns;

    List<ColumnDefinition> partitionColumns;

    @Builder.Default
    Boolean isPartitionedTable = false;

    public Boolean getIsPartitionedTable() {
        return !CollectionUtils.isEmpty(partitionColumns);
    }
}
