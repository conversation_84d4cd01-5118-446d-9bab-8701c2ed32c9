package com.ailpha.ailand.biz.api.collector;

import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.ParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/10/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApiImportTestVO implements Serializable {

    /**
     * API接口配置
     */
    @Schema(description = "请求地址")
    String url;

    @Schema(description = "请求方式")
    MethodEnum method;

    @Schema(description = "Params参数")
    List<ParamsBO> params;

    @Schema(description = "Body参数")
    String body;

    @Schema(description = "Body类型")
    BodyTypeEnum bodyType;

    @Schema(description = "Headers参数")
    List<ParamsBO> headers;

//    AuthExecutorVO.ExecutorInfo executorInfo;
}
