package com.ailpha.ailand.biz.api.collector;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginApiTypeEnum;
import com.ailpha.ailand.biz.api.dataset.BlockchainParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@SuperBuilder
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BlockchainApiImportTestVO extends ApiImportTestVO {

    @Schema(description = "API接口类型")
    private BlockchainPluginApiTypeEnum type;

    @Schema(description = "区块链专用Params参数")
    private List<BlockchainParamsBO> blockchainParams;

    @Schema(description = "区块链专用Header参数")
    private List<BlockchainParamsBO> blockchainHeaders;

    @Schema(description = "上报参数字段 data.message")
    private String upDataField;

}