package com.ailpha.ailand.biz.api.collector;

import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.dataset.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
public class HttpReaderTaskUtil {

    /**
     * 组装Params参数
     */
    public static LinkedHashMap<String, Object> buildParams(List<ParamsBO> paramsBOS) {
        try {
            LinkedHashMap<String, Object> paramsReal = new LinkedHashMap<>();
            if (!ObjectUtils.isEmpty(paramsBOS)) {
                for (ParamsBO paramsBO : paramsBOS) {
                    paramsReal.put(paramsBO.getKey(), paramsBO.getValue());
                }
            }
            return paramsReal;
        } catch (Exception e) {
            log.error("组装Params参数失败:", e);
            throw new RuntimeException("组装Params参数失败", e);
        }
    }


    /**
     * 组装Body参数
     */
    public static String buildBody(String bodyType, String body) {
        try {
            String bodyReal;
            if (ObjectUtils.isEmpty(body) || "NONE".equals(bodyType)) {
                bodyReal = null;
            } else if (ObjectUtils.nullSafeEquals(bodyType, BodyTypeEnum.FORM_DATA.toString())) {
                List<ParamsBO> bodyBOS = JacksonUtils.json2list(body, ParamsBO.class);
                LinkedHashMap<String, Object> bodyBOSReal = new LinkedHashMap<>();
                for (ParamsBO bodyBO : bodyBOS) {
                    bodyBOSReal.put(bodyBO.getKey(), bodyBO.getValue());
                }
                bodyReal = JacksonUtils.obj2json(bodyBOSReal);
            } else {
                bodyReal = body;
            }
            return bodyReal;
        } catch (Exception e) {
            log.error("组装Body参数失败:", e);
            throw new RuntimeException("组装Body参数失败", e);
        }
    }

}
