package com.ailpha.ailand.biz.api.collector;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: jackie.liu
 * @Date: 2021/6/4 5:14 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasourceCheckQuery {

    @Schema(description = "JDBC url")
    String jdbcUrl;
    @Schema(description = "表名")
    String table;
    @Schema(description = "数据源用户名")
    String username;
    @Schema(description = "数据源用户密码")
    String password;
    @Schema(description = "组织编号")
    String orgNo;
    @Schema(description = "数据源类型")
    String datasourceType;
    @Schema(description = "odps账号")
    String access_id;
    @Schema(description = "odps密钥")
    String access_key;
    @Schema(description = "odps项目")
    String project_name;
    @Builder.Default
    @Schema(description = "是否需要kerberos认证")
    Boolean hasKerberos = false;
    @Schema(description = "keytab认证文件路径")
    String keytabFilePath;
    @Schema(description = "kerberos用户")
    String kerberosPrincipal;
    @Schema(description = "kerberos配置文件路径")
    String kerberosConfPath;
    @Schema(description = "用户名称")
    String user_name;
    @Schema(description = "关联字段语句，a.id = b.id")
    String joinFieldSql;
    @Schema(description = "调试采样数")
    Integer samplingRowCount;
    @Schema(description = "同步条件，and a.id > 100 and b.name like '%alice'")
    String syncRule;
    //    @Schema(description = "字段分隔符")
//    String separator;
//    @Schema(description = "hdfs文件类型")
//    String fileType;
    @Schema(description = "前置机编号")
    String executorNo;
}
