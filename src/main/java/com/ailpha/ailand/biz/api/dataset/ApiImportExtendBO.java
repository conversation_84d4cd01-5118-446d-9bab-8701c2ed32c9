package com.ailpha.ailand.biz.api.dataset;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2023/4/8
 * <p>
 * API接口扩展配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApiImportExtendBO implements Serializable {

    String datasetId;

    @NotNull(message = "是否批量导入参数不能为空")
    @Schema(description = "是否批量导入参数")
    Boolean isBatchParams;

    @Schema(description = "批量参数文件ID")
    String batchParamsFileId;

    String batchParamsUrl;

    @Schema(description = "批量参数文件路径")
    String batchParamsPath;

    /**
     * 本地参数文件路径
     */
    String localBatchParamsPath;

    //    @NotNull(message = "是否鉴权不能为空")
    @Schema(description = "是否鉴权（保留字段，前端无需传递）")
    @Builder.Default
    Boolean isAuth = Boolean.FALSE;
}
