package com.ailpha.ailand.biz.web.vo.dataset;

import com.ailpha.ailand.biz.api.model.Table;
import com.ailpha.ailand.biz.dao.constants.ModelSourceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 数据集上传表单
 * @since 2020-04-06 21:42
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateModelRequestVO {
    @ApiModelProperty("模型名称")
    String name;
    @ApiModelProperty("模型描述")
    String desc;
    @ApiModelProperty("合约ID")
    String contractId;
    @ApiModelProperty("流程ID")
    String algorithmId;
    @ApiModelProperty("模型来源类型")
    ModelSourceType sourceType;
    @ApiModelProperty("流程名称")
    String flowName;
    @ApiModelProperty("数据集")
    Table table;
    @ApiModelProperty("是否为大语言模型")
    Boolean isLLM;
    @ApiModelProperty("大语言模型参数")
    LLM llmModel;
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm")
    Date expireDate;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LLM {
        String type;
        String paramSize;
        List<String> input;
        List<String> output;
    }
}
