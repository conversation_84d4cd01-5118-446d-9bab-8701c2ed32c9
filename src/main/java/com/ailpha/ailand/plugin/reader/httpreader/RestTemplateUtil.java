package com.ailpha.ailand.plugin.reader.httpreader;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.restclient.BufferingClientHttpResponseWrapper;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.*;
import org.springframework.http.client.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig.acceptsUntrustedCertsHttpClient;

/**
 * <AUTHOR>
 * 2023/3/2
 */
@Slf4j
public class RestTemplateUtil {

    public static RestTemplate restTemplate;
    private static final int CONNECT_TIMEOUT = 10 * 60 * 1000;
    private static final int READ_TIMEOUT = 10 * 60 * 1000;


    public static class RestTemplateResponseModifierInterceptor implements ClientHttpRequestInterceptor {

        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
            ClientHttpResponse response = execution.execute(request, body);
            BufferingClientHttpResponseWrapper httpResponseWrapper = new BufferingClientHttpResponseWrapper(response);
            String responseBody = IOUtils.toString(httpResponseWrapper.getBody(), Charset.defaultCharset());
            try {
                JSONObject jsonObject = JSONUtil.parseObj(responseBody);
                if (log.isDebugEnabled()) {
                    log.debug("[RestTemplateUtil] {} 请求 {} 响应 {}", request, new String(body, StandardCharsets.UTF_8), jsonObject);
                }
                if (Boolean.FALSE.equals(jsonObject.getBool("success"))) {
                    // NOTE: quick fix 数瀚接口失败响应空字符串导致反序列化失败
                    jsonObject.set("data", null);
                    responseBody = jsonObject.toString();
                }
            } catch (Exception ignore) {
            }
            String finalResponseBody = responseBody;
            return new ClientHttpResponse() {
                @Override
                public HttpStatusCode getStatusCode() throws IOException {
                    return response.getStatusCode();
                }

                @Override
                public String getStatusText() throws IOException {
                    return response.getStatusText();
                }

                @Override
                public void close() {

                }

                @Override
                public InputStream getBody() {
                    return new ByteArrayInputStream(finalResponseBody.getBytes(StandardCharsets.UTF_8));
                }

                @Override
                public HttpHeaders getHeaders() {
                    return response.getHeaders();
                }
            };
        }
    }

    static {
        restTemplate = new RestTemplate();
        restTemplate.setInterceptors(List.of(new RestTemplateResponseModifierInterceptor()));
        try {
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(acceptsUntrustedCertsHttpClient());
            requestFactory.setConnectTimeout(CONNECT_TIMEOUT);
            restTemplate.setRequestFactory(requestFactory);
        } catch (Exception e) {
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            requestFactory.setConnectTimeout(CONNECT_TIMEOUT);
            requestFactory.setReadTimeout(READ_TIMEOUT);
            restTemplate.setRequestFactory(requestFactory);
        }
    }

    public static RestTemplate getRestTemplate() {
        return restTemplate;
    }

    public static String sendHttp(String url, String method, Map<String, ?> params, String body, String bodyType, Map<String, ?> headers, Boolean apiOnline) {
        // 请求方式
        HttpMethod httpMethod = HttpMethod.valueOf(method);

        // 如果是在线API请求、POST 不需要追加url参数
        if (!(apiOnline && httpMethod == HttpMethod.POST)) {
            url = buildUrlParams(url, params);
        }

        // 构建请求头
        HttpHeaders httpHeaders = buildHttpHeaders(headers, bodyType);

        if (HttpMethod.GET.equals(httpMethod)) {
            // restTemplate.getForEntity 该形式不支持携带请求头， api 网关测试失败
//            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class, params);
//            if (ObjectUtil.equal(responseEntity.getStatusCode(), HttpStatus.FOUND) || ObjectUtil.equal(responseEntity.getStatusCode(), HttpStatus.MOVED_TEMPORARILY))
//                throw new RestfulApiException("接口测试异常");
//            return responseEntity.getBody();

            try {
                // 单独处理
                Map<String,String> headerMap = new HashMap<>();
                if (!ObjectUtils.isEmpty(headers)) {
                    for (Map.Entry<String, ?> entry : headers.entrySet()) {
                        headerMap.put(entry.getKey(), String.valueOf(entry.getValue()));
                    }
                }
                return cn.hutool.http.HttpRequest.get(url).addHeaders(headerMap).timeout(30 * 1000).execute().body();
            } catch (HttpException e) {
                log.error("接口测试异常: ", e);
                throw new RestfulApiException("接口测试异常");
            }
        }
        // 构建请求体
        HttpEntity<?> httpEntity = buildHttpEntity(httpHeaders, body);
        if (log.isDebugEnabled()) {
            log.debug("call api now: url: {}", url);
        }
        ResponseEntity<String> exchange = restTemplate.exchange(url, httpMethod, httpEntity, String.class);
        if (ObjectUtil.equal(exchange.getStatusCode(), HttpStatus.FOUND) || ObjectUtil.equal(exchange.getStatusCode(), HttpStatus.MOVED_TEMPORARILY))
            throw new RestfulApiException("接口测试异常");
        return exchange.getBody();
    }

    public static String postFormData(String url, Map<String, ?> headers, MultiValueMap<String, Object> formData) {
        // 请求方式
        HttpMethod httpMethod = HttpMethod.POST;
        // 构建请求头
        HttpHeaders httpHeaders = buildHttpHeaders(headers, BodyTypeEnum.FORM_DATA.toString());
        // 构建请求体
        HttpEntity<?> httpEntity = buildFormDataHttpEntity(httpHeaders, formData);
        if (log.isDebugEnabled()) {
            log.debug("[postFormData] call api now: url: {}", url);
        }
        return restTemplate.exchange(url, httpMethod, httpEntity, String.class).getBody();
    }

    /**
     * url拼接params参数
     */
    private static String buildUrlParams(String url, Map<String, ?> params) {
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "http://" + url;
        }
        url = url.split("\\?")[0];
        if (ObjectUtils.isEmpty(params)) {
            return url;
        }
        StringBuilder paramsBuilder = new StringBuilder(url);
        paramsBuilder.append("?");
        for (Map.Entry<String, ?> entry : params.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().toString().isEmpty()) {
                paramsBuilder.append(entry.getKey());
                paramsBuilder.append("=");
                paramsBuilder.append(entry.getValue());
                paramsBuilder.append("&");
            }
        }
        paramsBuilder.deleteCharAt(paramsBuilder.length() - 1);
        return paramsBuilder.toString();
    }

    /**
     * 构建请求头
     */
    private static HttpHeaders buildHttpHeaders(Map<String, ?> headers, String bodyType) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (ObjectUtils.nullSafeEquals(bodyType, BodyTypeEnum.FORM_DATA.toString())) {
            httpHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);
        } else if (ObjectUtils.nullSafeEquals(bodyType, BodyTypeEnum.JSON.toString())) {
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        }
        if (!ObjectUtils.isEmpty(headers)) {
            for (Map.Entry<String, ?> entry : headers.entrySet()) {
                httpHeaders.set(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        return httpHeaders;
    }

    /**
     * 构建请求体
     */
    private static HttpEntity<?> buildHttpEntity(HttpHeaders headers, String body) {
        if (ObjectUtils.isEmpty(body)) {
            body = "{}";
        }
        if (!ObjectUtils.isEmpty(headers) && !ObjectUtils.isEmpty(headers.getContentType())) {
            if (ObjectUtils.nullSafeEquals(headers.getContentType(), MediaType.MULTIPART_FORM_DATA)) {
                MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
                for (Map.Entry<String, ?> entry : JacksonUtils.json2map(body).entrySet()) {
                    formData.add(entry.getKey(), entry.getValue());
                }
                return new HttpEntity<>(formData, headers);
            } else if (ObjectUtils.nullSafeEquals(headers.getContentType(), MediaType.APPLICATION_JSON)) {
                return new HttpEntity<>(body, headers);
            }
        }
        return new HttpEntity<>(body, headers);
    }

    private static HttpEntity<?> buildFormDataHttpEntity(HttpHeaders headers, MultiValueMap<String, Object> formData) {
        return new HttpEntity<>(formData, headers);
    }
}
