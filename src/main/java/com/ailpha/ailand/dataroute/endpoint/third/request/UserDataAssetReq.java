package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 17:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserDataAssetReq {

    private String userId;

    @Schema(description = "数据资源 RESOURCE、数据产品 PRODUCT")
    AssetType type;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "数源单位")
    String providerOrg;

    @Schema(description = "行业分类")
    String industry;

    @Schema(description = "交付方式")
    String deliveryMode;
    @Schema(description = "MPC 数据资产用途")
    String mpcPurpose;
    Boolean currentRouter;
    Boolean currentUser;

    List<String> industryClassifyList;

}
