package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.ailpha.ailand.dataroute.endpoint.home.ContractOrderDealStatisticDTO;
import com.ailpha.ailand.dataroute.endpoint.home.OrderDealStatisticDTO;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticAssetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OrderStatisticDTO {

    @Schema(description = "签订合同统计")
    OrderDealStatisticDTO dealStatisticDTO;

    @Schema(description = "合同完成统计")
    ContractOrderDealStatisticDTO contractOrderDealStatisticDTO;

    @Schema(description = "登记数据资产")
    StatisticAssetDTO assetStatistic;


}
