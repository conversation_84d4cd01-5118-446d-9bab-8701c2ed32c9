package com.ailpha.ailand.dataroute.endpoint.openapi;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 2022/8/30
 */
public interface PlatformKeySecretRepository extends JpaRepository<PlatformAppKeySecret, Long>, QuerydslPredicateExecutor<PlatformAppKeySecret> {

    PlatformAppKeySecret findFirstByAppKey(String appKey);

    Optional<PlatformAppKeySecret> getFirstByPlatformName(String platformName);

    List<PlatformAppKeySecret> getAllByPlatformName(String platformName);
}
