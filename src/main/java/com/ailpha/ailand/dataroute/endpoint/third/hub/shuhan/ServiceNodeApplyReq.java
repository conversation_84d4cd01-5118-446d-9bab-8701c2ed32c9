package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/4/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeApplyReq implements Serializable {

    @Schema(description = "数由器ID")
    String routerId;

    @Schema(description = "业务节点ID")
    String serviceNodeId;
}
