package com.ailpha.ailand.dataroute.endpoint.deliveryScene.service;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.*;

/**
 * <AUTHOR>
 * @date 2024/11/18 14:41
 */
public interface DeliveryService {


    /**
     * 当前登录用户可用数据资产
     *
     * @param query query
     * @return 可用数据资产
     */
    PageResult<DataAssetResp> dataAssetApproveList(DataAssetQuery query);


    /**
     * 新增交付场景
     *
     * @param sceneSaveReq req
     */
    void deliverySceneAdd(SceneSaveReq sceneSaveReq);

    /**
     * 归属当前用户场景交付列表
     *
     * @param sceneListReq req
     * @return 场景交付列表
     */
    PageResult<SceneListResp> deliverySceneList(SceneListReq sceneListReq);

    /**
     * 场景交付详情
     *
     * @param id id
     * @return
     */
    SceneDetailResp detail(String id);

    Boolean contractPreCheck(PreCheckRequest request);

    void checkBaseCapabilityConfig(String id);

    SceneAssetResp deliveryAsset(String deliverId, String dataAssetId);
}
