package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 登录请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LoginRequest extends BaseRemoteRequest {
    /**
     * 账号
     */
    private String account;
    
    /**
     * 密码
     */
    private String password;
}
