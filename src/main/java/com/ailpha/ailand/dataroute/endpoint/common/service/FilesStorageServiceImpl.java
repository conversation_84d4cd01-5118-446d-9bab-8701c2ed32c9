package com.ailpha.ailand.dataroute.endpoint.common.service;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.utils.CheckFileFormatUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateTimeUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.FileUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ReadLinesFileProcessor;
import com.ailpha.ailand.invoke.api.CommonException;
import com.dbapp.rest.exception.RestfulApiException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021/12/14
 * @description
 */
@Slf4j
@Service
public class FilesStorageServiceImpl {

    private final AiLandProperties aiLandProperties;

    private Path root;

    public FilesStorageServiceImpl(AiLandProperties aiLandProperties) {
        this.aiLandProperties = aiLandProperties;
    }

    public Path getRootPath() {
        return root;
    }

    /**
     * 每天清理今天之前的临时文件
     * 注释理由：调试数据生成默认走的这个逻辑 如果被临时文件被删除了，那么后续会出现找不到的问题
     */
    @PostConstruct
    public void init() {
        try {
            root = Paths.get(this.aiLandProperties.getFileStorage().getBasePath());
            if (!root.toFile().exists()) {
                Files.createDirectories(root);
            }
        } catch (IOException e) {
            throw new RestfulApiException("无法创建数据文件存放目录: " + this.aiLandProperties.getFileStorage().getBasePath());
        }
    }

    public Path save(MultipartFile file, UnaryOperator<Path> targetPath) {
        try {
            Path path = targetPath.apply(this.root);
            if (!path.getParent().toFile().exists()) {
                Files.createDirectories(path.getParent());
            } else {
                Files.deleteIfExists(path);
            }
            Files.copy(file.getInputStream(), path);
            return path;
        } catch (Exception e) {
            throw new RestfulApiException("保存文件失败: " + e.getMessage());
        }
    }

    public Path save(MultipartFile file, Path target) {
        try {
            if (!target.getParent().toFile().exists()) {
                Files.createDirectories(target.getParent());
            }
            Files.deleteIfExists(target);
            Files.copy(file.getInputStream(), target);
            return target;
        } catch (Exception e) {
            throw new RestfulApiException("保存文件失败: " + e.getMessage());
        }
    }

    public Path saveTmpData2File(Path from, UnaryOperator<Path> targetPath) {
        try {
            if (!from.toFile().exists()) {
                throw new RestfulApiException("源文件不存在: " + from.toAbsolutePath());
            }
            Path target = targetPath.apply(this.root);
            Files.copy(from, target);
            return target;
        } catch (Exception e) {
            throw new RestfulApiException("保存文件失败: " + e.getMessage());
        }
    }

    public Path saveTmpData2File(Path from, Path target, boolean hasHeader) {
        if (!from.toFile().exists()) {
            throw new RestfulApiException("源文件不存在: " + from.toAbsolutePath());
        }
        try {
            if (!target.getParent().toFile().exists()) {
                Files.createDirectories(target.getParent());
            }
            Files.deleteIfExists(target);
        } catch (Exception e) {
            log.error("保存文件失败: ", e);
            throw new RestfulApiException(String.format("保存文件[%s]到[%s]失败", from, target));
        }
        try (FileInputStream fis = new FileInputStream(from.toFile());
             BufferedReader br = new BufferedReader(new InputStreamReader(fis));
             FileOutputStream fos = new FileOutputStream(target.toFile());
             BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(fos));) {
            String line;
            if (hasHeader) {
                // 上传时有表头的跳过
                br.readLine();
            }
            while ((line = br.readLine()) != null) {
                bw.write(line);
                bw.newLine();
            }
            return target;
        } catch (Exception e) {
            log.error("保存文件失败: ", e);
            throw new RestfulApiException(String.format("保存文件[%s]到[%s]失败", from, target));
        }
    }

    public Path saveTmpData2FileDirectly(Path from, Path target) {
        // 创建父文件夹，可能会不存在
        try {
            if (!target.getParent().toFile().exists()) {
                Files.createDirectories(target.getParent());
            }
            Files.deleteIfExists(target);
        } catch (Exception e) {
            log.error("保存文件失败: ", e);
            throw new RestfulApiException(String.format("保存文件[%s]到[%s]失败", from, target));
        }
        try (FileInputStream fis = new FileInputStream(from.toFile());
             FileOutputStream os = new FileOutputStream(target.toFile());) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            return target;
        } catch (IOException e) {
            log.error("保存文件失败: ", e);
            throw new CommonException(String.format("保存文件[%s]到[%s]失败", from, target));
        }
    }

    public Path getDebugFile(String datasetName, String datasetFileType) throws IOException {
        Path path = this.getRootPath().resolve("debugData")
                .resolve(DateTimeUtils.formatDate(new Date(), "yyyyMMdd"))
                .resolve(String.format("%s_调试数据_%s%s", datasetName, System.currentTimeMillis(), datasetFileType));
        if (!path.getParent().toFile().exists()) {
            Files.createDirectories(path.getParent());
        }
        Files.deleteIfExists(path);
        Files.createFile(path);
        return path;
    }

    public Path getBatchParamsFile(String datasetName) throws IOException {
        Path path = this.getRootPath().resolve("batchParamsFile")
                .resolve(DateTimeUtils.formatDate(new Date(), "yyyyMMdd"))
                .resolve(String.format("%s_批量参数_%s.csv", datasetName, System.currentTimeMillis()));
        if (!path.getParent().toFile().exists()) {
            Files.createDirectories(path.getParent());
        }
        Files.deleteIfExists(path);
        Files.createFile(path);
        return path;
    }

    public Path getDebugFilePath(String datasetName) throws IOException {
        return this.getRootPath().resolve("debugData")
                .resolve(DateTimeUtils.formatDate(new Date(), "yyyyMMdd"))
                .resolve(String.format("%s_调试数据_%s.csv", datasetName, System.currentTimeMillis()));
    }

    public Path getDebugFilePath(String datasetName, String datasetFileType) throws IOException {
        return this.getRootPath().resolve("debugData")
                .resolve(DateTimeUtils.formatDate(new Date(), "yyyyMMdd"))
                .resolve(String.format("%s_调试数据_%s%s", datasetName, System.currentTimeMillis(), datasetFileType));
    }

    public Path save2TempFile(MultipartFile tempFile) {
        return this.save(tempFile, this.getRootPath().resolve(".tmp")
                .resolve(DateTimeUtils.formatDate(new Date(), "yyyyMMdd"))
                .resolve(System.currentTimeMillis() + "_" + tempFile.getOriginalFilename()));
    }

    public Path save2KerberosFile(MultipartFile tempFile, String userId) {
        return this.save(tempFile, this.getRootPath().resolve("files").resolve("kerberos")
                .resolve(userId)
                .resolve(System.currentTimeMillis() + "_" + tempFile.getOriginalFilename()));
    }

    public Path saveTmpData2File(String datasetName, String datasetFileType, List<String> lines) throws IOException {
        Path debugFile = this.getDebugFile(datasetName, datasetFileType);
        appendContent2File(debugFile.toFile(), lines);
        return debugFile;
    }

    public File appendContent2File(File file, List<String> lines) throws IOException {
        try (BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(file.toPath()), StandardCharsets.UTF_8));) {
            for (int i = 0; i < lines.size(); i++) {
                bufferedWriter.write(lines.get(i));
                if (i < lines.size() - 1) {
                    bufferedWriter.newLine();
                }
            }
            return file;
        }
    }

    /**
     * 根据内容创建文件
     *
     * @param lines    文件内容
     * @param filePath 文件名路径
     */
    public void createAbsolutePathFileWithContent(List<String> lines, String filePath) throws IOException {
        File file = createAbsolutePathFile(filePath);
        try (BufferedWriter bufferedWriter = new BufferedWriter(new OutputStreamWriter(Files.newOutputStream(file.toPath()), StandardCharsets.UTF_8))) {
            for (String line : lines) {
                bufferedWriter.write(line);
                bufferedWriter.newLine();
            }
            log.info("createAbsolutePathFileWithContent success!! --文件-> [{}]", filePath);
        }
    }

    /**
     * 创建文件
     *
     * @param filePath 文件路径
     */
    private File createAbsolutePathFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (!Files.exists(file.toPath().getParent())) {
            Files.createDirectories(file.toPath().getParent());
        }
        Files.deleteIfExists(file.toPath());
        log.info("deleteFile success!! --文件-> [{}]", file.getAbsolutePath());
        Files.createFile(file.toPath());
        log.info("createFile success!! --文件-> [{}]", file.getAbsolutePath());
        return file;
    }

    /**
     * 读小文件
     *
     * @param target 文件绝对路径
     */
    public String readContent(Path target) throws Exception {
        StringBuilder stringBuilder = new StringBuilder();
        readFileContentByLine(target, bufferedReader -> {
            String line;
            while (true) {
                try {
                    if ((line = bufferedReader.readLine()) == null) break;
                } catch (Exception e) {
                    break;
                }
                stringBuilder.append(line);
            }
        });
        return stringBuilder.toString();
    }

    /**
     * 读大文件
     *
     * @param target        文件绝对路径
     * @param contentReader contentReader
     */
    public void readFileContentByLine(Path target, Consumer<BufferedReader> contentReader) throws Exception {
        try (FileInputStream inputStream = new FileInputStream(target.toFile().getAbsolutePath());
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            contentReader.accept(bufferedReader);
        }
    }

    public static void checkIfIsShellAndThrowExceptions(File file) throws RestfulApiException {
        if (FileUtils.isShellFile(file)) {
            try {
                Files.deleteIfExists(file.toPath());
            } catch (IOException ignore) {
            }
            throw new RestfulApiException("禁止上传脚本文件");
        }
    }

    public static void csvRequired(File file) throws RestfulApiException {
        String realSuffix = CheckFileFormatUtils.getFileType(file.getAbsolutePath());
        if (StringUtils.isNotBlank(realSuffix)) {
            try {
                Files.deleteIfExists(file.toPath());
            } catch (IOException ignore) {
            }
            throw new RestfulApiException(String.format("不能上传 %s 类型的文件", realSuffix));
        }
        if (!file.getName().endsWith(".csv")) {
            try {
                Files.deleteIfExists(file.toPath());
            } catch (IOException ignore) {
            }
            throw new RestfulApiException("文件后缀名必须是 .csv");
        }
    }

    public static File convert2UTF8(File file) throws Exception {
        Charset charset = FileUtils.getFileEncode(file.getAbsolutePath());
        if (charset == null) {
            throw new RestfulApiException("未知的调试数据编码，在本地加密为UTF-8密文文件失败");
        }
        if (charset.equals(StandardCharsets.UTF_8)) {
            return file;
        }
        String absolutePath = file.getAbsoluteFile().getAbsolutePath();
        File utf8File = new File(absolutePath + "_utf8");
        if (utf8File.exists()) {
            utf8File.delete();
        }
        utf8File.createNewFile();
        log.info("开始在同级目录下开始生成UTF-8编码的调试数据文件...");
        ReadLinesFileProcessor fileProcessor = new ReadLinesFileProcessor();
        fileProcessor.setSkipFirstLine(false);
        fileProcessor.setSourceEncoding(charset);
        fileProcessor.processFileByRows(file, utf8File);
        file.deleteOnExit();
        File newFile = new File(absolutePath);
        utf8File.renameTo(newFile);
        return newFile;
    }

    public List<List<String>> spiltLineList(List<String> lines, String separate) {
        if (CollectionUtils.isEmpty(lines)) {
            return new ArrayList<>();
        }
        return lines.stream().map(a -> Arrays.asList(a.split(separate, -1)))
                .collect(Collectors.toList());
    }

    public List<List<String>> getPreviewDataFromFile(String debugFilePath, String fieldSeparator) {
        File debugTempFile = this.getRootPath().resolve(debugFilePath).toFile();
        if (debugTempFile.exists() && debugTempFile.isFile()) {
            List<String> samplingRows = FileUtils.getSamplingRows(
                    debugTempFile,
                    false,
                    10,
                    null,
                    -1,
                    null,
                    false
            );
            if (!StringUtils.isAnyEmpty(fieldSeparator)) {
                return this.spiltLineList(samplingRows, fieldSeparator);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 保存文件
     *
     * @param multipartFile multipartFile
     * @param filePath      文件绝对路径
     */
    public void saveToAbsolutePath(MultipartFile multipartFile, String filePath) throws IOException {
        File file = new File(filePath);
        if (!Files.exists(file.toPath().getParent())) {
            Files.createDirectories(file.toPath().getParent());
        }
        Files.deleteIfExists(file.toPath());
        Files.copy(multipartFile.getInputStream(), file.toPath(), StandardCopyOption.REPLACE_EXISTING);
        log.info("saveToAbsolutePath success!! --文件-> [{}]", filePath);
    }

    /**
     * 复制文件
     *
     * @param fromFilePath 源文件绝对路径
     * @param toFilePath   目标文件绝对路径
     */
    public void copyToAbsolutePath(String fromFilePath, String toFilePath) throws IOException {
        File toFile = new File(toFilePath);
        if (!Files.exists(toFile.toPath().getParent())) {
            Files.createDirectories(toFile.toPath().getParent());
        }
        Files.copy(new File(fromFilePath).toPath(), toFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
        log.info("copyToAbsolutePath success!! --文件-> [{}]", toFilePath);
    }

    /**
     * 移动文件
     *
     * @param fromFilePath 源文件绝对路径
     * @param filePath     目标文件名称路径
     */
    public void moveToAbsolutePath(String fromFilePath, String filePath) throws IOException {
        File file = new File(filePath);
        if (!Files.exists(file.toPath().getParent())) {
            Files.createDirectories(file.toPath().getParent());
        }
        Files.deleteIfExists(file.toPath());
        Files.move(new File(fromFilePath).toPath(), file.toPath(), StandardCopyOption.REPLACE_EXISTING);
        log.info("moveToAbsolutePath success!! --文件-> [{}]", filePath);
    }

    /**
     * 删除临时文件目录
     *
     * @param tempFilePath 临时文件目录绝对路径
     */
    public void deleteTempFilePath(Path tempFilePath) {
        if (Files.exists(tempFilePath)) {
            try (Stream<Path> walkStream = Files.walk(tempFilePath)) {
                walkStream.sorted((p1, p2) -> -p1.compareTo(p2))
                        .forEach(path -> {
                            try {
                                if (Files.isDirectory(path)) {
                                    if (Files.isSymbolicLink(path)) {
                                        Files.delete(path);
                                    } else {
                                        Files.delete(path);
                                    }
                                } else {
                                    Files.delete(path);
                                }
                            } catch (Exception e) {
                                log.error("deleteTempFilePath failure!! error:", e);
                            }
                        });
                log.info("deleteTempFilePath success!! ---> [{}]", tempFilePath);
            } catch (Exception e) {
                log.error("deleteTempFilePath failure!! error:", e);
            }
        }
    }

}
