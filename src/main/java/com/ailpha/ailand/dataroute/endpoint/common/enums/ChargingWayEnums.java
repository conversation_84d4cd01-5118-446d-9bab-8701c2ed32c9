package com.ailpha.ailand.dataroute.endpoint.common.enums;

/**
 * @author: sunsas.yu
 * @date: 2024/11/18 14:55
 * @Description:
 */
public enum ChargingWayEnums {
    PREPAID("预付费"),

    POSTPAID("后付费");

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private String name;

    ChargingWayEnums(String name){
        this.name = name;
    }



}
