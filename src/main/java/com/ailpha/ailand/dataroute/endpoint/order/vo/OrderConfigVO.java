package com.ailpha.ailand.dataroute.endpoint.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderConfigVO implements Serializable {

    @Schema(description = "订单编号")
    String orderId;

    /**
     * 0未开始 1交付完成 2交付中 3订单时间到期 4订单次数完成
     */
    String deliveryStatus;


    public String changeOrderStatus() {
        if (StringUtils.isBlank(deliveryStatus)) {
            return "";
        } else {
            switch (deliveryStatus) {
                case "0", "1", "2":
                    return "APPROVED";
                case "3", "4":
                    return "COMPLETED";
                default:
                    return "";
            }
        }

    }

}
