package com.ailpha.ailand.dataroute.endpoint.third.mapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateTimeUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.TEEPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataProductRegistRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import org.mapstruct.*;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING, builder = @Builder(disableBuilder = true))
public abstract class DataAssetMapper {

    @Mapping(source = "productName", target = "dataProductName")
    @Mapping(source = "productNameCN", target = "dataExt.assetNameCN")
    @Mapping(source = "type", target = "dataExt.type")
    @Mapping(source = "dataCoverageTimeStart", target = "dataExt.dataCoverageTimeStart")
    @Mapping(source = "dataCoverageTimeEnd", target = "dataExt.dataCoverageTimeEnd")
    @Mapping(source = "industry1", target = "dataExt.industry1")
    @Mapping(source = "region", target = "dataExt.region")
    @Mapping(source = "region1", target = "dataExt.region1")
    @Mapping(source = "personalInformation", target = "dataExt.personalInformation")
    @Mapping(source = "source", target = "dataExt.source")
    @Mapping(target = "capacity", ignore = true)
    @Mapping(target = "updateFrequency", ignore = true)
    @Mapping(source = "deliveryMethod", target = "deliveryExt.deliveryMethod")
    @Mapping(source = "limitations", target = "deliveryExt.limitations")
    @Mapping(source = "authorize", target = "deliveryExt.authorize")
    @Mapping(source = "isSecondaryProcessed", target = "deliveryExt.isSecondaryProcessed")
    @Mapping(source = "resourceId", target = "dataExt.resourceId")
    @Mapping(source = "lineage", target = "dataExt.lineage")
    @Mapping(source = "other", target = "dataExt.other")
    @Mapping(source = "qualificationDoc.dataSampleAttach", target = "dataExt.qualificationDoc.dataSampleAttach")
    @Mapping(source = "qualificationDoc.complianceAndLegalStatementAttach", target = "dataExt.qualificationDoc.complianceAndLegalStatementAttach")
    @Mapping(source = "qualificationDoc.dataSourceStatementAttach", target = "dataExt.qualificationDoc.dataSourceStatementAttach")
    @Mapping(source = "qualificationDoc.safeLevelAttach", target = "dataExt.qualificationDoc.safeLevelAttach")
    @Mapping(source = "qualificationDoc.evaluationReportAttach", target = "dataExt.qualificationDoc.evaluationReportAttach")
    @Mapping(source = "qualificationDoc.complianceSelfCheckManualAttach", target = "dataExt.qualificationDoc.complianceSelfCheckManualAttach")
    @Mapping(source = "qualificationDoc.otherAttach", target = "dataExt.qualificationDoc.otherAttach")
    public abstract DataProduct dataProductRegistRequestToDataProduct(DataProductRegistRequest dataProductCreateRequest);

    @Mapping(source = "dataProductName", target = "productName")
    @Mapping(source = "provider.routerId", target = "routerId")
    @Mapping(source = "dataExt.type", target = "type")
    @Mapping(source = "dataExt.region", target = "region")
    @Mapping(source = "dataExt.publishStatus", target = "publishStatus")
    @Mapping(source = "dataExt.dataAssetPrepareStatus", target = "prepareStatus")
    @Mapping(source = "dataExt.qualificationDoc.dataSampleAttach", target = "dataSampleAttach")
    @Mapping(source = "deliveryExt.deliveryMethod", target = "deliveryMethod")
    @Mapping(source = "deliveryExt.deliveryModes", target = "deliveryModes")
    @Mapping(source = "deliveryExt.serviceNodes", target = "serviceNodes")
    @Mapping(source = "deliveryExt.billingMethod", target = "billingMethod")
    @Mapping(source = "deliveryExt.purchaseUnit", target = "purchaseUnit")
    @Mapping(source = "deliveryExt.price", target = "price")
    @Mapping(target = "registrationTime", ignore = true)
    @Mapping(target = "publishTime", ignore = true)
    @Mapping(source = "sourceType", target = "accessWay")
    @Mapping(source = "dataExt.processLogs", target = "processLogs")
    @Mapping(source = "dataExt.gatewayServiceRouteId", target = "gatewayServiceRouteId")
    public abstract DataProductListVO dataProductToDataProductListVO(DataProduct dataProduct);

    @AfterMapping
    void dataProductToDataProductListVO(DataProduct dataProduct, @MappingTarget DataProductListVO dataProductListVO) {
        if (!CollectionUtils.isEmpty(dataProductListVO.getServiceNodes())) {
            for (ServiceNodeApplyListVO serviceNode : dataProductListVO.getServiceNodes()) {
                serviceNode.setPushStatus(serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() == 1 ? PushStatus.ONLINE : PushStatus.OFFLINE);
            }
        }
        if (!CollectionUtils.isEmpty(dataProduct.getDeliveryExt().getMpcPurpose())) {
            for (MPCPurpose mpcPurpose : dataProduct.getDeliveryExt().getMpcPurpose()) {
                switch (mpcPurpose) {
                    case PRIVATE_INFORMATION_RETRIEVAL ->
                            dataProductListVO.getDeliveryModes().add(DeliveryMode.MPC_PRIVATE_INFORMATION_RETRIEVAL);
                    case PRIVATE_SET_INTERSECTION ->
                            dataProductListVO.getDeliveryModes().add(DeliveryMode.MPC_PRIVATE_SET_INTERSECTION);
                    case CIPHER_TEXT_COMPUTE ->
                            dataProductListVO.getDeliveryModes().add(DeliveryMode.MPC_CIPHER_TEXT_COMPUTE);
                }
            }
            dataProductListVO.getDeliveryModes().remove(DeliveryMode.MPC);
        }
        if (dataProduct.getDataExt().getRegistrationSubmitTime() != null) {
            dataProductListVO.setRegistrationSubmitTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataProduct.getDataExt().getRegistrationSubmitTime())));
        }
        if (dataProduct.getDataExt().getRegistrationTime() != null) {
            dataProductListVO.setRegistrationTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataProduct.getDataExt().getRegistrationTime())));
        }
        if (dataProduct.getDataExt().getPublishSubmitTime() != null) {
            dataProductListVO.setPublishSubmitTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataProduct.getDataExt().getPublishSubmitTime())));
        }
        if (dataProduct.getDataExt().getPublishTime() != null) {
            dataProductListVO.setPublishTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataProduct.getDataExt().getPublishTime())));
        }
    }

    @Mapping(source = "id", target = "assetId")
    @Mapping(source = "dataResourcePlatformId", target = "dataAssetPlatformId")
    @Mapping(target = "assetType", constant = "RESOURCE")
    @Mapping(source = "dataResourceName", target = "assetName")
    @Mapping(source = "description", target = "describeMessage")
    @Mapping(source = "platformId", target = "platformId")
    @Mapping(source = "provider.routerName", target = "routerName")
    @Mapping(source = "sourceType", target = "source")
    @Mapping(source = "dataExt.tag", target = "tag")
    @Mapping(source = "dataExt.dataCoverageTimeStart", target = "dataCoverageTimeStart")
    @Mapping(source = "dataExt.dataCoverageTimeEnd", target = "dataCoverageTimeEnd")
    @Mapping(source = "dataExt.dataType", target = "dataType")
    @Mapping(source = "dataExt.apiQueryWay", target = "extraData.apiQueryWay")
    @Mapping(source = "dataExt.dataSchema", target = "extraData.dataSchema")
    @Mapping(source = "dataExt.debugDataSource", target = "extraData.debugDataSource")
    @Mapping(source = "dataExt.debugDataPath", target = "extraData.debugDataPath")
    @Mapping(source = "dataExt.separator", target = "extraData.separator")
    @Mapping(source = "dataExt.hasHeader", target = "extraData.hasHeader")
    @Mapping(source = "dataExt.gatewayServiceRouteId", target = "extraData.gatewayServiceRouteId")
    @Mapping(source = "dataExt.apiSourceMetadata", target = "extraData.apiSourceMetadata")
    @Mapping(source = "dataExt.fileSourceMetadata", target = "extraData.fileSourceMetadata")
    @Mapping(source = "dataExt.databaseSourceMetadata", target = "extraData.databaseSourceMetadata")
    @Mapping(source = "dataExt.aiSortMetadata", target = "extraData.aiSortMetadata")
    @Mapping(source = "dataExt.exchangePluginIds", target = "extraData.exchangePluginIds")
    @Mapping(source = "provider.email", target = "extraData.email")
    @Mapping(source = "provider.phone", target = "extraData.phone")
    @Mapping(source = "dataExt.certificatePluginIds", target = "extraData.certificatePluginIds")
    @Mapping(source = "deliveryExt.deliveryModes", target = "extraData.deliveryModes")
    @Mapping(source = "deliveryExt.mpcPurpose", target = "extraData.mpcPurpose")
    @Mapping(source = "username", target = "userName")
    @Mapping(source = "dataExt.extractResponse", target = "extraData.extractResponse")
    @Mapping(target = "createTime", dateFormat = DateTimeUtils.DEFAULT_DATE_FORMAT)
    @Mapping(target = "updateTime", dateFormat = DateTimeUtils.DEFAULT_DATE_FORMAT)
    @Mapping(target = "pushStatus", expression = "java(com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus.getByCode(dataResource.getPushStatus()))")
    public abstract DataAsset dataResourceToDataAsset(DataResource dataResource);

    @AfterMapping
    void dataResourceToDataAsset(DataResource dataResource, @MappingTarget DataAsset dataAsset) {
        dataAsset.getExtraData().setCompanyId(dataResource.getProvider().getCompany().getId());
        dataAsset.setRouterId(dataResource.getProvider().getCompany().getNodeId());
    }

    @Mapping(source = "id", target = "assetId")
    @Mapping(source = "dataProductPlatformId", target = "dataAssetPlatformId")
    @Mapping(target = "assetType", constant = "PRODUCT")
    @Mapping(source = "dataProductName", target = "assetName")
    @Mapping(source = "description", target = "describeMessage")
    @Mapping(source = "platformId", target = "platformId")
    @Mapping(target = "routerId", ignore = true)
    @Mapping(source = "provider.routerName", target = "routerName")
    @Mapping(source = "sourceType", target = "source")
    @Mapping(source = "username", target = "userName")
    @Mapping(source = "dataExt.tag", target = "tag")
    @Mapping(source = "dataExt.dataCoverageTimeStart", target = "dataCoverageTimeStart")
    @Mapping(source = "dataExt.dataCoverageTimeEnd", target = "dataCoverageTimeEnd")
    @Mapping(source = "dataExt.dataType", target = "dataType")
    @Mapping(source = "dataExt.apiQueryWay", target = "extraData.apiQueryWay")
    @Mapping(source = "dataExt.dataSchema", target = "extraData.dataSchema")
    @Mapping(source = "dataExt.debugDataSource", target = "extraData.debugDataSource")
    @Mapping(source = "dataExt.debugDataPath", target = "extraData.debugDataPath")
    @Mapping(source = "dataExt.separator", target = "extraData.separator")
    @Mapping(source = "dataExt.hasHeader", target = "extraData.hasHeader")
    @Mapping(source = "dataExt.gatewayServiceRouteId", target = "extraData.gatewayServiceRouteId")
    @Mapping(source = "dataExt.apiSourceMetadata", target = "extraData.apiSourceMetadata")
    @Mapping(source = "dataExt.fileSourceMetadata", target = "extraData.fileSourceMetadata")
    @Mapping(source = "dataExt.databaseSourceMetadata", target = "extraData.databaseSourceMetadata")
    @Mapping(source = "dataExt.aiSortMetadata", target = "extraData.aiSortMetadata")
    @Mapping(source = "dataExt.exchangePluginIds", target = "extraData.exchangePluginIds")
    @Mapping(source = "dataExt.certificatePluginIds", target = "extraData.certificatePluginIds")
    @Mapping(source = "dataExt.lineage", target = "extraData.lineage")
    @Mapping(source = "dataExt.mpcOpenAPIId", target = "extraData.mpcOpenAPIId")
    @Mapping(source = "dataExt.industry1", target = "extraData.industry1")
    @Mapping(source = "provider.email", target = "extraData.email")
    @Mapping(source = "provider.phone", target = "extraData.phone")
    @Mapping(source = "deliveryExt.deliveryModes", target = "extraData.deliveryModes")
    @Mapping(source = "deliveryExt.mpcPurpose", target = "extraData.mpcPurpose")
    @Mapping(source = "dataExt.extractResponse", target = "extraData.extractResponse")
    @Mapping(target = "createTime", dateFormat = DateTimeUtils.DEFAULT_DATE_FORMAT)
    @Mapping(target = "updateTime", dateFormat = DateTimeUtils.DEFAULT_DATE_FORMAT)
    @Mapping(target = "pushStatus", expression = "java(com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus.getByCode(dataProduct.getPushStatus()))")
    public abstract DataAsset dataProductToDataAsset(DataProduct dataProduct);

    @AfterMapping
    void dataProductToDataAsset(DataProduct dataProduct, @MappingTarget DataAsset dataAsset) {
        dataAsset.getExtraData().setCompanyId(dataProduct.getProvider().getCompany().getId());
        dataAsset.setRouterId(dataProduct.getProvider().getCompany().getNodeId());
    }

    @Mapping(source = "dataExt.assetNameCN", target = "dataProductNameCN")
    @Mapping(source = "dataExt.type", target = "type")
    @Mapping(source = "dataExt.dataCoverageTimeStart", target = "dataCoverageTimeStart")
    @Mapping(source = "dataExt.dataCoverageTimeEnd", target = "dataCoverageTimeEnd")
    @Mapping(source = "dataExt.industry1", target = "industry1")
    @Mapping(source = "dataExt.region", target = "region")
    @Mapping(source = "dataExt.region1", target = "region1")
    @Mapping(source = "dataExt.personalInformation", target = "personalInformation")
    @Mapping(source = "deliveryExt.deliveryModes", target = "deliveryModes")
    @Mapping(source = "deliveryExt.deliveryMethod", target = "deliveryMethod")
    @Mapping(source = "deliveryExt.limitations", target = "limitations")
    @Mapping(source = "deliveryExt.authorize", target = "authorize")
    @Mapping(source = "dataExt.source", target = "source")
    @Mapping(target = "dataSize", ignore = true)
    @Mapping(source = "deliveryExt.isSecondaryProcessed", target = "isSecondaryProcessed")
    @Mapping(source = "dataExt.registrationTime", target = "registrationTime")
    @Mapping(source = "dataExt.registrationUpdateTime", target = "registrationUpdateTime")
    @Mapping(source = "dataExt.publishTime", target = "publishTime")
    @Mapping(source = "dataExt.publishUpdateTime", target = "publishUpdateTime")
    @Mapping(source = "dataExt.lineage", target = "lineage")
    @Mapping(source = "dataExt.other", target = "other")
    @Mapping(source = "dataExt.qualificationDoc", target = "qualificationDoc")
    @Mapping(source = "deliveryExt.serviceNodes", target = "serviceNodes")
    @Mapping(source = "deliveryExt.billingMethod", target = "billingMethod")
    @Mapping(source = "deliveryExt.purchaseUnit", target = "purchaseUnit")
    @Mapping(source = "deliveryExt.price", target = "price")
    @Mapping(source = "dataExt.dataType", target = "dataType")
    @Mapping(source = "dataExt.dataType1", target = "dataType1")
    @Mapping(source = "sourceType", target = "accessWay")
    @Mapping(source = "dataExt.apiQueryWay", target = "apiQueryWay")
    @Mapping(source = "deliveryExt.mpcPurpose", target = "mpcPurpose")
    @Mapping(source = "dataExt.debugDataSource", target = "debugDataSource")
    @Mapping(source = "dataExt.debugDataPath", target = "debugDataPath")
    @Mapping(source = "dataExt.dataSchema", target = "dataSchema")
    @Mapping(source = "dataExt.separator", target = "separator")
    @Mapping(source = "dataExt.hasHeader", target = "hasHeader")
    @Mapping(source = "dataExt.apiSourceMetadata", target = "apiSourceMetadata")
    @Mapping(source = "dataExt.fileSourceMetadata", target = "fileSourceMetadata")
    @Mapping(source = "dataExt.databaseSourceMetadata", target = "databaseSourceMetadata")
    @Mapping(source = "dataExt.aiSortMetadata", target = "aiSortMetadata")
    @Mapping(source = "dataExt.exchangePluginIds", target = "exchangePluginIds")
    @Mapping(source = "dataExt.certificatePluginIds", target = "certificatePluginIds")
    @Mapping(source = "dataExt.mpcOpenAPIId", target = "mpcOpenAPIId")
    @Mapping(source = "dataExt.gatewayServiceRouteId", target = "gatewayServiceRouteId")
    @Mapping(source = "dataExt.dataAssetPrepareStatus", target = "prepareStatus")
    @Mapping(source = "dataExt.publishStatus", target = "publishStatus")
    @Mapping(source = "dataProductPlatformId", target = "resourceId")
    @Mapping(source = "dataExt.updateWay", target = "updateWay")
    @Mapping(source = "dataExt.updateFreq", target = "updateFreq")
    @Mapping(source = "dataExt.selectDate", target = "selectDate")
    @Mapping(source = "dataExt.selectHour", target = "selectHour")
    @Mapping(source = "dataExt.isLLM", target = "isLLM")
    @Mapping(source = "dataExt.modelMetadata", target = "modelMetadata")
    @Mapping(source = "dataExt.processLogs", target = "processLogs")
    public abstract DataProductVO dataProductToDataProductVO(DataProduct dataProduct);

    @AfterMapping
    void dataProductToDataProductVO(DataProduct dataProduct, @MappingTarget DataProductVO dataProductVO) {
        if (dataProduct.getDataExt().getDataSize() != null) {
            dataProductVO.setDataSize(dataProduct.getDataExt().getDataSize() + dataProduct.getDataExt().getDataSizeUnit());
        }
        if (!CollectionUtils.isEmpty(dataProductVO.getServiceNodes())) {
            for (ServiceNodeApplyListVO serviceNode : dataProductVO.getServiceNodes()) {
                serviceNode.setPushStatus(serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() == 1 ? PushStatus.ONLINE : PushStatus.OFFLINE);
            }
        }
        if (!CollectionUtils.isEmpty(dataProductVO.getDeliveryModes())) {
            dataProductVO.setTeePurpose(new ArrayList<>());
            boolean hasTEE = false;
            Iterator<DeliveryMode> iterator = dataProductVO.getDeliveryModes().iterator();
            while (iterator.hasNext()) {
                DeliveryMode deliveryMode = iterator.next();
                switch (deliveryMode) {
                    case TEE_MODEL_OPTIMIZE -> {
                        hasTEE = true;
                        dataProductVO.getTeePurpose().add(TEEPurpose.TEE_MODEL_OPTIMIZE);
                        iterator.remove();
                    }
                    case TEE_MODEL_PREDICT -> {
                        hasTEE = true;
                        dataProductVO.getTeePurpose().add(TEEPurpose.TEE_MODEL_PREDICT);
                        iterator.remove();
                    }
                    case TEE_ONLINE -> {
                        hasTEE = true;
                        dataProductVO.getTeePurpose().add(TEEPurpose.TEE_ONLINE);
                        iterator.remove();
                    }
                    case TEE_OFFLINE -> {
                        hasTEE = true;
                        dataProductVO.getTeePurpose().add(TEEPurpose.TEE_OFFLINE);
                        iterator.remove();
                    }
                }
            }
            if (hasTEE) {
                dataProductVO.getDeliveryModes().add(DeliveryMode.TEE);
            }
        }
        User user = SpringUtil.getBean(UserService.class).findByUserId(dataProduct.getUserId());
        CompanyDTO companyDTO = SpringUtil.getBean(CompanyService.class).localCompany(dataProduct.getProvider().getCompany().getId());
        BeanUtil.copyProperties(user.getExt().getDelegateInfo(), companyDTO);
        dataProductVO.getProvider().setCompany(companyDTO);
        dataProductVO.getProvider().setAuthorizationLetterLocalUrl(SpringUtil.getBean(UserService.class).userDetailForThird(dataProduct.getUserId()).getDelegateInfo().getAuthorizationLetterLocalUrl());
    }

    @Mapping(source = "dataExt.assetNameCN", target = "dataResourceNameCN")
    @Mapping(source = "dataExt.resourceFormat", target = "resourceFormat")
    @Mapping(source = "dataExt.industry1", target = "industry1")
    @Mapping(source = "dataExt.region", target = "region")
    @Mapping(source = "dataExt.region1", target = "region1")
    @Mapping(source = "dataExt.personalInformation", target = "personalInformation")
    @Mapping(source = "dataExt.source", target = "source")
    @Mapping(source = "dataExt.registrationTime", target = "registrationTime")
    @Mapping(source = "dataExt.registrationUpdateTime", target = "registrationUpdateTime")
    @Mapping(source = "dataExt.dataType", target = "dataType")
    @Mapping(source = "dataExt.dataType1", target = "dataType1")
    @Mapping(source = "dataExt.dataSchema", target = "dataSchema")
    @Mapping(source = "dataExt.other", target = "other")
    @Mapping(source = "dataResourcePlatformId", target = "resourceId")
    @Mapping(source = "dataExt.processLogs", target = "processLogs")
    public abstract DataResourceVO dataResourceToDataResourceVO(DataResource dataResource);

    @AfterMapping
    void dataResourceToDataResourceVO(DataResource dataResource, @MappingTarget DataResourceVO dataResourceVO) {
        User user = SpringUtil.getBean(UserService.class).findByUserId(dataResource.getUserId());
        CompanyDTO companyDTO = SpringUtil.getBean(CompanyService.class).localCompany(dataResource.getProvider().getCompany().getId());
        BeanUtil.copyProperties(user.getExt().getDelegateInfo(), companyDTO);
        dataResourceVO.getProvider().setCompany(companyDTO);
        dataResourceVO.getProvider().setAuthorizationLetterLocalUrl(SpringUtil.getBean(UserService.class).userDetailForThird(dataResource.getUserId()).getDelegateInfo().getAuthorizationLetterLocalUrl());
    }

    @Mapping(source = "dataResourceName", target = "resourceName")
    @Mapping(source = "dataExt.resourceFormat", target = "resourceFormat")
    @Mapping(source = "dataExt.source", target = "source")
    @Mapping(source = "dataExt.dataType", target = "dataType")
    @Mapping(source = "dataExt.dataType1", target = "dataType1")
    @Mapping(target = "registrationTime", ignore = true)
    @Mapping(source = "dataExt.processLogs", target = "processLogs")
    public abstract DataResourceListVO dataProductToDataResourceListVO(DataResource dataResource);

    @AfterMapping
    void dataProductToDataProductListVO(DataResource dataResource, @MappingTarget DataResourceListVO dataResourceListVO) {
        if (dataResource.getDataExt().getRegistrationSubmitTime() != null) {
            dataResourceListVO.setRegistrationSubmitTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataResource.getDataExt().getRegistrationSubmitTime())));
        }
        if (dataResource.getDataExt().getRegistrationTime() != null) {
            dataResourceListVO.setRegistrationTime(new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_FORMAT).format(new Date(dataResource.getDataExt().getRegistrationTime())));
        }
    }
}
