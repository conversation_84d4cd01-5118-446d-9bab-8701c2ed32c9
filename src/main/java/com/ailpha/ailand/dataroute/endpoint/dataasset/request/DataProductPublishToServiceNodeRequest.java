package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品上架到指定业务节点请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublishToServiceNodeRequest {
    @NotNull
    @Schema(description = "数据产品id")
    String dataProductId;
    @Schema(description = "要上架的业务节点id")
    List<String> serviceNodeIds;
}
