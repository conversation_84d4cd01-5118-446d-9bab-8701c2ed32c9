package com.ailpha.ailand.dataroute.endpoint.order.schedule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.ConnectorMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.IPageDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.BusinessNodeOrderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderResolveService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderResolveDTO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.input.CompanyInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.response.CompanyInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/2/25 15:29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderSellerSyncSchedule {

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final CompanyRepository companyRepository;

    private final OrderResolveService orderResolveService;

    private final DataProductService dataProductService;

    private final OrderSyncSchedule orderSyncSchedule;

    private final EndpointRemote endpointRemote;

    private final AssetBeneficiaryRepository assetBeneficiaryRepository;
    private final CompanyService companyService;

    @Value("${order.sync.size:50}")
    private Integer pageSize;

    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    public void syncOrderSeller() {
        if (BUSY.get()) {
            return;
        }

        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        // saas化以企业维度同步订单数据
        companyRepository.findAll().forEach(company -> {
            try {
                if (StringUtils.isNotEmpty(company.getNodeId())) {
                    BUSY.set(true);
                    log.info("sync seller order from service business platform ....");
                    CompanyDTO companyDTO = companyService.detail(company.getId());
                    AsyncManager.getInstance().executeFuture(() -> {
                        TenantContext.setCurrentTenant("tenant_" + company.getId());
                        orderSyncSchedule.doCompanySync(companyDTO, false);
                        return true;
                    });

                }
            } finally {
                BUSY.set(false);
            }
        });

    }


    /**
     * 调用基础能力平台 获取 任务数据
     */
    public void doSync(Integer pageNum, CompanyDTO company, ServiceNodeInfo serviceNodeInfo) {

        // 【卖方】 企业信息
        String routeId = company.getNodeId();
        Long companyId = company.getId();

        // 国标查询
        CatalogQueryVM catalogQuery = new CatalogQueryVM();

        CatalogQueryVM.FilterVM filterVM = new CatalogQueryVM.FilterVM();

        filterVM.setFilterValue(routeId);
        filterVM.setFilterOperation("eq");
        filterVM.setFilterProperty("participant_router_id");

        CatalogQueryVM.FilterVM filterStatusVM = new CatalogQueryVM.FilterVM();
        filterStatusVM.setFilterProperty("delivery_status");
        filterStatusVM.setFilterOperation("in");
        filterStatusVM.setFilterValue("0,2");

        DateTime yesterday = DateUtil.yesterday();
        DateTime begin = DateUtil.beginOfDay(yesterday);
        DateTime end = DateUtil.dateNew(new Date());

        // 合约生效时间 —— 昨天到现在当前时间
        CatalogQueryVM.FilterVM filterTimeBetween = new CatalogQueryVM.FilterVM();
        filterTimeBetween.setFilterProperty("begin_time");
        filterTimeBetween.setFilterValue(String.format("%s-%s", begin.toTimestamp().getTime(), end.toTimestamp().getTime()));
        filterTimeBetween.setFilterOperation("between");


        catalogQuery.setPage(pageNum.longValue());
        catalogQuery.setSize(pageSize.longValue());
        catalogQuery.setFilters(Arrays.asList(filterVM, filterStatusVM, filterTimeBetween));

        IPageDTO<BusinessNodeOrderDTO> orderPageData = orderSyncSchedule.syncOrderRecords(serviceNodeInfo.getApiUrl(), catalogQuery);
        List<BusinessNodeOrderDTO> records = orderPageData.getData();
        if (CollectionUtil.isEmpty(records)) {
            log.info("当前同步 routeId【{}】卖方订单数据为空", routeId);
            return;
        }

        log.debug("获取卖方订单数据参数: {} 任务条数: {}", JSONUtil.toJsonStr(catalogQuery), records.size());

        // step 1、查看对应租户下是否存在 拉取到的订单信息
        List<BusinessNodeOrderDTO> rows = orderResolveService.filterExistOrder(records);
        if (CollectionUtil.isEmpty(rows)) {
            return;
        }

        // step 2、存在 —— 过滤
        List<OrderResolveDTO> list = rows.stream().map(orderInfo -> {
            OrderResolveDTO orderResolveDTO;
            try {
                orderResolveDTO = resolveOrderRecord(orderInfo, company, serviceNodeInfo);
            } catch (Exception e) {
                log.error("解析卖方订单信息异常：orderInfo: {}", orderInfo, e);
                return null;
            }
            return orderResolveDTO;
        }).filter(Objects::nonNull).toList();
        list.forEach(orderResolveService::save);

        long total = orderPageData.getPagination().getTotal();
        long pageCount = (total + pageSize - 1) / pageSize;
        if (pageCount > pageNum) {
            ++pageNum;
            doSync(pageNum, company, serviceNodeInfo);
        }
    }

    /**
     * 解析订单数据信息
     */
    private OrderResolveDTO resolveOrderRecord(BusinessNodeOrderDTO businessNodeOrderDTO, CompanyDTO sellerCompany, ServiceNodeInfo serviceNodeInfo) {
        // 发起方企业信息
        String routeId = sellerCompany.getNodeId();

        // 数据产品id
        BusinessNodeOrderDTO.TransactionExecutionStrategy strategy = businessNodeOrderDTO.getTransactionExecutionStrategy();
        String productPlatformId = strategy.getRegistrationId();
        long createTime = strategy.getCreateTime();
        // 查询产品详情
        DataProductVO dataProductVO;
        try {
            dataProductVO = dataProductService.getDataProductByDataProductPlatformId(productPlatformId, routeId);
        } catch (Exception e) {
            log.error("根据 productPlatformId【{}】获取产品数据异常：", productPlatformId, e);
            return null;
        }

        String buyerNodeId = businessNodeOrderDTO.getBuyerStrategyCode();
        CompanyInfoResp companyInfoResp;
        try {
            CompanyInfoRequest companyInfoRequest = new CompanyInfoRequest(buyerNodeId);
            ConnectorMetaData connectorMetaData = new ConnectorMetaData();
            connectorMetaData.setTargetNodeId(buyerNodeId);
            CommonResult<CompanyInfoResp> companyInfo = endpointRemote.companyInfo(companyInfoRequest, connectorMetaData.toBase64());
            companyInfoResp = companyInfo.getData();
        } catch (Exception e) {
            log.error("根据 buyerNodeId【{}】获取买方企业信息数据异常：", buyerNodeId, e);
            return null;
        }

        // 数据产品 企业信息
        ProviderExt provider = dataProductVO.getProvider();
        CompanyDTO company = provider.getCompany();

        OrderApprovalRecord record = OrderApprovalRecord.builder()
                .id(businessNodeOrderDTO.getCtrlInstructionId())
                .classify(OrderClassify.NORMAL)
                .type(AssetType.PRODUCT)
                .assetId(dataProductVO.getId())
                .assetName(dataProductVO.getDataProductName())
                .deliveryMode(strategy.getDeliveryMethod())
                // 使用连接器唯一用户id todo: 这里用的是第三方用户id，非连接器（查不到）
                .beneficiaryId(strategy.getInitiatorId())
                .beneficiaryUsername(strategy.getInitiatorUsername())
                .beneficiaryRouterId(buyerNodeId)
                // 买方企业信息 拿不到 加字段
                .beneficiaryEnterpriseName(strategy.getInitiator())
                .beneficiaryEnterpriseProperty("")
                .approverId(dataProductVO.getUserId())
                .approverUsername(dataProductVO.getProvider().getUsername())
                .approverRouterId(dataProductVO.getProvider().getRouterId())
                .approverEnterpriseName(company.getOrganizationName())
                // 访问控制配置

                // 预付费、后付费
                .chargingWay(strategy.getPaymentMethod())
                // 按需、按时间 —— 对应 MeasurementMethod
                .meteringWay(strategy.getMeasurementMethod())
                .allowance(strategy.getTotal() == null ? new BigInteger("0") : strategy.getTotal())
                .expireDate(strategy.getEndTime() == null ? null : DateUtil.date(strategy.getEndTime()))
                .successfulUsage(new BigInteger("0"))
                // 卖方的订单状态 同步查询业务节点
                .status("")
                .createTime(DateUtil.date(createTime))
                .updateTime(new Date())
                .approveTime(strategy.getStartTime() == null ? new Date() : DateUtil.date(strategy.getStartTime()))
                .pullTime(new Date())
                .build();

        BusinessNodeOrderDTO.DeliveryInfo deliveryInfo = strategy.getDeliveryInfo();
        OderRecordExtend recordExtend = OderRecordExtend.builder()
                .callPrice("面议")
                // 对应 MeasurementUint
                .cycleWay(strategy.getMeasurementUint())
                .price(strategy.getPrice())
                .beneficiaryCreditCode(companyInfoResp.getCreditCode())
                .beneficiaryCompanyId(String.valueOf(companyInfoResp.getId()))
                .approverCreditCode(company.getCreditCode())
                .approverCompanyId(String.valueOf(company.getId()))
                .dataProductPlatformId(dataProductVO.getDataProductPlatformId())
                // 扩展字段
                .productPrice(String.valueOf(dataProductVO.getPrice()))
                .mpcPurposes(dataProductVO.getMpcPurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .teePurposes(dataProductVO.getTeePurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .productionType(dataProductVO.getType())
                .dataType(dataProductVO.getDataType() == null ? null : dataProductVO.getDataType().name())
                .dataType1(dataProductVO.getDataType1())
                .isLLM(dataProductVO.getIsLLM())
                .summary(dataProductVO.getDescription())
                .providerOrg(company.getOrganizationName())
                .tradingStrategyCode(businessNodeOrderDTO.getTradingStrategyCode())
                .tradingStrategyName(businessNodeOrderDTO.getTradingStrategyName())
                .tradingStrategyContent(businessNodeOrderDTO.getTradingStrategyContent())
                .tradingStrategyTime(businessNodeOrderDTO.getTradingStrategyTime())
                .serviceNodeId(serviceNodeInfo.getServiceNodeId())
                .serviceNodeUrl(serviceNodeInfo.getApiUrl())
                .serviceNodeName(serviceNodeInfo.getEntryName())
                .deliveryModeStandards(dataProductVO.getDeliveryMethod())
                .transferMode(deliveryInfo == null ? "" : deliveryInfo.getTransferMode())
                .deliveryInfo(deliveryInfo == null ? "" : deliveryInfo.getDeliveryInfo())
                .build();

        List<DeliveryMode> deliveryModeList = DataAssetDeliveryExt.deliveryModesForOrderResolve(dataProductVO.getDeliveryModes(), dataProductVO.getMpcPurpose(), dataProductVO.getTeePurpose());
        String deliveryModes = deliveryModeList.stream().map(Enum::name).collect(Collectors.joining(","));

        recordExtend.setDeliveryModes(deliveryModes);

        record.setExtend(recordExtend);


        AssetBeneficiaryRel assetBeneficiaryRel = assetBeneficiaryRepository.findFirstByOrderIdOrderByCreateTimeDesc(record.getId());
        if (assetBeneficiaryRel == null) {
            assetBeneficiaryRel = AssetBeneficiaryRel.builder()
                    .id(UUID.randomUUID().toString().replace("-", ""))
                    .assetId(record.getAssetId())
                    .beneficiaryId(record.getBeneficiaryId())
                    .orderId(record.getId())
                    // 可以不填充
                    .extend(new AssetBeneficiaryExtend())
                    .createTime(new Date())
                    .updateTime(new Date())
                    .build();
        } else {
            assetBeneficiaryRel.setAssetId(record.getAssetId());
            assetBeneficiaryRel.setBeneficiaryId(record.getBeneficiaryId());
            assetBeneficiaryRel.setUpdateTime(new Date());
        }

        return new OrderResolveDTO(record, assetBeneficiaryRel);
    }


}
