package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/3 14:28
 */
@Data
public class CompanyInfoResp {

    @Schema(description = "企业ID")
    private Long id;

    @Schema(description = "企业名称")
    private String organizationName;

    @Schema(description = "统一社会信用代码")
    private String creditCode;

    @Schema(description = "法定代表人姓名")
    private String legalRepresentativeName;
}
