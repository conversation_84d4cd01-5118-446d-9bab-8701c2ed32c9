package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.biz.api.dataset.PartitionColumnBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DatabaseSourceMetadata {
    @Schema(description = "数据库表")
    String tableName;
//    @Schema(description = "数据库表字段（前端回显用）")
//    List<DataSchemaBO> columns;
    @Schema(description = "数据源名称")
    String name;
    @Schema(description = "数据源描述")
    String desc;
    @NotEmpty(message = "jdbcUrl 不能为空")
    @Schema(description = "数据源 jdbcUrl", example = "*********************************************************************")
    String jdbcUrl;
    @Valid
    @Schema(description = "数据源用户名")
    String username;
    @Schema(description = "数据源用户密码")
    String password;
    @Valid
    @NotEmpty(message = "数据源类型不能为空")
    @Schema(description = "数据源类型")
    String datasourceType;
    @Valid
    @Schema(description = "odps账号")
    String access_id;
    @Valid
    @Schema(description = "odps密钥")
    String access_key;
    @Valid
    @Schema(description = "odps项目")
    String project_name;
    @Valid
    @Builder.Default
    @Schema(description = "是否需要kerberos认证")
    Boolean hasKerberos = false;
    @Valid
    @Schema(description = "keytab认证文件")
    String keytabFilePath;
    @Valid
    @Schema(description = "kerberos用户")
    String kerberosPrincipal;
    @Valid
    @Schema(description = "kerberos配置文件")
    String kerberosConfPath;
    @Schema(description = "同步条件")
    String syncRule;
    @Schema(description = "分区表分区字段")
    List<PartitionColumnBO> partitionColumns;
}
