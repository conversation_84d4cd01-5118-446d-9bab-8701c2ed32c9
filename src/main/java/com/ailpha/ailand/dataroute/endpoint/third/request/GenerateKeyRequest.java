package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/13 13:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GenerateKeyRequest {
    String productId;
    String productUserId;
    String username;
    String dataProductName;
    String gatewayServiceRouteId;
    DeliveryType deliveryType;
}
