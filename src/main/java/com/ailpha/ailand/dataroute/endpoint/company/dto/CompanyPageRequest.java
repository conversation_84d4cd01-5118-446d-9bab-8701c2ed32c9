package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.dbapp.rest.request.Page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CompanyPageRequest extends Page {
    // 企业名称
    @Schema(description = "企业名称")
    String name;
    // 企业状态
    @Schema(description = "企业状态")
    CompanyStatus status;
    AccountStatus accountStatus;
    // 统一信用代码
    @Schema(description = "统一信用代码")
    String creditCode;
    // 法人
    @Schema(description = "法人")
    String legalPerson;
    // 经办人
    @Schema(description = "经办人")
    String operator;
    // 账号
    @Schema(description = "账号")
    String account;
}
