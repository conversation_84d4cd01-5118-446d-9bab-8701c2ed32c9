package com.ailpha.ailand.dataroute.endpoint.tenant.config;

import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.tenant.service.TenantService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.sql.DataSource;

@Slf4j
@RequiredArgsConstructor
@Component("FlywayConfig")
public class FlywayConfig {

    private final DataSource dataSource;
    private final TenantRepository tenantRepository;
    private final TenantService tenantService;
    @Value("${ailand.flyway.enabled:true}")
    private Boolean enabled;


    @PostConstruct
    public void migrateFlyway() {
        if (!enabled)
            return;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("flyway_init");
        // 公共schema迁移
        try {
            Flyway.configure()
                    .dataSource(dataSource)
                    .schemas("data_route_public")
                    .locations("classpath:db/migration/public")
                    .baselineOnMigrate(true)
                    .load()
                    .migrate();
        } catch (Exception e) {
            log.error("初始化主库schema异常 ", e);
            throw e;
        }

        // 租户schema迁移
        tenantRepository.findAll().forEach(tenant -> {
                    if (!StringUtils.equals(tenant.getSchemaName(), TenantIdentifierResolver.DEFAULT_TENANT)) {
                        tenantService.initTenantSchema(tenant.getSchemaName());
                    }
                }
        );
        stopWatch.stop();
        log.info("flyway 初始化成功 耗时：{}ms", stopWatch.getTotalTimeMillis());
    }
}
