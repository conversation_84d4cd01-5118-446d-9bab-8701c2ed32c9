package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_heng_nao_agent_task")
@Data
public class HengNaoAgentTask {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "task_id", nullable = false)
    private String taskId;

    @Enumerated(EnumType.STRING)
    @Column(name = "task_type", nullable = false)
    private AgentTaskType taskType;

    @Column(name = "user_id", nullable = false)
    private String userId;

    @Column(name = "file_name", nullable = false)
    private String fileName;

    @Column(name = "status", nullable = false)
    private Integer status; // 1-执行中，2-成功，3-取消，4-失败

    @Column(name = "result_json", columnDefinition = "TEXT")
    private String resultJson;

    @Column(name = "fail_reason")
    private String failReason;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Embedded
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "ext")
    Ext ext;

    @Data
    @Embeddable
    public static class Ext {
        String dataSourcePdfFileName;
        Boolean deleted = false;
    }
}