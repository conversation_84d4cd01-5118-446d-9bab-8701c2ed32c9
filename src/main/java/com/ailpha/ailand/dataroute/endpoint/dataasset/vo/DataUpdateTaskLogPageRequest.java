package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.dbapp.rest.request.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

@Data
public class DataUpdateTaskLogPageRequest extends Page {
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "查询开始时间")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "查询结束时间")
    private Date endDate;

    @Schema(description = "更新类型：单次、定时、手动")
    private UpdateWay updateWay;

    @Schema(description = "状态：已创建、更新中、更新成功、更新失败")
    private DataUpdateStatus updateStatus;

    @Schema(description = "数据产品资产ID")
    @NotBlank(message = "数据产品资产ID不能为空")
    private String dataAssetId;
}
