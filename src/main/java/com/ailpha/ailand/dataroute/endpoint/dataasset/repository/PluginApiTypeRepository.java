package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:48
 * @Description:
 */
public interface PluginApiTypeRepository extends JpaRepository<PluginApiType, Long>, QuerydslPredicateExecutor<PluginApiType> {
    List<PluginApiType> findAllByType(PluginApiTypeEnums type);
}
