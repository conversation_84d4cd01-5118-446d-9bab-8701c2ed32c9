package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou;

import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataProductInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.FileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.PlatformListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;

public interface DataProductApi {

    /**
     * 数据产品登记接口
     *
     * @param dataProductInfoRegist 数据产品基本信息
     * @return
     */
    @PostExchange("/open/product/dataProductInfoRegist")
    ResponseWrapper<String> dataProductInfoRegist(@RequestBody DataProductInfoRegist dataProductInfoRegist);

    /**
     * 数据产品登记更新接口
     *
     * @param dataProductUpdate 数据产品基本信息
     * @return
     */
    @PostExchange("/open/product/dataProductInfoUpdate")
    ResponseWrapper<DataAssetUpdateVO> dataProductInfoUpdate(@RequestBody DataProductInfoUpdate dataProductUpdate);

    /**
     * 数据产品登记撤销接口
     *
     * @param dataProductRevoke 数据标识
     * @return 执行结果状态：0 数据产品登记撤销成功 1 执行失败
     */
    @PostExchange("/open/product/dataProductInfoRevoke")
    ResponseWrapper<DataAssetRevokeVO> dataProductInfoRevoke(@RequestBody DataProductInfoRevoke dataProductRevoke);

    /**
     * 数据产品上架接口
     *
     * @param dataProductPublish 数据产品基本信息
     * @return
     */
    @PostExchange("/open/productListing/dataProductPublish")
    ResponseWrapper<DataProductPublishVO> dataProductPublish(@RequestBody DataProductPublish dataProductPublish);

    /**
     * 数据产品下架接口
     *
     * @param dataProductUnPublish 数据上架ID
     * @return 执行结果状态
     */
    @PostExchange("/open/productListing/dataProductUnpublish")
    ResponseWrapper<DataAssetUpdateVO> dataProductUnpublish(@RequestBody DataProductUnpublish dataProductUnPublish);

    /**
     * 产品发布接口
     *
     * @param publishProduct 数据产品发布信息
     * @return
     */
    @PostExchange("/open/product/publishProduct")
    ResponseWrapper<DataProductPublishVO> publishProduct(@RequestBody PublishProduct publishProduct);

    /**
     * 数据目录查询接口
     *
     * @param catalogQuery 数据目录查询参数
     */
    @PostExchange("/open/product/dataCatalogQuery")
    ResponseListWrapper<DataProductInfo> dataCatalogQuery(@RequestBody DataProductCatalogQuery catalogQuery);

    /**
     * 数据目录详情查询接口
     *
     * @param getProductInfo
     */
    @PostExchange("/open/product/getByProductInfo")
    ResponseWrapper<DataProductInfo> getByProductInfo(@RequestBody GetByProductInfo getProductInfo);

    /**
     * 文件上传接口
     *
     * @param fileUploadRequest 文件内容
     */
    @PostExchange("/open/entity/fileUpload")
    ResponseWrapper<FileUploadResponse> fileUpload(@RequestBody FileUploadRequest fileUploadRequest);

    /**
     * 发布平台列表查询接口
     *
     * @return 发布平台列表
     */
    @PostExchange("/open/platform/platformList")
    ResponseWrapper<List<PlatformListVO>> platformList();
}
