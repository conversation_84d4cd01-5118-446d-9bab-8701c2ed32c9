package com.ailpha.ailand.dataroute.endpoint.order.remote.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/24 09:27
 */
@Data
public class ContractInfo {
    private String contractNo;        // 合同唯一标识
    private String srcPlatformId;     // 来源平台标识
    private String srcUserId;         // 来源用户标识
    private String srcContractNo;      // 来源平台合同编号
    private String contractName;       // 合同名称
    private String signTime;           // 签订时间 yyyy-MM-dd HH:mm:ss
    private String validStartTime;     // 生效时间 yyyy-MM-dd HH:mm:ss
    private String validEndTime;       // 到期时间 yyyy-MM-dd HH:mm:ss
    private String producerId;        // 提供方标识
    private String consumerId;         // 使用方标识
    private Long amount;              // 金额,单位:分
    private String fileInfo;          // 附件
    private String contractStatus;     // 合同状态      ⭐️ 1:已签署  2:已终止  3:冻结 ⭐️
    private String remark;            // 备注
    private String producerName;      // 提供主体名称
    private String consumerName;       // 使用主体名称
    private String platformId;        // 平台 id
    private String producerCode;      // 提供方编码
    private String consumerCode;       // 使用方编码


}
