package com.ailpha.ailand.dataroute.endpoint.order.remote;

import com.ailpha.ailand.dataroute.endpoint.base.GanZhouPage;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.DataRouterManagerInterceptor;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.ContractPageQuery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderPageQuery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.ContractInfo;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.OrderInfo;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = DataRouterManagerInterceptor.class)
public interface GanZhouOrderRemoteService {


    @POST("/prod-api/data-base/open/order/dataCatalogQuery")
    GanZhouPage<OrderInfo> orderCatalogQuery(@Body OrderPageQuery request);


    @POST("/prod-api/data-base/open/contract/dataCatalogQuery")
    GanZhouPage<ContractInfo> contractCatalogQuery(@Body ContractPageQuery request);

}
