package com.ailpha.ailand.dataroute.endpoint.dataprope.controller;

import com.ailpha.ailand.dataroute.endpoint.dataprope.service.DataProbeService;
import com.ailpha.ailand.dataroute.endpoint.dataprope.vo.request.DataProbeListReq;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortColumnResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortDataSourceResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortTablesResp;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/2/20
 */
@RestController
@Tag(name = "数据探查")
@RequestMapping("/data-probe")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('TRADER')")
public class DataProbeController {

    private final DataProbeService dataProbeService;

    @PostMapping("/tables")
    @Operation(summary = "数据目录–获取表分页")
    public SuccessResponse<List<AiSortTablesResp.Record>> tables(@Valid @RequestBody DataProbeListReq dataProbeListReq) {
        return dataProbeService.tables(dataProbeListReq);
    }

    @GetMapping("/getById")
    @Parameters({
            @Parameter(name = "id", description = "id", in = ParameterIn.QUERY)
    })
    @Operation(summary = "数据源管理-根据ID获取数据源详情")
    public SuccessResponse<AiSortDataSourceResp> getById(@RequestParam(value = "id") Long id) {
        AiSortDataSourceResp aiSortDataSource = dataProbeService.getById(id);
        return SuccessResponse.success(aiSortDataSource).build();
    }

    @GetMapping("/columns")
    @Operation(summary = "数据目录–根据表id获取字段列表")
    @Parameters({
            @Parameter(name = "comment", description = "comment", in = ParameterIn.QUERY),
            @Parameter(name = "id", description = "表id", in = ParameterIn.QUERY),
            @Parameter(name = "name", description = "name", in = ParameterIn.QUERY)
    })
    public SuccessResponse<List<AiSortColumnResp>> columns(@RequestParam(value = "comment", required = false) String comment, @RequestParam(value = "id") Long id, @RequestParam(value = "name", required = false) String name) {
        List<AiSortColumnResp> columns = dataProbeService.columns(comment, id, name);
        return SuccessResponse.success(columns).build();
    }
}
