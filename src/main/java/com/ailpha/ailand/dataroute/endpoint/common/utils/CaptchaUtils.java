package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.ailpha.ailand.dataroute.endpoint.user.contants.CacheCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/2/10
 * @description
 */
@Slf4j
public final class CaptchaUtils {

    public static CacheCode generateCaptcha(long expireInMinutes) throws IOException, InterruptedException {
        String captcha = RandomStringUtils.random(5, true, true);
        String captchaId = UUID.nameUUIDFromBytes((captcha + captcha.toLowerCase()).getBytes(StandardCharsets.UTF_8)).toString();
        log.debug("captcha: {} -> {}", captchaId, captcha);
        return CacheCode.builder()
                .code(captcha)
                .codeId(captchaId)
                .createTime(System.currentTimeMillis())
                .expirInMinutes(TimeUnit.MINUTES.toMinutes(expireInMinutes))
                .build();
    }

    public static CacheCode generateMailCode(String mail, long expireInMinutes) {
        String captcha = RandomStringUtils.random(5, true, true);
        return CacheCode.builder()
                .code(captcha)
                .codeId(mail)
                .createTime(System.currentTimeMillis())
                .expirInMinutes(TimeUnit.MINUTES.toMillis(expireInMinutes))
                .build();
    }
}
