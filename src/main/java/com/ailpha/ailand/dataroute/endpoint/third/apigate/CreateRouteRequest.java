package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateRouteRequest {
    InvokeParam invokeParam;

    public String getInvokeParam() {
        return JacksonUtils.obj2json(invokeParam);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeParam {
        @JsonProperty("service_id")
        String serviceId;
        @JsonProperty("service_name")
        String serviceName;

        String name;

        String uri;

        String desc;

        @Builder.Default
        Integer priority = 0;
        /**
         * 空代表ALL
         * ALL GET POST PUT DELETE PATCH HEAD OPTIONS CONNECT TRACE PURGE
         */
        @Builder.Default
        List<String> methods = new ArrayList<>();
        PluginWrapper plugins;
        @Builder.Default
        Map<String, String> labels = new HashMap<>();
        // [["http_ROUTE_ID","==","route-1000"]]
        @Builder.Default
        List<List<String>> vars = new ArrayList<>();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class PluginWrapper {
        @JsonProperty("proxy-rewrite")
        ProxyReWrite proxyReWrite;
        @JsonProperty("dynamic-data-masking")
        DynamicDataMasking dynamicDataMasking;
        @JsonProperty("response-extract")
        ResponseExtract responseExtract;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ResponseExtract {
        @JsonProperty("_meta")
        PluginMetadata metadata;
        @JsonProperty("source_fields")
        List<FieldPath> sourceFields;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ProxyReWrite {
        String uri;
        Headers headers;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DynamicDataMasking {
        @JsonProperty("consumer_filter")
        ConsumerFilter consumerFilter;
        List<DMask> dmasks;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ConsumerFilter {
        boolean all;
        Object rules; // TODO
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DMask {
        /**
         * <pre>
         * {
         *     "datatag_id": "1008000000000000070",
         *     "dataTagId": "1008000000000000070",
         *     "alg_param": "{\"keep_name\": true, \"keep_surname\": false}",
         *     "algorithm": "fullname_parting",
         *     "dataMaskingRulesId": "1010000000000000002",
         *     "dataTagCategoryId": "1006000000000001614",
         *     "dataTagGradeId": "1007000000000000001",
         *     "enabled": true,
         *     "path": "sensitiveA",
         *     "source": "user",
         *     "dataMaskingAlgorithmId": "1010000000000000001"
         * }
         * </pre>
         */
        String dataTagId;
        @JsonProperty("alg_param")
        String algParam;
        String algorithm;
        String dataMaskingRulesId;
        String dataTagCategoryId;
        String dataTagGradeId;
        boolean enabled;
        String path;
        String source;
        String dataMaskingAlgorithmId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Headers {
//        Map<String, String> add = new HashMap<>();

        Map<String, String> set = new HashMap<>();

//        Set<String> remove = new HashSet<>();

//        public void addHeader(String headerName, String headerValue) {
//            add.put(headerName, headerValue);
//        }

        public void setHeader(String headerName, String headerValue) {
            set.put(headerName, headerValue);
        }

//        public void removeHeader(String headerName) {
//            remove.add(headerName);
//        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class PluginMetadata {
        @Builder.Default
        Boolean disabled = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class FieldPath {
        String path;
    }
}
