package com.ailpha.ailand.dataroute.endpoint.node.dto;

import com.ailpha.ailand.dataroute.endpoint.node.domain.NodeStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@Schema(description = "节点信息")
public class NodeDTO implements Serializable {
    @Schema(description = "节点ID")
    private Long id;

    @Schema(description = "节点唯一标识")
    private String nodeId;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "节点类型")
    private String nodeType;

    @Schema(description = "节点状态")
    private NodeStatus status;

    @Schema(description = "接入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date accessTime;

    @Schema(description = "枢纽信息")
    HubInfo hubInfo;
    @Schema(description = "连接器信息")
    ConnectorInfo connectorInfo;

    @Data
    public static class HubInfo implements Serializable {
        @Schema(description = "枢纽url")
        String url;
        String authType;
        String publicKey;
        String ak;
        String sk;
        String certificateNo;
    }

    @Data
    public static class ConnectorInfo implements Serializable {
        String exposeUrl;
    }
}