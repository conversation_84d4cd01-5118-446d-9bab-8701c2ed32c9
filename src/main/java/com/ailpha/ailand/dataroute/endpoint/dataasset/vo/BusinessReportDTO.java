package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/17 11:25
 * 数商上报内容
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BusinessReportDTO {


    /**
     * 企业名称
     */
    String companyName;

    /**
     * 企业统信码
     */
    String companySucc;

    /**
     * 法人姓名,非必传
     */
    String companyLegalPerson;

    /**
     * 法人身份证,非必传
     */
    String companyLegalIdCard;

    /**
     * 法人手机号,非必传
     */
    String companyLegalMobile;

    /**
     * 数商类型1普通，2卖家，3第三方服务机构
     */
    Integer dataCompanyType = 1;

    /**
     * 登记平台企业ID(对接方id)
     */
    String platformCompanyId;

    /**
     * 登记平台id(平台码)
     */
    String sourcePlatformId;

    /**
     * 联系人
     */
    List<CompanyLink> companyLinkParams;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyLink {

        /**
         * 姓名
         */
        String companyLinkName;

        /**
         * 身份证
         */
        String companyLinkIdCard;

        /**
         * 手机
         */
        String companyLinkMobile;

    }


}
