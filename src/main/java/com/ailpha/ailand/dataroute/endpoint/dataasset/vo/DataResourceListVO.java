package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产（资源、产品）信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceListVO {
    @Schema(description = "资产id")
    String id;
    @Schema(description = "数据资源全局（连接器空间）唯一标识")
    String dataResourcePlatformId;
    @Schema(description = "资产名称")
    String resourceName;
    @Schema(description = "资源格式")
    String resourceFormat;
    @Schema(description = "产品简介")
    String description;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "数据资源来源")
    String source;
    @Schema(description = "数据类型")
    DataType dataType;
    @Schema(description = "数据类型")
    String dataType1;
    @Schema(description = "登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销 item_status5 功能节点待审批 item_status6 功能节点通过 item_status7 功能节点拒绝")
    String itemStatus;

    @Schema(description = "创建时间")
    String createTime;
    @Schema(description = "更新时间")
    String updateTime;
    String userId;
    String username;
    @Schema(description = "连接器ID")
    String routerId;

    @Schema(description = "登记提交时间")
    String registrationSubmitTime;
    @Schema(description = "登记时间")
    String registrationTime;

    List<DataAssetExt.ProcessLog> processLogs;
}
