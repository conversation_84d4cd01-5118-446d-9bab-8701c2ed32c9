package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2021/12/29
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MPCDataSchemaVO {

    @Schema(description = "是否参与计算")
    Boolean isParticipateCompute = Boolean.FALSE;

    @Schema(description = "字段名")
    String fieldName;

    @Schema(description = "数据类型", allowableValues = {"STRING", "INT", "FLOAT", "DOUBLE", "DECIMAL", "TIMESTAMP", "BOOLEAN", "BIGINT", "BINARY", "DATE"})
    String dataType;

    @Schema(description = "字段描述")
    String description;
    @Schema(description = "原始字段名")
    String originFieldName;
}
