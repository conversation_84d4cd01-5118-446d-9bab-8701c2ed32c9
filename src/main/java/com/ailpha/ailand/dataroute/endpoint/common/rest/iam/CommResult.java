package com.ailpha.ailand.dataroute.endpoint.common.rest.iam;

import lombok.Data;

@Data
public class CommResult<T> {

    Integer code = 0;
    String msg;
    T content;

    public CommResult() {
    }

    public CommResult(int code, String msg, T content) {
        this.code = code;
        this.msg = msg;
        this.content = content;
    }

    public boolean isSuccess() {
        return code == 0;
    }
}
