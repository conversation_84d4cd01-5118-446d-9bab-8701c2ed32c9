package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductInfoRegist {
    @NotBlank(message = "来源平台内部标识")
    String outerProductId;
    @NotBlank(message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他")
    String productType;
    @NotBlank(message = "产品名称")
    String productName;
    @NotBlank(message = "有效期起始")
    String validStartTime;
    @NotBlank(message = "有效期截止")
    String validEndTime;
    @NotBlank(message = "行业分类，详见附录《行业代码表》")
    String industry;
    @NotBlank(message = "地域分类，详见附录《全国区划表》")
    String productRegion;
    @NotBlank(message = "是否涉及个人信息：0-否,1-是")
    String personalInformation;
    @NotBlank(message = "产品简介")
    String description;
    //    @NotBlank(message = "交付方式：01-文件传输,02-数据流传输,03-API传输")
    String deliveryMethod;
    @NotBlank(message = "使用限制")
    String limitations;
    @NotBlank(message = "授权使用:0：否;1：是")
    String authorize;
    @NotBlank(message = "数据主体：01-个人信息,02-企业数据,03-公共数据")
    String dataSubject;
    @NotBlank(message = "数据规模（如10GB）")
    String dataSize;
    @NotBlank(message = "更新频率；单位为次/天、次/周、次/月、次/季度、次/年、其他")
    String updateFrequency;
    //    @NotBlank(message = "其他")
    String others;
    @NotBlank(message = "提供方姓名")
    String providerName;
    @NotBlank(message = "提供方主体类型:01：自然人 02：法人 03：非法人组织")
    String providerType;
    @NotBlank(message = "主体信息,JSON 格式")
    String entityInformation;
    @NotBlank(message = "身份标识码")
    String identityId;
    @NotBlank(message = "提供方简介")
    String providerDesc;
    //    @NotBlank(message = "法人经办人姓名")
    String operatorName;
    //    @NotBlank(message = "法人经办人电话")
    String operatorTelephone;
    //    @NotBlank(message = "法人经办人身份证")
    String operatorIdCard;
    //    @NotBlank(message = "授权委托书")
    String commission;
    //    @NotBlank(message = "数据样例 文件地址，通过文件上传接口获取。")
    String dataSample;
    @NotBlank(message = "合法合规声明 文件地址，通过文件上传接口获取。")
    String complianceAndLegalStatement;
    @NotBlank(message = "数据来源声明 文件地址，通过文件上传接口获取。")
    String dataSourceStatement;
    //    @NotBlank(message = "安全分级分类 文件地址，通过文件上传接口获取。")
    String safeLevel;
    //    @NotBlank(message = "数据质量、产品价值 文件地址，通过文件上传接口获取。")
    String evaluationReport;
    @NotBlank(message = "主体ID")
    String entityId;
    @NotBlank(message = "主体编号")
    String entityCode;
    @NotBlank(message = "平台 ID")
    String platformId;
    /**
     * List<String>
     */
    @NotBlank(message = "资源编码")
    String resourceCodes;
    //    @NotBlank(message = "提供方接入连接器标识")
    String dpe;
}
