package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/8 14:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderResolveDTO {

    OrderApprovalRecord record;

    AssetBeneficiaryRel rel;

    DeliveryScene scene;

    SceneAsset sceneAsset;

}
