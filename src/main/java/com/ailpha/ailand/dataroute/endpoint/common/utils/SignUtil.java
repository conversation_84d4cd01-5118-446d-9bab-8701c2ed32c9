package com.ailpha.ailand.dataroute.endpoint.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.security.MessageDigest;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class SignUtil {

    public static final String KEY = "key";
    public static final String TIMESTAMP = "timestamp";
    public static final String NONCE = "nonce";
    public static final String SIGN = "sign";
    public static final String ACCESS_KEY = "accessKey";

    public static String createSign(Map<String, ?>paramsMap, String secretKey) {
        if (secretKey == null || secretKey.isEmpty()) {
            throw new IllegalArgumentException("secretKey不能为空");
        }

        // 不小心传入sign参数，则需要将sign参数排除在外
        if (paramsMap.containsKey(SIGN)) {
            paramsMap = new TreeMap<>(paramsMap);
            paramsMap.remove(SIGN);
        }

        String paramsStr = joinParamsDictSort(paramsMap);
        String fullStr = paramsStr + "&" + KEY + "=" + secretKey;
        return abstractStr(fullStr);
    }

    /**
     * 使用摘要算法创建签名
     * @param fullStr 待摘要的字符串
     * @return 签名
     */
    public static String abstractStr(String fullStr) {
        log.info("签名内容:{}", fullStr);
        return md5(fullStr);
    }

    // ----------------------- 计算摘要 -----------------------
    /**
     * md5 加密
     * @param str 指定字符串
     * @return 加密后的字符串
     */
    public static String md5(String str) {
        str = (str == null ? "" : str); char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' }; try {
            byte[] btInput = str.getBytes();
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char[] strA = new char[j * 2];
            int k = 0; for (byte byte0 : md) {
                strA[k++] = hexDigits[byte0 >>> 4 & 0xf]; strA[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(strA);
        } catch (Exception e) {
            throw new RuntimeException("MD5 加密错误");
        }
    }
    /**
     * 生成指定长度的随机字符串
     *
     * @param length 字符串的长度
     * @return 一个随机字符串
     */
    public static String getRandomString(int length) {
        String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = ThreadLocalRandom.current().nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }
    /**
     * 将所有参数按照字典顺序连接成一个字符串，形如：a=18b=28c=3
     * @param paramsMap 参数列表
     * @return 拼接出的参数字符串
     */
    public static String joinParamsDictSort(Map<String, ?> paramsMap) {
        // 保证字段按照字典顺序排列
        if( ! (paramsMap instanceof TreeMap) ) {
            paramsMap = new TreeMap<>(paramsMap); }
        // 拼接
        return joinParams(paramsMap); }

    // ----------- 拼接参数
    /**
     * 将所有参数连接成一个字符串(不排序)，形如：b=28a=18c=3
     * @param paramsMap 参数列表
     * @return 拼接出的参数字符串
     */
    public static String joinParams(Map<String, ?> paramsMap) {
        // 按照 k1=v1&k2=v2&k3=v3 排列
        StringBuilder sb = new StringBuilder();
        for (String key : paramsMap.keySet()) {
            Object value = paramsMap.get(key);
            if( !SignUtil.isEmpty(value) ) {
                sb.append(key).append("=").append(value).append("&");
            }
        }
        // 删除最后一位 &
        if(sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        // .
        return sb.toString();
    }

    /**
     * 指定元素是否为 null 或者空字符串
     * @param str 指定元素
     * @return 是否为 null 或者空字符串
     */
    public static boolean isEmpty(Object str) {
        return str == null || "".equals(str);
    }
}
