package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.home.*;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryOderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryOderTableDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderStatisticDTO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderStatisticRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.AssetBeneficiaryRelDTOListReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.AssetBeneficiaryRelDTOReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.OrderRecordsReq;
import com.ailpha.ailand.dataroute.endpoint.third.response.OrderRecordsResp;
import com.dbapp.rest.response.SuccessResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Deprecated
@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.NONE, tokenUrl = "/third/app/token")
public interface HubOrderRemote {

    /**
     * @param assetBeneficiaryRelDTOReq 资产ID、获益人ID
     * @return 资产获益人关联订单信息
     */
    @POST("/api/order-manager/asset-beneficiary-order")
    SuccessResponse<AssetBeneficiaryOderDTO> selectAssetBeneficiaryOderDTOWhereAssetIdAndBeneficiaryId(@Body AssetBeneficiaryRelDTOReq assetBeneficiaryRelDTOReq);

    /**
     * @param assetBeneficiaryRelDTOListReq 资产ID列表、获益人ID
     * @return 资产获益人关联订单信息
     */
    @POST("/api/order-manager/asset-beneficiary-order-list")
    SuccessResponse<List<AssetBeneficiaryOderDTO>> selectAssetBeneficiaryOderDTOWhereAssetIdInAndBeneficiaryId(@Body AssetBeneficiaryRelDTOListReq assetBeneficiaryRelDTOListReq);

    /**
     * 写订单合同审批记录
     *
     * @param orderApprovalRecords 订单合同审批记录
     * @return rows
     */
    @POST("/api/order-manager/insert-order-approval-records")
    SuccessResponse<Integer> insertIntoOrderApprovalRecords(@Body List<OrderApprovalRecord> orderApprovalRecords);

    /**
     * @param orderRecordsReq 资产ID列表、状态
     * @return 订单合同审批记录-分页
     */
    @POST("/api/order-manager/order-approval-records-page")
    SuccessResponse<OrderRecordsResp> orderApprovalRecordsPage(@Body OrderRecordsReq orderRecordsReq);


    /**
     * 过期订单
     */
    @POST("/api/order-manager/expired-order-page")
    SuccessResponse<OrderRecordsResp> expiredOrderPage(@Body OrderRecordsReq orderRecordsReq);

    /**
     * @param orderId 订单ID
     * @return 订单合同审批记录
     */
    @GET("/api/order-manager/order-approval-record")
    SuccessResponse<OrderApprovalRecord> selectOrderApprovalRecordWhereId(@Query("orderId") String orderId);

    /**
     * @param assetBeneficiaryRelDTOReq 资产ID、获益人ID
     * @return 资产获益人关联表信息
     */
    @POST("/api/order-manager/asset-beneficiary-rel")
    SuccessResponse<AssetBeneficiaryRel> selectAssetBeneficiaryRelWhereAssetIdAndBeneficiaryId(@Body AssetBeneficiaryRelDTOReq assetBeneficiaryRelDTOReq);

    /**
     * 更新订单审批记录、存储/更新资产获益人信息记录
     *
     * @param assetBeneficiaryOderTableDTO 订单审批记录、资产获益人信息记录
     * @return rows
     */
    @POST("/api/order-manager/replace-into-order-approval-asset-beneficiary")
    SuccessResponse<Integer> replaceIntoOrderApprovalAssetBeneficiary(@Body AssetBeneficiaryOderTableDTO assetBeneficiaryOderTableDTO);

    /**
     * 更新订单审批记录
     *
     * @param orderApprovalRecord 订单审批记录
     * @return rows
     */
    @POST("/api/order-manager/update-order-approval-record")
    SuccessResponse<Integer> updateOrderApprovalRecord(@Body OrderApprovalRecord orderApprovalRecord);

    /**
     * @param orderIds 订单ID列表
     * @return 订单合同审批记录列表
     */
    @POST("/api/order-manager/order-approval-records")
    SuccessResponse<List<OrderApprovalRecord>> selectOrderApprovalRecordWhereIdIn(@Body List<String> orderIds);

    /**
     * @param assetBeneficiaryRelDTOListReq 资产ID列表、获益人ID
     * @return 订单合同审批记录列表
     */
    @POST("/api/order-manager/order-approval-records-by-beneficiary")
    SuccessResponse<List<OrderApprovalRecord>> selectOrderApprovalRecordWhereAssetIdInAndBeneficiaryId(@Body AssetBeneficiaryRelDTOListReq assetBeneficiaryRelDTOListReq);

    /**
     * 更新订单审批记录列表
     *
     * @param orderApprovalRecords 订单审批记录列表
     * @return rows
     */
    @POST("/api/order-manager/update-order-approval-records")
    SuccessResponse<Integer> updateOrderApprovalRecords(@Body List<OrderApprovalRecord> orderApprovalRecords);

    /**
     * 首页订单相关指标信息
     */
    @POST("/api/order-manager/order/statistic")
    SuccessResponse<OrderStatisticDTO> statistic(@Body OrderStatisticRequest request);

    /**
     * 埋点：交付记录
     *
     * @param statisticDeliveryRecords 交付记录
     * @return rows
     */
    @POST("/api/order-manager/insert-statistic-delivery-records")
    SuccessResponse<Integer> insertStatisticDeliveryRecords(@Body List<StatisticDeliveryRecord> statisticDeliveryRecords);

    /**
     * 首页-交付记录分页
     *
     * @param statisticDeliveryRecordsReq 资产创建人用户ID
     * @return 交付记录列表
     */
    @POST("/api/order-manager/statistic-delivery-records-page")
    SuccessResponse<StatisticDeliveryRecordsResp> statisticDeliveryRecordsPage(@Body StatisticDeliveryRecordsReq statisticDeliveryRecordsReq);

    /**
     * 统计-资产订单交付
     *
     * @param assetIds 资产ID列表
     * @return 统计列表
     */
    @POST("/api/order-manager/statistic-asset-order-delivery")
    SuccessResponse<Map<String, StatisticAssetOrderDeliveryVO>> statisticAssetOrderDelivery(@Body List<String> assetIds);

    /**
     * 统计-资产订单交付分页
     *
     * @param statisticAssetOrderDeliveryReq 筛选项
     * @return 交付记录列表
     */
    @POST("/api/order-manager/statistic-asset-order-delivery-page")
    SuccessResponse<StatisticDeliveryRecordsResp> statisticAssetOrderDeliveryPage(@Body StatisticAssetOrderDeliveryReq statisticAssetOrderDeliveryReq);
}
