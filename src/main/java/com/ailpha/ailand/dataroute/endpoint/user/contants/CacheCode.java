/**
 * 
 */
package com.ailpha.ailand.dataroute.endpoint.user.contants;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2022-02-12
 * @description
 */
@Getter
@Builder
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CacheCode {
	String codeId;
	String code;
	long expirInMinutes;
	long createTime;
}
