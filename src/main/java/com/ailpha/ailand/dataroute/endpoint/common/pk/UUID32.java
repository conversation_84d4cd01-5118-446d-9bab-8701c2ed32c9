package com.ailpha.ailand.dataroute.endpoint.common.pk;

import org.hibernate.annotations.IdGeneratorType;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;

/**
 * @author: yuwenping
 * @date: 2025/3/11 21:09
 * @Description:
 */
@IdGeneratorType( UUID32Generator.class)
@Retention( RetentionPolicy.RUNTIME)
@Target({ FIELD, METHOD})
public @interface UUID32 {

}
