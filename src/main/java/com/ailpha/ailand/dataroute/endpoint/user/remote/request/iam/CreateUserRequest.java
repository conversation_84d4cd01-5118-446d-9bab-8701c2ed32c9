package com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam;

import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CreateUserRequest {
    @JsonProperty("uuid")
    String userId;
    @JsonProperty("userName")
    String username;
    @JsonProperty("accountNumber")
    String account;
    String mobile;
    String mail;
    String password;
    String bimRequestId;
    public CreateUserRequest() {
        this.bimRequestId = UuidUtils.uuid32();
    }
}
