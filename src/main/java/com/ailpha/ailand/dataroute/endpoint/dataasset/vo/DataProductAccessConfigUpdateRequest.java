package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.APIQueryWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品接入配置更新信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductAccessConfigUpdateRequest {
    @Schema(description = "数据标识")
    String id;

    @Schema(description = "BROWSER_UPLOAD 浏览器页面上传 SERVER_FILE_PATH 服务器文件路径")
    FileSourceEnum fileSource;
    @Schema(description = "数据资产文件(文件/图像/模型)临时id，页面上传接口返回的或者服务器路径校验接口返回的")
    String fileId;

    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;

    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
}
