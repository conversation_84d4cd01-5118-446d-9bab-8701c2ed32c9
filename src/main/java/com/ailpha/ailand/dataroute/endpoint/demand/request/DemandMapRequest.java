package com.ailpha.ailand.dataroute.endpoint.demand.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DemandMapRequest extends Page {
    @Schema(hidden = true)
    String userId;

    @Schema(hidden = true)
    String routerId;
    @Schema(description = "需求标题")
    private String title;

    @Schema(description = "数据类型")
    private String dataType;

    @Schema(description = "状态", example = "review_pass:已发布 review_refuse:拒绝 negotiation:磋商中 done:已成交 expired:过期 closed:已关闭")
    private String status;

    @Schema(description = "期望交付方式", example = "API ")
    private String expectedDeliveryMethod;
}
