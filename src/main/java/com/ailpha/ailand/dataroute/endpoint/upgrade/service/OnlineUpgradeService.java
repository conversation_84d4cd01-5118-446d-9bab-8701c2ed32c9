package com.ailpha.ailand.dataroute.endpoint.upgrade.service;

import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeStatus;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UploadMethod;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DigestUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ZipUtil;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.PackageInfo;
import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.QPackageInfo;
import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.QUpgradeTask;
import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.UpgradeTask;
import com.ailpha.ailand.dataroute.endpoint.upgrade.mapper.OnlineUpgradeMapper;
import com.ailpha.ailand.dataroute.endpoint.upgrade.repository.PackageInfoRepository;
import com.ailpha.ailand.dataroute.endpoint.upgrade.repository.UpgradeTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.UpgradeTaskVO;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.VersionInfo;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.PackageListReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.PackageUploadReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.UpgradeReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.UpgradeTaskListReq;
import com.ailpha.ailand.utils.safe.RSAUtil;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineUpgradeService {

    public static Map<String, String> TEMP_FILE_PATH = new ConcurrentHashMap<>();
    public static VersionInfo VERSION_INFO;
    private static final String INIT_VERSION = "V001";
    private static boolean readLog = false;
    private static final QPackageInfo qPackageInfo = QPackageInfo.packageInfo;
    private static final QUpgradeTask qUpgradeTask = QUpgradeTask.upgradeTask;

    private final KeyPair keyPair;
    private final FilesStorageServiceImpl filesStorageService;
    private final OnlineUpgradeMapper onlineUpgradeMapper;
    private final PackageInfoRepository packageInfoRepository;
    private final UpgradeTaskRepository upgradeTaskRepository;

    @Value("${upgrade.md5.skip-check}")
    private boolean upgradeMD5SkipCheck;

    @PostConstruct
    public void init() {
        try {
            saveSH();

            VERSION_INFO = readVersionInfo();

            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            updateLastUpgradeTaskLog();

            Path tempFilePath = Paths.get(Constants.DATA_PATH, Constants.PACKAGE_PATH, ".tmp");
            filesStorageService.deleteTempFilePath(tempFilePath);
        } catch (Exception e) {
            log.error("init failure!! error:", e);
        }
    }

    /**
     * 私钥加密MD5
     */
    public String rsaPrivateEncryptMd5(String md5) throws Exception {
        return RSAUtil.encryptByPrivateKey(md5, (RSAPrivateKey) keyPair.getPrivate());
    }

    /**
     * 包管理-上传
     */
    public String packageUpload(MultipartFile multipartFile) throws Exception {
        String originalFilename = multipartFile.getOriginalFilename();
        Assert.isTrue(originalFilename != null, "升级包损坏，请重新上传");
        int lastDotIndex = originalFilename.lastIndexOf('.');
        Assert.isTrue(lastDotIndex != -1, "升级包无格式，请重新上传");
        String fileSuffix = originalFilename.substring(lastDotIndex).toLowerCase();
        Assert.isTrue(".zip".equals(fileSuffix), "升级包非zip格式，请重新上传");
        Assert.isTrue(!originalFilename.contains(".."), "原始文件名不得跨目录");
        Assert.isTrue(!originalFilename.contains(File.separator), "原始文件名不得包含文件夹分割符");

        String tempFileId = UUID.randomUUID().toString();
        Path tempFilePath = Paths.get(Constants.DATA_PATH, Constants.PACKAGE_PATH, ".tmp", tempFileId, originalFilename);
        filesStorageService.saveToAbsolutePath(multipartFile, tempFilePath.toString());
        TEMP_FILE_PATH.put(tempFileId, tempFilePath.toString());
        return tempFileId;
    }

    /**
     * 包管理-创建
     */
    public String packageCreate(PackageUploadReq packageUploadReq) throws Exception {
        Assert.isTrue(!INIT_VERSION.equals(packageUploadReq.getVersion()), String.format("%s已作为初始版本号,请重新填写版本号", INIT_VERSION));
        List<PackageInfo> packageInfos = packageInfoRepository.findAllByVersion(packageUploadReq.getVersion());
        Assert.isTrue(ObjectUtils.isEmpty(packageInfos), "版本号已存在,请重新填写");

        File packageFile = null;
        if (UploadMethod.WEB.equals(packageUploadReq.getUploadMethod())) {
            Assert.isTrue(!ObjectUtils.isEmpty(packageUploadReq.getFileId()), "升级包文件ID不能为空");
            String tempFilePath = TEMP_FILE_PATH.get(packageUploadReq.getFileId());
            Assert.isTrue(!ObjectUtils.isEmpty(tempFilePath), "临时文件已被清理，请重新上传升级包");
            packageFile = new File(tempFilePath);
            Assert.isTrue(Files.exists(packageFile.toPath()), "临时文件已被清理，请重新上传升级包");
        } else if (UploadMethod.SERVER.equals(packageUploadReq.getUploadMethod())) {
            String serverFilePath = packageUploadReq.getServerFilePath();
            Assert.isTrue(!ObjectUtils.isEmpty(serverFilePath), "服务器文件路径不能为空");
            packageFile = new File(serverFilePath);
            Assert.isTrue(Files.exists(packageFile.toPath()), "服务文件已被清理，请重新上传升级包");
            int lastDotIndex = packageFile.getName().lastIndexOf('.');
            Assert.isTrue(lastDotIndex != -1, "升级包无格式，请重新上传升级包");
            String fileSuffix = packageFile.getName().substring(lastDotIndex).toLowerCase();
            Assert.isTrue(".zip".equals(fileSuffix), "升级包非zip格式，请重新上传升级包");
        }

        String md5 = DigestUtil.getHash(DigestUtil.MD5, packageFile);
        if (!upgradeMD5SkipCheck) {
            String decryptMd5;
            try {
                decryptMd5 = RSAUtil.decryptByPublicKey(packageUploadReq.getMd5(), (RSAPublicKey) keyPair.getPublic());
            } catch (Exception e) {
                throw new IllegalArgumentException("MD5解密失败，请正确填写私钥加密的MD5");
            }
            Assert.isTrue(md5.equals(decryptMd5), "MD5不匹配，请重新填写MD5或重新上传升级包");
            if (UploadMethod.WEB.equals(packageUploadReq.getUploadMethod())) {
                TEMP_FILE_PATH.remove(packageUploadReq.getFileId());
            }
        }

        long timestamp = System.currentTimeMillis();
        String id = String.format("%s_%s", packageUploadReq.getVersion(), timestamp);
        assert packageFile != null;
        Path filePath = Paths.get(Constants.DATA_PATH, Constants.PACKAGE_PATH, id, packageFile.getName());
        filesStorageService.moveToAbsolutePath(packageFile.getAbsolutePath(), filePath.toString());

        PackageInfo packageInfo = PackageInfo.builder().id(id).name(packageUploadReq.getName()).module(UpgradeModule.DATA_ROUTE)
                .version(packageUploadReq.getVersion()).description(packageUploadReq.getDescription()).md5(md5).source(UpgradeSource.DATA_ROUTE)
                .filePath(filePath.toString()).uploadTime(new Date()).createTime(new Date()).updateTime(new Date()).build();
        packageInfoRepository.saveAndFlush(packageInfo);
        return id;
    }

    /**
     * 包管理-删除
     */
    public void packageDelete(String id) {
        Optional<PackageInfo> packageInfoOptional = packageInfoRepository.findById(id);
        Assert.isTrue(packageInfoOptional.isPresent(), "升级包已被删除，请刷新页面");
        PackageInfo packageInfo = packageInfoOptional.get();
        filesStorageService.deleteTempFilePath(new File(packageInfo.getFilePath()).toPath().getParent());
        packageInfoRepository.deleteById(id);
    }

    /**
     * 包管理-列表
     */
    public SuccessResponse<List<PackageInfo>> packageList(PackageListReq packageListReq) {
        Predicate predicate = buildPackageInfoPredicate(packageListReq.getName(), packageListReq.getModule(), packageListReq.getVersion(), packageListReq.getStartTime(), packageListReq.getEndTime());
        Sort sort = Sort.by(Sort.Direction.DESC, "uploadTime");
        Pageable pageable = PageRequest.of((int) packageListReq.getNum() - 1, (int) packageListReq.getSize(), sort);
        Page<PackageInfo> packageInfoPage = packageInfoRepository.findAll(predicate, pageable);
        return SuccessResponse.success(packageInfoPage.getContent()).total(packageInfoPage.getTotalElements())
                .page(com.dbapp.rest.request.Page.of(packageListReq.getNum(), packageListReq.getSize())).build();
    }

    /**
     * 节点升级
     */
    public String upgrade(UpgradeReq upgradeReq) throws Exception {
        Optional<PackageInfo> packageInfoOptional = packageInfoRepository.findById(upgradeReq.getPackageId());
        Assert.isTrue(packageInfoOptional.isPresent(), "升级包已被删除，请刷新页面");
        PackageInfo packageInfo = packageInfoOptional.get();

        Assert.isTrue(!ObjectUtils.isEmpty(packageInfo) && !ObjectUtils.isEmpty(packageInfo.getFilePath()), "选择的升级包已被清理，请重新选择升级包");
        File pacakgeFile = new File(packageInfo.getFilePath());
        Assert.isTrue(Files.exists(pacakgeFile.toPath()), "选择的升级包已被清理，请重新选择升级包");

        UpgradeTask upgradeTaskLast = upgradeTaskRepository.findTopByOrderByCreateTimeDesc();
        if (!ObjectUtils.isEmpty(upgradeTaskLast)
                && (UpgradeStatus.WAIT.equals(upgradeTaskLast.getStatus()) || UpgradeStatus.UPGRADING.equals(upgradeTaskLast.getStatus()))) {
            upgradeTaskLast.setStatus(UpgradeStatus.INVALID);
            upgradeTaskRepository.saveAndFlush(upgradeTaskLast);
        }

        long timestamp = System.currentTimeMillis();
        String id = String.format("%s_%s", packageInfo.getVersion(), timestamp);
        UpgradeTask upgradeTask = UpgradeTask.builder().id(id).module(packageInfo.getModule()).beforePackageId(VERSION_INFO.getAfterPackageId()).beforeVersion(VERSION_INFO.getAfterVersion())
                .afterPackageId(packageInfo.getId()).afterPackageName(packageInfo.getName()).afterVersion(packageInfo.getVersion())
                .md5(packageInfo.getMd5()).status(UpgradeStatus.WAIT).upgradeTime(!Boolean.TRUE.equals(upgradeReq.getImmediately()) ? upgradeReq.getUpgradeTime() : null)
                .source(UpgradeSource.DATA_ROUTE).immediately(upgradeReq.getImmediately()).log("").createTime(new Date()).upgradeTime(new Date()).build();
        if (!Boolean.TRUE.equals(upgradeReq.getImmediately())) {
            upgradeTask.setUpgradeTime(upgradeReq.getUpgradeTime());
        }
        upgradeTaskRepository.saveAndFlush(upgradeTask);
        return id;
    }

    /**
     * 节点升级-列表
     */
    public SuccessResponse<List<UpgradeTaskVO>> upgradeList(UpgradeTaskListReq upgradeTaskListReq) {
        List<UpgradeTaskVO> upgradeTaskVOS = new ArrayList<>();
        Predicate predicate = buildUpgradeTaskPredicate(upgradeTaskListReq.getSource(), upgradeTaskListReq.getStatus(), upgradeTaskListReq.getVersion(), upgradeTaskListReq.getStartTime(), upgradeTaskListReq.getEndTime());
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of((int) upgradeTaskListReq.getNum() - 1, (int) upgradeTaskListReq.getSize(), sort);
        Page<UpgradeTask> upgradeTaskPage = upgradeTaskRepository.findAll(predicate, pageable);

        List<UpgradeTask> upgradeTasks = upgradeTaskPage.getContent();
        if (!ObjectUtils.isEmpty(upgradeTasks)) {
            Set<String> afterPackageIds = upgradeTasks.stream().map(UpgradeTask::getAfterPackageId).collect(Collectors.toSet());
            List<PackageInfo> packageInfos = packageInfoRepository.findAllById(afterPackageIds);
            Map<String, String> packageNameMap = new HashMap<>();
            if (!ObjectUtils.isEmpty(packageInfos)) {
                packageNameMap = packageInfos.stream().collect(Collectors.toMap(PackageInfo::getId, PackageInfo::getName, (s1, s2) -> s2));
            }
            for (UpgradeTask upgradeTask : upgradeTasks) {
                UpgradeTaskVO upgradeTaskVO = onlineUpgradeMapper.upgradeTaskToUpgradeTaskVO(upgradeTask);
                if (!ObjectUtils.isEmpty(packageNameMap) && !ObjectUtils.isEmpty(packageNameMap.get(upgradeTaskVO.getAfterPackageId()))) {
                    upgradeTaskVO.setAfterPackageName(packageNameMap.get(upgradeTaskVO.getAfterPackageId()));
                }
                upgradeTaskVOS.add(upgradeTaskVO);
            }
        }
        return SuccessResponse.success(upgradeTaskVOS).total(upgradeTaskPage.getTotalElements()).page(com.dbapp.rest.request.Page.of(upgradeTaskListReq.getNum(), upgradeTaskListReq.getSize())).build();
    }

    /**
     * 节点升级-日志
     */
    public String upgradeLog(String id) {
        Optional<UpgradeTask> upgradeTaskOptional = upgradeTaskRepository.findById(id);
        Assert.isTrue(upgradeTaskOptional.isPresent(), "升级任务已被删除，请刷新页面");
        UpgradeTask upgradeTask = upgradeTaskOptional.get();
        return upgradeTask.getLog();
    }

    private Predicate buildPackageInfoPredicate(String name, UpgradeModule module, String version, Date startTime, Date endTime) {
        BooleanBuilder builder = new BooleanBuilder();
        if (name != null && !name.isEmpty()) {
            builder.and(qPackageInfo.name.like("%" + name + "%"));
        }
        if (module != null) {
            builder.and(qPackageInfo.module.eq(module));
        }
        if (version != null && !version.isEmpty()) {
            builder.and(qPackageInfo.version.like("%" + version + "%"));
        }
        if (startTime != null && endTime != null) {
            builder.and(qPackageInfo.createTime.between(startTime, endTime));
        }
        return builder;
    }

    private Predicate buildUpgradeTaskPredicate(UpgradeSource source, UpgradeStatus status, String afterVersion, Date startTime, Date endTime) {
        BooleanBuilder builder = new BooleanBuilder();
        if (source != null) {
            builder.and(qUpgradeTask.source.eq(source));
        }
        if (status != null) {
            builder.and(qUpgradeTask.status.eq(status));
        }
        if (afterVersion != null && !afterVersion.isEmpty()) {
            builder.and(qUpgradeTask.afterVersion.like("%" + afterVersion + "%"));
        }
        if (startTime != null && endTime != null) {
            builder.and(qUpgradeTask.upgradeTime.between(startTime, endTime));
        }
        return builder;
    }

    public void saveSH() {
        String userDir = System.getProperty("user.dir");
        try {
            RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
            String publicKey = Base64.getEncoder().encodeToString(rsaPublicKey.getEncoded());
            Path rsaPublicKeyPath = Paths.get(Constants.DATA_PATH, Constants.RSA_PUBLIC_KEY);
            filesStorageService.createAbsolutePathFileWithContent(Collections.singletonList(publicKey), rsaPublicKeyPath.toString());

            RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
            String privateKey = Base64.getEncoder().encodeToString(rsaPrivateKey.getEncoded());
            Path rsaPrivateKeyPath = Paths.get(Constants.DATA_PATH, Constants.RSA_PRIVATE_KEY);
            filesStorageService.createAbsolutePathFileWithContent(Collections.singletonList(privateKey), rsaPrivateKeyPath.toString());

            Path rsaPrivateEncryptShPath = Paths.get(Constants.DATA_PATH, Constants.RSA_PRIVATE_ENCRYPT_SH);
            filesStorageService.copyToAbsolutePath(Paths.get(userDir, Constants.RSA_PRIVATE_ENCRYPT_SH).toString(), rsaPrivateEncryptShPath.toString());

            Path rsaPublicDecryptShPath = Paths.get(Constants.DATA_PATH, Constants.RSA_PUBLIC_DECRYPT_SH);
            filesStorageService.copyToAbsolutePath(Paths.get(userDir, Constants.RSA_PUBLIC_DECRYPT_SH).toString(), rsaPublicDecryptShPath.toString());

            Path upgradeShPath = Paths.get(Constants.DATA_PATH, Constants.UPGRADE_SH);
            filesStorageService.copyToAbsolutePath(Paths.get(userDir, Constants.UPGRADE_SH).toString(), upgradeShPath.toString());

            Path upgradeReadmeMdPath = Paths.get(Constants.DATA_PATH, Constants.UPGRADE_README_MD);
            filesStorageService.copyToAbsolutePath(Paths.get(userDir, Constants.UPGRADE_README_MD).toString(), upgradeReadmeMdPath.toString());
        } catch (Exception e) {
            log.error("saveSH failure!! error:", e);
        }
    }

    public VersionInfo readVersionInfo() {
        String userDir = System.getProperty("user.dir");
        VersionInfo versionInfo = VersionInfo.builder().beforeVersion("-").afterVersion(INIT_VERSION).afterPath(userDir).build();
        try {
            Path versionInfoPath = Paths.get(userDir, Constants.VERSION_INFO);
            if (Files.exists(versionInfoPath)) {
                String versionInfoJson = filesStorageService.readContent(versionInfoPath);
                versionInfo = JacksonUtils.json2pojo(versionInfoJson, VersionInfo.class);
                versionInfo.setAfterPath(userDir);
            }
        } catch (Exception e) {
            log.error("readVersionInfo failure!! error:", e);
        }
        return versionInfo;
    }

    public void updateLastUpgradeTaskLog() {
        VersionInfo versionInfo = readVersionInfo();
        if (!ObjectUtils.isEmpty(versionInfo) && !ObjectUtils.isEmpty(versionInfo.getId())) {
            UpgradeTask upgradeTask = upgradeTaskRepository.getReferenceById(versionInfo.getId());
            if (!ObjectUtils.isEmpty(upgradeTask)) {
                upgradeTask.setStatus(versionInfo.getStatus());
                upgradeTask.setLog(versionInfo.getLog());
            } else {
                upgradeTask = onlineUpgradeMapper.versionInfoToUpgradeTask(versionInfo);
            }
            upgradeTaskRepository.saveAndFlush(upgradeTask);
        }
    }

    /**
     * 节点升级
     */
    @Scheduled(initialDelay = 30, fixedDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void upgrade() {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        if (!readLog) {
            VERSION_INFO = readVersionInfo();
            updateLastUpgradeTaskLog();
            readLog = true;
        }
        UpgradeTask upgradeTask = upgradeTaskRepository.findTopByOrderByCreateTimeDesc();
        if (ObjectUtils.isEmpty(upgradeTask)) {
            return;
        }
        if (!UpgradeStatus.WAIT.equals(upgradeTask.getStatus())) {
            return;
        }
        if (!Boolean.TRUE.equals(upgradeTask.getImmediately()) && !ObjectUtils.isEmpty(upgradeTask.getUpgradeTime())
                && upgradeTask.getUpgradeTime().after(new Date())) {
            return;
        }

        long timestamp = System.currentTimeMillis();
        upgradeTask.setStatus(UpgradeStatus.UPGRADING);
        upgradeTask.setUpgradeTime(ObjectUtils.isEmpty(upgradeTask.getUpgradeTime()) ? new Date() : upgradeTask.getUpgradeTime());
        StringBuilder logBuilder = new StringBuilder(!ObjectUtils.isEmpty(upgradeTask.getLog()) ? upgradeTask.getLog() : "");

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String userDir = System.getProperty("user.dir");
            // TODO 1.检查升级包是否存在
            log.info("[{}] 1. 检查升级包 [{}] 是否存在", upgradeTask.getId(), upgradeTask.getAfterVersion());
            PackageInfo packageInfo = packageInfoRepository.getReferenceById(upgradeTask.getAfterPackageId());
            upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                    .append("1. 检查 [").append(upgradeTask.getAfterVersion()).append("] 升级包是否存在").append(System.lineSeparator()).toString());

            if (ObjectUtils.isEmpty(packageInfo) || !Files.exists(new File(packageInfo.getFilePath()).toPath())) {
                log.warn("[{}] 升级包 [{}] 不存在，无法升级", upgradeTask.getId(), packageInfo.getVersion());
                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" WARN ")
                        .append(" [").append(packageInfo.getVersion()).append("] 升级包不存在，无法升级").append(System.lineSeparator()).toString());

                upgradeTask.setStatus(UpgradeStatus.FAILURE);
            } else {
                // TODO 2.拉取升级包到升级包工作目录
                log.info("[{}] 2. 拉取升级包 [{}] 到升级包工作目录", upgradeTask.getId(), packageInfo.getVersion());
                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                        .append("2. 拉取升级包 [").append(packageInfo.getVersion()).append("] 到升级包工作目录").append(System.lineSeparator()).toString());

                Path upgradePackageParentPath = Paths.get(Constants.DATA_PATH, Constants.UPGRADE_PACKAGE);
                filesStorageService.deleteTempFilePath(upgradePackageParentPath);

                File pacakgeFile = new File(packageInfo.getFilePath());
                Path upgradePackagePath = Paths.get(upgradePackageParentPath.toString(), pacakgeFile.getName());
                filesStorageService.copyToAbsolutePath(pacakgeFile.getAbsolutePath(), upgradePackagePath.toString());
                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                        .append("->>> pull success!! ---> [").append(upgradePackagePath).append("]").append(System.lineSeparator()).toString());

                // TODO 3.解压升级包
                log.info("[{}] 3. 解压升级包 [{}]", upgradeTask.getId(), packageInfo.getVersion());
                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                        .append("3. 解压升级包 [").append(packageInfo.getVersion()).append("]").append(System.lineSeparator()).toString());

                ZipUtil.decompress(upgradePackagePath.toString(), upgradePackageParentPath.toString());
                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                        .append("->>> decompress success!! --文件夹路径-> [").append(upgradePackageParentPath).append("]").append(System.lineSeparator()).toString());

                // TODO 4.升级前后包版本等信息写入版本文件
                log.info("[{}] 4. 升级前后包版本等信息写入版本文件 [{}] -> [{}]", upgradeTask.getId(), VERSION_INFO.getAfterVersion(), packageInfo.getVersion());
                Path upgradePackageVersionPath = Paths.get(upgradePackageParentPath.toString(), Constants.VERSION_INFO);

                upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                        .append("4. 升级前后包版本等信息写入版本文件 [").append(VERSION_INFO.getAfterVersion())
                        .append("] -> [").append(packageInfo.getVersion()).append("]").append(" --文件-> [").append(upgradePackageVersionPath).append("]").append(System.lineSeparator()).toString());
                upgradeTaskRepository.saveAndFlush(upgradeTask);

                VersionInfo versionInfo = VersionInfo.builder().id(upgradeTask.getId()).module(upgradeTask.getModule()).beforePackageId(VERSION_INFO.getAfterPackageId())
                        .beforeVersion(VERSION_INFO.getAfterVersion()).beforePath(userDir).afterPackageId(packageInfo.getId()).afterPackageName(packageInfo.getName())
                        .afterVersion(packageInfo.getVersion()).md5(packageInfo.getMd5()).source(upgradeTask.getSource()).immediately(upgradeTask.getImmediately())
                        .upgradeTime(simpleDateFormat.format(upgradeTask.getUpgradeTime())).status(upgradeTask.getStatus()).log(upgradeTask.getLog()).build();
                filesStorageService.createAbsolutePathFileWithContent(Collections.singletonList(JacksonUtils.obj2json(versionInfo)), upgradePackageVersionPath.toString());

                // TODO 5.执行升级脚本
                if (System.getProperty("os.name").toLowerCase().contains("windows")) {
                    log.warn("[{}] windows系统跳过", upgradeTask.getId());
                    throw new IllegalArgumentException("windows系统暂不支持在线升级");
                } else {
                    Path upgradeScriptPath = Paths.get(upgradePackageParentPath.toString(), Constants.UPGRADE_SH);
                    if (!Files.exists(upgradeScriptPath)) {
                        log.warn("[{}] 5.1 升级包中未检测到升级脚本 [{}]，尝试使用已部署的升级脚本", upgradeTask.getId(), upgradeScriptPath);
                        upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" WARN ")
                                .append("5.1 升级包中未检测到升级脚本 [").append(upgradeScriptPath).append("]，尝试使用已部署的升级脚本").append(System.lineSeparator()).toString());
                        Path deployedUpgradeScriptPath = Paths.get(userDir, Constants.UPGRADE_SH);
                        Assert.isTrue(Files.exists(deployedUpgradeScriptPath), String.format("升级脚本[%s]不存在，请在升级包中加入升级脚本", deployedUpgradeScriptPath));

                        filesStorageService.copyToAbsolutePath(deployedUpgradeScriptPath.toString(), upgradeScriptPath.toString());
                        upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                                .append("->>> pull success!! [").append(deployedUpgradeScriptPath).append("] ---> [").append(upgradeScriptPath).append("]").append(System.lineSeparator()).toString());
                    } else {
                        log.warn("[{}] 5.1 升级包中检测到升级脚本 [{}]，将使用升级包中的升级脚本", upgradeTask.getId(), upgradeScriptPath);
                        upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                                .append("5.1 升级包中检测到升级脚本 [").append(upgradeScriptPath).append("]，将使用升级包中的升级脚本").append(System.lineSeparator()).toString());
                    }

                    log.info("[{}] 5.2 赋权升级脚本  -> [chmod +x {}]", upgradeTask.getId(), upgradeScriptPath);
                    upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                            .append("5.2 赋权升级脚本 -> [chmod +x ").append(upgradeScriptPath).append("]").append(System.lineSeparator()).toString());

                    ProcessBuilder processBuilder = new ProcessBuilder("chmod", "+x", upgradeScriptPath.toString());
                    processBuilder.start();
                    processBuilder = new ProcessBuilder("sed", "-i", "'s/\\r$//'", upgradeScriptPath.toString());
                    processBuilder.start();

                    log.info("[{}] 5.3 执行升级脚本 -> [/bin/sh {}]", upgradeTask.getId(), upgradeScriptPath);
                    upgradeTask.setLog(logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" INFO ")
                            .append("5.3 执行升级脚本 -> [/bin/sh ").append(upgradeScriptPath).append("]").toString());
                    processBuilder = new ProcessBuilder("/bin/sh", upgradeScriptPath.toString());
                    versionInfo.setLog(upgradeTask.getLog());
                    filesStorageService.createAbsolutePathFileWithContent(Collections.singletonList(JacksonUtils.obj2json(versionInfo)), upgradePackageVersionPath.toString());
                    upgradeTaskRepository.saveAndFlush(upgradeTask);
                    processBuilder.start();

                    boolean readLog = true;
                    long costTime = 2 * 60 * 60 * 1000L;
                    while (readLog) {
                        TimeUnit.SECONDS.sleep(10);
                        try {
                            String versionInfoJson = filesStorageService.readContent(upgradePackageVersionPath);
                            versionInfo = JacksonUtils.json2pojo(versionInfoJson, VersionInfo.class);
                            Assert.isTrue(!ObjectUtils.isEmpty(versionInfo), "versionInfo is null");
                        } catch (Exception e) {
                            // 日志文件更新中会读取异常
                            continue;
                        }

                        Optional<UpgradeTask> upgradeTaskOptional = upgradeTaskRepository.findById(upgradeTask.getId());
                        if (upgradeTaskOptional.isPresent()) {
                            upgradeTask = upgradeTaskOptional.get();
                            if (UpgradeStatus.INVALID.equals(upgradeTask.getStatus()) || UpgradeStatus.FAILURE.equals(upgradeTask.getStatus()) || UpgradeStatus.SUCCESS.equals(upgradeTask.getStatus())) {
                                readLog = false;
                            } else {
                                upgradeTask.setStatus(versionInfo.getStatus());
                                upgradeTask.setLog(versionInfo.getLog());
                                if (UpgradeStatus.FAILURE.equals(versionInfo.getStatus()) || UpgradeStatus.SUCCESS.equals(versionInfo.getStatus())) {
                                    readLog = false;
                                }
                                if (UpgradeStatus.SUCCESS.equals(versionInfo.getStatus())) {
                                    VERSION_INFO = versionInfo;
                                }
                                if (System.currentTimeMillis() - timestamp > costTime) {
                                    log.warn("[{}] 升级耗时超过2小时，请联系运维管理员手动升级", upgradeTask.getId());
                                    StringBuilder upgradeHistoryLogBuilder = new StringBuilder(!ObjectUtils.isEmpty(upgradeTask.getLog()) ? upgradeTask.getLog() : "");
                                    upgradeTask.setLog(upgradeHistoryLogBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" WARN 升级耗时超过2小时，请联系运维管理员手动升级").toString());
                                    readLog = false;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[{}] 发生异常 error:" + e, upgradeTask.getId());
            StringBuilder logAppend = logBuilder.append(simpleDateFormat.format(System.currentTimeMillis())).append(" ERROR ")
                    .append("发生异常 error:").append(System.lineSeparator())
                    .append(e.getClass()).append(System.lineSeparator()).append(e.getLocalizedMessage()).append(System.lineSeparator());
            StackTraceElement[] arr = e.getStackTrace();
            for (StackTraceElement stackTraceElement : arr) {
                logAppend.append(stackTraceElement.toString()).append(System.lineSeparator());
            }
            upgradeTask.setLog(logAppend.toString());
            upgradeTask.setStatus(UpgradeStatus.FAILURE);
        } finally {
            upgradeTaskRepository.saveAndFlush(upgradeTask);
        }
    }
}
