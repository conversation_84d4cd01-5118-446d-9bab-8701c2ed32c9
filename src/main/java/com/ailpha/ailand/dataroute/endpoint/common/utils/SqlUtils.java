package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.ailpha.ailand.dataroute.endpoint.common.utils.sql.SubStringVariableUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.sql.VariableUtils;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLAggregateExpr;
import com.alibaba.druid.sql.ast.statement.SQLSelect;
import com.alibaba.druid.sql.ast.statement.SQLSelectItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.dialect.mysql.parser.MySqlStatementParser;
import com.alibaba.druid.sql.visitor.SchemaStatVisitor;
import com.alibaba.druid.stat.TableStat;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.base.Preconditions;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Classname SqlUtils
 * @Date 2020/9/16 下午2:21
 * @Created by fei.liu
 */
public class SqlUtils {

    private static final int ONE_DAY = 24 * 60 * 60 * 1000;

    private static final int ONE_HOUR = 60 * 60 * 1000;

    //    private static String CN_PATTERN_STRING = "[\\u4E00-\\u9FA5]";
//    private static String CN_PATTERN_REPLACE = "a";
    public static String DATE_PATTERN_STRING = "<%=([^>]*)%>";

    public static Pattern DATEPATTERN = Pattern.compile(DATE_PATTERN_STRING);

    public static String getCreateTable(String sql) {

        List<SQLStatement> sqlStatements = SQLUtils.parseStatements(sql, JdbcConstants.HIVE);
        for (SQLStatement sqlStatement : sqlStatements) {
            SchemaStatVisitor visitor = new SchemaStatVisitor();
            sqlStatement.accept(visitor);

            for (Map.Entry<TableStat.Name, TableStat> tableEntry : visitor.getTables().entrySet()) {
                if (tableEntry.getValue().getCreateCount() > 0) {
                    return tableEntry.getKey().getName();
                }
            }
        }
        return null;
    }

    public static boolean checkSql(String sql) {
        try {
            SQLUtils.parseStatements(sql, JdbcConstants.HIVE);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static String replaceDateExpression(String sql, List<Long> dataDates) throws IllegalArgumentException {
        return replaceDateExpression(System.currentTimeMillis(), sql, dataDates);
    }

    /**
     * 替换sql中的日期参数
     */
    public static String replaceDateExpression(Long time, String sql, List<Long> dataDates)
            throws IllegalArgumentException {

        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        Matcher matcher = DATEPATTERN.matcher(sql);
        while (matcher.find()) {
            String matcherItem = matcher.group();
            String expression = matcherItem.substring(3, matcherItem.length() - 2);

            // 找到sql中日期的日期类型
            DateEnums dateEnums = null;
            int maxCommonLength = 0;
            for (DateEnums dateEnum : DateEnums.values()) {
                if (expression.contains(dateEnum.getType())) {
                    if (dateEnum.getType().length() > maxCommonLength) {
                        dateEnums = dateEnum;
                        maxCommonLength = dateEnum.getType().length();
                    }
                }
            }

            Preconditions.checkNotNull(dateEnums, "date expression is not format");

            // 获得一天前的日期
            long newTime = 0;
            long date = 0;

            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {
                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMMddHH"), "yyyyMMddHH").getTime();
                date = new DateTime(newTime).hourOfDay().addToCopy(-1).toDate().getTime();
            } else if (dateEnums.equals(DateEnums.LOG_WEEK)) {
                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMMdd"), "yyyyMMdd").getTime();
                date = new DateTime(newTime).minusWeeks(1).dayOfWeek().withMinimumValue().toDate().getTime();
            } else if (dateEnums.equals(DateEnums.LOG_MONTH)) {
                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMM"), "yyyyMM").getTime();
                date = new DateTime(newTime).minusMonths(1).dayOfMonth().withMinimumValue().toDate().getTime();
            } else {
                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMMdd"), "yyyyMMdd").getTime();
                date = new DateTime(newTime).dayOfMonth().addToCopy(-1).toDate().getTime();
            }

            // 获得上周第一天的日期
            if (matcherItem.contains("first_" + dateEnums.getType() + "_of_week")) {
                date = new DateTime(newTime).minusWeeks(1).dayOfWeek().withMinimumValue().toDate().getTime();
            }

            // 获得上周最后一天的日期
            if (matcherItem.contains("last_" + dateEnums.getType() + "_of_week")) {
                date = new DateTime(newTime).minusWeeks(1).dayOfWeek().withMaximumValue().toDate().getTime();
            }

            // 获得上月第一天的日期
            if (matcherItem.contains("first_" + dateEnums.getType() + "_of_month")) {
                date = new DateTime(newTime).minusMonths(1).dayOfMonth().withMinimumValue().toDate().getTime();
            }

            // 获得上月最后一天的日期
            if (matcherItem.contains("last_" + dateEnums.getType() + "_of_month")) {
                date = new DateTime(newTime).minusMonths(1).dayOfMonth().withMaximumValue().toDate().getTime();
            }

            try {
                if ((dateEnums.equals(DateEnums.LOG_DATE) || dateEnums.equals(DateEnums.DATE)) && expression.contains("-")) {
                    String[] items = expression.split("-");
                    for (String item : items) {
                        if (!item.trim().contains(dateEnums.getType())) {
                            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {
                                date = date - (Long.valueOf(item.trim()) * ONE_HOUR);
                            } else {
                                date = date - (Long.valueOf(item.trim()) * ONE_DAY);
                            }

                            break;
                        }
                    }
                } else if ((dateEnums.equals(DateEnums.LOG_DATE) || dateEnums.equals(DateEnums.DATE)) && expression.contains("+")) {
                    String[] items = expression.split("\\+");
                    for (String item : items) {
                        if (!item.trim().contains(dateEnums.getType())) {
                            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {
                                date = date + (Long.valueOf(item.trim()) * ONE_HOUR);
                            } else {
                                date = date + (Long.valueOf(item.trim()) * ONE_DAY);
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("date expression is not format", e);
            }

            // 将日期格式化
            if (dateEnums.getType().equals(DateEnums.TIMESTAMP.getType())) {
                sql = sql.replace(matcherItem, String.valueOf(date / 1000));
            } else {
                sql = sql.replace(matcherItem, DateConvertUtils.format(new Date(date), dateEnums.getFormat()));
            }

            dataDates.add(date);

        }
        sql = VariableUtils.replaceVariableExpression(time, sql, dataDates, VariableUtils.DEFAULT_ADJUST_VALUE);
        try {
            sql = SubStringVariableUtils.replaceSubStrExpression(sql);
            sql = SubStringVariableUtils.replaceSubLenStrExpression(sql);
        } catch (Exception e) {
            throw new IllegalArgumentException("date expression is not format", e);
        }
        return sql;
    }

    /**
     * 替换sql中的logdate格式的日期参数
     *
     * @param time
     * @param sql
     * @param dataDates
     * @throws IllegalArgumentException
     */
    public static String replaceLogDateExpression(Long time, String sql, List<Long> dataDates)
            throws IllegalArgumentException {

        sql = replaceDateExpressionByDateType(time, sql, dataDates, DateEnums.LOG_DATE);
        sql = VariableUtils.replaceVariableExpressionByDateFormat(time, sql, dataDates,
                VariableUtils.DEFAULT_ADJUST_VALUE,
                VariableUtils.DATA_FORMAT_YYYYMMDD);
        try {
            sql = SubStringVariableUtils.replaceSubStrExpression(sql);
            sql = SubStringVariableUtils.replaceSubLenStrExpression(sql);
        } catch (Exception e) {
            throw new IllegalArgumentException("date expression is not format", e);
        }
        return sql;
    }

    /**
     * 替换sql中的指定格式的日期参数
     *
     * @param time
     * @param sql
     * @param dataDates
     * @param enums
     * @throws IllegalArgumentException
     */
    public static String replaceDateExpressionByDateType(Long time, String sql, List<Long> dataDates, DateEnums enums)
            throws IllegalArgumentException {

        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        Matcher matcher = DATEPATTERN.matcher(sql);
        while (matcher.find()) {
            String matcherItem = matcher.group();
            String expression = matcherItem.substring(3, matcherItem.length() - 2);

            // 找到sql中日期的日期类型
            DateEnums dateEnums = null;
            int maxCommonLength = 0;
            for (DateEnums dateEnum : DateEnums.values()) {
                if (expression.contains(dateEnum.getType())) {
                    if (dateEnum.getType().length() > maxCommonLength) {
                        dateEnums = dateEnum;
                        maxCommonLength = dateEnum.getType().length();
                    }
                }
            }

            Preconditions.checkNotNull(dateEnums, "date expression is not format");

            // 仅替换指定类型的日期
            if (dateEnums.equals(enums)) {
                String format = replaceDateExpression(time, matcherItem, dataDates);
                sql = sql.replace(matcherItem, format);
            }
        }

        return sql;

    }

    /**
     * 替换sql中的日期参数
     *
     * @param time
     * @param sql
     * @return
     * @throws IllegalArgumentException
     */
    public static String replaceDateExpressionNoMinus(Long time, String sql, List<Long> dataDates)
            throws IllegalArgumentException {

        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        Matcher matcher = DATEPATTERN.matcher(sql);
        while (matcher.find()) {
            String matcherItem = matcher.group();
            String expression = matcherItem.substring(3, matcherItem.length() - 2);

            // 找到sql中日期的日期类型
            DateEnums dateEnums = null;
            int maxCommonLength = 0;
            for (DateEnums dateEnum : DateEnums.values()) {
                if (expression.contains(dateEnum.getType())) {
                    if (dateEnum.getType().length() > maxCommonLength) {
                        dateEnums = dateEnum;
                        maxCommonLength = dateEnum.getType().length();
                    }
                }
            }

            Preconditions.checkNotNull(dateEnums, "date expression is not format");

            // 获得一天前的日期
            long newTime = 0;
            long date = 0;

            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {

                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMMddHH"), "yyyyMMddHH").getTime();
                date = newTime;
            } else {

                newTime = DateConvertUtils.parse(DateConvertUtils.format(new Date(time), "yyyyMMdd"), "yyyyMMdd").getTime();
                date = newTime;
            }

            // 获得上周第一天的日期
            if (matcherItem.contains("first_" + dateEnums.getType() + "_of_week")) {
                date = new DateTime(newTime).dayOfWeek().withMinimumValue().toDate().getTime();
            }

            // 获得上周最后一天的日期
            if (matcherItem.contains("last_" + dateEnums.getType() + "_of_week")) {
                date = new DateTime(newTime).dayOfWeek().withMaximumValue().toDate().getTime();
            }

            // 获得上月第一天的日期
            if (matcherItem.contains("first_" + dateEnums.getType() + "_of_month")) {
                date = new DateTime(newTime).dayOfMonth().withMinimumValue().toDate().getTime();
            }

            // 获得上月最后一天的日期
            if (matcherItem.contains("last_" + dateEnums.getType() + "_of_month")) {
                date = new DateTime(newTime).dayOfMonth().withMaximumValue().toDate().getTime();
            }

            try {
                if (expression.contains("-")) {
                    String[] items = expression.split("-");
                    for (String item : items) {
                        if (!item.trim().contains(dateEnums.getType())) {

                            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {
                                date = date - (Long.valueOf(item.trim()) * ONE_HOUR);
                            } else {
                                date = date - (Long.valueOf(item.trim()) * ONE_DAY);
                            }
                            break;
                        }
                    }
                } else if (expression.contains("+")) {
                    String[] items = expression.split("\\+");
                    for (String item : items) {
                        if (!item.trim().contains(dateEnums.getType())) {

                            if (dateEnums.equals(DateEnums.LOG_HOUR) || dateEnums.equals(DateEnums.LOG_DATE_HOUR)) {
                                date = date + (Long.valueOf(item.trim()) * ONE_HOUR);
                            } else {
                                date = date + (Long.valueOf(item.trim()) * ONE_DAY);
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("date expression is not format", e);
            }

            // 将日期格式化
            if (dateEnums.getType().equals(DateEnums.TIMESTAMP.getType())) {
                sql = sql.replace(matcherItem, String.valueOf(date / 1000));
            } else {
                sql = sql.replace(matcherItem, DateConvertUtils.format(new Date(date), dateEnums.getFormat()));
            }

            dataDates.add(date);

        }
        sql = VariableUtils.replaceVariableExpression(time, sql, dataDates, VariableUtils.ZERO_ADJUST_VALUE);
        try {
            sql = SubStringVariableUtils.replaceSubStrExpression(sql);
            sql = SubStringVariableUtils.replaceSubLenStrExpression(sql);
        } catch (Exception e) {
            throw new IllegalArgumentException("date expression is not format", e);
        }
        return sql;
    }

    /**
     * 获取sql中获取格式化的数据执行日期
     */
    public static String getFormatExecuteDateFromSql(Long time, String sql) throws IllegalArgumentException {

        String firstDate = new String();
        String dateType = getDateTypeFromSql(sql);

        List<Long> dataDates = new ArrayList<>();
        replaceDateExpression(time, sql, dataDates);
        Long date = dataDates.get(0);

        // 将日期格式化
        if (dateType.equals(DateEnums.TIMESTAMP.getType())) {
            firstDate = String.valueOf(date);

        } else {
            firstDate = String.valueOf(convertDateValueToFormat(dateType, date));

        }

        return firstDate;
    }

    public static String getWhereSegmentFromSql(String tableName, String sql) {
        sql = sql.toLowerCase().replace("\n", " ").trim();

        String[] arrays = sql.split(tableName.toLowerCase());

        if (arrays.length <= 1) {
            return null;
        }

        if (!arrays[1].contains("where")) {
            return null;
        }

        String whereString = arrays[1].trim();

        if (!whereString.startsWith("where")) {
            return null;
        }
        whereString = whereString.substring(5);

        List<String> keyWordsList = Arrays.asList(new String[]{"group by", "order by", "limit", "select", "where",
                "join", "union"});

        // 到where下个关键词截断
        for (String keyword : keyWordsList) {
            if (whereString.contains(keyword)) {
                whereString = whereString.split(keyword)[0];
            }
        }

        return whereString;

    }

    /**
     * 从sql中获取时间参数类型
     */
    public static String getDateTypeFromSql(String sql) {
        Matcher matcher = DATEPATTERN.matcher(sql);

        DateEnums dateEnums = null;
        String type = null;

        while (matcher.find()) {
            String matcherItem = matcher.group();
            String expression = matcherItem.substring(3, matcherItem.length() - 2);

            // 找到sql中日期的日期类型
            int maxCommonLength = 0;
            for (DateEnums dateEnum : DateEnums.values()) {

                if (expression.contains(dateEnum.getType())) {

                    if (dateEnum.getType().length() > maxCommonLength) {
                        dateEnums = dateEnum;
                        type = dateEnums.getType();
                    }
                }
            }
            if (type != null) {
                break;
            }
        }

        return type;
    }

    public static String formatSql(String sql) {
        return SQLUtils.format(sql, JdbcConstants.HIVE);
    }

    public static List<String> extractSelectItem(String sql) {
        List<String> fields = new ArrayList<>();
        String selectSql = new String(sql).trim();
        if (!selectSql.toLowerCase().startsWith("select")) {
            return fields;
        }
        selectSql = selectSql.substring(0, selectSql.toLowerCase().replace("\n", " ").indexOf(" from") + 5) + " table1";

        selectSql = selectSql.replace("\n", " ").trim();

        MySqlStatementParser parser = new MySqlStatementParser(selectSql);

        // List的size为1
        List<SQLStatement> statementList = parser.parseStatementList();
        for (SQLStatement statement : statementList) {
            if (statement instanceof SQLSelectStatement) {

                SQLSelect select = ((SQLSelectStatement) statement).getSelect();

                SQLSelectQueryBlock query = (SQLSelectQueryBlock) select.getQuery();

                // 遍历select项
                for (SQLSelectItem sqlSelectItem : query.getSelectList()) {
                    if (sqlSelectItem.getExpr() instanceof SQLAggregateExpr) {
                        if (StringUtils.isNotBlank(sqlSelectItem.getAlias())) {
                            fields.add(sqlSelectItem.getAlias());
                        } else {
                            fields.add(((SQLAggregateExpr) sqlSelectItem.getExpr()).getMethodName());
                        }
                    } else {
                        String item = sqlSelectItem.toString();
                        String[] items = item.split(" ");
                        boolean existAs = false;
                        int indexAs = -1;
                        for (int i = 0; i < items.length; i++) {
                            if (items[i].equals("AS")) {
                                existAs = true;
                                indexAs = i;
                                break;
                            }
                        }
                        if (existAs) {
                            item = items[indexAs + 1];

                            // 若别名为`中文别名`，则去掉反引号
                            if (item.contains("`")) {
                                item = item.replace("`", "");
                            }
                        }
                        fields.add(item);
                    }
                }
            }
        }
        return fields;
    }

    /**
     * 寻找sql中的表名
     */
    public static List<String> extractTableName(String sql) {
        List<String> tableNameList = new ArrayList<>();
        String command = new String(sql).trim();

        if (!command.toLowerCase().contains("from")) {
            return null;
        }

        String[] fromPart = command.toLowerCase().replace("\n", " ").split(" from ");

        for (int i = 1; i < fromPart.length; i++) {

            String keyWord = findNextKeyWord(fromPart[i]);

            if (keyWord != null && keyWord.equals("select")) {
                continue;
            }

            tableNameList.add(fromPart[i].trim().split(" ")[0]);
        }
        return tableNameList;
    }

    /**
     * 寻找第一个sql关键字(order by, limit, where, select)
     */
    public static String findNextKeyWord(String sqlPart) {
        String keyWord = null;

        List<String> keyWordsList = Arrays.asList(new String[]{"group by", "order by", "limit", "where", "select"});

        List<String> wordList = Arrays.asList(sqlPart.toLowerCase().replace("\n", " ").split(" "));

        for (String word : wordList) {

            for (String keyWords : keyWordsList) {

                if (word.contains(keyWords)) {
                    return keyWords;
                }
            }

        }

        return keyWord;
    }

    public static List<Object> convertDateValueToFormat(String field, List<Object> values) {
        List<Object> results = new ArrayList<>(values.size());
        for (Object value : values) {
            results.add(convertDateValueToFormat(field, value));
        }
        return results;
    }

    public static Object convertDateValueToFormat(String field, Object value) {
        if (field.equals(DateEnums.LOG_DATE.getType())) {
            return value.toString().replace("-", "");
        } else if (field.equals(DateEnums.TIMESTAMP.getType()) || field.equals(DateEnums.DATE.getType())) {
            return DateConvertUtils.parse(value.toString(), DateEnums.DATE.getFormat()).getTime() + "";
        } else {
            return value;
        }
    }

    public static Object convertDateValueToFormat(String field, Long value) {
        if (field.equals(DateEnums.LOG_DATE.getType())) {
            return DateConvertUtils.format(new Date(value), DateEnums.LOG_DATE.getFormat());
        } else if (field.equals(DateEnums.DATE.getType())) {
            return DateConvertUtils.format(new Date(value), DateEnums.DATE.getFormat());
        } else if (field.equals(DateEnums.CTIME.getType())) {
            return DateConvertUtils.format(new Date(value), DateEnums.CTIME.getFormat());
        } else if (field.equals(DateEnums.MTIME.getType())) {
            return DateConvertUtils.format(new Date(value), DateEnums.MTIME.getFormat());
        } else {
            return value;
        }
    }

    public static void main(String[] args) {
        /*
         * String sql = "DROP TABLE `pokes`"; Map<String, List<String>> results = SqlUtils.convertTables(sql); if
         * (results != null) { for (Map.Entry<String, List<String>> stringListEntry : results.entrySet()) {
         * System.out.println(stringListEntry.getKey()); for (String s : stringListEntry.getValue()) {
         * System.out.println(s); } } }
         */
        /*String sql = "<%=log_hour - 12%>= <%=(log_date)%> and log_date < <%=(date)+1%> and log_date > ${yyyyMMdd}";

        System.out.println(SqlUtils.replaceDateExpression(sql, new ArrayList<>()));
        String formatSql = SqlUtils.replaceLogDateExpression(Clock.systemUTC().millis(), sql, new ArrayList<>());
        System.out.println(formatSql);*/

        String testSql = "select\n"
                + "\t'<%=log_date%>' as log_date\n"
                + "\t,'日' as period\n"
                + "\t,COALESCE(up_from,'全部') as up_from\n"
                + "\t,COALESCE(original,'全部') as original\n"
                + "\t,COALESCE(tname,'全部') as tname\n"
                + "\t,COALESCE(duration_type,'全部') as duration_type\n"
                + "\t,count(distinct avid) as av_cnt\n"
                + "\t,count(distinct mid) as m_cnt\n"
                + "\t,case when count(distinct mid)>0 then count(distinct avid)/count(distinct mid) else 0 end as av_cnt_per\n"
                + "\t,count(distinct case when is_new='Y' then mid end) as new_m_cnt\n" + "from\n" + "(\n"
                + "select\n" + "\tavid,a.mid,original,tname,duration_type,COALESCE(up_from,'其他') as up_from\n"
                + "\t,case when c.mid is not null then 'Y' else 'N' end as is_new\n" + "from\n" + "(\n"
                + "select\n" + "\tavid,mid\n" + "\t,case\n" + "\t\twhen original=1 then '原创'\n"
                + "\t\telse '转载'\n" + "\tend as original\n"
                + "\t,coalesce(tid,0) as tid,coalesce(b_typename(tid),'其他') as tname\n" + "\t,case\n"
                + "\t\twhen duration<60 then 'a_0-1min'\n" + "\t\twhen duration<60*3 then 'b_1-3min'\n"
                + "\t\twhen duration<60*5 then 'c_3-5min'\n" + "\t\twhen duration<60*10 then 'd_5-10min'\n"
                + "\t\twhen duration<60*15 then 'e_10-15min'\n"
                + "\t\twhen duration<60*30 then 'f_15-30min'\n"
                + "\t\twhen duration<60*60 then 'g_30-60min'\n" + "\t\telse 'h_60min+'\n"
                + "\tend as duration_type\n" + "from\n" + "\tarchive.dws_archive_daily\n" + "where\n"
                + "\tlog_date='<%=log_date%>'\n"
                + "    and substr(regexp_replace(ctime,'-',''),1,8)='<%=log_date%>'\n" + ") a\n"
                + "left outer join\n" + "(\n" + "select\n" + "\taid\n" + "\t,case\n"
                + "\t\twhen up_from = 0 then 'web'\n" + "\t\twhen up_from in (1,5,6) then 'PGC'\n"
                + "\t\twhen up_from = 2 then 'PC客户端'\n" + "\t\twhen up_from =7 then '创作姬'\n"
                + "        when up_from =8  then '粉Android'\n" + "        when up_from =9  then '粉Ios'\n"
                + "\t\telse '其他'\n" + "\tend as up_from\n" + "from\n" + "\tods.ods_archive_addition\n"
                + ") b\n" + "on a.avid=b.aid\n" + "left outer join\n" + "(\n" + "select\n" + "\tmid\n"
                + "from\n" + "\tarchive.dws_up_daily\n" + "where\n" + "\tlog_date='<%=log_date%>'\n"
                + "\tand substr(regexp_replace(first_up,'-',''),1,8)='<%=log_date%>'\n" + ") c\n"
                + "on a.mid=c.mid\n" + ") t\n" + "group by\n" + "\tup_from,original,tname,duration_type\n"
                + "with cube";


        String sql1 = "select <%= log_date_hour%>";
        Long date = 1514736000000L; // 20180101
        System.out.println(SqlUtils.replaceDateExpression(date, testSql, new ArrayList<>()));
        System.out.println(SqlUtils.replaceDateExpressionNoMinus(date, sql1, new ArrayList<>()));
        System.out.println();
        date = 1517328000000L; // 20180131
        System.out.println(SqlUtils.replaceDateExpression(date, sql1, new ArrayList<>()));
        System.out.println(SqlUtils.replaceDateExpressionNoMinus(date, sql1, new ArrayList<>()));
        System.out.println();
        date = 1519747200000L; // 20180228
        System.out.println(SqlUtils.replaceDateExpression(date, sql1, new ArrayList<>()));
        System.out.println(SqlUtils.replaceDateExpressionNoMinus(date, sql1, new ArrayList<>()));

        String sql2 = "select\n" + "  upid,\n" + "  sum(coalesce(attention, 0)) as attention,\n"
                + "  sum(coalesce(un_attention, 0)) as un_attention\n" + "from\n"
                + "  archive.dm_up_attention_stats\n" + "where\n"
                + "  log_date between '${yyyyMM}01' and '${yyyyMMdd}'\n" + "group by\n" + "  upid";

        System.out.println(SqlUtils.replaceDateExpression(sql2, new ArrayList<>()));

        String sql3 = "hadoop jar /application/archer/resource/mr-jar/departmentId_1/mr.jar " +
                "mapreduce.CanalBinLogParser -D mapreduce.job.queuename=report_queue " +
                "-D mapreduce.output.fileoutputformat.compress=true -D " +
                "mapreduce.output.fileoutputformat.compress.codec=com.hadoop.compression.lzo.LzopCodec " +
                "-D mapreduce.job.name=TagBinLog_$substr(<%=log_date_hour%> ,0,8) " +
                "/flume/main/tag/tag_resource_binlog/$substr(<%=log_date_hour%> ,0,8)/$substr(<%=log_date_hour%> ,9)/*.lzo " +
                "/warehouse1/archive.db/dwb_tag_resource_binlog/log_date=$substr(<%=log_date_hour%> ,0,8)/log_hour=$substr(<%=log_date_hour%> ,9) tag_resource ^tag_resource_[0-9]+$ id-bigint;oid-bigint;type-int;tid-bigint;mid-bigint;role-int;enjoy-bigint;hate-bigint;attr-int;state-int;ctime-string;mtime-string xxxxxxx xxxxx";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , 5, 3)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , 5, -3)";
        //System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , -5, 3)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , -8, 1)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , 8, 1)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , 9)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date_hour %> , 10)";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

        sql3 = "$substr( <%= log_date %> , -3)/a/b%=/c==\\$substr(   0000001,    -1)/";
        System.out.println(SqlUtils.replaceDateExpression(sql3, new ArrayList<>()));

    }

    public static boolean containsDateKey(Object key) {
        for (DateEnums dateEnums : DateEnums.values()) {
            if (dateEnums.getType().equals(key)) {
                return true;
            }
        }
        return false;
    }

    public enum DateEnums {
        LOG_DATE("log_date", "yyyyMMdd"),

        LOG_WEEK("log_week", "yyyyMMdd"), // first day of month

        LOG_MONTH("log_month", "yyyyMM"),

        LOG_HOUR("log_hour", "HH"),

        LOG_DATE_HOUR("log_date_hour", "yyyyMMddHH"),

        DATE("date", "yyyy-MM-dd"),

        CTIME("ctime", "yyyy-MM-dd HH:mm:ss"),

        MTIME("mtime", "yyyy-MM-dd HH:mm:ss"),

        TIMESTAMP("ts", " ");

        private String type;

        private String format;

        DateEnums(String type, String format) {
            this.type = type;
            this.format = format;
        }

        public String getType() {
            return type;
        }

        public String getFormat() {
            return format;
        }
    }

    public static boolean isDDL(String sql) {
        // 去除注释的干扰
        while (sql.startsWith("--")) {
            sql = sql.substring(sql.indexOf('\n'));
        }

        char[] word = new char[50];
        boolean isStart = false;
        int j = 0;
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            if (isStart && isControlChar(c)) {
                break;
            } else if (isStart && !isControlChar(c)) {
                word[j++] = c;
                isStart = true;
            } else if (!isStart && isControlChar(c)) {
                continue;
            } else {
                word[j++] = c;
                isStart = true;
            }
        }

        String w = new String(word, 0, j).toLowerCase();
        return w.equals("alter") || w.equals("create") || w.equals("drop") || w.equals("truncate")
                || w.equals("insert") || w.equals("msck");
    }

    public static boolean isControlChar(char c) {
        return c == ' ' || c == '\n' || c == '\r' || c == '\t';
    }


}
