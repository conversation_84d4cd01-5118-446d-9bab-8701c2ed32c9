package com.ailpha.ailand.dataroute.endpoint.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 时间转换
 */
public final class DateConvertUtils {

    public static final String TIME_INTERVAL_SECOND = "second";
    public static final String TIME_INTERVAL_MINUTE = "minute";
    public static final String TIME_INTERVAL_HOUR = "hour";
    public static final String TIME_INTERVAL_DAY = "day";
    public static final String TIME_INTERVAL_WEEK = "week";
    public static final String TIME_INTERVAL_MONTH = "month";
    public static final String TIME_INTERVAL_QUARTER = "quarter";
    public static final String TIME_INTERVAL_YEAR = "year";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_SHORT_FORMAT = "yyyyMMdd";
    public static final String DATE_MONTH_FORMAT = "yyyy-MM";
    public static final String TIME_FORMAT = "HH:mm:ss";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_MS_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    public final static int MILLISECONDS_ONE_MINUTE = 1000 * 60;

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT);
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT);

    public static Date parse(String dateString, String dateFormat) {
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        try {
            return format.parse(dateString);
        } catch (ParseException e) {
            return null;
        }
    }

    public static <T extends Date> T parse(String dateString, String dateFormat, Class<T> targetResultType) {
        if (StringUtils.isEmpty(dateString)) return null;
        DateFormat df = new SimpleDateFormat(dateFormat);
        try {
            T t = targetResultType.getConstructor(new Class[]{Long.TYPE}).newInstance(new Object[]{Long.valueOf(df.parse(dateString).getTime())});
            return t;
        } catch (ParseException e) {
            String errorInfo = "cannot use dateformat:" + dateFormat + " parse datestring:" + dateString;
            throw new IllegalArgumentException(errorInfo, e);
        } catch (Exception e) {
            throw new IllegalArgumentException("error targetResultType:" + targetResultType.getName(), e);
        }
    }

    public static String format(Date date, String dateFormat) {
        if (date == null) return null;
        DateFormat df = new SimpleDateFormat(dateFormat);
        return df.format(date);
    }

    public static String getDateString(String datePattern) {
        return new SimpleDateFormat(datePattern).format(new Date());
    }

    public static String getDateString(String datePattern, Date date) {
        return new SimpleDateFormat(datePattern).format(date);
    }

    public static String getYesterdayString(String dateFormat) {
        Date yesterday = add(Calendar.DAY_OF_YEAR, new Date(), -1);
        return DateConvertUtils.format(yesterday, dateFormat);
    }

    public static Integer getCurrentWeekOfYear(long time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        return calendar.get(Calendar.WEEK_OF_YEAR);
    }

    public static Date add(int field, Date date, int value) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        int fieldNewValue = (c.get(field) + value);
        c.set(field, fieldNewValue);
        return c.getTime();
    }

    public static long dateDiff(String timeInterval, Date date1, Date date2) {
        Calendar calendar = Calendar.getInstance();
        if (timeInterval.equals(TIME_INTERVAL_YEAR)) {
            calendar.setTime(date1);
            int time = calendar.get(Calendar.YEAR);
            calendar.setTime(date2);
            return time - calendar.get(Calendar.YEAR);
        }

        if (timeInterval.equals(TIME_INTERVAL_QUARTER)) {
            calendar.setTime(date1);
            int time = calendar.get(Calendar.YEAR) * 4;
            calendar.setTime(date2);
            time -= calendar.get(Calendar.YEAR) * 4;
            calendar.setTime(date1);
            time += calendar.get(Calendar.MONTH) / 4;
            calendar.setTime(date2);
            return time - calendar.get(Calendar.MONTH) / 4;
        }

        if (timeInterval.equals(TIME_INTERVAL_MONTH)) {
            calendar.setTime(date1);
            int time = calendar.get(Calendar.YEAR) * 12;
            calendar.setTime(date2);
            time -= calendar.get(Calendar.YEAR) * 12;
            calendar.setTime(date1);
            time += calendar.get(Calendar.MONTH);
            calendar.setTime(date2);
            return time - calendar.get(Calendar.MONTH);
        }

        if (timeInterval.equals(TIME_INTERVAL_WEEK)) {
            calendar.setTime(date1);
            int time = calendar.get(Calendar.YEAR) * 52;
            calendar.setTime(date2);
            time -= calendar.get(Calendar.YEAR) * 52;
            calendar.setTime(date1);
            time += calendar.get(Calendar.WEEK_OF_YEAR);
            calendar.setTime(date2);
            return time - calendar.get(Calendar.WEEK_OF_YEAR);
        }

        if (timeInterval.equals(TIME_INTERVAL_DAY)) {
            calendar.setTime(date1);
            int time = calendar.get(Calendar.DAY_OF_YEAR) + calendar.get(Calendar.YEAR) * 365;
            calendar.setTime(date2);
            return time - (calendar.get(Calendar.DAY_OF_YEAR) + calendar.get(Calendar.YEAR) * 365);
        }

        if (timeInterval.equals(TIME_INTERVAL_HOUR)) {
            long time = date1.getTime() / 1000 / 60 / 60;
            return time - date2.getTime() / 1000 / 60 / 60;
        }

        if (timeInterval.equals(TIME_INTERVAL_MINUTE)) {
            long time = date1.getTime() / 1000 / 60;
            return time - date2.getTime() / 1000 / 60;
        }

        if (timeInterval.equals(TIME_INTERVAL_SECOND)) {
            long time = date1.getTime() / 1000;
            return time - date2.getTime() / 1000;
        }

        return date1.getTime() - date2.getTime();
    }

    public static long dateDiff(String timeInterval, Long unixTime1, Long unixTime2) {
        return dateDiff(timeInterval, new Date(unixTime1), new Date(unixTime2));
    }

    /**
     * <p>
     * 得到传入时间所在的月的最后一天
     * </p>
     *
     * @param date
     * @return
     * <AUTHOR>
     * @see
     */
    public static Date monthLastTime(Date date, int beforeMonth) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + beforeMonth);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    /**
     * <p>
     * 得到传入时间所在月的第一天
     * </p>
     *
     * @param date
     * @return
     * <AUTHOR>
     * @see
     */
    public static Date monthFirstTime(Date date, int beforeMonth) {
        Calendar cal = Calendar.getInstance();// 获取当前日期
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号,当前日期既为本月第一天
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + beforeMonth, cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * <p>
     * 得到传入时间后几天的开始时间
     * </p>
     *
     * @return
     * <AUTHOR>
     * @see
     */
    public static Date beforeDateStartTime(Date date, int before) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH) + before, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * <p>
     * 得到传入时间后几天的结束时间
     * </p>
     *
     * @return
     * <AUTHOR>
     * @date 2013-12-5 下午9:04:18
     * @see
     */
    public static Date beforeDateLastTime(Date date, int before) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH) + before, 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    /**
     * 获取所传月份之前的月份格式
     *
     * @param date
     * @return
     * <AUTHOR>
     * @see
     */
    public static String monthVersion(Date date, int beforeMonth, String dateFormat) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + beforeMonth);
        return getDateString(dateFormat, cal.getTime());
    }

    /**
     * 获取所传月份之前的月份格式
     *
     * @return
     * <AUTHOR>
     * @see
     */
    public static String monthVersion(String dateVersion, int beforeMonth, String dateFormat) {
        return monthVersion(parse(dateVersion, dateFormat), beforeMonth, dateFormat);
    }

    /**
     * localDate to date
     */
    public static Date localDate2Date(LocalDate date) {
        Instant instant = date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * localDatetime to date
     */
    public static Date localDate2Date(LocalDateTime date) {
        Instant instant = date.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    /**
     * date to localDatetime
     */
    public static LocalDate date2LocalDate(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toLocalDate();
    }

    public static long localDateTime2Timestamp(LocalDateTime date) {
        return date.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }

    public static LocalDateTime timestamp2LocalDateTime(long date) {
        Timestamp ts = new Timestamp(date);
        return LocalDateTime.ofInstant(ts.toInstant(), ZoneId.systemDefault());
    }

    /**
     * date to localDate
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static String timestamp2Str(long timestamp, String format) {
        Timestamp ts = new Timestamp(timestamp);
        LocalDateTime time = LocalDateTime.ofInstant(ts.toInstant(), ZoneId.systemDefault());
        return time.format(DateTimeFormatter.ofPattern(format));
    }

    public static long str2Timestamp(String str, String format) {
        LocalDateTime time = LocalDateTime.parse(str, DateTimeFormatter.ofPattern(format));
        return time.toInstant(OffsetDateTime.now().getOffset()).toEpochMilli();
    }

    /**
     * localDatetime to str
     */
    public static String localDateTime2String(LocalDateTime time) {
        return time.format(DATE_TIME_FORMATTER);
    }

    /**
     * localDate to str
     */
    public static String localDate2String(LocalDate time) {
        return time.format(DATE_FORMATTER);
    }

}
