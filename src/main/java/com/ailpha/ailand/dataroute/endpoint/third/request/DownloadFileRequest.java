package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AttachType;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DownloadFileRequest extends BaseRemoteRequest {
    String dataProductPlatformId;
    AttachType attachType;
}
