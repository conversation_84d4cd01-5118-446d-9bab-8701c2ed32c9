package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import cn.hutool.json.JSONObject;
import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.biz.api.collector.DatasourceCheckQuery;
import com.ailpha.ailand.biz.api.collector.DatasourceColumnResponse;
import com.ailpha.ailand.biz.api.collector.DatasourceJoinFieldDebugDataResponse;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AttachType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.ApiImportFileVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataAssetFileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DebugFileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.SimpleLocalFileVO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DataSourceTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.output.DataCollectorApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import org.ehcache.Cache;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Tag(name = "数据资产")
@RequiredArgsConstructor
@RequestMapping("data-asset")
@PreAuthorize("hasAuthority('TRADER')")
public class DataAssetController {

    private final DataAssetService dataAssetService;
    private final DataCollectorApi dataCollectorApi;
    private final TeeRemote teeRemote;
    private final Cache<String, String> dataAssetCache;
    private final HubShuHanApiClient hubShuHanApiClient;
    private final BaseCapabilityManager baseCapabilityManager;
    private final DataProductService dataProductService;
    private final DataProductRepository dataProductRepository;

    @PostMapping(value = "upload-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传数据文件")
    public SuccessResponse<DataAssetFileUploadResponse> fileUpload(@RequestParam("file") MultipartFile file) throws IOException {
        return SuccessResponse.success(dataAssetService.uploadFile(file)).build();
    }

    @PostMapping(value = "upload-model-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传模型文件")
    public SuccessResponse<DataAssetFileUploadResponse> modelFileUpload(@RequestParam("file") MultipartFile file) {
        return SuccessResponse.success(dataAssetService.uploadModelFile(file)).build();
    }

    @Data
    public static class ServerFileCheck {
        String filepath;
    }

    @PostMapping(value = "server-file-check")
    @Operation(summary = "服务器(模型)文件检查")
    public SuccessResponse<DataAssetFileUploadResponse> serverFileCheck(@RequestBody ServerFileCheck serverFileCheck) throws IOException {
        return SuccessResponse.success(dataAssetService.exploreFile(serverFileCheck.getFilepath())).build();
    }

    @PostMapping(value = "upload-debug-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传调试数据文件")
    public SuccessResponse<DebugFileUploadResponse> uploadDebugFile(@RequestParam(name = "dataAssetId", required = false) String dataAssetId,
                                                                    @RequestParam("file") MultipartFile file,
                                                                    @RequestParam(name = "separator", required = false, defaultValue = ",") String separator,
                                                                    @RequestParam(name = "hasHeader", required = false, defaultValue = "1") Integer hasHeader,
                                                                    @RequestParam(name = "dataType", required = false, defaultValue = "STRUCTURED") String dataType
    ) throws Exception {
        if (!StringUtils.hasText(file.getOriginalFilename()) || file.getOriginalFilename().contains("../")) {
            throw new RestfulApiException("非法的文件名称");
        }
        if (DataType.STRUCTURED.name().equals(dataType) && !file.getOriginalFilename().endsWith(".csv")) {
            throw new RestfulApiException("上传的文件必须是csv后缀名");
        } else if (DataType.UNSTRUCTURED.name().equals(dataType) &&
                !(file.getOriginalFilename().endsWith(".zip") || file.getOriginalFilename().endsWith(".tar.gz"))) {
            throw new RestfulApiException("上传的文件必须是zip/tar.gz后缀名");
        }
        DebugFileUploadResponse fileInfo = dataAssetService.uploadDebugDataReturnSchemaAndExampleData(dataAssetId, file, separator, hasHeader, dataType);

        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        if (!ObjectUtils.isEmpty(dataAssetId) && baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TEE)) {
            String debugDataFilePath = dataAssetCache.get(fileInfo.getTempDebugFileId());
            File debugFile = new File(debugDataFilePath);
            okhttp3.RequestBody debugDataFile = okhttp3.RequestBody.create(debugFile, okhttp3.MediaType.parse("multipart/form-data"));
            MultipartBody.Builder builder = new MultipartBody.Builder();
            builder.setType(MultipartBody.FORM)
                    .addFormDataPart("assetId", dataAssetId)
                    .addFormDataPart("dataStructureType", dataType)
                    .addFormDataPart("hasHeader", String.valueOf(hasHeader))
                    .addFormDataPart("separator", separator)
                    .addFormDataPart("file", debugFile.getName(), debugDataFile);
            teeRemote.uploadDebugFile(builder.build());
        }
        return SuccessResponse.success(fileInfo).build();
    }

    @GetMapping(value = "download-debug-file")
    @Operation(summary = "下载调试数据文件")
    public void downloadDownloadDebugFile(@RequestParam("assetType") AssetType assetType, @RequestParam String assetId, HttpServletResponse response) {
        dataAssetService.downloadDownloadDebugFile(assetType, assetId, response);
    }

    @Operation(summary = "数据源配置字段")
    @GetMapping(value = "datasource/properties")
    public SuccessResponse<String> datasourceProperties() throws Exception {
        return SuccessResponse.success(dataAssetService.datasourceProperties()).build();
    }

    @Operation(summary = "数据源hdfs文件类型")
    @GetMapping(value = "datasource/fileType")
    public SuccessResponse<String> datasourceFileType() throws Exception {
        String json = dataAssetService.datasourceFileType();
        return SuccessResponse.success(json).build();
    }

    @Operation(summary = "支持的数据源")
    @GetMapping(value = "supported-datasource")
    public SuccessResponse<List<Tuple2<String, String>>> datasourceTypes() {
        List<Tuple2<String, String>> list = Arrays.stream(DataSourceTypeEnums.values())
                .map(a -> new Tuple2<>(a.getCode(), a.getDesc()))
                .collect(Collectors.toList());
        return SuccessResponse.success(list).build();
    }

    @Operation(summary = "测试数据源连接")
    @PostMapping(value = "datasource/test")
    public SuccessResponse<Boolean> checkConn(@RequestBody DatasourceCheckQuery query) {
        return ApiResponse.success(dataCollectorApi.checkConn(query)).build();
    }

    @Operation(summary = "测试数据源表连接")
    @PostMapping(value = "datasource/check/table")
    public SuccessResponse<Boolean> checkTable(@RequestBody DatasourceCheckQuery query) {
        return ApiResponse.success(dataCollectorApi.checkTable(query)).build();
    }

    @Operation(description = "查询调试数据")
    @PostMapping(value = "datasource/table/debug-data")
    public SuccessResponse<DatasourceJoinFieldDebugDataResponse> debugData(@RequestBody DatasourceCheckQuery query) {
        return ApiResponse.success(dataCollectorApi.debugData(query)).build();
    }

    @Operation(summary = "获取表字段")
    @PostMapping(value = "datasource/table/columns")
    public SuccessResponse<DatasourceColumnResponse> checkTableColumn(@RequestBody DatasourceCheckQuery query) {
        return ApiResponse.success(dataCollectorApi.fetchTableColumn(query)).build();
    }

    @PostMapping("datasource/kerberosKeytabFilePath")
    @Operation(summary = "上传kerberos 认证文件")
    public SuccessResponse<SimpleLocalFileVO> uploadKerberosKeytabFile(@RequestPart("file") MultipartFile file) {
        if (org.apache.commons.lang3.StringUtils.isBlank(file.getOriginalFilename()) || file.getOriginalFilename().contains("../")) {
            throw new RestfulApiException("非法的文件名称");
        }
        SimpleLocalFileVO simpleLocalFileVO = dataAssetService.uploadKerberosKeytabFile(file);
        return ApiResponse.success(simpleLocalFileVO).build();
    }

    @PostMapping("datasource/kerberosConfFilePath")
    @Operation(summary = "上传kerberos 配置文件")
    public SuccessResponse<SimpleLocalFileVO> uploadKerberosConfFile(@RequestPart("file") MultipartFile file) {
        if (org.apache.commons.lang3.StringUtils.isBlank(file.getOriginalFilename()) || file.getOriginalFilename().contains("../") ||
                !file.getOriginalFilename().endsWith(".conf")) {
            throw new RestfulApiException("非法的文件名称");
        }
        SimpleLocalFileVO simpleLocalFileVO = dataAssetService.uploadKerberosConfFile(file);
        return ApiResponse.success(simpleLocalFileVO).build();
    }

    @Operation(summary = "API接口导入-上传批量参数文件")
    @PostMapping(value = "apiImport/upload-batch-params-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public SuccessResponse<ApiImportFileVO> apiImportUploadBatchParamsFile(@RequestPart("file") MultipartFile file) throws Exception {
        if (org.apache.commons.lang3.StringUtils.isBlank(file.getOriginalFilename()) || file.getOriginalFilename().contains("../")) {
            throw new RestfulApiException("非法的文件名称");
        }
        return SuccessResponse.success(dataAssetService.apiImportUploadBatchParamsFile(file, ",", 0)).build();
    }

    @GetMapping("apiImport/download-batch-params-file")
    @Operation(summary = "下载批量参数文件")
    public void downloadBatchParamsFile(@RequestParam("dataAssetId") String dataAssetId, @RequestParam("assetType") AssetType assetType, @RequestParam("routerId") String routerId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        dataAssetService.downloadBatchParamsFile(assetType, dataAssetId, routerId, request, response);
    }

    @GetMapping("apiImport/preview-batch-params")
    @Operation(summary = "预览批量参数")
    public SuccessResponse<List<List<String>>> previewBatchParams(@RequestParam("dataAssetId") String dataAssetId, @RequestParam("assetType") AssetType assetType, @RequestParam("routerId") String routerId) {
        return ApiResponse.success(dataAssetService.previewBatchParams(assetType, dataAssetId, routerId)).build();
    }

    @Operation(summary = "API接口导入-连接测试")
    @PostMapping("apiImport/api-test")
    @OpLog(message = "API接口")
    public String add(@RequestBody ApiImportTestVO apiImportTestVO) throws Exception {

        boolean dispatch = false;
        if (apiImportTestVO.getHeaders() != null && apiImportTestVO.getHeaders().stream().anyMatch(paramsBO -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(paramsBO.getKey(), "apiKey"))) {
            // 正式使用的时候记录
            OPLogContext.putOpType(InternalOpType.API_INVOKE);
            dispatch = true;
        } else {
            // 非正式
            OPLogContext.closeSwitch();
        }

        // API接口导入url限制
        return dataAssetService.callApi(apiImportTestVO, dispatch);
        // return ApiResponse.success(response).build();
    }

    @PostMapping(value = "upload-attach-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传附件文件")
    public SuccessResponse<String> fileAttachUpload(@RequestParam("file") MultipartFile file) {
        return SuccessResponse.success(dataAssetService.uploadAttachFile(file)).build();
    }

    @Operation(summary = "下载附件文件")
    @GetMapping(value = "attach-file")
    public void fileAttachDownload(@RequestParam("dataProductPlatformId") String dataProductPlatformId,
                                   @RequestParam AttachType attachType, HttpServletResponse response) throws IOException {
        dataProductService.downloadAttachFile(dataProductPlatformId, attachType, response, LoginContextHolder.currentUser().getCompany().getNodeId());
    }

    @Operation(summary = "下载附件文件（审批详情）")
    @GetMapping(value = "attach-file-local")
    @PreAuthorize("hasAnyAuthority('COMPANY_ADMIN', 'TRADER')")
    public void fileAttachDownloadLocal(@RequestParam("dataProductId") String dataProductId,
                                        @RequestParam AttachType attachType, HttpServletResponse response) throws IOException {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
        Assert.notNull(dataProduct, "未找到 id 为 " + dataProductId + " 的数据产品");
        dataProductService.downloadAttachFile(dataProduct, attachType, response);
    }

    @Operation(summary = "数据目录查询")
    @PostMapping("catalogQuery")
    public SuccessResponse<List<JSONObject>> dataCatalogQuery(@RequestBody CatalogQueryVM catalogQuery) {
        return hubShuHanApiClient.dataCatalogQuery(catalogQuery);
    }
}
