package com.ailpha.ailand.dataroute.endpoint.common.enums;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 10:28
 * @Description:
 */
public enum SSEMessageTypeEnum {
    /**
     * 资产申请审批1
     */
    DATA_ASSET_APPROVAL,

    DATA_ASSET_APPROVED,

    DATA_ASSET_REFUSED,

    DATA_ASSET_COMPLETED,

    DATA_ASSET_TERMINATE,

    HEARTBEAT,

    TOTAL_COUNT,

    SUBMIT_DEMAND_SOLUTION,

    ACCEPT_DEMAND_SOLUTION,

    ;

    public static String ofName(SSEMessageTypeEnum sseMessageTypeEnum) {
        return switch (sseMessageTypeEnum) {
            case DATA_ASSET_APPROVAL -> "订单合同待审批";
            case DATA_ASSET_APPROVED, DATA_ASSET_REFUSED -> "订单合同已审批";
            case DATA_ASSET_COMPLETED -> "订单合同已完成";
            case DATA_ASSET_TERMINATE -> "订单合同已终止";
            case HEARTBEAT -> "心跳";
            case TOTAL_COUNT -> "全部消息数量";
            default -> "";

        };
    }

    public static String ofStatusName(SSEMessageTypeEnum sseMessageTypeEnum) {
        return switch (sseMessageTypeEnum) {
            case DATA_ASSET_APPROVAL -> "待审批";
            case DATA_ASSET_APPROVED -> "已审批通过";
            case DATA_ASSET_REFUSED -> "已审批拒绝";
            case DATA_ASSET_COMPLETED -> "已完成";
            case DATA_ASSET_TERMINATE -> "已终止";
            case HEARTBEAT -> "心跳";
            case TOTAL_COUNT -> "全部消息数量";
            default -> "";
        };
    }

}
