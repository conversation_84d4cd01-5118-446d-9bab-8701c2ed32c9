package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderRecordDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderRecordsResp implements Serializable {

    @Schema(description = "订单合同审批记录列表")
    List<OrderRecordDTO> data;

    Long total;
}
