package com.ailpha.ailand.dataroute.endpoint.user.domain;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@Schema(description = "用户信息")
public class UserDTO implements UserDetails {
    @Schema(description = "用户ID")
    @Alias("userId")
    String id;
    @Schema(description = "用户ID(数瀚)")
    @Alias("userIdShuhan")
    String idShuhan;
    @Schema(description = "用户账号")
    String username;
    @Schema(description = "用户名称")
    @Alias("name")
    String realName;
    @Schema(description = "用户手机号")
    @Alias("mobile")
    String phone;
    @Schema(description = "邮箱")
    String email;
    @JsonIgnore
    @Alias("password")
    String securityCode;
    List<String> roleName;
    @Schema(description = "所属企业信息")
    CompanyDTO company;
    Boolean enable;
    @Schema(description = "有效期")
    Date expireDate;
    Boolean resetInitPwd;
    @Schema(description = "经办人身份核验是否通过")
    Boolean reviewPass;

    String identityId;
    String identityCode;

    @Override
    public boolean isEnabled() {
        return enable;
    }

    @Override
    public boolean isAccountNonExpired() {
        return ObjectUtil.isNull(expireDate) || expireDate.after(DateUtil.date());
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roleName.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toList());
    }

    @Override
    public String getPassword() {
        return securityCode;
    }
}
