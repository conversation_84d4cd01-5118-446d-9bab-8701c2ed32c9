package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.NegotiateTransfer;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.NegotiateTransferRepository;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import com.dbapp.rest.exception.RestfulApiException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

@Slf4j
@RestController
@Tag(name = "数据资产统一交付")
@RequiredArgsConstructor
public class DataAssetDeliverController {

    private final DataAssetService dataAssetService;

    private final NegotiateTransferRepository negotiateTransferRepository;

    @PostMapping("/transfer/data/{transferId}")
    @Operation(summary = "POST请求API统一交付")
    public String transferApi(
            @PathVariable("transferId") String transferId,
            @RequestBody ApiImportTestVO apiImportTestVO) {

        if (StringUtils.isBlank(transferId)) {
            throw new RestfulApiException("数据接口请求参数异常");
        }
        NegotiateTransfer negotiateTransfer = AsyncManager.getInstance().executeFuture(() -> negotiateTransferRepository.getReferenceById(transferId));
        Assert.isTrue(Objects.nonNull(negotiateTransfer), "协商传输【transferId】无效");
        NegotiateDataTransferDTO.ExecutionStrategy strategy = negotiateTransfer.getTransactionExecutionStrategy();

        return dataAssetService.callDataApiProcess(AssetType.PRODUCT, strategy.getDeliverySceneId(), strategy.getSellerCompanyId(), strategy.getProductId(), apiImportTestVO, true);
    }


    @GetMapping("/transfer/data/{transferId}")
    @Operation(summary = "GET请求文件下载统一交付")
    public void transferFile(
            @PathVariable("transferId") String transferId,
            @RequestParam String accessKey,
            @RequestParam String secretKey,
            HttpServletResponse response) {

        if (StringUtils.isBlank(transferId)) {
            throw new RestfulApiException("数据接口请求参数异常");
        }
        NegotiateTransfer negotiateTransfer = AsyncManager.getInstance().executeFuture(() -> negotiateTransferRepository.getReferenceById(transferId));
        Assert.isTrue(Objects.nonNull(negotiateTransfer), "协商传输【transferId】无效");
        NegotiateDataTransferDTO.ExecutionStrategy strategy = negotiateTransfer.getTransactionExecutionStrategy();

        dataAssetService.download(AssetType.PRODUCT, strategy.getDeliverySceneId(), strategy.getSellerCompanyId(), strategy.getProductId(), accessKey, secretKey, true, response);
    }

}
