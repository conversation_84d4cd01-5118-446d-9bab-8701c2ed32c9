package com.ailpha.ailand.dataroute.endpoint.common.utils;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

@Getter
@Setter
public class ReadLinesFileProcessor {

    private RowProcessor rowProcessor;

    private Charset sourceEncoding = StandardCharsets.UTF_8;

    private Charset targetEncoding = StandardCharsets.UTF_8;

    private boolean skipFirstLine = false;

    private String header;

    public ReadLinesFileProcessor() {
    }

    public ReadLinesFileProcessor(RowProcessor rowProcessor) {
        this.rowProcessor = rowProcessor;
    }

    public int processFileByRows(File source, File target) throws IOException {

        BufferedReader br = null;
        BufferedWriter bw = null;
        int i;
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(source), sourceEncoding));
            String line;
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(target), targetEncoding));
            if (skipFirstLine) {
                br.readLine();
            }
            //为了避免多append一个换行符，需要这样子处理
            String lastLine = br.readLine();
            if (StringUtils.isEmpty(lastLine)) {
                return 0;
            }
            i = 1;
            if (StringUtils.isNotBlank(header)) {
                bw.append(header).append("\n");
            }
            while ((line = br.readLine()) != null) {
                bw.append(rowProcessor == null ? lastLine : rowProcessor.processRowString(lastLine));
                bw.append("\n");
                lastLine = line;
                i++;
            }
            bw.append(rowProcessor == null ? lastLine : rowProcessor.processRowString(lastLine));
        } catch (FileNotFoundException e) {
            return 0;
        } finally {
            try {
                if (br != null)
                    br.close();
            } finally {
                if (bw != null)
                    bw.close();
            }
        }
        return i;
    }

}
