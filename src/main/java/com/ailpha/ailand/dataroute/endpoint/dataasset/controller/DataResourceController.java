package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.ResolutionResponseDataResource;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Slf4j
@RestController
@Tag(name = "数据资产-数据资源")
@RequiredArgsConstructor
@RequestMapping("data-resource")
@PreAuthorize("hasAuthority('TRADER')")
public class DataResourceController {

    private final DataResourceService dataResourceService;
    private final HubShuHanApiClient hubShuHanApiClient;

    @PostMapping()
    @Operation(summary = "数据资源列表")
    public SuccessResponse<List<DataResourceListVO>> allDataAsset(@RequestBody DataResourceListQuery dataResourceListQuery) {
        UserDTO currentUser = LoginContextHolder.currentUser();
        return dataResourceService.allDataAssets((int) dataResourceListQuery.getNum(), (int) dataResourceListQuery.getSize(), specification -> {
            specification = DataResource.dataResourceNameLike(specification, dataResourceListQuery.getResourceName());
            specification = DataResource.dataTypeIs(specification, dataResourceListQuery.getDataType());
            specification = DataResource.userIdIs(specification, currentUser.getId());
            specification = DataResource.itemStatusIs(specification, dataResourceListQuery.getItemStatus());
            return specification;
        });
    }

    @GetMapping("{dataResourceId}")
    @Operation(summary = "获取数据资源详情")
    @Parameters({
            @Parameter(name = "dataResourceId", description = "数据资源id", in = ParameterIn.PATH)
    })
    public SuccessResponse<DataResourceVO> getDataAsset(@PathVariable("dataResourceId") String dataResourceId) {
        DataResourceVO resourceVO = dataResourceService.getDataResource(dataResourceId);
        return SuccessResponse.success(resourceVO).build();
    }

    @GetMapping("market/{dataResourcePlatformId}")
    @Operation(summary = "获取数据资源(数据目录)详情")
    @Parameters({
            @Parameter(name = "dataResourcePlatformId", description = "数据资源全局id", in = ParameterIn.PATH)
    })
    public SuccessResponse<ResolutionResponseDataResource> getDataResourceByDataResourcePlatformId(@PathVariable("dataResourcePlatformId") String dataResourcePlatformId) {
        return SuccessResponse.success(hubShuHanApiClient.dataResourceDetail(dataResourcePlatformId)).build();
    }

    @PostMapping("registration/temporary-save")
    @OpLog(message = "数据资源登记暂存")
    @Operation(summary = "数据资源登记暂存")
    public SuccessResponse<Boolean> tempSaveRegistration(@RequestBody DataResourceRegistRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_TEMPORARY_SAVE);
        if (StringUtils.hasText(request.getId())) {
            request.setResourceName(null);
        } else {
            dataResourceService.checkNameExists(null, request.getResourceName());
        }
        dataResourceService.temporarySave(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("registration")
    @OpLog(message = "数据资源登记")
    @Operation(summary = "数据资源登记")
    public SuccessResponse<Boolean> submitRegistration(@RequestBody @Valid DataResourceRegistRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST);
        if (StringUtils.hasText(request.getId())) {
            request.setResourceName(null);
        } else {
            dataResourceService.checkNameExists(null, request.getResourceName());
        }
        dataResourceService.registration(request);
        return SuccessResponse.success(true).build();
    }

    @PutMapping("registration")
    @OpLog(message = "数据资源登记更新")
    @Operation(summary = "数据资源登记更新")
    public SuccessResponse<Boolean> updateRegistration(@RequestBody DataResourceRegistUpdateRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST_UPDATE);
        dataResourceService.updateRegistration(request);
        return SuccessResponse.success(true).build();
    }

    @DeleteMapping("registration/revoke/{dataResourceId}")
    @OpLog(message = "数据资源登记撤销")
    @Operation(summary = "数据资源登记撤销")
    public SuccessResponse<Boolean> revokeRegistration(@PathVariable String dataResourceId) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST_REVOKE);
        dataResourceService.revokeRegistration(dataResourceId);
        // NOTE: bug-174558
//        SuccessResponse<List<OrderApprovalRecord>> orderList = hubOrderRemote.selectOrderApprovalRecordWhereAssetIdInAndBeneficiaryId(AssetBeneficiaryRelDTOListReq.builder()
//                .assetIds(List.of(dataResourceId))
//                .build());
//        List<String> orderIds = null;
//        if (orderList.isSuccess() && !CollectionUtils.isEmpty(orderList.getData())) {
//            List<OrderApprovalRecord> approvalRecords = orderList.getData().stream().filter(order -> org.apache.commons.lang3.StringUtils.equals(order.getStatus(), "APPLY") || org.apache.commons.lang3.StringUtils.equals(order.getStatus(), "APPROVED"))
//                    .peek(order -> {
//                        order.setStatus(OrderStatus.TERMINATED.name());
//                        order.setChangeStatus(true);
//                    }).toList();
//            hubOrderRemote.updateOrderApprovalRecords(approvalRecords);
//            orderIds = approvalRecords.stream().map(OrderApprovalRecord::getId).collect(Collectors.toList());
//        }
//        try {
//            // TEE MPC 数据集删除、合约终止
//            orderManagerService.terminalContract(orderIds, Collections.singletonList(dataResourceId));
//        } catch (Exception ignore) {
//        }
        return SuccessResponse.success(true).build();
    }


    @DeleteMapping("delete/{dataResourceId}")
    @OpLog(message = "数据资源（暂存）删除: resourceId -> {dataResourceId}")
    @Operation(summary = "数据资源（暂存）删除")
    public SuccessResponse<Boolean> delete(@PathVariable String dataResourceId) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_DELETE);
        OPLogContext.put("dataResourceId", dataResourceId);

        dataResourceService.delete(dataResourceId);
        return SuccessResponse.success(true).build();
    }
}
