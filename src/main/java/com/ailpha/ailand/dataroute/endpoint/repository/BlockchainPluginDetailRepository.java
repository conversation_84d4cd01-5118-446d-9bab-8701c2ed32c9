package com.ailpha.ailand.dataroute.endpoint.repository;

import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface BlockchainPluginDetailRepository extends JpaRepository<BlockchainPluginDetail, Long> {
    List<BlockchainPluginDetail> findALLByStatusAndModuleType(int status, String moduleType);

    boolean existsByNameAndIdNot(String name, Long id);

    boolean existsByName(String name);
}