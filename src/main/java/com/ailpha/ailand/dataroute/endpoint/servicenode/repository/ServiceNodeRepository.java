package com.ailpha.ailand.dataroute.endpoint.servicenode.repository;

import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/7/30
 */
public interface ServiceNodeRepository extends JpaRepository<ServiceNodeInfo, String>, QuerydslPredicateExecutor<ServiceNodeInfo> {

    List<ServiceNodeInfo> findAllByServiceNodeId(String serviceNodeId);

    List<ServiceNodeInfo> findAllByCreateTimeBeforeOrderByCreateTimeDesc(Date createTime, Pageable pageable);

    List<ServiceNodeInfo> findAllByProcessStatusOrderByCreateTimeDesc(String approvalStatus);
}
