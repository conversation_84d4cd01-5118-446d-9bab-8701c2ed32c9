package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface HubShuHanDataProductApi {

    /**
     * 数据产品登记接口
     *
     * @param registRequest 数据产品基本信息
     * @return
     */
    @PostExchange("/dataProductInfoRegist")
    ShuhanResponse<DataAssetSavedVO> dataProductInfoRegist(@RequestBody DataProductSaveVM registRequest);

    /**
     * 数据产品登记更新接口
     *
     * @param dataProductUpdate 数据产品基本信息
     * @return
     */
    @PostExchange("/dataProductInfoUpdate")
    ShuhanResponse<DataAssetUpdateVO> dataProductInfoUpdate(@RequestBody DataProductUpdateVM dataProductUpdate);

    /**
     * 数据产品登记撤销接口
     *
     * @param dataProductRevoke 数据标识
     * @return 执行结果状态：0 数据产品登记撤销成功 1 执行失败
     */
    @PostExchange("/dataProductInfoRevoke")
    ShuhanResponse<DataAssetRevokeVO> dataProductInfoRevoke(@RequestBody DataAssetRevokeVM dataProductRevoke);

    /**
     * 数据产品上架更新接口
     *
     * @param dataProductPublish 数据产品基本信息
     * @return
     */
    @PostExchange("/dataProductUpdate")
    ShuhanResponse<DataProductPublishVO> dataProductUpdate(@RequestBody DataProductPublishVM dataProductPublish);

    /**
     * 数据产品标识解析
     *
     * @param resolutionRequest 标识
     * @return 数据产品信息
     */
    @PostExchange("/regionNodeResolution")
    ShuhanResponse<ResolutionResponseDataProduct> regionNodeResolutionDataProduct(@RequestBody ResolutionRequest resolutionRequest);
}
