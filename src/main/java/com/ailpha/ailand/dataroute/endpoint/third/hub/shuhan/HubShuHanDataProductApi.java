package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface HubShuHanDataProductApi {

    /**
     * 数据产品登记接口
     *
     * @param registRequest 数据产品基本信息
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductInfoRegist")
    ShuhanResponse<DataAssetSavedVO> dataProductInfoRegist(@RequestBody DataProductSaveVM registRequest);

    /**
     * 数据产品登记更新接口
     *
     * @param dataProductUpdate 数据产品基本信息
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductInfoUpdate")
    ShuhanResponse<DataAssetUpdateVO> dataProductInfoUpdate(@RequestBody DataProductUpdateVM dataProductUpdate);

    /**
     * 数据产品登记撤销接口
     *
     * @param dataProductRevoke 数据标识
     * @return 执行结果状态：0 数据产品登记撤销成功 1 执行失败
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductInfoRevoke")
    ShuhanResponse<DataAssetRevokeVO> dataProductInfoRevoke(@RequestBody DataAssetRevokeVM dataProductRevoke);

    /**
     * 数据产品上架接口
     *
     * @param dataProductPublish 数据产品基本信息
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductPublish")
    ShuhanResponse<DataProductPublishVO> dataProductPublish(@RequestBody DataProductPublishVM dataProductPublish);

    /**
     * 数据产品上架更新接口
     *
     * @param dataProductPublish 数据产品基本信息
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductUpdate")
    ShuhanResponse<DataProductPublishVO> dataProductUpdate(@RequestBody DataProductPublishVM dataProductPublish);

    /**
     * 数据产品下架接口
     *
     * @param dataProductUnPublish 数据上架ID
     * @return 执行结果状态
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataProduct/dataProductUnPublish")
    ShuhanResponse<DataAssetUpdateVO> dataProductUnpublish(@RequestBody DataProductUnPublishVM dataProductUnPublish);

    /**
     * 数据目录查询接口
     *
     * @param catalogQuery 数据目录查询参数
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataCatalogQuery")
    ShuhanResponse<IPage<CatalogQueryDataProduct>> dataCatalogQuery(@RequestBody CatalogQueryVM catalogQuery);
}
