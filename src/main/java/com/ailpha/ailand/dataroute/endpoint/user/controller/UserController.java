package com.ailpha.ailand.dataroute.endpoint.user.controller;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple4;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.tenant.service.TenantService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserRole;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.UpdateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserListResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.*;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("user")
@RequiredArgsConstructor
@Tag(name = "用户管理")
public class UserController {

    private final UserService userService;
    private final TenantService tenantService;


    @GetMapping("/current")
    @Operation(summary = "当前用户信息")
    public SuccessResponse<CurrentUserResponse> current() {
        return SuccessResponse.success(userService.currentUser()).build();
    }

    @GetMapping("/{userId}")
    @Operation(summary = "用户详情")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN','TRADER')")
    public SuccessResponse<UserDetailsResponse> userDetails(@PathVariable String userId) {
        return ApiResponse.success(userService.userDetail(userId)).build();
    }

    @PostMapping("page")
    @Operation(summary = "查询用户列表")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
    public SuccessResponse<List<UserListResponse>> userList(@RequestBody UserPageRequest request) {
        return userService.userList(request);
    }

    @GetMapping("check-account-repeat")
    @Operation(method = "post", summary = "用户账号重复检查", description = "true-重名了 false-没有重名")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
    public SuccessResponse<Boolean> checkName(String userId, String account) {
        return SuccessResponse.success(userService.checkNameRepeat(userId, account)).build();
    }

    @PostMapping
    @Operation(method = "post", summary = "新增用户")
    @OpLog(message = "新增用户")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
    public SuccessResponse<String> addUser(@RequestBody AddUserRequest request) {
        OPLogContext.putOpType(InternalOpType.ADD_USER);
        if (LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN))
            throw new RestfulApiException("非法操作：不允许创建用户");
        Tuple4<String, User, List<UserRole>, Boolean> addUser = userService.addUser(request);

        if (LoginContextHolder.currentUserRole().contains(RoleEnums.COMPANY_ADMIN)) {
            ThreadUtil.execAsync(() -> userService.updatePublic(addUser.second, addUser.third));
        }

        return ApiResponse.success(addUser.first).build();
    }

//    @PostMapping("/companyAdmin/register")
//    @Operation(method = "post", summary = "注册企业管理员用户")
//    public SuccessResponse<CompanyUserRegisterResponse> registerCompanyUser(@RequestBody CompanyUserRegisterRequest request) {
//        Tuple2<Tuple4<String, User, UserRole, Boolean>, Company> registerCompanyUser = userService.registerCompanyUser(request);
//        if (registerCompanyUser.first.four) {
//            ThreadUtil.execAsync(() -> {
//                tenantService.addTenantSchema("tenant_" + registerCompanyUser.second.getId());
//                userService.updateSchema(registerCompanyUser.first.second, registerCompanyUser.first.third);
//                SpringUtil.getBean(CompanyService.class).updateTenantSchema(registerCompanyUser.second);
//            });
//        }
//        return ApiResponse.success(CompanyUserRegisterResponse.builder().pwd(registerCompanyUser.first.first)
//                .loginUrl(JSONUtil.parseObj(registerCompanyUser.first.second.getExt()).getStr("url")).build()).build();
//    }

    @PutMapping
    @Operation(summary = "编辑用户")
    @OpLog(message = "编辑用户信息")
    public SuccessResponse<Boolean> updateUser(@RequestBody @Valid UpdateUserRequest request) {
        OPLogContext.putOpType(InternalOpType.UPDATE_USER_INFO);
        Tuple2<Boolean, User> data = userService.updateUser(request);
        if (data.first) {
            ThreadUtil.execAsync(() -> userService.updatePublic(data.second));
        }
        userService.reloadUserDetails();
        return ApiResponse.success(data.first).build();
    }

    @PostMapping("/resetPwd/{userId}")
    @Operation(summary = "重置密码")
    @OpLog(message = "重置密码")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
    public SuccessResponse<String> resetPwd(@PathVariable String userId) {
        OPLogContext.putOpType(InternalOpType.RESET_USER_PWD);
        Tuple2<String, User> resetPwd = userService.resetPwd(userId);
        ThreadUtil.execAsync(() -> userService.updatePublic(resetPwd.second));
        return ApiResponse.success(resetPwd.first).build();
    }

    @DeleteMapping("{userId}")
    @Operation(summary = "删除用户")
    @OpLog(message = "删除用户")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
    public SuccessResponse<Boolean> deleteUser(@PathVariable String userId) {
        OPLogContext.putOpType(InternalOpType.DELETE_USER);
        return ApiResponse.success(userService.deleteUser(userId)).build();
    }


    @PostMapping("/submit/delegateInfo")
    @Operation(summary = "提交经办人信息")
//    @OpLog(message = "提交经办人信息")
    @PreAuthorize("hasAnyAuthority('TRADER')")
    public SuccessResponse<Boolean> submitDelegateInfo(@RequestBody DelegateInfoRequest request) {
//        OPLogContext.putOpType(InternalOpType.RESET_USER_PWD);
        userService.submitDelegateToReview(request);
        return ApiResponse.success(true).build();
    }
}
