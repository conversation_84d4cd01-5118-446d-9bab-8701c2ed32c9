package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiDetailVO extends OpenApiListVO {

    @Schema(description = "api id")
    String id;

    @Schema(description = "api url")
    String apiUrl;

    @Schema(description = "结果集字段")
    List<OpenApiResultVO> result;
}
