package com.ailpha.ailand.dataroute.endpoint.dataAssetApproval;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.service.DataAssetApprovalService;
import com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.vo.DataAssetIdReq;
import com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.vo.DataAssetIdsReq;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataProductListQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataResourceListQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/28
 */
@RestController
@Tag(name = "数据资产审批")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('COMPANY_ADMIN')")
@RequestMapping("/data-asset-approval")
public class DataAssetApprovalController {

    private final DataResourceService dataResourceService;
    private final DataProductService dataProductService;
    private final DataAssetApprovalService dataAssetApprovalService;

    @PostMapping("/resource/list")
    @Operation(summary = "数据资源登记审批列表")
    public SuccessResponse<List<DataResourceListVO>> allDataResource(@RequestBody DataResourceListQuery dataResourceListQuery) {
        return dataResourceService.allDataAssets((int) dataResourceListQuery.getNum(), (int) dataResourceListQuery.getSize(), specification -> {
            specification = DataResource.dataResourceNameLike(specification, dataResourceListQuery.getResourceName());
            specification = DataResource.itemStatusIs(specification, dataResourceListQuery.getItemStatus());
            specification = DataResource.registrationSubmitTimeAfter(specification, dataResourceListQuery.getRegistrationSubmitTimeStart());
            specification = DataResource.registrationSubmitTimeBefore(specification, dataResourceListQuery.getRegistrationSubmitTimeEnd());
            specification = DataResource.registrationTimeAfter(specification, dataResourceListQuery.getRegistrationTimeStart());
            specification = DataResource.registrationTimeBefore(specification, dataResourceListQuery.getRegistrationTimeEnd());
            specification = DataResource.itemStatusNot(specification, "item_status0");
            return specification;
        });
    }

    @GetMapping("/resource/detail")
    @Operation(summary = "数据资源审批详情")
    @Parameters({
            @Parameter(name = "dataResourceId", description = "数据资源id", in = ParameterIn.QUERY),
    })
    public SuccessResponse<DataResourceVO> getDataResource(@RequestParam(value = "dataResourceId") String dataResourceId) {
        return SuccessResponse.success(dataResourceService.getDataResource(dataResourceId)).build();
    }

    @PostMapping("/product/list")
    @Operation(summary = "数据产品登记审批列表")
    public SuccessResponse<List<DataProductListVO>> allDataAsset(@RequestBody DataProductListQuery dataProductListQuery) {
        return dataProductService.allDataAssets((int) dataProductListQuery.getNum(), (int) dataProductListQuery.getSize(), specification -> {
            specification = DataProduct.dataProductNameLike(specification, dataProductListQuery.getProductName());
            specification = DataProduct.itemStatusIs(specification, dataProductListQuery.getItemStatus());
            specification = DataProduct.registrationTimeAfter(specification, dataProductListQuery.getRegistrationTimeStart());
            specification = DataProduct.registrationTimeBefore(specification, dataProductListQuery.getRegistrationTimeEnd());
            specification = DataProduct.registrationSubmitTimeAfter(specification, dataProductListQuery.getRegistrationSubmitTimeStart());
            specification = DataProduct.registrationSubmitTimeBefore(specification, dataProductListQuery.getRegistrationSubmitTimeEnd());
            specification = DataProduct.deliveryModeIs(specification, dataProductListQuery.getDeliveryMode());
            specification = DataProduct.itemStatusNot(specification, "item_status0");
            return specification;
        });
    }

    @PostMapping("/product-publish/list")
    @Operation(summary = "数据产品上架审批列表")
    public SuccessResponse<List<DataProductListVO>> allDataAssetPublish(@RequestBody DataProductListQuery dataProductListQuery) {
        return dataProductService.allDataAssets((int) dataProductListQuery.getNum(), (int) dataProductListQuery.getSize(), specification -> {
            specification = DataProduct.dataProductNameLike(specification, dataProductListQuery.getProductName());
            specification = DataProduct.publishStatusIs(specification, dataProductListQuery.getPublishStatus());
            specification = DataProduct.publishTimeAfter(specification, dataProductListQuery.getPublishTimeStart());
            specification = DataProduct.publishTimeBefore(specification, dataProductListQuery.getPublishTimeEnd());
            specification = DataProduct.publishSubmitTimeAfter(specification, dataProductListQuery.getPublishSubmitTimeStart());
            specification = DataProduct.publishSubmitTimeBefore(specification, dataProductListQuery.getPublishSubmitTimeEnd());
            specification = DataProduct.deliveryModeIs(specification, dataProductListQuery.getDeliveryMode());
            specification = DataProduct.publishStatusNot(specification, "0");
            return specification;
        });
    }

    @GetMapping("/product/detail")
    @Operation(summary = "数据产品审批详情")
    @Parameters({
            @Parameter(name = "dataAssetId", description = "数据资产id", in = ParameterIn.QUERY),
    })
    public SuccessResponse<DataProductVO> getDataAsset(@RequestParam(value = "dataProductId") String dataProductId) {
        return SuccessResponse.success(dataProductService.getDataAssetById(dataProductId)).build();
    }

    @PostMapping("registration/approval")
    @Operation(summary = "数据资产（资源、产品）登记审批")
    @OpLog(message = "审批通过: {pass}，资产ID：{dataAssetId}")
    public SuccessResponse<Boolean> approve(@Valid @RequestBody DataAssetIdReq dataAssetIdReq) {
        OPLogContext.putOpType(InternalOpType.ASSET_AUDIT_REGISTRATION);
        OPLogContext.put("pass", Boolean.TRUE.equals(dataAssetIdReq.getPass()));
        OPLogContext.put("dataAssetId", dataAssetIdReq.getDataAssetId());
        if (Boolean.TRUE.equals(dataAssetIdReq.getPass())) {
            dataAssetApprovalService.approve(dataAssetIdReq.getDataAssetId(), dataAssetIdReq.getType());
        } else {
            dataAssetApprovalService.reject(dataAssetIdReq.getDataAssetId(), dataAssetIdReq.getType());
        }
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PostMapping("registration/approval-batch")
    @Operation(summary = "数据资产（资源、产品）登记审批（批量）")
    @OpLog(message = "数据资产（资源、产品）登记审批（批量）通过: {pass}，资产ID：{dataAssetIds}")
    public SuccessResponse<Boolean> approve(@Valid @RequestBody DataAssetIdsReq assetIdsReq) {
        OPLogContext.putOpType(InternalOpType.ASSET_AUDIT_REGISTRATION);
        OPLogContext.put("pass", Boolean.TRUE.equals(assetIdsReq.getPass()));
        OPLogContext.put("dataAssetId", assetIdsReq.getDataAssetIds());
        for (String dataAssetId : assetIdsReq.getDataAssetIds()) {
            if (Boolean.TRUE.equals(assetIdsReq.getPass())) {
                dataAssetApprovalService.approve(dataAssetId, assetIdsReq.getType());
            } else {
                dataAssetApprovalService.reject(dataAssetId, assetIdsReq.getType());
            }
        }
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PutMapping("publish/approval")
    @Operation(summary = "数据产品上架审批")
    @OpLog(message = "审批通过: {pass}，资产ID：{dataAssetId}")
    public SuccessResponse<Boolean> approvePublish(@RequestBody DataAssetIdReq dataAssetIdReq) {
        OPLogContext.putOpType(InternalOpType.ASSET_AUDIT_PUBLISH);
        OPLogContext.put("pass", Boolean.TRUE.equals(dataAssetIdReq.getPass()));
        OPLogContext.put("dataAssetId", dataAssetIdReq.getDataAssetId());
        dataAssetApprovalService.processPublishApproval(dataAssetIdReq.getDataAssetId(), dataAssetIdReq.getType(), Boolean.TRUE.equals(dataAssetIdReq.getPass()));
        return SuccessResponse.success(true).build();
    }

    @PutMapping("publish/approval-batch")
    @Operation(summary = "数据产品上架审批(批量)")
    @OpLog(message = "数据产品上架审批(批量)通过: {pass}，资产ID：{dataAssetId}")
    public SuccessResponse<Boolean> approvePublishBatch(@RequestBody DataAssetIdsReq dataAssetIdsReq) {
        OPLogContext.putOpType(InternalOpType.ASSET_AUDIT_PUBLISH);
        OPLogContext.put("pass", Boolean.TRUE.equals(dataAssetIdsReq.getPass()));
        OPLogContext.put("dataAssetId", dataAssetIdsReq.getDataAssetIds());
        for (String dataAssetId : dataAssetIdsReq.getDataAssetIds()) {
            dataAssetApprovalService.processPublishApproval(dataAssetId, dataAssetIdsReq.getType(), Boolean.TRUE.equals(dataAssetIdsReq.getPass()));
        }
        return SuccessResponse.success(true).build();
    }
}
