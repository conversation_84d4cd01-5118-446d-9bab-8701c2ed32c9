package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.biz.api.dataset.ApiImportExtendBO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.*;
import com.ailpha.ailand.dataroute.endpoint.third.request.UpdateFreqBO;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 2023/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApiImportFormVO {

    /**
     * 数据集名称
     */
    String datasetName;

    /**
     * 数据集调用名称
     */
    private String executeCallName;

    /**
     * 采样数量
     */
    private Integer samplingRowCount;

    /**
     * 业务领域
     */
    List<String> purposeList;

    /**
     * 更新类型
     */
    SyncWayEnum updateType;

    @JsonUnwrapped
    UpdateFreqBO updateFreqBO;

    /**
     * 审批人ID列表
     */
    List<String> approverIds;

    /**
     * 可见性
     */
    VisibilityEnum visibility;

    /**
     * 可见人ID列表
     */
    List<String> visibleUserIds;

    /**
     * 调试数据来源
     */
    DebugDataSourceEnum debugDataSource;

    /**
     * 字段分隔符
     */
    String separator;

    /**
     * 是否包含表头 1是0不是
     */
    Integer hasHeader;

    /**
     * 调试文件路径
     */
    String debugDataFilePath;

    /**
     * 调试数据名称
     */
    String debugDataFileName;

    /**
     * 数据源类型：FILE（文件）DATABASE（数据库）
     */
    DatasetSourceTypeEnums datasetSourceType;

    /**
     * 数据集描述
     */
    List<DataSchemaBO> dataSchema;

    /**
     * 请求地址
     */
    String url;

    /**
     * 请求方式
     */
    MethodEnum method;

    /**
     * Params参数
     */
    List<ParamsBO> params;

    /**
     * Body参数
     */
    String body;

    /**
     * Body类型
     */
    BodyTypeEnum bodyType;

    /**
     * Headers参数
     */
    List<ParamsBO> headers;

    /**
     * 返回响应体（后端解析用）
     */
    List<ResponseBO> response;

    /**
     * 返回响应体（前端回显用）
     */
//    String responseEcho;

    /**
     * 元数据路径及类型
     */
    List<PathTypeBO> dataPath;

    /**
     * 扩展配置
     */
    ApiImportExtendBO extend;

    /**
     * 过期时间
     */
    Date expireDate;

    /**
     * 生成调试数据的策略
     */
    String strategy;
}
