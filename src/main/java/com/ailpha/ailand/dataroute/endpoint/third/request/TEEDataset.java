package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.third.constants.BodyTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.MethodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.PathTypeBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.ResponseBO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.core.io.FileSystemResource;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/12/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TEEDataset implements Serializable {
    /**
     * 资产类型
     */
    AssetType assetType;
    /**
     * 资产ID
     */
    String assetId;
    String dataProductPlatformId;
    /**
     * 资产名称
     */
    String name;
    /**
     * 交付类型（离线/在线）
     */
    DeliveryMode type;
    /**
     * dataSchema
     */
    List<DataSchemaBO> dataSchema;
    /**
     * 原始数据文件路径
     */
    String filePath;
    /**
     * 调试数据来源类型
     */
    DebugDataSourceEnum debugDataSource;
    /**
     * 调试数据文件路径 -> 需要转文件流
     */
    FileSystemResource debugFile;
    /**
     * 调试数据分隔符
     */
    String separator;
    /**
     * 调试数据是否包含表头
     */
    Integer hasHeader;
    /**
     * 业务领域
     */
    String purposeList;
    /**
     * 请求地址
     */
    String url;
    /**
     * 请求方式
     */
    MethodEnum method;
    /**
     * Params参数
     */
    List<ParamsBO> params;
    /**
     * Body参数
     */
    String body;
    /**
     * Body类型
     */
    BodyTypeEnum bodyType;
    /**
     * Headers参数
     */
    List<ParamsBO> headers;
    /**
     * 返回响应体（后端解析用）
     */
    List<ResponseBO> response;
    /**
     * 元数据路径及类型
     */
    List<PathTypeBO> dataPath;
    /**
     * 创建用户ID
     */
    String userId;
    /**
     * 创建用户名
     */
    String userName;
    /**
     * 连接器ID
     */
    String routerId;
    /**
     * 企业ID
     */
    String companyId;
    /**
     * 前置机虚拟IP+端口（xxx:6112）
     */
    String executorUrl;

    @ApiModelProperty(value = "数据类型 UNSTRUCTURED STRUCTURE")
    String dataStructureType = "STRUCTURE";

    @ApiModelProperty(value = "文件后缀名 .tar.gz, .zip .csv")
    String datasetFileType;
    /**
     * 是否改写响应体
     */
    Boolean extractResponse;
    @Schema(description = "返回响应体（前端回显用）")
    String responseEcho;

    /**
     * 扩展字段
     */
    Extend ext;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Extend implements Serializable {
        /**
         * 数据接入方式：API、数据库、文件
         */
        SourceType source;
    }

    public MultiValueMap<String, Object> toFormDataBody() {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("assetId", this.assetId);
        body.add("dataProductPlatformId", this.dataProductPlatformId);
        body.add("name", this.name);
        body.add("type", this.type);
        body.add("dataSchema", this.dataSchema);
        body.add("filePath", this.filePath);
        body.add("debugDataSource", this.debugDataSource);
        body.add("debugFile", this.debugFile);
        body.add("separator", this.separator);
        body.add("hasHeader", this.hasHeader);
        body.add("purposeList", this.purposeList);
        body.add("url", this.url);
        body.add("method", this.method);
        body.add("params", this.params);
        body.add("body", this.body);
        body.add("bodyType", this.bodyType);
        body.add("headers", this.headers);
        body.add("response", this.response);
        body.add("dataPath", this.dataPath);
        body.add("userId", this.userId);
        body.add("userName", this.userName);
        body.add("routerId", this.routerId);
        body.add("companyId", this.companyId);
        body.add("executorUrl", this.executorUrl);
        body.add("datasetFileType", this.datasetFileType);
        body.add("dataStructureType", this.dataStructureType);
        body.add("extractResponse", this.extractResponse);
        body.add("responseEcho", this.responseEcho);
        body.add("ext", this.ext);
        return body;
    }
}
