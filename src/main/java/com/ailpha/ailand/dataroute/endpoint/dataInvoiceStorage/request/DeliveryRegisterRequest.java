package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * 交付登记
 *
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DeliveryRegisterRequest implements Serializable {

    @Schema(description = "第三方交易ID  —— 【交付场景ID+资产ID】")
    String thirdTransactionId;
    @Schema(description = "交付方式", allowableValues = "数据文件,API,平台", example = "API")
    String deliveryType;
    @Schema(description = "数据Hash")
    String dataHash;
    @Schema(description = "第三方平台扩展信息json")
    String extend;
    @Schema(description = "第三方平台业务ID  —— 【交付场景ID+资产ID】")
    String thirdBusinessId;
}
