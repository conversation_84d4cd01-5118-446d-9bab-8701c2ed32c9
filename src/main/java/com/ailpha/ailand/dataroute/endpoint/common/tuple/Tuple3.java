package com.ailpha.ailand.dataroute.endpoint.common.tuple;

import java.util.Objects;

public final class Tuple3<F, S, T> {
    public F first;
    public S second;
    public T third;

    public Tuple3() {
    }

    public Tuple3(F first, S second, T third) {
        this.first = first;
        this.second = second;
        this.third = third;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Tuple3<?, ?, ?> tuple3 = (Tuple3<?, ?, ?>) o;
        if (!Objects.equals(first, tuple3.first)) return false;
        if (!Objects.equals(second, tuple3.second)) return false;
        return Objects.equals(third, tuple3.third);
    }

    @Override
    public int hashCode() {
        int result = first != null ? first.hashCode() : 0;
        result = 31 * result + (second != null ? second.hashCode() : 0);
        result = 31 * result + (third != null ? third.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Tuple3{" +
                "first=" + first +
                ", second=" + second +
                ", third=" + third +
                '}';
    }

}
