package com.ailpha.ailand.dataroute.endpoint.order.remote.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractPageQuery {

    private Integer pageSize;          // 分页大小
    private Integer pageNum;           // 当前页数
    private String contractNo;        // 合同唯一标识
    private String srcPlatformId;     // 来源平台标识
    private String srcUserId;         // 来源用户标识
    private String srcContractNo;      // 来源平台合同编号
    private String contractName;       // 合同名称
    private String producerId;        // 提供方标识
    private String consumerId;         // 使用方标识
    private String contractStatus;     // 合同状态（附录字典项 contract status）
    private String producerCode;      // 提供方编码
    private String consumerCode;       // 使用方编码

}
