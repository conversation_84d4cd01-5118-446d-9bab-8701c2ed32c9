package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DeliveryListResponse implements Serializable {
    @Schema(description = "交付ID")
    private String deliveryId;
    @Schema(description = "交付场景")
    private String deliveryType;
    @Schema(description = "交付方式 国标 01、02、03")
    private String deliveryMethod;
    @Schema(description = "交易ID")
    private String transactionId;
    @Schema(description = "交易名称")
    private String transactionName;
    @Schema(description = "卖家企业ID")
    private String sellerEnterpriseId;
    @Schema(description = "卖家企业名称")
    private String sellerEnterpriseName;
    @Schema(description = "买家企业ID")
    private String buyerEnterpriseId;
    @Schema(description = "买家企业名称")
    private String buyerEnterpriseName;
    @Schema(description = "场景证书ID")
    private String sceneId;
    @Schema(description = "场景名称")
    private String sceneName;
    @Schema(description = "交付登记时间 yyyy/MM/dd HH:mm:ss")
    private String deliveryCreateTime;
    @Schema(description = "存证ID")
    private String evidenceId;
    @Schema(description = "交付ID（第三方）")
    // 场景ID:资产ID
    private String thirdBusinessId;
    @Schema(description = "第三方平台卖家企业ID")
    private String thirdSellerEnterpriseId;
    @Schema(description = "第三方平台买家企业ID")
    String thirdBuyerEnterpriseId;
    @Schema(description = "第三方平台扩展信息json")
    private String extend;
    @Schema(description = "数据资产名称")
    String dataAssetName;
    @Schema(description = "数据资产类型")
    AssetType type;
}
