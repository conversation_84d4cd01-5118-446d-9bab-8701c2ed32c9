package com.ailpha.ailand.dataroute.endpoint.tenant.repository;

import com.ailpha.ailand.dataroute.endpoint.tenant.entity.Tenant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

public interface TenantRepository extends JpaRepository<Tenant, Long>, QuerydslPredicateExecutor<Tenant> {
    Tenant findByTenantCode(String tenantCode);
    Tenant findBySchemaName(String schemaName);
    boolean existsByTenantCode(String tenantCode);
    boolean existsBySchemaName(String schemaName);
}