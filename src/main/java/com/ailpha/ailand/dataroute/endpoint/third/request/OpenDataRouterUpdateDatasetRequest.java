package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * @author: sunsas.yu
 * @date: 2024/12/10 13:57
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenDataRouterUpdateDatasetRequest {

    @NotNull
    @ApiModelProperty(value = "资产id", required = true)
    String assetId;

    @ApiModelProperty(value = "分隔符")
    String separator;

    @ApiModelProperty(value = "是否包含表头")
    Integer hasHeader;

    @ApiModelProperty("元数据类型，默认结构化")
    String dataStructureType = "STRUCTURE";

    @ApiModelProperty(value = "调试数据")
    MultipartFile file;

}
