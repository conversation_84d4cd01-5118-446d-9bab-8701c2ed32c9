package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiEncryptTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginApiDetailRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 14:01
 * @Description:
 */
@Data
@Schema(description = "配置详情")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PluginDetailVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "插件名称")
    private String name;

    /**
     * 插件类型
     */
    @Schema(description = "插件类型")
    private PluginApiTypeEnums type;

    /**
     * 对接域名
     */
    @Schema(description = "对接域名")
    private String domain;

    /**
     * 插件加密方式
     */
    @Schema(description = "插件加密方式")
    private PluginApiEncryptTypeEnums encryptType;

    @Schema(description = "插件加密凭证")
    private PluginCredentials plugCredentials;

    /**
     * 插件状态
     */
    @Schema(description = "插件状态 true-启用")
    private Boolean status;

    @Schema(description = "插件关联接口信息")
    private List<PluginApiDetailRequest> pluginApiDetails;
}
