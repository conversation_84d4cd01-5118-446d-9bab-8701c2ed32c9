package com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou;

import lombok.Data;

import java.io.Serializable;

@Data
public class OperatorIdentityQuery implements Serializable {

    private static final long serialVersionUID = -6664713570587514848L;
    /**
     * 用户唯一标识
     */
    private String userName;

    /**
     * 主体主键ID
     */
    private String entityId;

    /**
     * 身份主键ID
     */
    private String identityId;

    /**
     * 身份唯一标识
     */
    private String identityCode;

    String entityCode;
    /**
     * 主体名称
     */
    private String entityName;

    /**
     * 统一社会信用代码
     */
    private String socialCode;

    /**
     * 主体性质
     * 1-企业法人
     * 2-机关事业单位
     * 4-基金会
     * 5-个体工商户
     * 6-社团法人
     * 8-民办非企业
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.EntityNature
     */
    private String entityNature;

    /**
     * 主体类型
     * 0-个人
     * 1-组织
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.EntityType
     */
    private String entityType;

    /**
     * 经营期起始
     */
    private String entityValidStartTime;

    /**
     * 经营期截止
     */
    private String entityValidEndTime;

    /**
     * 法人姓名
     */
    private String legalName;

    /**
     * 法人证件类型
     * 0-身份证
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.CertType
     */
    private String legalCertType;

    /**
     * 法人证件号码
     */
    private String legalCertNo;

    /**
     * 主体状态
     * 0-可用
     * 1-不可用
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.EntityStatus
     */
    private String entityStatus;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 证件类型
     * 0-身份证
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.CertType
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 证件有效期起始
     */
    private String validStartTime;

    /**
     * 证件有效期截止
     */
    private String validEndTime;

    /**
     * 身份状态
     * 0-可用
     * 1-不可用
     * @see com.ailpha.ailand.ganzhouinterface.hub.ganzhou.enums.IdentityStatus
     */
    private String identityStatus;

    /**
     * 实名等级
     */
    private String realnameAuthStatus;

    /**
     * 实名方式
     */
    private String realnameAuthMethod;

    /**
     * 实名时间
     */
    private String realnameAuthTime;
}