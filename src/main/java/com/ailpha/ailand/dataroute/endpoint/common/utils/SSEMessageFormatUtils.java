package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.ailpha.ailand.dataroute.endpoint.common.enums.ChargingWayEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.MeteringWayEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.common.enums.TraderRoleEnums;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 13:25
 * @Description:
 */
public class SSEMessageFormatUtils {
    private final static String NORMAL_MESSAGE_FORMAT = "【%s】%s%s，请%s前往查看\n" +
            "%s：%s；计量方式：%s,%s；计费方式：%s";

    private final static String TOTAL_MESSAGE = "您有%s条未读消息";

    /**
     * 功能描述:
     *
     * @param type              type SSEMessageTypeEnum 消息类型
     * @param dataNo            dataNo 订单号
     * @param target            TraderRoleEnums 目标角色
     * @param assertName        assertName 数据资产名称
     * @param applyCompanyName  applyCompanyName 申请人企业名称
     * @param measurementMethod MeteringWayEnums 计费类型
     * @param chargingMethods   ChargingWayEnums 付费类型
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/11/18 14:45
     */
    public static String formatMessage(SSEMessageTypeEnum type, String dataNo, TraderRoleEnums target,
                                       String assertName, String applyCompanyName, MeteringWayEnums measurementMethod, Integer measurementTime, ChargingWayEnums chargingMethods) {

        return String.format(NORMAL_MESSAGE_FORMAT,
                SSEMessageTypeEnum.ofName(type), dataNo,
                SSEMessageTypeEnum.ofStatusName(type), target.getName(),
                assertName, applyCompanyName, measurementMethod.getName(), measurementTime, chargingMethods.getName());
    }

    public static String formatMessage(SSEMessageTypeEnum type, String dataNo, TraderRoleEnums target,
                                       String assertName, String applyCompanyName, String measurementMethod, String measurementTime, String chargingMethods) {

        return String.format(NORMAL_MESSAGE_FORMAT,
                SSEMessageTypeEnum.ofName(type), dataNo,
                SSEMessageTypeEnum.ofStatusName(type), target.getName(),
                assertName, applyCompanyName, measurementMethod, measurementTime, chargingMethods);
    }

    public static String formatTotalMessage(Integer size) {
        return String.format(TOTAL_MESSAGE, String.valueOf(size));
    }

}
