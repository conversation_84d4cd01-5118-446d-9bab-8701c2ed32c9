package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OfflineAPIResponse extends GatewayResponse<OfflineAPIResponse.InvokeResultWrapper> {

    @Override
    public String toString() {
        return "OfflineAPIResponse{" +
                "success=" + success +
                ", data=" + data +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {

        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }

    }

    /**
     * {
     * "code": "200",
     * "message": "success",
     * "success": true,
     * "data": {
     * "invokeResult": "{\"create_time\":1732433912380,\"id\":\"soiyVrHISV63du-hlafYEg\",\"labels\":{\"API_VERSION\":\"V1\",\"API_TYPE\":\"BUYER\"},\"methods\":[],\"name\":\"0ff910ae7654faaa930150dc2ec1726f-1860587994222718977\",\"plugins\":{\"proxy-rewrite\":{\"uri\":\"/data_1860587994222718977\",\"headers\":{\"add\":{\"ROUTE_ID\":\"b8717d27dda29a5f88383a4d394c1a370863a59d10788aa6ee9335eea1ef860e\",\"API_TYPE\":\"BUYER\"}}}},\"priority\":0,\"service_id\":\"fDPLhqULRRC6BogkFimuRQ\",\"status\":0,\"update_time\":1732849082453,\"uri\":\"/deliver_0ff910ae7654faaa930150dc2ec1726f/asset_1860587994222718977\",\"vars\":[]}"
     * },
     * "requestId": "27c719c3-edc0-49e9-8464-c35fa0a2f465",
     * "requestTotalCost": 56
     * }
     */
    @Data
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult {
        String id;
        String uri;
        Integer status;
        @JsonProperty("create_time")
        Long createTime;
        @JsonProperty("update_time")
        Long updateTime;
    }

    public static void main(String[] args) throws JsonProcessingException {
        String json = """
                {
                    "code": "200",
                    "message": "success",
                    "success": true,
                    "data": {
                        "invokeResult": "{\\"create_time\\":1732433912380,\\"id\\":\\"soiyVrHISV63du-hlafYEg\\",\\"labels\\":{\\"API_VERSION\\":\\"V1\\",\\"API_TYPE\\":\\"BUYER\\"},\\"methods\\":[],\\"name\\":\\"0ff910ae7654faaa930150dc2ec1726f-1860587994222718977\\",\\"plugins\\":{\\"proxy-rewrite\\":{\\"uri\\":\\"/data_1860587994222718977\\",\\"headers\\":{\\"add\\":{\\"ROUTE_ID\\":\\"b8717d27dda29a5f88383a4d394c1a370863a59d10788aa6ee9335eea1ef860e\\",\\"API_TYPE\\":\\"BUYER\\"}}}},\\"priority\\":0,\\"service_id\\":\\"fDPLhqULRRC6BogkFimuRQ\\",\\"status\\":0,\\"update_time\\":1732849082453,\\"uri\\":\\"/deliver_0ff910ae7654faaa930150dc2ec1726f/asset_1860587994222718977\\",\\"vars\\":[]}"
                    },
                    "requestId": "27c719c3-edc0-49e9-8464-c35fa0a2f465",
                    "requestTotalCost": 56
                }
                """;

        OfflineAPIResponse response = JacksonUtils.json2pojo(json, OfflineAPIResponse.class);
        System.out.println("response = " + response);
    }

}
