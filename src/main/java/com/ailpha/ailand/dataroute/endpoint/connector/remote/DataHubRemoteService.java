package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ActivateRouterRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.HubUpsertRouteRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.GetHubNodeResponse;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataAssetSaveRequest;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;

import java.util.List;

@RetrofitClient(baseUrl = "http://127.0.0.1:8082")
@Sign(baseCapabilityType = BaseCapabilityType.TRADE_PLATFORM, tokenUrl = "/third/app/token")
public interface DataHubRemoteService {

    @GET("/node/currentNode")
    CommonResult<GetHubNodeResponse> hubInfo();

    @POST(value = "connectors/upsert")
    CommonResult<Void> upsert(@Body HubUpsertRouteRequest request);

    @POST(value = "/api/dataasset/list")
    CommonResult<List<DataAssetDTO>> dataAssets(@Body DataAssetListRequest request);

    /**
     * 新增
     *
     * @param request req
     * @return
     */
    @POST("/api/dataasset/add")
    CommonResult<Boolean> add(@Body DataAssetSaveRequest request);

    @POST("/api/dataasset/update")
    CommonResult<Boolean> updateDataAsset(@Body DataAssetUpdateRequest dataAssetUpdateRequest);

    @POST("/api/drClientInfo/remote/activate")
    CommonResult<Boolean> activate(@Body ActivateRouterRequest request);

    @GET("/api/drClientInfo/remote/getByClientNo/{clientNo}")
    CommonResult<DrClientInfoVO> getByClientNo(@Path("clientNo") String clientNo);

    @POST("/api/drClientInfo/remote/router/list")
    CommonResult<List<DrClientInfoVO>> routers(@Body RouterListRequest request);

    @POST("/api/drClientInfo/remote/update")
    CommonResult<Boolean> update(@Body UpdateRouterRequest request);
}
