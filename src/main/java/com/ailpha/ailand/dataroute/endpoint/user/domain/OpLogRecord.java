package com.ailpha.ailand.dataroute.endpoint.user.domain;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "t_log")
public class OpLogRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    String id;
    @Column(name = "user_id")
    String userId;
    @Column(name = "op_type")
    private String opType;

    @Column(name = "op_module")
    private String opModule;

    private String description;

    private String message;

    private boolean success;

    private String username;
    @Column(name = "create_time")
    private Date createTime;

    private String ip;
}
