package com.ailpha.ailand.dataroute.endpoint.upgrade.vo;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VersionInfo implements Serializable {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    private UpgradeModule module;

    @Schema(description = "升级前包ID")
    private String beforePackageId;

    @Schema(description = "升级前版本")
    private String beforeVersion;

    @Schema(description = "升级前工程目录")
    private String beforePath;

    @Schema(description = "升级后包ID")
    private String afterPackageId;

    @Schema(description = "升级后包名称")
    private String afterPackageName;

    @Schema(description = "升级后版本")
    private String afterVersion;

    @Schema(description = "升级后工程目录")
    private String afterPath;

    @Schema(description = "MD5")
    private String md5;

    @Schema(description = "来源：DATA_ROUTE（连接器）")
    private UpgradeSource source;

    @Schema(description = "是否立即升级")
    private Boolean immediately;

    @Schema(description = "升级时间")
    private String upgradeTime;

    @Schema(description = "升级状态：WAIT（待升级）UPGRADING（升级中）SUCCESS（升级成功）FAILURE（升级失败）INVALID（已失效）")
    private UpgradeStatus status;

    @Schema(description = "日志")
    private String log;
}
