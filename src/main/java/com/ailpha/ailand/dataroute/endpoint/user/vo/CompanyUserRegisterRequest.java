package com.ailpha.ailand.dataroute.endpoint.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Schema(description = "企业用户注册请求")
public class CompanyUserRegisterRequest {
    String companyName;
    String creditCode;
    @Schema(description = "账号")
    String account;

    @Schema(description = "姓名")
    String name;

    @Schema(description = "手机号")
    String mobile;

    @Schema(description = "邮箱")
    String email;
    @Schema(description = "功能节点ID")
    Long serviceNodeId;
    String password;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date expireDate;
}