package com.ailpha.ailand.dataroute.endpoint.common;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.lianjiatech.retrofit.spring.boot.config.RetrofitProperties;
import com.github.lianjiatech.retrofit.spring.boot.log.LogLevel;
import com.github.lianjiatech.retrofit.spring.boot.log.LogStrategy;
import com.github.lianjiatech.retrofit.spring.boot.log.Logging;
import com.github.lianjiatech.retrofit.spring.boot.log.LoggingInterceptor;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class CustomLoggingInterceptor extends LoggingInterceptor {

    public CustomLoggingInterceptor(RetrofitProperties retrofitProperties) {
        super(retrofitProperties.getGlobalLog());
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Logging logging = findLogging(chain);
        if (!needLog(logging)) {
            return chain.proceed(chain.request());
        }
        LogStrategy logStrategy = logging == null ? globalLogProperty.getLogStrategy() : logging.logStrategy();
        if (logStrategy == LogStrategy.NONE) {
            return chain.proceed(chain.request());
        }

        LogLevel logLevel = logging == null ? globalLogProperty.getLogLevel() : logging.logLevel();
        boolean aggregate = logging == null ? globalLogProperty.isAggregate() : logging.aggregate();
        String logName = logging == null || logging.logName().isEmpty() ? globalLogProperty.getLogName() : logging.logName();
        HttpLoggingInterceptor.Logger matchLogger = matchLogger(logName, logLevel);
        HttpLoggingInterceptor.Logger logger = aggregate ? new BufferingLogger(matchLogger) : matchLogger;
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor(logger)
                .setLevel(HttpLoggingInterceptor.Level.valueOf(logStrategy.name()));
        Response response = httpLoggingInterceptor.intercept(chain);
        if (aggregate) {
            ((BufferingLogger) logger).flush();
        }
        return response;
    }

    private static class BufferingLogger implements HttpLoggingInterceptor.Logger {

        private StringBuilder buffer = new StringBuilder(System.lineSeparator());

        private final HttpLoggingInterceptor.Logger delegate;

        public BufferingLogger(HttpLoggingInterceptor.Logger delegate) {
            this.delegate = delegate;
        }

        @Override
        public void log(String message) {
            if (StringUtils.startsWith(message, "{") && StringUtils.endsWith(message, "}")) {
                try {
                    // 移除非国标字段
                    JSONObject entries = JSONUtil.parseObj(message);
                    entries.remove("hubInfo");
                    entries.remove("targetNodeId");
                    entries.remove("targetCompanyId");
                    entries.remove("localCompanyId");
                    entries.remove("requestId");
                    entries.remove("url");
                    entries.remove("currentNodeId");
                    message = entries.toString();
                } catch (Exception ignored) {
                }
            }
            buffer.append(message).append(System.lineSeparator());
        }

        public void flush() {
            delegate.log(buffer.toString());
            buffer = new StringBuilder(System.lineSeparator());
        }
    }

    public static void main(String[] args) {
        String s = "{\"hubInfo\":\"123123\"}";
        JSONObject entries = JSONUtil.parseObj(s);
        entries.remove("hubInfo");
        System.out.println(entries);
    }
}
