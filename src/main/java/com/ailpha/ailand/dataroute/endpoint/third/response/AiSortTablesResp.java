package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiSortTablesResp implements Serializable {

    Long current;
    Long pages;
    List<Record> records;
    Long size;
    Long total;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Record implements Serializable {
        @Schema(description = "平台数据源id")
        String aiguardId;
        @Schema(description = "业务系统")
        String businessSystem;
        @Schema(description = "分类id-V2.1新增")
        Long classifyId;
        @Schema(description = "分类id，根据表策略可能有多个")
        List<Long> classifyIds;
        @Schema(description = "分类名-V2.1新增")
        String classifyName;
        @Schema(description = "分类名称，根据表策略可能有多个")
        List<String> classifyNames;
        @Schema(description = "字段数")
        Long columnCount;
        @Schema(description = "是否梳理")
        Boolean combing;
        @Schema(description = "已梳理占比")
        String combingPercent;
        @Schema(description = "库别名")
        String dbAlias;
        @Schema(description = "库名")
        String dbName;
        @Schema(description = "数据源类型")
        String dbType;
        @Schema(description = "部门")
        String department;
        @Schema(description = "ip地址")
        String host;
        @Schema(description = "表id")
        Long id;
        @Schema(description = "级别")
        String level;
        @Schema(description = "敏感等级标签")
        String levelLabel;
        @Schema(description = "数据源类型是否支持脱敏")
        Boolean maskSupport;
        @Schema(description = "责任人")
        String owner;
        @Schema(description = "端口号")
        String port;
        @Schema(description = "schema名称")
        String schemaName;
        @Schema(description = "敏感字段数")
        Long sensitiveColumnCount;
        @Schema(description = "是否显示脱敏")
        Boolean showMask;
        @Schema(description = "数据源id")
        Long sourceId;
        @Schema(description = "数据源名")
        String sourceName;
        @Schema(description = "表别名")
        String tableAlias;
        @Schema(description = "表注释")
        String tableDesc;
        @Schema(description = "表名")
        String tableName;
        @Schema(description = "最后梳理时间")
        String updateTime;
    }
}
