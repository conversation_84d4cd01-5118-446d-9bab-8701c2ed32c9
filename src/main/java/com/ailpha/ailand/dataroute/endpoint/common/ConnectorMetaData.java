package com.ailpha.ailand.dataroute.endpoint.common;

import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Data
public class ConnectorMetaData {
    String schema;
    String targetNodeId;

    public String toBase64() {
        return Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(this).getBytes(StandardCharsets.UTF_8));
    }
}
