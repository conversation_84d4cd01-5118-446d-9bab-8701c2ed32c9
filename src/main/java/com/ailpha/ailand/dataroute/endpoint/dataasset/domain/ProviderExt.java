package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;


import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ProviderExt {
    String phone;
    String email;
    /**
     * 连接器id
     */
    String routerId;
    /**
     * 连接器名称
     */
    String routerName;
    /**
     * 用户id
     */
    String userId;
    /**
     * 用户id
     */
    String userIdShuhan;
    /**
     * 用户名
     */
    String username;

    CompanyDTO company;
}