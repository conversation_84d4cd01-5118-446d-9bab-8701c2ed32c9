package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DescribeRoutesResponse extends GatewayResponse<DescribeRoutesResponse.InvokeResultWrapper> {
    @Getter
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {
        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }
    }

    @Data
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult {
        List<DescribeRouteResponse.InvokeResult> rows;

        Integer totalCount;
    }
}
