package com.ailpha.ailand.dataroute.endpoint.connector.remote.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class IdentityProviderItem {
    private GetIdentityProviderLisVerifyBO verify; // JSON 对象，具体结构未知，可以使用 Map 或自定义类
    private String identityId;
    private String description;
    private String authType;

    @Data
    public static class GetIdentityProviderLisVerifyBO implements Serializable {
        private String verify;
        private String verifyRegions;
    }

}
