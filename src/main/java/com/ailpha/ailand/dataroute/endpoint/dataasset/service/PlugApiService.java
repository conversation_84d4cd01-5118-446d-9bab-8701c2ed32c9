package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiType;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:45
 * @Description:
 */
public interface PlugApiService {
    List<PluginApiType> getPlugApiList(PluginApiTypeEnums type);
}
