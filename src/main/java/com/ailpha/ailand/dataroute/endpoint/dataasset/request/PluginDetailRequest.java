package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiEncryptTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginCredentials;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 16:39
 * @Description:
 */
@Data
@Schema(description = "插件新增/修改请求")
public class PluginDetailRequest {

    @Schema(description = "插件id")
    private Long id;

    @Schema(description = "插件状态")
    private Boolean status;

    @Schema(description = "插件名称")
    private String name;

    @Schema(description = "插件类型")
    private PluginApiTypeEnums type;

    @Schema(description = "对接域名")
    private String domain;

    @Schema(description = "插件加密方式")
    private PluginApiEncryptTypeEnums encryptType;

    @Schema(description = "插件凭证")
    private PluginCredentials plugCredentials;

    @Schema(description = "插件api信息")
    private List<PluginApiDetailRequest> pluginApiDetails;
}
