package com.ailpha.ailand.dataroute.endpoint.restclient;

import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

public class BufferingClientHttpResponseWrapper implements ClientHttpResponse {

    private final ClientHttpResponse response;

    @Nullable
    private byte[] body;


    public BufferingClientHttpResponseWrapper(ClientHttpResponse response) {
        this.response = response;
    }


    @NotNull
    @Override
    public HttpStatusCode getStatusCode() throws IOException {
        return this.response.getStatusCode();
    }

    @NotNull
    @Override
    public String getStatusText() throws IOException {
        return this.response.getStatusText();
    }

    @NotNull
    @Override
    public HttpHeaders getHeaders() {
        return this.response.getHeaders();
    }

    @NotNull
    @Override
    public InputStream getBody() throws IOException {
        if (this.body == null) {
            this.body = StreamUtils.copyToByteArray(this.response.getBody());
        }
        return new ByteArrayInputStream(this.body);
    }

    @Override
    public void close() {
        this.response.close();
    }
}
