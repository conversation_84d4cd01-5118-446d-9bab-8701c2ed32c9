package com.ailpha.ailand.dataroute.endpoint.order.service;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.SceneAssetRepository;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.OrderInfo;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderResolveDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GanZhouResolveService {

    private final DeliverySceneRepository deliverySceneRepository;

    private final SceneAssetRepository sceneAssetRepository;

    private final AssetBeneficiaryRepository assetBeneficiaryRepository;

    private final OrderRecordRepository orderRecordRepository;


    public List<OrderInfo> filterExistOrder(List<OrderInfo> orderInfos) {
        List<String> orderIdList = orderInfos.stream().map(OrderInfo::getOrderNo).toList();
        List<OrderApprovalRecord> recordList = orderRecordRepository.findAllById(orderIdList);
        List<String> existOrderIdList = recordList.stream().map(OrderApprovalRecord::getId).toList();
        orderInfos.removeIf(orderInfo -> existOrderIdList.contains(orderInfo.getOrderNo()));

        log.info("获取新订单信息:{}", JSONUtil.toJsonStr(orderInfos));
        return orderInfos;
    }

    public void save(OrderResolveDTO resolveDTO) {
        OrderApprovalRecord record = resolveDTO.getRecord();
        AssetBeneficiaryRel rel = resolveDTO.getRel();
        DeliveryScene scene = resolveDTO.getScene();
        SceneAsset sceneAsset = resolveDTO.getSceneAsset();
        try {
            log.info("GanZhou resolve save request: {}", JSONUtil.toJsonStr(resolveDTO));

            orderRecordRepository.saveAndFlush(record);
            assetBeneficiaryRepository.saveAndFlush(rel);
            deliverySceneRepository.saveAndFlush(scene);
            sceneAssetRepository.saveAndFlush(sceneAsset);

            log.info("GanZhou resolve save request done");
        } catch (Exception e) {
            log.error("GanZhou resolve error orderId: {}", resolveDTO.getRecord().getId(), e);
            try {
                orderRecordRepository.delete(record);
            } catch (Exception ignore) {
            }
            try {
                assetBeneficiaryRepository.delete(rel);
            } catch (Exception ignore) {
            }
            try {
                deliverySceneRepository.delete(scene);
            } catch (Exception ignore) {
            }
            try {
                sceneAssetRepository.delete(sceneAsset);
            } catch (Exception ignore) {
            }
        }
    }


}
