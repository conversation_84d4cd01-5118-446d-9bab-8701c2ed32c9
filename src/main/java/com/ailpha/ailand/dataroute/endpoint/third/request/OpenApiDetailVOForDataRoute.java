package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiDetailVOForDataRoute extends OpenApiDetailVO {

    @Schema(description = "数据集列表")
    List<OpenApiDatasetVO> datasets;
}
