package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceCatalogQuery {
    /**
     * 分页大小
     */
    Integer pageSize;
    /**
     * 当前页数
     */
    Integer pageNum;
    /**
     * 资源名称
     */
    String resourceName;
    /**
     * 来源平台内部标识
     */
    String outerResourceId;
    /**
     * 上架业务节点ID
     */
    String platformId;
    /**
     * 数据类型：个人、企业、公共
     */
    String dataType;
    /**
     * 资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据
     */
    String resourceType;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 资源持有方ID
     */
    String resourceOwnerId;
    /**
     * 数据来源：01-原始取得,02-收集取得,03-交易取得，04-其他
     */
    String dataSource;
    /**
     * 资源状态：01 待登记 02 已登记 03 已撤销
     */
    String resourceStatus;
}
