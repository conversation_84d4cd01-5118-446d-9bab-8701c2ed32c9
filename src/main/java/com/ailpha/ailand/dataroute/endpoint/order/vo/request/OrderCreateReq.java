package com.ailpha.ailand.dataroute.endpoint.order.vo.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderCreateReq implements Serializable {

    @Schema(description = "资产类型 RESOURCE、PRODUCT")
    AssetType type;

    @NotEmpty(message = "资产ID列表不能为空")
    @Schema(description = "资产ID | 产品id")
    List<String> assetIds;

    @Schema(description = "计量方式：按次、按周期")
    @NotBlank(message = "计量方式不能为空")
    String meteringWay;

    @Schema(description = "使用次数")
    BigInteger allowance;

    @Schema(description = "计费方式：预付费、后付费")
    String chargingWay;

    @Schema(description = "订单配置")
    OderRecordExtend orderConfig;

    @Schema(description = "订单有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date expireDate;
}
