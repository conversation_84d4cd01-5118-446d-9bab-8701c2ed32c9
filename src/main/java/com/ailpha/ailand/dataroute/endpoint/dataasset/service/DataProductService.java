package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.PublishProduct;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.PlatformListVO;
import com.dbapp.rest.response.SuccessResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.jpa.domain.Specification;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public interface DataProductService {

    /**
     * 获取数据资产
     *
     * @param dataAssetId 数据资产ID
     * @return DataAssetVO
     */
    DataProductVO getDataAssetById(String dataAssetId);

    /**
     * 数据集详情
     *
     * @param dataAssetId 数据集ID
     * @return
     */
    DataProductVO getDataProduct(String dataAssetId);

    /**
     * 获取数据资产
     *
     * @param dataProductPlatformId 数据资产全局ID
     * @return DataResourceVO
     */
    DataProductVO getDataProductByDataProductPlatformId(String dataProductPlatformId);

    void downloadAttachFile(String dataProductPlatformId, AttachType attachType, HttpServletResponse response) throws IOException;

    void downloadAttachFile(DataProduct dataProduct, AttachType attachType, HttpServletResponse response) throws IOException;

    Long getApprovedCount();

    DataProductVO getDataProductByOldAssetId(String dataAssetId);

    String getCompanyIdByAssetId(String dataAssetId);

    /**
     * 获取所有数据资产
     *
     * @param dataAssetQuery 查询条件
     * @return 所有符合条件的数据资产
     */
    SuccessResponse<List<DataProductListVO>> allDataAssets(int page, int size, Function<Specification<DataProduct>, Specification<DataProduct>> specificationFunction);

    /**
     * 将数据资产作为文件下载
     *
     * @param dataAssetId 数据资产ID
     * @param accessKey
     * @param secretKey
     * @param response    响应
     */
    void download(String dataAssetId, String accessKey, String secretKey, HttpServletResponse response);

    void updateDataExt(String dataAssetId, Function<DataProduct.DataProductExt, DataProduct.DataProductExt> dataExtUpdate);

    void checkNameExists(String dataProductId, String dataProductName);

    /**
     * 数据产品登记暂存
     *
     * @param request
     */
    void temporarySave(DataProductRegistRequest request);

    /**
     * 数据产品登记
     *
     * @param request
     */
    void registration(DataProductRegistRequest request);

    /**
     * 数据产品登记更新
     *
     * @param request
     */
    void updateRegistration(DataProductRegistUpdateRequest request);

    /**
     * 数据产品登记撤销
     *
     * @param productId
     */
    void revokeRegistration(String productId);

    /**
     * 数据产品发布
     */
    void publishProduct(PublishProduct publishProduct);

    /**
     * 数据产品上架
     *
     * @param request
     */
    void publish(DataProductPublishRequest request);

    /**
     * 数据产品上架更新
     *
     * @param request
     */
    void publishUpdate(DataProductPublishUpdateRequest request);

    /**
     * 数据产品下架
     *
     * @param dataProductId 数据上架ID
     * @param serviceNodeId 上架的业务节点id
     */
    void unpublish(String dataProductId, List<String> serviceNodeId);

    /**
     * 上架数据产品到指定业务节点
     *
     * @param dataProductId 数据产品ID
     * @param serviceNodeId 业务节点ID
     */
    Map<String, String> publishToNode(String dataProductId, List<String> serviceNodeId);

    void delete(String dataProductId);

}
