package com.ailpha.ailand.dataroute.endpoint.user.service;

import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.domain.OpLogRecord;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QOpLogRecord;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.mapstruct.LogMapper;
import com.ailpha.ailand.dataroute.endpoint.user.repository.LogRepository;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.vo.LogResponse;
import com.ailpha.ailand.dataroute.endpoint.user.vo.QueryLogRequest;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class LogService {
    private final LogRepository logRepository;
    private final LogMapper logMapper;
    private final UserService userService;

    public SuccessResponse<List<LogResponse>> query(QueryLogRequest request) {

        UserDTO currentUserDTO = LoginContextHolder.currentUser();
        QOpLogRecord opLogRecord = QOpLogRecord.opLogRecord;
        BooleanBuilder queryBuilder = new BooleanBuilder();
        // admin 查所有人 trader 查自己
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.TRADER)) {
            queryBuilder.and(opLogRecord.userId.eq(currentUserDTO.getId()));
        }

        if (ObjectUtil.isNotNull(request.getStartDate()) && ObjectUtil.isNotNull(request.getEndDate())) {
            queryBuilder.and(opLogRecord.createTime.between(request.getStartDate(), request.getEndDate()));
        }
        if (StringUtils.isNotEmpty(request.getUsername()) && LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            List<UserDetailsResponse> userDetailsResponse = userService.findBy(request.getUsername());
            Assert.isTrue(userDetailsResponse != null, "用户不存在");
            queryBuilder.and(opLogRecord.userId.in(userDetailsResponse.stream().map(UserDetailsResponse::getUserId).collect(Collectors.toList())));
        }
        if (StringUtils.isNotEmpty(request.getOpModule()))
            queryBuilder.and(opLogRecord.opModule.eq(request.getOpModule()));
        if (StringUtils.isNotEmpty(request.getOpType()))
            queryBuilder.and(opLogRecord.opType.eq(request.getOpType()));
        if (StringUtils.isNotEmpty(request.getIp()))
            queryBuilder.and(opLogRecord.ip.like("%" + request.getIp() + "%"));

        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Page<OpLogRecord> opLogRecords;
        if (ObjectUtil.isNull(queryBuilder.getValue())) {
            opLogRecords = logRepository.findAll(PageRequest.of((int) request.getNum() - 1, (int) request.getSize(), sort));
        } else
            opLogRecords = logRepository.findAll(queryBuilder.getValue(), PageRequest.of((int) request.getNum() - 1, (int) request.getSize(), sort));
        if (opLogRecords.isEmpty())
            return SuccessResponse.success(null).total(0L).build();
        else
            return SuccessResponse.success(opLogRecords.stream().map(logMapper::toResponse).collect(Collectors.toList())).total(opLogRecords.getTotalElements()).build();
    }
}
