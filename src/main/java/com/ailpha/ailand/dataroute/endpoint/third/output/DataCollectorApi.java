package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.biz.api.collector.DatasourceCheckQuery;
import com.ailpha.ailand.biz.api.collector.DatasourceColumnResponse;
import com.ailpha.ailand.biz.api.collector.DatasourceJoinFieldDebugDataResponse;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataCollectorJobParam;
import com.ailpha.ailand.dataroute.endpoint.third.util.DataCollectorUtils;
import com.ailpha.ailand.dataroute.endpoint.third.response.JobInfo;
import com.ailpha.ailand.dataroute.endpoint.third.request.SubmitDataCollectorJobRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;

import java.nio.charset.Charset;

@Slf4j
@Component
public class DataCollectorApi {
    private final RestClient restClient;
    private final AiLandProperties aiLandProperties;

    public DataCollectorApi(RestClient.Builder restClientBuilder, AiLandProperties aiLandProperties) {
        this.restClient = restClientBuilder
                .requestInterceptor((request, body, execution) -> {
                    if (log.isDebugEnabled()) {
                        log.debug("前置机接口 {} 请求体 {}", request.getURI(), new String(body, Charset.defaultCharset()));
                    }
                    return execution.execute(request, body);
                })
                .build();
        this.aiLandProperties = aiLandProperties;
    }

    /**
     * 测试数据源连接
     *
     * @param query 测试数据源连接参数
     */
    public boolean checkConn(DatasourceCheckQuery query) {
        CommonResult<Void> checkConnectionResult = this.restClient.post()
                .uri(uriBuilder ->
                        uriBuilder
                                .host("127.0.0.1")
                                .port(aiLandProperties.getExecutorServer().getPort())
                                .scheme("http")
                                .path("/datasource/test").build()
                )
                .body(query)
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<Void>>() {
                });
        Assert.isTrue(checkConnectionResult != null && checkConnectionResult.isSuccess(), checkConnectionResult != null ? checkConnectionResult.getMessage() : "数据源连接失败");
        return true;
    }

    /**
     * 测试数据源表连接
     *
     * @param query 测试数据源表连接参数
     */
    public boolean checkTable(DatasourceCheckQuery query) {
        CommonResult<Void> checkTableResult = this.restClient.post()
                .uri(uriBuilder ->
                        uriBuilder
                                .host("127.0.0.1")
                                .port(aiLandProperties.getExecutorServer().getPort())
                                .scheme("http")
                                .path("/datasource/check/table").build()
                )
                .body(query)
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<Void>>() {
                });
        Assert.isTrue(checkTableResult != null && checkTableResult.isSuccess(), checkTableResult != null ? checkTableResult.getMessage() : "数据源表连接测试失败");
        return true;
    }

    /**
     * 查询调试数据
     *
     * @param query 查询调试数据请求参数
     */
    public DatasourceJoinFieldDebugDataResponse debugData(DatasourceCheckQuery query) {
        CommonResult<DatasourceJoinFieldDebugDataResponse> tableData = this.restClient.post()
                .uri(uriBuilder ->
                        uriBuilder
                                .host("127.0.0.1")
                                .port(aiLandProperties.getExecutorServer().getPort())
                                .scheme("http")
                                .path("/datasource/table/debug-data").build()
                )
                .body(query)
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<DatasourceJoinFieldDebugDataResponse>>() {
                });
        Assert.isTrue(tableData != null && tableData.isSuccess(), tableData != null ? tableData.getMessage() : "数据源表调试数据查询失败");
        return tableData.getData();
    }

    /**
     * 获取表字段
     *
     * @param query 获取表字段请求参数
     */
    public DatasourceColumnResponse fetchTableColumn(DatasourceCheckQuery query) {
        CommonResult<DatasourceColumnResponse> tableColumnMetadata = this.restClient.post()
                .uri(uriBuilder ->
                        uriBuilder
                                .host("127.0.0.1")
                                .port(aiLandProperties.getExecutorServer().getPort())
                                .scheme("http")
                                .path("/datasource/table/columns").build()
                )
                .body(query)
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<DatasourceColumnResponse>>() {
                });
        Assert.isTrue(tableColumnMetadata != null && tableColumnMetadata.isSuccess(), tableColumnMetadata != null ? tableColumnMetadata.getMessage() : "获取表字段信息失败");
        return tableColumnMetadata.getData();
    }

    /**
     * 异步提交数据采集任务
     *
     * @param jobParam 提交数据采集任务请求参数
     */
    public boolean asyncSubmitJob(DataCollectorJobParam jobParam) {
//        DataCollectorJobParam.builder()
//                .dataAssetId(reqDTO.getDatasetId())
//                .datasourceType(reqDTO.getDatasourceType())
//                .jdbcUrl(reqDTO.getJdbcUrl())
//                .username(reqDTO.getUsername())
//                .password(reqDTO.getPassword())
//                .columns(reqDTO.getSchemaList().stream().map(DataSchemaDTO::getFieldName).collect(Collectors.toList()))
//                .columnAlias(reqDTO.getSchemaList().stream().map(x -> StringUtils.isEmpty(x.getAlias()) ? x.getFieldName() : x.getAlias()).collect(Collectors.toList()))
//                .tableName(reqDTO.getTableName())
//                .filepath(reqDTO.getFilepath())
//                .conditions(reqDTO.getConditions())
//                .build()
        CommonResult<Void> submitAsyncExecutorJobResult = this.restClient.post()
                .uri(uriBuilder ->
                        uriBuilder
                                .host("127.0.0.1")
                                .port(aiLandProperties.getExecutorServer().getPort())
                                .scheme("http")
                                .path("/api/collector/async/job").build()
                )
                .body(SubmitDataCollectorJobRequest.builder()
                        .jobContext(DataCollectorUtils.generateJobParam(jobParam))
                        .build())
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<Void>>() {
                });
        Assert.isTrue(submitAsyncExecutorJobResult != null && submitAsyncExecutorJobResult.isSuccess(), submitAsyncExecutorJobResult != null ? submitAsyncExecutorJobResult.getMessage() : "异步提交Executor任务失败");
        return true;
    }

    public JobInfo queryJobStatus(String dataAssetId) {
        CommonResult<JobInfo> jobInfoResult = this.restClient.get()
                .uri(uriBuilder -> uriBuilder
                        .host("127.0.0.1")
                        .port(aiLandProperties.getExecutorServer().getPort())
                        .scheme("http")
                        .path("/api/collector/query/job/only")
                        .queryParam("jobId", dataAssetId)
                        .build())
                .retrieve()
                .body(new ParameterizedTypeReference<CommonResult<JobInfo>>() {
                });
        if (jobInfoResult != null && jobInfoResult.isSuccess()) {
            return jobInfoResult.getData();
        }
        return null;
    }
}
