package com.ailpha.ailand.dataroute.endpoint.configuration.controller;

import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.configuration.vo.ConfigurationInfoVO;
import com.ailpha.ailand.dataroute.endpoint.configuration.vo.request.ConfigurationSaveReq;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;

/**
 * <AUTHOR>
 * 2025/2/24
 */
@RestController
@Tag(name = "系统配置")
@RequestMapping("/configuration")
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
public class ConfigurationController {

    private final FilesStorageServiceImpl filesStorageService;

    @PostMapping("/save")
    @Operation(summary = "保存配置")
    @OpLog(message = "保存配置")
    public SuccessResponse<ConfigurationSaveReq> save(@Valid @RequestBody ConfigurationSaveReq configurationSaveReq) throws IOException {
        OPLogContext.putOpType(InternalOpType.SAVE_CONFIGURATION);
        String json = JacksonUtils.obj2json(configurationSaveReq);
        Path path = Paths.get(Constants.DATA_PATH, Constants.CONFIG_FILE_NAME);
        filesStorageService.createAbsolutePathFileWithContent(Collections.singletonList(json), path.toString());
        return SuccessResponse.success(configurationSaveReq).build();
    }

    @GetMapping("/info")
    @Operation(summary = "配置详情")
    public SuccessResponse<ConfigurationInfoVO> info() throws Exception {
        ConfigurationInfoVO configurationInfo = new ConfigurationInfoVO();
        Path path = Paths.get(Constants.DATA_PATH, Constants.CONFIG_FILE_NAME);
        if (path.toFile().exists()) {
            String configJson = filesStorageService.readContent(path);
            configurationInfo = JacksonUtils.json2pojo(configJson, ConfigurationInfoVO.class);
        }
        return SuccessResponse.success(configurationInfo).build();
    }

}
