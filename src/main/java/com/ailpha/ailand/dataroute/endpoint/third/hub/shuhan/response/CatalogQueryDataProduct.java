package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.Length;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 实体类
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-08
 * <p>
 * Copyright ©2023 Shuhan All Rights Reserved
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "CatalogQueryDataProduct", description = "")
public class CatalogQueryDataProduct {

    /**
     * 数据唯一标识（数据产品平台唯一编号）
     */
    @ApiModelProperty(value = "数据唯一标识（数据产品平台唯一编号）")
    @NotBlank(message = "数据唯一标识（数据产品平台唯一编号）不能为空")
    @Length(max = 65, message = "数据唯一标识（数据产品平台唯一编号）长度不能超过65")
    private String registrationId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    @Length(max = 128, message = "产品名称长度不能超过128")
    private String productName;

    /**
     * 产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他
     */
    @ApiModelProperty(value = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他")
    @NotBlank(message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他不能为空")
    @Length(max = 2, message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他长度不能超过2")
    private String productType;

    /**
     * 覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）
     */
    @ApiModelProperty(value = "覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）")
    @Length(max = 50, message = "覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）长度不能超过50")
    private String timeRange;

    /**
     * 行业分类（GB/T 4754-2017门类代码）
     */
    @ApiModelProperty(value = "行业分类（GB/T 4754-2017门类代码）")
    @NotBlank(message = "行业分类（GB/T 4754-2017门类代码）不能为空")
    @Length(max = 1, message = "行业分类（GB/T 4754-2017门类代码）长度不能超过1")
    private String industry;

    /**
     * 地域分类（GB/T 2260-2007代码）
     */
    @ApiModelProperty(value = "地域分类（GB/T 2260-2007代码）")
    @Length(max = 12, message = "地域分类（GB/T 2260-2007代码）长度不能超过12")
    private String productRegion;

    /**
     * 是否涉及个人信息：0-否,1-是
     */
    @ApiModelProperty(value = "是否涉及个人信息：0-否,1-是")
    @NotNull(message = "是否涉及个人信息：0-否,1-是不能为空")
    private Boolean personalInformation;

    /**
     * 产品简介
     */
    @ApiModelProperty(value = "产品简介")
    @NotBlank(message = "产品简介不能为空")
    @Length(max = 65535, message = "产品简介长度不能超过65535")
    private String description;

    /**
     * 交付方式：01-文件传输,02-数据流传输,03-API传输
     */
    @ApiModelProperty(value = "交付方式：01-文件传输,02-数据流传输,03-API传输")
    @Length(max = 2, message = "交付方式：01-文件传输,02-数据流传输,03-API传输长度不能超过2")
    private String deliveryMethod;

    /**
     * 使用限制
     */
    @ApiModelProperty(value = "使用限制")
    @NotBlank(message = "使用限制不能为空")
    @Length(max = 65535, message = "使用限制长度不能超过65535")
    private String limitations;

    /**
     * 授权使用：0-否,1-是
     */
    @ApiModelProperty(value = "授权使用：0-否,1-是")
    @NotNull(message = "授权使用：0-否,1-是不能为空")
    private Boolean authorize;

    /**
     * 数据主体：01-个人信息,02-企业数据,03-公共数据
     */
    @ApiModelProperty(value = "数据主体：01-个人信息,02-企业数据,03-公共数据")
    @NotBlank(message = "数据主体：01-个人信息,02-企业数据,03-公共数据不能为空")
    @Length(max = 2, message = "数据主体：01-个人信息,02-企业数据,03-公共数据长度不能超过2")
    private String dataSubject;

    /**
     * 数据规模（如10GB）
     */
    @ApiModelProperty(value = "数据规模（如10GB）")
    @Length(max = 16, message = "数据规模（如10GB）长度不能超过16")
    private String dataSize;

    /**
     * 更新频率（次/天、次/周等）
     */
    @ApiModelProperty(value = "更新频率（次/天、次/周等）")
    @NotBlank(message = "更新频率（次/天、次/周等）不能为空")
    @Length(max = 10, message = "更新频率（次/天、次/周等）长度不能超过10")
    private String updateFrequency;

    /**
     * 数据资源标识码 数组型（一个或多个数据源标识码）
     */
    @ApiModelProperty(value = "数据资源标识码 数组型（一个或多个数据源标识码）（JSON数组）")
    @Length(max = 65535, message = "数据资源标识码不能超过65535")
    private String resourceId;

    /**
     * 其他扩展信息（JSON格式）
     */
    @ApiModelProperty(value = "其他扩展信息（对象类型）")
    @Length(max = 65535, message = "其他扩展信息（对象类型）长度不能超过65535")
    private JSONObject others;

    /**
     * 提供方名称
     */
    @ApiModelProperty(value = "提供方名称")
    @NotBlank(message = "提供方名称不能为空")
    @Length(max = 128, message = "提供方名称长度不能超过128")
    private String providerName;

    /**
     * 提供方主体类型：01-自然人,02-法人,03-非法人组织
     */
    @ApiModelProperty(value = "提供方主体类型：01-自然人,02-法人,03-非法人组织")
    @NotBlank(message = "提供方主体类型：01-自然人,02-法人,03-非法人组织不能为空")
    @Length(max = 2, message = "提供方主体类型：01-自然人,02-法人,03-非法人组织长度不能超过2")
    private String providerType;

    /**
     * 主体信息（对象类型）
     */
    @ApiModelProperty(value = "主体信息（对象类型）")
    @NotBlank(message = "主体信息（对象类型）不能为空")
    @Length(max = 65535, message = "主体信息（对象类型）长度不能超过65535")
    private JSONObject entityInformation;

    /**
     * 身份标识码（NDI-TR-2025-04编码规则）
     */
    @ApiModelProperty(value = "身份标识码（NDI-TR-2025-04编码规则）")
    @NotBlank(message = "身份标识码（NDI-TR-2025-04编码规则）不能为空")
    @Length(max = 255, message = "身份标识码（NDI-TR-2025-04编码规则）长度不能超过255")
    private String identifyId;

    /**
     * 提供方简介
     */
    @ApiModelProperty(value = "提供方简介")
    @NotBlank(message = "提供方简介不能为空")
    @Length(max = 65535, message = "提供方简介长度不能超过65535")
    private String providerDesc;

    /**
     * 法人经办人姓名
     */
    @ApiModelProperty(value = "法人经办人姓名")
    @Length(max = 10, message = "法人经办人姓名长度不能超过10")
    private String operatorName;

    /**
     * 法人经办人电话
     */
    @ApiModelProperty(value = "法人经办人电话")
    @Length(max = 11, message = "法人经办人电话长度不能超过11")
    private String operatorTelephone;

    /**
     * 法人经办人身份证
     */
    @ApiModelProperty(value = "法人经办人身份证")
    @Length(max = 18, message = "法人经办人身份证长度不能超过18")
    private String operatorIdCard;

    /**
     * 授权委托书（二进制文件）
     */
    @ApiModelProperty(value = "授权委托书（二进制文件）")
    @Length(max = 65535, message = "授权委托书（二进制文件）长度不能超过65535")
    private String commission;

    /**
     * 数据样例（二进制文件）
     */
    @ApiModelProperty(value = "数据样例（二进制文件）")
    @Length(max = 255, message = "数据样例（二进制文件）长度不能超过255")
    private String dataSample;

    /**
     * 合法合规声明（二进制文件）
     */
    @ApiModelProperty(value = "合法合规声明（二进制文件）")
    @NotBlank(message = "合法合规声明（二进制文件）不能为空")
    @Length(max = 255, message = "合法合规声明（二进制文件）长度不能超过255")
    private String complianceAndLegalStatement;

    /**
     * 数据来源声明（二进制文件）
     */
    @ApiModelProperty(value = "数据来源声明（二进制文件）")
    @NotBlank(message = "数据来源声明（二进制文件）不能为空")
    @Length(max = 255, message = "数据来源声明（二进制文件）长度不能超过255")
    private String dataSourceStatement;

    /**
     * 安全分级分类（二进制文件）
     */
    @ApiModelProperty(value = "安全分级分类（二进制文件）")
    @Length(max = 255, message = "安全分级分类（二进制文件）长度不能超过255")
    private String safeLevel;

    /**
     * 数据质量/价值评估报告（二进制文件）
     */
    @ApiModelProperty(value = "数据质量/价值评估报告（二进制文件）")
    @Length(max = 255, message = "数据质量/价值评估报告（二进制文件）长度不能超过255")
    private String evaluationReport;

    /**
     * 数据版本号，覆盖更新
     */
    @ApiModelProperty(value = "数据版本号，覆盖更新")
    @Length(max = 2, message = "数据版本号，覆盖更新长度不能超过2")
    private String dataVersion;

    /**
     * 数由器平台唯一编号
     */
    @ApiModelProperty(value = "数由器平台唯一编号")
    @Length(max = 255, message = "数由器平台唯一编号长度不能超过255")
    private String clientPlatformUniqueNo;

    /**
     * 删除标识: 0否 1是
     */
    @ApiModelProperty(value = "删除标识: 0否 1是")
    private Integer delFlag;

}

