package com.ailpha.ailand.dataroute.endpoint.plugin.service.impl;

import com.ailpha.ailand.dataroute.endpoint.plugin.aop.TrackingContext;
import com.ailpha.ailand.dataroute.endpoint.plugin.aop.TrackingModuleType;
import com.ailpha.ailand.dataroute.endpoint.plugin.service.PluginService;
import com.ailpha.ailand.dataroute.endpoint.plugin.service.TrackingModuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: yuwenping
 * @date: 2025/5/28 16:14
 * @Description:
 */
@Service
@RestController
@RequiredArgsConstructor
public class TrackingModuleServiceImpl implements TrackingModuleService {

    private final PluginService pluginService;

    @Override
    public List<String> getModules() {
        return getConstantsValues(TrackingModuleType.class);
    }

    @Override
    public void track(String moduleName, Map<String, Object> params) {
        String data = (String) params.get(TrackingContext.DATA);
        pluginService.upBlockchain(moduleName, data);
    }

    public static List<String> getConstantsValues(Class<?> clazz) {
        return Arrays.stream(clazz.getDeclaredFields())
                .filter(f -> Modifier.isPublic(f.getModifiers()) &&
                        Modifier.isStatic(f.getModifiers()) &&
                        Modifier.isFinal(f.getModifiers()))
                .map(
                        f -> {
                            try {
                                return (String) f.get(null);
                            } catch (IllegalAccessException e) {
                                throw new RuntimeException(e);
                            }
                        }
                ).collect(Collectors.toList());
    }
}
