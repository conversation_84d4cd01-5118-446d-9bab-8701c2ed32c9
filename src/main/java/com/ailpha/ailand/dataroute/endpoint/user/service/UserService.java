package com.ailpha.ailand.dataroute.endpoint.user.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple4;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateTimeUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.dto.AddCompanyRequest;
import com.ailpha.ailand.dataroute.endpoint.company.mapstruct.CompanyMapper;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyRemoteService;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.license.DasLicenceService;
import com.ailpha.ailand.dataroute.endpoint.license.LicenceInfoDTO;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.contants.TenantConstant;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.input.PreviewAuthorizationLetterRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.user.domain.*;
import com.ailpha.ailand.dataroute.endpoint.user.enums.IdentityAuthStatus;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.mapstruct.UserMapper;
import com.ailpha.ailand.dataroute.endpoint.user.remote.UserRemoteService;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.HubUpdateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.ResetPwdRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.UpdateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.AddUserResponse;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserListResponse;
import com.ailpha.ailand.dataroute.endpoint.user.repository.*;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.vo.CompanyUserRegisterRequest;
import com.ailpha.ailand.dataroute.endpoint.user.vo.CurrentUserResponse;
import com.ailpha.ailand.dataroute.endpoint.user.vo.DelegateInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.user.vo.UserPageRequest;
import com.ailpha.ailand.dataroute.endpoint.user.vo.loginSetting.LoginSetting;
import com.ailpha.ailand.dataroute.endpoint.user.vo.loginSetting.LoginSettingVO;
import com.ailpha.ailand.utils.safe.RSAUtil;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Example;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import retrofit2.Response;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.SecureRandom;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserService implements UserDetailsService {
    private final UserRoleRepository userRoleRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final MenuRepository menuRepository;
    private final BaseCapabilityManager baseCapabilityManager;

    private final DasLicenceService licenceService;
    private final AiLandProperties aiLandProperties;

    @Value("${checkLicense:true}")
    private Boolean checkLicense;
    private final SecurityContextRepository securityContextRepository;

    public void updateLoginTime(String username) {
        User firstByAccount = userRepository.findFirstByAccount(username);
        firstByAccount.getExt().setLastLoginTime(System.currentTimeMillis());
        userRepository.saveAndFlush(firstByAccount);
    }

    public User findByUserId(String userId) {
        return AsyncManager.getInstance().executeFuture(() -> {
            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            final Optional<User> byId = userRepository.findById(userId);
            return byId.orElse(null);
        });
    }

    public void reloadUserDetails() {
        if (LoginContextHolder.isLogin()) {
            Authentication currentAuth = SecurityContextHolder.getContext().getAuthentication();
            UserDetails refreshedUser = loadUserByUsername(LoginContextHolder.currentUser().getUsername());
            // 3. 构建新的 Authentication 对象
            Authentication newAuth = new UsernamePasswordAuthenticationToken(
                    refreshedUser,
                    currentAuth.getCredentials(),
                    refreshedUser.getAuthorities());
            // 4. 更新 SecurityContextHolder
            SecurityContextHolder.getContext().setAuthentication(newAuth);
            securityContextRepository.saveContext(SecurityContextHolder.getContext(), ServletUtils.getRequest(), ServletUtils.getResponse());
            if (log.isDebugEnabled())
                log.debug("刷新用户【{}】登录信息成功", LoginContextHolder.currentUser().getUsername());
        }
    }


    private final cn.hutool.cache.Cache<String, Integer> LOGIN_FAILED_COUNT_CACHE = CacheUtil.newLRUCache(64);
    private final cn.hutool.cache.Cache<String, Long> LOGIN_FAILED_TIME_CACHE = CacheUtil.newLRUCache(64);
    private final cn.hutool.cache.Cache<String, Long> LOGIN_LOCK_TIME_CACHE = CacheUtil.newLRUCache(64);
    private static final String LOGIN_FAILED_COUNT_PREFIX = "ailand:login:failedCount:";
    private static final String LOGIN_FAILED_TIME_PREFIX = "ailand:login:failedTime:";
    private static final String TEMP_LOGIN_LOCK_PREFIX = "ailand:login:failedTooManyLock:";


    /**
     * 重置登录失败次数
     *
     * @param username 登录账号
     */
    public void resetLoginFailedTimes(String username) {
        LOGIN_FAILED_COUNT_CACHE.remove(LOGIN_FAILED_COUNT_PREFIX + username);
        LOGIN_FAILED_TIME_CACHE.remove(LOGIN_FAILED_TIME_PREFIX + username);
    }

    public void checkAccountIsLock(String username) {
        if (LOGIN_LOCK_TIME_CACHE.containsKey(TEMP_LOGIN_LOCK_PREFIX + username)) {
            Long lockTime = LOGIN_LOCK_TIME_CACHE.get(TEMP_LOGIN_LOCK_PREFIX + username);
            throw new BadCredentialsException("登录失败次数过多，请在" + formatSeconds((lockTime - System.currentTimeMillis()) / 1000) + "后登录!");
        }
    }

    public void afterLoginFailed(String username) {
        LoginSetting loginSetting = getLoginSetting(username);
        Integer allowFailedCount = loginSetting.getMaxFailNum();
        long lockPeriod = loginSetting.getLockPeriod();
        Integer failedCount = LOGIN_FAILED_COUNT_CACHE.get(LOGIN_FAILED_COUNT_PREFIX + username);
        failedCount = failedCount == null ? 0 : failedCount;
        if (++failedCount >= allowFailedCount) {
            lockUserWhenFailed(username);
        } else {
            addLoginFailedTimes(allowFailedCount, failedCount, username, lockPeriod);
        }
    }

    private void addLoginFailedTimes(Integer allowFailedCount, Integer failedCount, String account, long lockPeriod) {
        Long firstTime = LOGIN_FAILED_TIME_CACHE.get(LOGIN_FAILED_TIME_PREFIX + account);
        long time;
        if (firstTime == null) {
            firstTime = System.currentTimeMillis() + lockPeriod * 1000;
            time = lockPeriod;
            LOGIN_FAILED_TIME_CACHE.put(LOGIN_FAILED_TIME_PREFIX + account, firstTime, time * 1000);
        } else {
            time = (firstTime - System.currentTimeMillis()) / 1000;
            if (time <= 0)
                time = lockPeriod;
        }

        int num = allowFailedCount - failedCount;
        LOGIN_FAILED_COUNT_CACHE.put(LOGIN_FAILED_COUNT_PREFIX + account, failedCount, time * 1000);
        throw new BadCredentialsException("用户名或密码错误，在" + formatSeconds(time) + "内，您还有" + num + "次机会！");
    }

    private void lockUserWhenFailed(String username) {
        String lockKey = TEMP_LOGIN_LOCK_PREFIX + username;
        long lockTime = getLoginSetting(username).getUnlockTime();
        LOGIN_LOCK_TIME_CACHE.put(lockKey, System.currentTimeMillis() + lockTime * 1000, lockTime * 1000);
        throw new BadCredentialsException("登录失败次数过多，请在" + formatSeconds(lockTime) + "后登录!");
    }

    private LoginSetting getLoginSetting(String username) {
        String loginSettingKey;
        if (StringUtils.equals(username, "admin")) {
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "data_route_public" + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        } else {
            List<User> byAccountLike = userRepository.findByAccountLike(username);
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "tenant_" + byAccountLike.getFirst().getCompanyId() + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        }
        if (!FileUtil.exist(loginSettingKey)) {
            FileUtil.writeUtf8String(JSONUtil.toJsonStr(new LoginSetting()), loginSettingKey);
            return new LoginSetting();
        }
        return JSONUtil.toBean(FileUtil.readUtf8String(loginSettingKey), LoginSetting.class);
    }

    public static String formatSeconds(long time) {
        long hour = time / 3600;
        long minute = (time % 3600) / 60;
        long seconds = time % 60;

        StringBuilder result = new StringBuilder();
        if (hour > 0) {
            result.append(hour).append("小时");
        }
        if (minute > 0) {
            result.append(minute).append("分钟");
        }
        if (seconds > 0 || result.isEmpty()) { // 如果没有小时和分钟，确保显示秒
            result.append(seconds).append("秒");
        }

        return result.toString();
    }

    public CurrentUserResponse currentUser() {
        RoleEnums currentUserRole = LoginContextHolder.currentUserRole();
        CurrentUserResponse currentUserResponse = new CurrentUserResponse();
        currentUserResponse.setDeployMode(aiLandProperties.getDeploy().getMode());
        UserDTO userDTO = LoginContextHolder.currentUser();
        currentUserResponse.setUserDTO(userDTO);
        currentUserResponse.setRole(LoginContextHolder.currentUserRole());
        List<UserRole> userRoles = userRoleRepository.findByUserId(userDTO.getId());
        UserRole userRole;
        if (userRoles.isEmpty()) {
            // 首次登录时 用户角色还未持久化
            userRole = new UserRole();
            userRole.setRoleId(RoleEnums.SUPER_ADMIN.name());
            userRole.setUserId(userDTO.getId());
        } else
            userRole = userRoles.stream().filter(ur -> StringUtils.equals(ur.getRoleId(), currentUserRole.name()))
                    .findFirst().orElseThrow(() -> new RestfulApiException("不识别的角色类型"));
//        NodeInfoResponse nodeInfoResponse = routerService.currentNode();
//        if (!ObjectUtil.equals(nodeInfoResponse.getStatus(), RouteStatus.activated))
//            currentUserResponse.setMenu(new HashSet<>(List.of("RouterRegister")));
//        else {
        // 检查license 有效性
        LicenceInfoDTO licenceInfo = licenceService.getLicenceInfo();
        CurrentUserResponse.License license = new CurrentUserResponse.License();
        // 机器上检测不到lic
        if (checkLicense) {
            if (licenceInfo == null) {
                if (LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN))
                    currentUserResponse.setMenu(new HashSet<>(List.of("LicenseManager")));
                else
                    currentUserResponse.setMenu(new HashSet<>(List.of("Login")));
                license.setMessage("平台没有检测到有效的许可证文件，请登录并上传您的许可");
                license.setStatus(CurrentUserResponse.LicenseStatus.not_import_license);
                currentUserResponse.setLicense(license);
                return currentUserResponse;
            }
            boolean isOfficial = licenceInfo.getLicenseType() == 1;
            boolean licenseExpired = new Date().after(licenceInfo.getProductExpireTime());
            int daysBeforeExpire = DateTimeUtils.daysBetween(new Date(), licenceInfo.getProductExpireTime());
            if (licenseExpired) {
                if (isOfficial) {
                    license.setStatus(CurrentUserResponse.LicenseStatus.official_license_expired);
                    license.setMessage(aiLandProperties.getLicenseMessages().getExpiredOfficial());
                } else {
                    license.setStatus(CurrentUserResponse.LicenseStatus.not_official_license_expired);
                    license.setMessage(aiLandProperties.getLicenseMessages().getExpiredNonOfficial());
                    if (LoginContextHolder.currentUserRole().equals(RoleEnums.TRADER)) {
                        currentUserResponse.setLicense(license);
                        currentUserResponse.setMenu(new HashSet<>(List.of("DataMarket")));
                        return currentUserResponse;
                    }
                }
            } else if (daysBeforeExpire < aiLandProperties.getLicenseMessages().getDaysThreshold()) {
                if (isOfficial) {
                    license.setStatus(CurrentUserResponse.LicenseStatus.official_license_expired_lt_90_days);
                    license.setMessage(String.format(aiLandProperties.getLicenseMessages().getWillExpireOfficial(), daysBeforeExpire));
                } else {
                    license.setStatus(CurrentUserResponse.LicenseStatus.not_official_license_expired_lt_90_days);
                    license.setMessage(String.format(aiLandProperties.getLicenseMessages().getWillExpireNonOfficial(), daysBeforeExpire));
                    currentUserResponse.setLicense(license);
                }
            }
        }

        currentUserResponse.setLicense(license);
//        if (LoginContextHolder.currentUserRole().equals(RoleEnums.COMPANY_ADMIN)) {
//            if (!ObjectUtil.equals(LoginContextHolder.currentUser().getCompany().getStatus(), CompanyStatus.REVIEW_PASS)) {
//                currentUserResponse.setMenu(new HashSet<>(List.of("CompanyRegister")));
//                return currentUserResponse;
//            }
//        }
        Permission p = new Permission();
        p.setRoleId(userRole.getRoleId());
        List<Permission> permissions = permissionRepository.findAll(Example.of(p));
        List<Menu> menus = menuRepository.findAllById(permissions.stream().map(Permission::getMenuId).collect(Collectors.toList()));
        currentUserResponse.setMenu(menus.stream().map(Menu::getId).collect(Collectors.toSet()));
        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        if (!baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.AI_GATE)) {
            currentUserResponse.setMenu(currentUserResponse.getMenu().stream()
                    .filter(id -> !"DATABASE".equals(id))
                    .collect(Collectors.toSet()));
        }
//        }
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            userRoleRepository.saveAndFlush(userRole);
            if (aiLandProperties.getDeploy().getMode().equals(DeployMode.share))
                currentUserResponse.setMenu(currentUserResponse.getMenu().stream().filter(x -> !StringUtils.equals(x, "MPC")).collect(Collectors.toSet()));
        }
        if (!baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.DATA_INVOICE)) {
            Set<String> menu = currentUserResponse.getMenu();
            menu.remove("DataInvoiceStorage");
            currentUserResponse.setMenu(menu);
        }

        if (!baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.AI_SORT)) {
            Set<String> menu = currentUserResponse.getMenu();
            menu.remove("DataProbe");
            currentUserResponse.setMenu(menu);
        }
        if (currentUserResponse.getMenu().contains("Home")) {
            Set<String> oldMenus = currentUserResponse.getMenu();
            oldMenus.remove("Home");
            Set<String> newMenus = new LinkedHashSet<>();
            newMenus.add("Home");
            newMenus.addAll(oldMenus);
            currentUserResponse.setMenu(newMenus);
        }
        if (DeployMode.share.equals(aiLandProperties.getDeploy().getMode())) {
            // sass 节点MPC,TEE不可用
            Set<String> menu = currentUserResponse.getMenu();
            menu.remove("MPC");
            currentUserResponse.setMenu(menu);
        }
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.TRADER) && !Boolean.TRUE.equals(LoginContextHolder.currentUser().getReviewPass())) {
            currentUserResponse.setMenu(Set.of("SubmitAgentPersonInfo"));
        }
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            // admin 不配置 TEE
            currentUserResponse.getMenu().remove("TEE");
        }
        return currentUserResponse;
    }

    public UserDetailsResponse userDetail(String userId) {
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.TRADER)) {
            userId = LoginContextHolder.currentUser().getId();
        }
        User user = userRepository.findById(userId).orElseThrow(() -> new RestfulApiException("非法操作"));
        return userMapper.toDetails(user);
    }

    public UserDetailsResponse userDetailForThird(String userId) {
        User user = userRepository.findById(userId).orElseThrow(() -> new RestfulApiException("非法操作"));
        return userMapper.toDetails(user);
    }

    private final JPAQueryFactory jpaQueryFactory;

    public SuccessResponse<List<UserListResponse>> userList(UserPageRequest request) {
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        BooleanBuilder booleanBuilder = new BooleanBuilder(user.deleted.isFalse());
        if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN))
            booleanBuilder.and(userRole.roleId.eq(RoleEnums.COMPANY_ADMIN.name()));
        else if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.COMPANY_ADMIN))
            booleanBuilder.and(userRole.roleId.eq(RoleEnums.TRADER.name()));
        if (StringUtils.isNotEmpty(request.getAccount()))
            booleanBuilder.and(user.account.contains(request.getAccount()));
        if (StringUtils.isNotEmpty(request.getName()))
            booleanBuilder.and(user.ext.delegateInfo.delegateName.contains(request.getName()));
        assert booleanBuilder.getValue() != null;
        Long total = jpaQueryFactory.select(user.countDistinct()).from(user).leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(booleanBuilder).fetch().getFirst();
        List<User> users = jpaQueryFactory.select(user).from(user)
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .orderBy(user.createTime.desc())
                .where(booleanBuilder).fetch();
        return SuccessResponse.success(users.stream().map(userMapper::toListRsp).collect(Collectors.toList()))
                .total(total).build();
    }

    //    public final IamRemoteService iamRemoteService;
    private final UserMapper userMapper;
    private final KeyPair keyPair;
    private final PasswordEncoder passwordEncoder;

    @Value("${ailand.endpoint.ip}")
    private String endpointIpPort;
    private final UserRemoteService userRemoteService;

    @Transactional(rollbackFor = Exception.class)
    public Tuple2<Tuple4<String, User, UserRole, Boolean>, Company> registerCompanyUser(CompanyUserRegisterRequest request) {
        Assert.isTrue(!StringUtils.equals(request.getAccount(), "admin"), "账号已存在，不允许重复注册");
        // 1. 初始化企业信息
        CompanyService companyService = SpringUtil.getBean(CompanyService.class);
        AddCompanyRequest addCompanyRequest = new AddCompanyRequest();
        addCompanyRequest.setServiceNodeId(request.getServiceNodeId());
        addCompanyRequest.setCreditCode(request.getCreditCode());
        addCompanyRequest.setOrganizationName(request.getCompanyName());
        Company company = companyService.add(addCompanyRequest);
        Long companyId = company.getId();

        // 2. 构建用户请求
        AddUserRequest addUserRequest = new AddUserRequest();
        addUserRequest.setAccount(request.getAccount());
        addUserRequest.setName(request.getName());
        addUserRequest.setMobile(request.getMobile());
        addUserRequest.setEmail(request.getEmail());
        addUserRequest.setPassword(request.getPassword());

        // 3. 设置企业相关信息
        addUserRequest.setLocalCompanyId(String.valueOf(companyId));
        addUserRequest.setLoginUrl(String.format("%s/data-route/#/login?schema=%s", endpointIpPort, TenantConstant.TENANT_SCHEMA_PREFIX + companyId));
        addUserRequest.setExpireDate(request.getExpireDate());
        addUserRequest.setCompanyAdmin(true);

        NodeService nodeService = SpringUtil.getBean(NodeService.class);
        NodeDTO node = nodeService.getNode(request.getServiceNodeId());
        // 写入功能节点信息，以便后续调用功能节点正常
        addUserRequest.setHubInfo(node.getHubInfo());
        // 4. 调用现有方法创建用户
        return new Tuple2<>(addUser(addUserRequest), company);
    }

    private final ServiceNodeRemoteService serviceNodeRemoteService;

    @Transactional(rollbackFor = Exception.class)
    public Tuple4<String, User, UserRole, Boolean> addUser(AddUserRequest addUserRequest) {
        // 如果用户自己输入了密码 不用强制修改密码
        boolean needResetPwd;
        if (StringUtils.isNotEmpty(addUserRequest.getPassword())) {
            addUserRequest.setPassword(decryptPwd(addUserRequest.getPassword()));
            needResetPwd = false;
        } else {
            addUserRequest.setPassword(generatePwd());
            needResetPwd = true;
        }
        if (LoginContextHolder.isLogin() && ObjectUtil.isNotNull(LoginContextHolder.currentUser().getExpireDate()))
            addUserRequest.setExpireDate(LoginContextHolder.currentUser().getExpireDate());

        User.Ext ext = new User.Ext();
//        if (addUserRequest.getCompanyAdmin()) {
//            addUserRequest.setUserType("companyManager");
//            addUserRequest.setHubInfo(SpringUtil.getBean(CompanyService.class).getHubInfo(addUserRequest.getCompanyId()));
//            CommonResult<AddUserResponse> addUserResponse = userRemoteService.addCompanyAdmin(addUserRequest);
//            Assert.isTrue(addUserResponse.isSuccess(), addUserResponse.getMessage());
////            CommonResult<AddUserResponse> addUserResponseCommonResult = serviceNodeRemoteService.addCompanyAdmin(addUserRequest);
////            Assert.isTrue(addUserResponseCommonResult.isSuccess(), addUserResponseCommonResult.getMessage());
//            ext.setId(addUserResponse.getData().getId());
//        } else {
        addUserRequest.setUserType("normal");
        addUserRequest.setHubInfo(SpringUtil.getBean(CompanyService.class).getHubInfo(LoginContextHolder.isLogin() && !ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN) ? LoginContextHolder.currentUser().getCompany().getId() : addUserRequest.getTargetCompanyId()));
//        CommonResult<AddUserResponse> addUserResponse = userRemoteService.addUser(addUserRequest);
//        Assert.isTrue(addUserResponse.isSuccess(), addUserResponse.getMessage());
        NodeDTO.HubInfo hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo(LoginContextHolder.isLogin() && !ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN) ? LoginContextHolder.currentUser().getCompany().getId() : addUserRequest.getTargetCompanyId());
        hubInfo.setUrl(hubInfo.getServiceNodeUrl());
        addUserRequest.setHubInfo(hubInfo);
        if (LoginContextHolder.isLogin() && !ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN)) {
            addUserRequest.setEntityId(LoginContextHolder.currentUser().getCompany().getThirdBusinessId());
        }

        CommonResult<AddUserResponse> addUserResponse = serviceNodeRemoteService.addUser(addUserRequest, addUserRequest.getNodeId());
        Assert.isTrue(addUserResponse.isSuccess(), addUserResponse.getMessage());
        ext.setId(addUserResponse.getData().getId());
//        }


        User user = userMapper.toDo(addUserRequest);

        user.setCompanyId(
                ObjectUtil.isNull(addUserRequest.getLocalCompanyId()) ? LoginContextHolder.currentUser().getCompany().getId()
                        : Long.valueOf(addUserRequest.getLocalCompanyId()));

        ext.setUrl(addUserRequest.getLoginUrl());
        ext.setExpireDate(addUserRequest.getExpireDate());
//        ext.setResetInitPwd(!needResetPwd);
        ext.setResetInitPwd(true);
        if (!addUserRequest.getCompanyAdmin()) {
            ext.setReviewPass(false);
            ext.setAuthStatus(IdentityAuthStatus.init);
        }

        user.setExt(ext);
        user.setId(UuidUtils.uuid32());
        user.setPassword(passwordEncoder.encode(addUserRequest.getPassword()));
        user.setCreateTime(new Date());
        user.setDeleted(false);
        user.setEnabled(true);
        userRepository.saveAndFlush(user);
        Role role = roleRepository.findByName(RoleEnums.TRADER.name());
        if (addUserRequest.getCompanyAdmin()) {
            role = roleRepository.findByName(RoleEnums.COMPANY_ADMIN.name());
        }
        UserRole userRole = new UserRole();
        userRole.setRoleId(role.getName());
        userRole.setUserId(user.getId());
        userRoleRepository.saveAndFlush(userRole);
        return new Tuple4<>(addUserRequest.getPassword(), user, userRole, addUserRequest.getCompanyAdmin());
    }

    private static final String UPPER_CASE_LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER_CASE_LETTERS = "abcdefghijklmnopqrstuvwxyz";
    private static final String NUMBERS = "0123456789";
    private static final String SPECIAL_CHARACTERS = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    private static final SecureRandom random = new SecureRandom();

    private static String generatePwd() {

        ArrayList<Character> passwordChars = new ArrayList<>();

        // 确保密码中至少包含一个大写字母、一个小写字母、一个数字和一个特殊字符
        passwordChars.add(UPPER_CASE_LETTERS.charAt(random.nextInt(UPPER_CASE_LETTERS.length())));
        passwordChars.add(LOWER_CASE_LETTERS.charAt(random.nextInt(LOWER_CASE_LETTERS.length())));
        passwordChars.add(NUMBERS.charAt(random.nextInt(NUMBERS.length())));
        passwordChars.add(SPECIAL_CHARACTERS.charAt(random.nextInt(SPECIAL_CHARACTERS.length())));

        // 填充剩余的字符
        String allChars = UPPER_CASE_LETTERS + LOWER_CASE_LETTERS + NUMBERS + SPECIAL_CHARACTERS;
        for (int i = 4; i < 8; i++) {
            passwordChars.add(allChars.charAt(random.nextInt(allChars.length())));
        }

        // 打乱字符顺序
        Collections.shuffle(passwordChars);

        // 将字符列表转换为字符串
        StringBuilder password = new StringBuilder(passwordChars.size());
        for (char c : passwordChars) {
            password.append(c);
        }

        return password.toString();
    }

    public Boolean checkNameRepeat(String userId, String account) {
        int count;
        if (StringUtils.isNotEmpty(userId))
            count = userRepository.countByAccountAndDeletedFalseAndIdIsNot(account, userId);
        else
            count = userRepository.countByAccountAndDeletedFalse(account);
        return count > 0;
    }

    public String decryptPwd(String encryptPwd) {
        if (!needDecryptPwd)
            return encryptPwd;
        try {
            return new String(
                    RSAUtil.decryptLargeDataByPrivateKey(
                            Base64.getDecoder().decode(encryptPwd.getBytes(StandardCharsets.UTF_8)),
                            (RSAPrivateKey) keyPair.getPrivate()),
                    StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密密码异常：", e);
            throw new RestfulApiException("密码或用户名错误", e);
        }
    }

    private void changePwdCheck(User user, String oldPwd, String newPwd, String confirmPwd) {
        Assert.isTrue(passwordEncoder.matches(oldPwd, user.getPassword()), "输入的旧密码错误");
        Assert.isTrue(StringUtils.equals(newPwd, confirmPwd), "输入的新密码与确认密码不一致");
        Assert.isTrue(!StringUtils.equals(newPwd, oldPwd), "输入的新密码不能与旧密码相同");
    }

    public void updatePublic(User user) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        userRepository.saveAndFlush(user);
        if (log.isDebugEnabled())
            log.debug("用户【{}】数据同步至主库成功", user.getId());
    }


    public void updatePublic(User user, UserRole userRole) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        userRepository.saveAndFlush(user);
        userRoleRepository.saveAndFlush(userRole);
        if (log.isDebugEnabled())
            log.debug("用户【{}】数据同步至主库成功", user.getId());
    }

    @Async
    public void updateSchema(User user) {
        TenantContext.setCurrentTenant(TenantConstant.TENANT_SCHEMA_PREFIX + user.getCompanyId());
        userRepository.saveAndFlush(user);
        if (log.isDebugEnabled())
            log.debug("用户【{}】数据同步至租户库【{}】成功", user.getId(), TenantConstant.TENANT_SCHEMA_PREFIX + user.getCompanyId());
    }

    @Async
    public void updateSchema(User user, UserRole userRole) {
        TenantContext.setCurrentTenant("tenant_" + user.getCompanyId());
        userRepository.saveAndFlush(user);
        userRoleRepository.saveAndFlush(userRole);
        if (log.isDebugEnabled())
            log.debug("用户【{}】数据同步至租户库【{}】成功", user.getId(), TenantConstant.TENANT_SCHEMA_PREFIX + user.getCompanyId());
    }

    @Value("${needDecryptPwd:true}")
    private Boolean needDecryptPwd;

    /**
     * 修改个人信息，包括真实姓名 手机号 初始密码
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Tuple2<Boolean, User> updateUser(UpdateUserRequest request) {
        boolean changePwd = StringUtils.isNotEmpty(request.getNpassword())
                && StringUtils.isNotEmpty(request.getCpassword()) && StringUtils.isNotEmpty(request.getOpassword());
        if (changePwd || StringUtils.isNotEmpty(request.getEmail()) || StringUtils.isNotEmpty(request.getMobile())
                || StringUtils.isNotEmpty(request.getName()))
            if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.TRADER))
                request.setId(LoginContextHolder.currentUser().getId());
        User user = userRepository.findById(request.getId()).orElseThrow(() -> new RestfulApiException("非法操作"));
        User.Ext ext = user.getExt();
        User.DelegateInfo delegateInfo = ext.getDelegateInfo();
        if (changePwd) {
//            Assert.isTrue(ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.TRADER),
//                    "非法操作：admin不允许修改密码");
            request.setOpassword(decryptPwd(request.getOpassword()));
            request.setNpassword(decryptPwd(request.getNpassword()));
            request.setCpassword(decryptPwd(request.getCpassword()));
            changePwdCheck(user, request.getOpassword(), request.getNpassword(), request.getCpassword());
            ext.setResetInitPwd(true);
        }
        com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.UpdateUserRequest updateUserRequest = new com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.UpdateUserRequest();
        updateUserRequest.setUuid(user.getId());
        updateUserRequest.setAccountNumber(user.getAccount());

        if (StringUtils.isNotEmpty(request.getName())) {
            updateUserRequest.setUserName(request.getName());
            user.setRealName(request.getName());
        }
        if (StringUtils.isNotEmpty(request.getEmail())) {
            user.setEmail(request.getEmail());
            if (ObjectUtil.isNotNull(delegateInfo))
                delegateInfo.setDelegateEmail(request.getEmail());
        }
        if (StringUtils.isNotEmpty(request.getMobile())) {
            user.setPhone(request.getMobile());
            if (ObjectUtil.isNotNull(delegateInfo))
                delegateInfo.setDelegatePhone(request.getMobile());

        }
        if (changePwd) {
            user.setPassword(passwordEncoder.encode(request.getNpassword()));
            updateUserRequest.setPassword(request.getCpassword());
        }

        ext.setExpireDate(request.getExpireDate());
        ext.setDelegateInfo(delegateInfo);
        user.setExt(ext);
        user.setUpdateTime(new Date());
        userRepository.saveAndFlush(user);
//        UserCommRes userCommResResponse = iamRemoteService.updateUser(updateUserRequest).body();
//        Assert.isTrue(ObjectUtil.isNotNull(userCommResResponse), "零信任统一认证中心创建用户 接口返回为空");
//        Assert.isTrue(userCommResResponse.isSuccess(), "零信任更新用户信息异常：" + userCommResResponse.getMessage());

        HubUpdateUserRequest hubUpdateUserRequest = BeanUtil.copyProperties(request, HubUpdateUserRequest.class, "id");
        hubUpdateUserRequest.setId(JSONUtil.parseObj(user.getExt()).getLong("id"));
        CommonResult<Boolean> booleanCommonResult;
        if (changePwd) {
            if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.COMPANY_ADMIN)) {
                booleanCommonResult = userRemoteService.updateCompanyUser(hubUpdateUserRequest);
            } else
                booleanCommonResult = userRemoteService.updateUser(hubUpdateUserRequest);
        } else {
            if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN)) {
                // 更新过期时间不需要调用枢纽
                if (request.getUpdateExpireDate() || StringUtils.equals(request.getId(), "1"))
                    return new Tuple2<>(true, user);

                hubUpdateUserRequest.setHubInfo(SpringUtil.getBean(CompanyService.class).getHubInfo(user.getCompanyId()));
                booleanCommonResult = userRemoteService.updateCompanyUser(hubUpdateUserRequest);
            } else
                booleanCommonResult = userRemoteService.updateUser(hubUpdateUserRequest);
        }
        Assert.isTrue(booleanCommonResult.isSuccess(), "更新用户信息异常：" + booleanCommonResult.getMessage());

        return new Tuple2<>(true, user);
    }

    @Transactional(rollbackFor = Exception.class)
    public Tuple2<String, User> resetPwd(String userId) {
        User user = userRepository.findById(userId).orElseThrow(() -> new RestfulApiException("非法操作"));
        String randomPwd = generatePwd();
        user.setPassword(passwordEncoder.encode(randomPwd));
        User.Ext ext = user.getExt();
        ext.setResetInitPwd(false);
        user.setExt(ext);
        userRepository.saveAndFlush(user);
        ResetPwdRequest resetPwdRequest = new ResetPwdRequest();
        resetPwdRequest.setPassword(randomPwd);
        resetPwdRequest.setId(JSONUtil.parseObj(user.getExt()).getLong("id"));
        CommonResult<Boolean> booleanCommonResult;
        if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN)) {
            resetPwdRequest.setHubInfo(SpringUtil.getBean(CompanyService.class).getHubInfo(user.getCompanyId()));
            booleanCommonResult = userRemoteService.companyUserResetPwd(resetPwdRequest);
        } else
            booleanCommonResult = userRemoteService.resetPwd(resetPwdRequest);
        Assert.isTrue(booleanCommonResult.isSuccess(), "更新用户信息异常：" + booleanCommonResult.getMessage());
        return new Tuple2<>(randomPwd, user);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteUser(String userId) {
        User user = userRepository.findById(userId).orElseThrow(() -> new RestfulApiException("非法操作"));
        // 只能删除未登录过的账号
        Assert.isTrue(user.getExt().getLastLoginTime() == null, "只能删除未登录过的账号");
        user.setDeleted(true);
        userRepository.saveAndFlush(user);
        com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.DeleteUserRequest iamDeleteUser = new com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.DeleteUserRequest();
        iamDeleteUser.setUuid(userId);
        CommonResult<String> deleteUser = userRemoteService.delUser(user.getExt().getId());
        Assert.isTrue(deleteUser.isSuccess(), "调用基础支持平台【删除经办人用户】接口异常：" + deleteUser.getMessage());
        return true;
    }

    public List<UserDetailsResponse> findBy(String account) {
        // CommonResult<List<UserDetailsResponse>> findBy =
        // userRemoteService.findBy(username, account);
        // Assert.isTrue(findBy.isSuccess(), findBy.getMsg());
        List<User> users = userRepository.findByAccountLike(account);
        return users.stream().map(userMapper::toDetails).collect(Collectors.toList());
    }

    private final UserRepository userRepository;
    private final CompanyMapper companyMapper;

    public void addEntityUser(String userId, String username, String password, Long companyId) {
        int count = userRepository.countByAccountAndDeletedFalse(username);
        if (count == 0) {
            // 新增接入主体用户
            User user = new User();

            user.setCompanyId(companyId);
            User.Ext ext = new User.Ext();
            ext.setResetInitPwd(true);
            ext.setId(userId);
            user.setExt(ext);
            user.setId(UuidUtils.uuid32());
            user.setPassword(passwordEncoder.encode(password));
            user.setCreateTime(new Date());
            user.setDeleted(false);
            user.setEnabled(true);
            userRepository.saveAndFlush(user);
            Role role = roleRepository.findByName(RoleEnums.COMPANY_ADMIN.name());
            UserRole userRole = new UserRole();
            userRole.setRoleId(role.getName());
            userRole.setUserId(user.getId());
            userRoleRepository.saveAndFlush(userRole);
            // 数据同步至接入主体schema
            updateSchema(user, userRole);
        }
    }

    @Override
    public UserDTO loadUserByUsername(String username) throws UsernameNotFoundException {
        User user = userRepository.findFirstByAccountOrderByCreateTimeDesc(username);
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户异常，请检查登录地址与账号是否匹配");
        Assert.isTrue(!user.getDeleted(), "user is deleted");
        List<UserRole> userRoles = userRoleRepository.findByUserId(user.getId());
        User.Ext ext = user.getExt();
        UserDTO.UserDTOBuilder userDTOBuilder = UserDTO.builder();

        userDTOBuilder.id(user.getId())
                .phone(user.getPhone()).email(user.getEmail())
                .securityCode(user.getPassword()).username(username)
                .realName(ObjectUtil.isNotNull(user.getExt().getDelegateInfo()) ? user.getExt().getDelegateInfo().getDelegateName() : user.getRealName())
                .roleName(userRoles.getFirst().getRoleId()).enable(user.getEnabled())
                .resetInitPwd(ext.getResetInitPwd())
                .expireDate(ext.getExpireDate());
        if (userRoles.getFirst().getRoleId().equals(RoleEnums.TRADER.name()))
            userDTOBuilder.reviewPass(user.getExt().getReviewPass());
        try {
            userDTOBuilder.idShuhan(JSONUtil.parseObj(user.getExt()).getStr("id"));
        } catch (Exception ignore) {
        }
        if (ObjectUtil.isNull(user.getCompanyId()))
            userDTOBuilder
                    .company(CompanyDTO.builder().schema("data_route_public").build())
                    .build();
        else {
            CompanyService companyService = SpringUtil.getBean(CompanyService.class);
            CompanyDTO detail = companyService.detail(user.getCompanyId());
            if (userRoles.getFirst().getRoleId().equals(RoleEnums.TRADER.name()) && Boolean.TRUE.equals(user.getExt().getReviewPass())) {
                BeanUtil.copyProperties(user.getExt().getDelegateInfo(), detail);
                detail.setAuthorizationLetter(user.getExt().getDelegateInfo().getAuthorizationLetterLocalUrl());
//                detail.setDelegateContact(user.getExt().getDelegateInfo().getDelegatePhone());
//                detail.setDelegateAuthMethod(user.getExt().getDelegateInfo().getDelegateAuthType());
            }
            userDTOBuilder.company(detail);
//            if (detail.second)
//                companyService.updatePublicSchema(detail.first);
        }
        return userDTOBuilder.build();
    }


    private final CompanyRemoteService companyRemoteService;

    public void submitDelegateToReview(DelegateInfoRequest request) {
//        CompanyApplyDTO dto = BeanUtil.copyProperties(request, CompanyApplyDTO.class);
//        dto.setOrganizationName(LoginContextHolder.currentUser().getCompany().getOrganizationName());
//        dto.setCreditCode(LoginContextHolder.currentUser().getCompany().getCreditCode());
//        dto.setAccessType(AccessType.AGENT_PERSON);
//        CompanyVerifyRequest verifyRequest = companyMapper.toVerifyRequest(dto);
//        com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<Boolean> companyVerify = companyRemoteService.companyVerify(verifyRequest);
//        Assert.isTrue(companyVerify.isSuccess(), "提交经办人信息异常，请联系管理员");
        User user = userRepository.findById(LoginContextHolder.currentUser().getId()).orElseThrow(() -> new RestfulApiException("用户数据异常"));
        User.Ext ext = user.getExt();
        ext.setAuthStatus(IdentityAuthStatus.review_pass);
        ext.setReviewPass(true);
        ext.setReviewTime(System.currentTimeMillis());
        ext.setDelegateInfo(BeanUtil.copyProperties(request, User.DelegateInfo.class));
        user.setExt(ext);
        userRepository.saveAndFlush(user);
        ThreadUtil.execAsync(() -> updatePublic(user));
        reloadUserDetails();
    }

    public User findCompanyAdminByCompanyId(Long companyId) {
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        List<User> users = jpaQueryFactory.selectFrom(user)
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(userRole.roleId.eq(RoleEnums.COMPANY_ADMIN.name()).and(user.companyId.eq(companyId))).fetch();
        // 接入主体目前只有一个管理员
        return users.stream().findFirst().orElse(null);
    }


    // 禁用用户
    public User disable(String id) {
        User user = userRepository.findById(id).orElseThrow(() -> new RestfulApiException("用户不存在"));
        user.setEnabled(false);
        userRepository.saveAndFlush(user);
        return user;
    }

    /**
     * 禁用接入主体下面的所有用户
     *
     * @param id 接入主体唯一标识
     */
    public List<User> disableCompanyUsers(Long id) {
        List<User> users = userRepository.findByCompanyId(id);
        List<User> updatedUsers = new ArrayList<>();
        users.forEach(u -> {
            User disable = disable(u.getId());
            updatedUsers.add(disable);
        });
        return updatedUsers;
    }

    public List<User> enableCompanyUsers(Long id) {
        List<User> users = userRepository.findByCompanyId(id);
        List<User> updatedUsers = new ArrayList<>();
        users.forEach(u -> {
            User disable = enable(u.getId());
            updatedUsers.add(disable);
        });
        return updatedUsers;
    }

    // 启用用户
    public User enable(String id) {
        User user = userRepository.findById(id).orElseThrow(() -> new RestfulApiException("用户不存在"));
        user.setEnabled(true);
        userRepository.saveAndFlush(user);
        return user;
    }

    private final SessionRegistry sessionRegistry;

    public void destroyUserSession(String userId, Long companyId) {
        TenantContext.setCurrentTenant("tenant_" + companyId);
        // 获取所有session
        List<Object> principals = sessionRegistry.getAllPrincipals();

        // 遍历查找目标用户的session
        for (Object principal : principals) {
            if (principal instanceof UserDTO userDTO && userDTO.getId().equals(userId)) {
                // 获取用户的所有session
                List<SessionInformation> sessionInfo = sessionRegistry.getAllSessions(principal, false);
                // 使所有session失效
                sessionInfo.forEach(SessionInformation::expireNow);
            }
        }
        if (log.isDebugEnabled())
            log.debug("销毁用户【{}】 登录session信息成功", userId);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<User> updateAccessEntityUserExpireDate(Long id, Date date) {
        List<User> users = userRepository.findByCompanyId(id);
        List<User> updatedUsers = new ArrayList<>();
        users.forEach(user -> {
            UpdateUserRequest updateUserRequest = new UpdateUserRequest();
            updateUserRequest.setId(user.getId());
            updateUserRequest.setExpireDate(date);
            updateUserRequest.setUpdateExpireDate(true);
            Tuple2<Boolean, User> updateUser = updateUser(updateUserRequest);
            updatedUsers.add(updateUser.second);
        });

        return updatedUsers;
    }


    public void saveLoginSetting(LoginSettingVO vo) {
        LoginSetting loginSetting = BeanUtil.copyProperties(vo, LoginSetting.class);
        String loginSettingKey;
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.COMPANY_ADMIN)) {
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "tenant_" + LoginContextHolder.currentUser().getCompany().getId() + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        } else {
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "data_route_public" + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        }
        FileUtil.writeUtf8String(JSONUtil.toJsonStr(loginSetting), loginSettingKey);
    }

    public LoginSettingVO loginSetting() {
        String loginSettingKey;
        if (LoginContextHolder.currentUserRole().equals(RoleEnums.COMPANY_ADMIN)) {
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "tenant_" + LoginContextHolder.currentUser().getCompany().getId() + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        } else {
            loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "data_route_public" + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        }
        if (!FileUtil.exist(loginSettingKey))
            return new LoginSettingVO();
        return JSONUtil.toBean(FileUtil.readUtf8String(loginSettingKey), LoginSettingVO.class);
    }


    private final EndpointRemote endpointRemote;

    public void preview(String authorizationLetter, String targetNodeId, Long targetCompanyId) throws IOException {
        if (authorizationLetter.contains("../"))
            throw new RestfulApiException("非法文件类型");
        String filename = StringUtils.contains(authorizationLetter, "/") ? StringUtils.substringAfterLast(authorizationLetter, "/") : authorizationLetter;
        Path attachFilePath = Paths.get(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "company", filename);
        HttpServletResponse response = ServletUtils.getResponse();
        if (attachFilePath.toFile().exists()) {
            try (InputStream inputStream = Files.newInputStream(attachFilePath)) {
                ServletOutputStream outputStream = response.getOutputStream();
                response.reset();
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
                byte[] bytes = new byte[2048];
                int len;
                while ((len = inputStream.read(bytes)) > 0) {
                    outputStream.write(bytes, 0, len);
                }
                outputStream.close();
            }
        } else {
            PreviewAuthorizationLetterRequest previewAuthorizationLetterRequest = new PreviewAuthorizationLetterRequest();
            previewAuthorizationLetterRequest.setFilename(authorizationLetter);
            previewAuthorizationLetterRequest.setTargetNodeId(targetNodeId);
            previewAuthorizationLetterRequest.setTargetCompanyId(targetCompanyId);
            Response<ResponseBody> bodyResponse = endpointRemote.previewAuthorizationLetter(previewAuthorizationLetterRequest);
            try (ResponseBody responseBody = bodyResponse.body()) {
                Assert.isTrue(responseBody != null, "下载附件文件失败");
                ServletOutputStream outputStream = response.getOutputStream();
                response.reset();
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
                InputStream inputStream = responseBody.byteStream();
                byte[] bytes = new byte[2048];
                int len;
                while ((len = inputStream.read(bytes)) > 0) {
                    outputStream.write(bytes, 0, len);
                }
                outputStream.close();
            }
        }

    }
}
