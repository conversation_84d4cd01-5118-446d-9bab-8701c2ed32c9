package com.ailpha.ailand.dataroute.endpoint.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiEncryptTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:49
 * @Description:
 */
@Data
@Entity
@Table(name = "t_plugin_detail")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PluginDetail {
    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;


    @OneToMany(mappedBy = "pluginDetail")
    @JsonIgnore
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    List<PluginApiDetail> pluginApiDetails;

    String createUser;

    Date createTime;

    Date updateTime;

    /**
     * 插件名称
     */
    String name;

    /**
     * 插件类型
     */
    @Enumerated(EnumType.STRING)
    PluginApiTypeEnums type;

    /**
     * 对接域名
     */
    String domain;

    /**
     * 插件加密方式
     */
    @Enumerated(EnumType.STRING)
    PluginApiEncryptTypeEnums encryptType;

    /**
     * 插件状态, true-启用
     */
    Boolean status;

    /**
     * 插件凭证 PlugCredentials json格式字符串存储
     */
    String plugCredentials;
}
