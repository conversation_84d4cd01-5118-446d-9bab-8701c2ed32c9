package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/16
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpdateRouteAssetRequest implements Serializable {

    @Schema(description = "资产id")
    @NotBlank(message = "资产id不能为空")
    String assetId;

    @NotBlank(message = "数据集领域不能为空")
    @Schema(description = "数据集业务领域")
    String businessArea;

}
