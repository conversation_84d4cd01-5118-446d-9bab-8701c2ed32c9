package com.ailpha.ailand.dataroute.endpoint.third.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "data-route.minio")
public class MinioProperties {
    boolean tlsEnabled = false;
    String endpoint = "http://127.0.0.1:9000";
    String rootUser = "ailand";
    String rootPassword = "2wsxVFR_";
}
