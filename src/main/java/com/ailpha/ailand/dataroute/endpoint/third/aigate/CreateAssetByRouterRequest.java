package com.ailpha.ailand.dataroute.endpoint.third.aigate;

import com.ailpha.ailand.dataroute.endpoint.third.constants.DbType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateAssetByRouterRequest {
    @JsonProperty("Ip")
    String ip;
    @JsonProperty("Port")
    String port;
    @JsonProperty("DbType")
    DbType dbType;
}
