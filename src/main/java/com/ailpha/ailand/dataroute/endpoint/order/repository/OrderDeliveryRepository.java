package com.ailpha.ailand.dataroute.endpoint.order.repository;

import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderDeliveryRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface OrderDeliveryRepository extends JpaRepository<OrderDeliveryRelation, String>, JpaSpecificationExecutor<OrderDeliveryRelation> {

    List<OrderDeliveryRelation> findAllByOrderId(String orderId);

    List<OrderDeliveryRelation> findDistinctByAssetNameLike(String assetName);

}
