package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule;

import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.FormStatusQuery;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.FormStatusQueryResponse;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 业务状态（数据资源登记、数据产品登记、数据产品上架）
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BusinessStatusUpdateSchedule {

    private final TenantRepository tenantRepository;

    private final CompanyRepository companyRepository;

    private final DataResourceRepository dataResourceRepository;

    private final DataProductRepository productRepository;

    private final HubShuHanApiClient shuHanApiClient;

    static final Cache<Object, Object> FETCH_STATUS_WAIT_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .maximumSize(500)
            .initialCapacity(50).build();

    private static final BlockingQueue<DataProduct> dataProductQueue = new LinkedBlockingQueue<>();
    private static final BlockingQueue<DataResource> dataResourcesQueue = new LinkedBlockingQueue<>();

    Date lastQueryTimeForP = null;

    Date lastQueryTimeForR = null;

    @Scheduled(fixedDelay = 30, initialDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void update() {
        Set<Company> companySet = tenantRepository.findAll().stream().map(t -> {
                    try {
                        Optional<Company> companyReference = companyRepository.findById(Long.valueOf(StringUtils.substringAfter(t.getSchemaName(), "_")));
                        return companyReference.orElse(null);
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        companySet.forEach(c -> {
            try {
                TenantContext.setCurrentTenant("tenant_" + c.getId());
                Specification<DataResource> specification = DataResource.itemStatusIs(null, "item_status5");
//                specification = specification.or(DataResource.itemStatusIs(null, "item_status2"));
                if (lastQueryTimeForR != null && lastQueryTimeForR.getTime() < new Date().getTime()) {
                    specification = DataResource.createTimeAfter(specification, lastQueryTimeForR);
                }
                Page<DataResource> dataResources = dataResourceRepository.findAll(specification, PageRequest.of(0, 50, Sort.by(Sort.Direction.ASC, "createTime")));
                if (dataResources.getTotalElements() > 0) {
                    dataResourcesQueue.addAll(dataResources.getContent());
                    lastQueryTimeForR = dataResources.getContent().getLast().getCreateTime();
                }
                Specification<DataProduct> productSpecification = DataProduct.itemStatusIs(null, "item_status5");
//                productSpecification = productSpecification.or(DataProduct.itemStatusIs(null, "item_status2"));
                productSpecification = productSpecification.or(DataProduct.hasNonSyncedPublishStatus(null, "1"));
                if (lastQueryTimeForP != null && lastQueryTimeForP.getTime() < new Date().getTime()) {
                    productSpecification = DataProduct.createTimeAfter(productSpecification, lastQueryTimeForP);
                }
                Page<DataProduct> dataProducts = productRepository.findAll(productSpecification, PageRequest.of(0, 50, Sort.by(Sort.Direction.ASC, "createTime")));
                if (dataProducts.getTotalElements() > 0) {
                    dataProductQueue.addAll(dataProducts.getContent());
                    lastQueryTimeForP = dataProducts.getContent().getLast().getCreateTime();
                }
            } catch (Exception e) {
                log.warn("搜索待更新业务状态的数据资源和数据产品失败 {}", c, e);
            } finally {
                TenantContext.clear();
            }
        });
    }

    public static final int DEFAULT_THREAD_POOL_SIZE = Math.max(Math.min(Runtime.getRuntime().availableProcessors(), 4), 2);
    private static final ExecutorService ASYNC_WORKER_EXECUTE_POOL = Executors.newFixedThreadPool(DEFAULT_THREAD_POOL_SIZE);
    private static final AtomicInteger threadCounter = new AtomicInteger();

    @PostConstruct
    public void initStatusUpdateThread() {
        Runnable statusUpdateTask = () -> {
            while (!dataProductQueue.isEmpty() || !dataResourcesQueue.isEmpty()) {
                DataProduct dataProduct = null;
                try {
                    dataProduct = dataProductQueue.poll(1, TimeUnit.SECONDS);
                    if (dataProduct != null) {
                        if (FETCH_STATUS_WAIT_CACHE.getIfPresent(dataProduct.getId()) != null) {
                            // NOTE 避免高频请求
                            dataProductQueue.add(dataProduct);
                            continue;
                        }
                        if ("item_status5".equals(dataProduct.getItemStatus())) {
                            if (dataProduct.getDataExt().getProcessId() == null) {
                                continue;
                            }
                            FormStatusQueryResponse statusQueryResponse = shuHanApiClient.setCompany(dataProduct.getProvider().getCompany()).formStatusQuery(FormStatusQuery.builder()
                                    .processId(dataProduct.getDataExt().getProcessId())
                                    .processType("2")
                                    .build());
                            if (statusQueryResponse.getProcessStatus() != null && statusQueryResponse.getProcessStatus() != 0) {
                                TenantContext.setCurrentTenant("tenant_" + dataProduct.getProvider().getCompany().getId());
                                dataProduct = productRepository.getReferenceById(dataProduct.getId());
                                dataProduct.setItemStatus(statusQueryResponse.getProcessStatus() == 1 ? "item_status6" : "item_status7");
                                dataProduct.setDataProductPlatformId(statusQueryResponse.getIdentityId());
                                dataProduct.getDataExt().addProcessLog("功能节点登记审批" + (statusQueryResponse.getProcessStatus() == 1 ? "通过" : "拒绝"),
                                        System.currentTimeMillis(), null, null);
                                productRepository.saveAndFlush(dataProduct);
                            }
                        }
                        if ("1".equals(dataProduct.getDataExt().getHasNonSyncedPublishStatus())) {
                            TenantContext.setCurrentTenant("tenant_" + dataProduct.getProvider().getCompany().getId());
                            dataProduct = productRepository.getReferenceById(dataProduct.getId());
                            List<ServiceNodeApplyListVO> serviceNodes = dataProduct.getDeliveryExt().getServiceNodes();
                            if (!CollectionUtils.isEmpty(serviceNodes)) {
                                for (ServiceNodeApplyListVO serviceNode : serviceNodes) {
                                    if (serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() != 0) {
                                        continue;
                                    }
                                    if (serviceNode.getProcessId() == null) {
                                        continue;
                                    }
                                    FormStatusQueryResponse statusQueryResponse = shuHanApiClient.setCompany(dataProduct.getProvider().getCompany()).formStatusQuery(FormStatusQuery.builder()
                                            .processId(serviceNode.getProcessId())
                                            .processType("3")
                                            .build());
                                    if (statusQueryResponse.getProcessStatus() != null && statusQueryResponse.getProcessStatus() != 0) {
                                        serviceNode.setProcessStatus(statusQueryResponse.getProcessStatus());
                                        serviceNode.setProcessId(statusQueryResponse.getIdentityId());
                                        dataProduct.getDataExt().addProcessLog("业务节点[" + serviceNode.getServiceNodeName() + "]上架审批" + (statusQueryResponse.getProcessStatus() == 1 ? "通过" : "拒绝"),
                                                System.currentTimeMillis(), null, null);
                                    }
                                }
                                dataProduct.getDataExt().setHasNonSyncedPublishStatus(serviceNodes.stream()
                                        .anyMatch(serviceNode -> serviceNode.getProcessStatus() == null || serviceNode.getProcessStatus() == 0) ? "1" : "0");
                                productRepository.saveAndFlush(dataProduct);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("更新数据产品业务状态失败", e);
                } finally {
                    if (dataProduct != null && ("item_status5".equals(dataProduct.getItemStatus()) || "1".equals(dataProduct.getDataExt().getHasNonSyncedPublishStatus()))) {
                        FETCH_STATUS_WAIT_CACHE.put(dataProduct.getId(), 1);
                        dataProductQueue.add(dataProduct);
                    }
                    TenantContext.clear();
                }
                DataResource dataResource = null;
                try {
                    dataResource = dataResourcesQueue.poll(1, TimeUnit.SECONDS);
                    if (dataResource != null) {
                        if (FETCH_STATUS_WAIT_CACHE.getIfPresent(dataResource.getId()) != null) {
                            // NOTE 避免高频请求
                            dataResourcesQueue.add(dataResource);
                            continue;
                        }
                        if ("item_status5".equals(dataResource.getItemStatus())) {
                            if (dataResource.getDataExt().getProcessId() == null) {
                                continue;
                            }
                            FormStatusQueryResponse statusQueryResponse = shuHanApiClient.setCompany(dataResource.getProvider().getCompany()).formStatusQuery(FormStatusQuery.builder()
                                    .processId(dataResource.getDataExt().getProcessId())
                                    .processType("1")
                                    .build());
                            if (statusQueryResponse.getProcessStatus() != null && statusQueryResponse.getProcessStatus() != 0) {
                                TenantContext.setCurrentTenant("tenant_" + dataResource.getProvider().getCompany().getId());
                                String itemStatus = statusQueryResponse.getProcessStatus() == 1 ? "item_status6" : "item_status7";
                                dataResource.setItemStatus(itemStatus);
                                dataResourceRepository.updateItemStatus(dataResource.getId(), itemStatus);
                                dataResource = dataResourceRepository.getReferenceById(dataResource.getId());
                                dataResource.setDataResourcePlatformId(statusQueryResponse.getIdentityId());
                                dataResource.getDataExt().addProcessLog("功能节点登记审批" + (statusQueryResponse.getProcessStatus() == 1 ? "通过" : "拒绝"),
                                        System.currentTimeMillis(), null, null);
                                dataResourceRepository.saveAndFlush(dataResource);
                            } else {
                                FETCH_STATUS_WAIT_CACHE.put(dataResource.getId(), 1);
                                dataResourcesQueue.add(dataResource);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("更新数据资源业务状态失败", e);
                } finally {
                    if (dataResource != null && "item_status5".equals(dataResource.getItemStatus())) {
                        FETCH_STATUS_WAIT_CACHE.put(dataResource.getId(), 1);
                        dataResourcesQueue.add(dataResource);
                    }
                    TenantContext.clear();
                }
                try {
                    TimeUnit.MICROSECONDS.sleep(500);
                } catch (InterruptedException ignore) {
                }
            }
            log.debug("更新数据资源业务状态线程[{}]退出，剩余线程数：{}", Thread.currentThread().getName(), threadCounter.decrementAndGet());
        };
        final String threadName = "async-business-status-update-thread-" + threadCounter.get();
        ASYNC_WORKER_EXECUTE_POOL.submit(() -> {
            Thread.currentThread().setName(threadName);
            while (!Thread.currentThread().isInterrupted()) {
                if (dataProductQueue.isEmpty() && dataResourcesQueue.isEmpty()) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                    } catch (InterruptedException ignore) {
                    }
                    continue;
                }
                if (threadCounter.get() <= DEFAULT_THREAD_POOL_SIZE) {
                    ASYNC_WORKER_EXECUTE_POOL.submit(statusUpdateTask);
                    log.debug("新增更新数据资源业务状态线程[{}]，现有线程数：{}", Thread.currentThread().getName(), threadCounter.incrementAndGet());
                }
            }
        });
    }
}
