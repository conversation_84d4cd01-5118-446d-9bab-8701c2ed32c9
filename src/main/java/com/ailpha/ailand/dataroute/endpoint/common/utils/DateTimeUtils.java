package com.ailpha.ailand.dataroute.endpoint.common.utils;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 日期工具类
 */
public class DateTimeUtils {

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // ISO 8601 format
    private static final String ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    // Alternate ISO 8601 format without fractional seconds
    private static final String ALTERNATIVE_ISO8601_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    /**** 年格式 ****/
    public final static String YEAR_FORMAT = "yyyy";

    /**** 月格式 ****/
    public final static String MONTH_FORMAT = "yyyy-MM";

    /**** 天格式 ****/
    public final static String DAY_FORMAT = "yyyy-MM-dd";

    /**** 小时格式 ****/
    public final static String HOUR_FORMAT = "yyyy-MM-dd HH";

    /**** 小时格式（带分钟位 HH:00） ****/
    public final static String HOUR_CONTAIN_MINUTE_FORMAT = "yyyy-MM-dd HH:00";

    /**** 分钟格式 ****/
    public final static String MINUTE_FORMAT = "yyyy-MM-dd HH:mm";

    /**** 分钟格式 ****/
    public final static String EXCEL_MINUTE_FORMAT = "yyyy-MM-dd HH:mm";

    /**** 秒格式 ****/
    public final static String SECOND_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 一天的毫秒数
     **/
    public final static long ONEDAY = (long) 24 * 60 * 60 * 1000;

    /**
     * 一小时的毫秒数
     **/
    public final static long ONEHOUR = (long) 60 * 60 * 1000;

    /**
     * 一分钟的毫秒数
     **/
    public final static long ONEMINUTE = (long) 60 * 1000;


    public static Date parseIso8601Date(String dateString) throws ParseException {
        try {
            return getIso8601DateFormat().parse(dateString);
        } catch (ParseException e) {
            return getAlternativeIso8601DateFormat().parse(dateString);
        }
    }

    public static String formatIso8601Date(Date date) {
        return getIso8601DateFormat().format(date);
    }

    public static String formatAlternativeIso8601Date(Date date) {
        return getAlternativeIso8601DateFormat().format(date);
    }

    private static DateFormat getIso8601DateFormat() {
        SimpleDateFormat df = new SimpleDateFormat(ISO8601_DATE_FORMAT,
                Locale.US);
        df.setTimeZone(new SimpleTimeZone(0, "GMT"));

        return df;
    }

    private static DateFormat getAlternativeIso8601DateFormat() {
        SimpleDateFormat df = new SimpleDateFormat(
                ALTERNATIVE_ISO8601_DATE_FORMAT, Locale.US);
        df.setTimeZone(new SimpleTimeZone(0, "GMT"));

        return df;
    }


    /**
     * 当前月份添加日期
     *
     * @param date 日期
     * @param days 添加天数
     * @return
     */
    public static Date addDays(Date date, int days) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, days);
        return c.getTime();
    }

    /**
     * 当前月份添加日期
     *
     * @param date   日期
     * @param months 添加月数
     * @return
     */
    public static Date addMonths(Date date, int months) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.MONTH, months);
        return c.getTime();
    }

    /**
     * 当前月份减少日期
     *
     * @param date 日期
     * @param days 减少天数
     * @return
     */
    public static Date minusDays(Date date, int days) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DAY_OF_MONTH, -days);
        return c.getTime();
    }

    /**
     * 日期类型转化成字符串
     *
     * @param date       日期
     * @param dateFormat 日期格式化字符串 默认 yyyy-MM-dd HH:mm:ss
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date, String dateFormat) {
        if (date == null) {
            return null;
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DEFAULT_DATE_FORMAT;
        }
        DateFormat format = new SimpleDateFormat(dateFormat);
        return format.format(date);
    }

    public static String formatDate(String date, String dateFormat) throws ParseException {
        if (StringUtils.isBlank(date)) {
            return "";
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DEFAULT_DATE_FORMAT;
        }
        DateFormat format = new SimpleDateFormat(dateFormat);

        return format.format(format.parse(date));
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }

        DateFormat format = new SimpleDateFormat(DEFAULT_DATE_FORMAT);
        return format.format(date);
    }

    /**
     * 格式化日期 Long 型
     *
     * @param timestamp
     * @return
     */

    public static String formatDate(Long timestamp) {
        if (timestamp == null) {
            throw new IllegalArgumentException("timestamp is not null!");
        }
        DateFormat fm = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
        fm.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return fm.format(new Date(timestamp));
    }

    public static String formatDate(Long timestamp, String pattern) {
        DateFormat fm = new SimpleDateFormat(pattern, Locale.US);
        fm.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return fm.format(new Date(timestamp));
    }

    /**
     * @param date       字符串日期
     * @param dateFormat 日期格式化字符串 默认 yyyy-MM-dd HH:mm:ss
     * @return 返回java日期对象
     * @throws ParseException
     */
    public static Date parseDate(String date, String dateFormat) throws ParseException {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DEFAULT_DATE_FORMAT;
        }
        DateFormat format = new SimpleDateFormat(dateFormat);
        return format.parse(date);
    }

    public static Date parseDate(String date) {
        try {
            return parseDate(date, "yyyy-MM-dd");
        } catch (Exception e) {
            return null;
        }

    }

    /***
     * @return 返回当前系统日期
     */
    public static String getCurrentDateTime() {
        return formatDate(new Date(), null);
    }

    /***
     * @return 返回当前系统日期
     */
    public static String curTime(String dateFormat) {
        return formatDate(new Date(), dateFormat);
    }

    /**
     * @param date 日期
     * @return 返回日期起始点
     */
    public static Date getDateStart(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * @param date 日期
     * @return 返回日期结束点
     */
    public static Date getDateEnd(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    /**
     * 返回每月的最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfMonth(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        return cal.getTime();
    }

    /**
     * 返回每月的第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfMonth(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is not null!");
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        return cal.getTime();
    }

    public static short getDayOfYear() {
        Calendar cal = Calendar.getInstance();
        return (short) cal.get(Calendar.DAY_OF_YEAR);
    }

    /**
     * 获取utc分钟数
     *
     * @return
     */
    public static int getMinOffset(Long timestamp) {
        return (int) (timestamp / 1000 / 60);
    }

    public static boolean sameDay(int minOffset1, int minOffset2) {
        Calendar c1 = Calendar.getInstance();
        c1.setTime(new Date(minOffset1 * 60 * 1000L));
        Calendar c2 = Calendar.getInstance();
        c1.setTime(new Date(minOffset2 * 60 * 1000L));
        return c1.get(Calendar.DAY_OF_YEAR) == c2.get(Calendar.DAY_OF_YEAR);
    }

    public static boolean sameDay(Date date1, Date date2) {
        return formatDate(date1, "yyyyMMdd").equals(formatDate(date2, "yyyyMMdd"));
    }

    /**
     * 将时间毫秒数转为字符串
     *
     * @param t
     * @return
     */
    public static String time2Str(Long t) {
        StringBuffer buf = new StringBuffer();
        if (t >= 24L * 3600L * 1000L) {
            buf.append(t / (24L * 3600L * 1000L));
            buf.append('天');
            t %= 24L * 3600L * 1000L;
        }
        if (t >= 3600L * 1000L) {
            buf.append(t / (3600L * 1000L));
            buf.append("小时");
            t %= 3600L * 1000L;
        }
        if (t >= 60L * 1000L) {
            buf.append(t / (60L * 1000L));
            buf.append("分钟");
            t %= 60L * 1000L;
        }
        if (t >= 1000L) {
            buf.append(t / 1000L);
            buf.append('秒');
        }
        return buf.toString();
    }

    /**
     * 时间戳转Date
     *
     * @param stamp
     * @param dateFormat
     * @return
     * @throws Exception
     */
    public static Date stampToDate(String stamp, String dateFormat) {
        if (StringUtils.isBlank(stamp)) {
            throw new IllegalArgumentException("date is not null!");
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DEFAULT_DATE_FORMAT;
        }
        SimpleDateFormat format = new SimpleDateFormat(dateFormat);
        Long time = Long.parseLong(stamp);
        try {
            return format.parse(format.format(time));
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 当前时间
     *
     * @return
     */
    public static Date now() {
        Calendar cal = Calendar.getInstance();
        return cal.getTime();
    }

    /**
     * 上一个小时的最后时刻
     *
     * @return
     */
    public static Date lastHourEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY) - 1);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     * 上一个小时的开始时刻
     *
     * @return
     */
    public static Date lastHourStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY) - 1);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    public static Date getWeekStartDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 计算日期相差天数
     *
     * @param begin 开始时间
     * @param end   结束时间
     * @return 天数
     */
    public static int daysBetween(Date begin, Date end) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(begin);
        long time1 = cal.getTimeInMillis();
        cal.setTime(end);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);

        return Integer.parseInt(String.valueOf(between_days));
    }

    public static int monthsBetween(Date begin, Date end) {
        DateTime begin1 = new DateTime(begin);
        DateTime end1 = new DateTime(end);
        return (int) DateUtil.betweenMonth(begin1, end1, false);
    }


}
