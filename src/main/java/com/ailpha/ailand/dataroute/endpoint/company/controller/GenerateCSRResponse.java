package com.ailpha.ailand.dataroute.endpoint.company.controller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@Schema(description = "生成CSR响应")
public class GenerateCSRResponse {
    @Schema(description = "csr")
    String csr;
    @Schema(description = "id")
    String id;
    @Schema(description = "id")
    Long companyId;
}
