package com.ailpha.ailand.dataroute.endpoint.common.filters;

import cn.hutool.core.text.AntPathMatcher;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import com.ailpha.ailand.dataroute.endpoint.openapi.OpenAPITokenController;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.IOpenApiCheck;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

import static com.dbapp.rest.constant.OpenApiConstant.*;
import static com.dbapp.rest.constant.OpenApiConstant.SIGN;

@Slf4j
@RequiredArgsConstructor
public class OpenAPIAuthorizeFilter extends OncePerRequestFilter implements IOpenApiCheck {
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        if (log.isDebugEnabled()) {
            log.debug("第三方URI：{}", uri);
        }
        // 开放接口鉴权（被其他连接器、TEE、MPC等调用）
        if (!"/openapi/token".equals(uri) && !"/third/userinfo".equals(uri) && (antPathMatcher.match("/openapi/**", uri)
                || antPathMatcher.match("/third/userinfo/**", uri)
                || antPathMatcher.match("/callback/mpc/**", uri)
                || antPathMatcher.match("/callback/tee/**", uri)
                || antPathMatcher.match("/third/contract/execute/callback", uri)
        )
        ) {
            String routeId = request.getHeader("routerId");
            String timestamp = request.getHeader(TIMESTAMP);
            Assert.isTrue(StringUtils.hasLength(timestamp), "缺少" + TIMESTAMP);
            String token = request.getHeader(TOKEN);
            String nonce = request.getHeader(NONCE);
            Assert.isTrue(StringUtils.hasLength(nonce), "缺少" + NONCE);
            String sign = request.getHeader(SIGN);
            Assert.isTrue(StringUtils.hasLength(sign), "缺少" + SIGN);
            checkOpenApi(token, nonce, timestamp, sign, request.getQueryString());
            PreAuthenticatedAuthenticationToken authentication = new PreAuthenticatedAuthenticationToken(token, null, Collections.singletonList(new SimpleGrantedAuthority("SCOPE_openapi")));
            SecurityContextHolder.getContext().setAuthentication(authentication);
        }
        filterChain.doFilter(request, response);
    }


    @Override
    public void checkToken(String token) {
        Assert.isTrue(StringUtils.hasLength(token), "缺少" + TOKEN);
        long currentTime = System.currentTimeMillis();
        String realToken = SecureUtil.aes(OpenAPITokenController.key).decryptStr(token, CharsetUtil.CHARSET_UTF_8);
        String[] tokenAndExpireTime = realToken.split("_");
        Assert.isTrue(tokenAndExpireTime.length == 2, "非法的token");
        if (currentTime > Long.parseLong(tokenAndExpireTime[1])) {
            throw new RestfulApiException("token 已过期");
        }
    }

    @Override
    public void checkNonce(String token, String nonce) {

    }

    @Override
    public void checkTimestamp(String timestamp) {
        if (!StringUtils.hasLength(timestamp)) {
            throw new RestfulApiException("缺少timestamp");
        }
        if (13 != timestamp.length()) {
            throw new RestfulApiException("timestamp 格式不正确，请使用13位毫秒时间戳！");
        }
    }
}
