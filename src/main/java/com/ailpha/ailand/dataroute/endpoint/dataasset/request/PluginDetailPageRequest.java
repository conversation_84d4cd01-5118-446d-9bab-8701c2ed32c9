package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 16:39
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "插件列表请求")
public class PluginDetailPageRequest extends Page {

    @Schema(description = "插件状态")
    private Boolean status;

    @Schema(description = "插件名称")
    private String name;
}
