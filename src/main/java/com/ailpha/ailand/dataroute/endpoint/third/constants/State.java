package com.ailpha.ailand.dataroute.endpoint.third.constants;

public enum State {

    SUBMITTING(10),
    WAITING(20),
    RUNNING(30),
    KILLING(40),
    KILLED(50),
    FAILED(60),
    SUCCEEDED(70),
    CANCEL(80),
    SY<PERSON>ING(90);

    final int value;

    State(int value) {
        this.value = value;
    }

    public static State getByValue(Integer value) {
        for (State state : State.values()) {
            if (state.value == value) {
                return state;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public boolean isFinished() {
        return this == KILLED || this == FAILED || this == SUCCEEDED || this == CANCEL;
    }

    public boolean isRunning() {
        return !isFinished();
    }
}
