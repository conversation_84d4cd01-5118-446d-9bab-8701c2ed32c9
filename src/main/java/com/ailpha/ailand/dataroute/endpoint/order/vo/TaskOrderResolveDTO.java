package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/8 14:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskOrderResolveDTO {

    OrderApprovalRecord record;

    AssetBeneficiaryRel rel;

}
