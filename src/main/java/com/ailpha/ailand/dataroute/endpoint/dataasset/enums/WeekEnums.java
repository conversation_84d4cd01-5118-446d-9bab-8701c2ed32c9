package com.ailpha.ailand.dataroute.endpoint.dataasset.enums;

import java.util.Calendar;
import java.util.Date;

public enum WeekEnums {

    SUNDAY(1, "SUN"),
    MONDAY(2, "MON"),
    TUESDAY(3, "TUE"),
    WEDNESDAY(4, "WED"),
    THURSDAY(5, "THU"),
    FRIDAY(6, "FRI"),
    SATURDAY(7, "SAT");

    private Integer index;
    private String abbr;

    WeekEnums(Integer index, String abbr){
        this.index = index;
        this.abbr = abbr;
    }

    /**
     * Getter method for property index.
     *
     * @return property value of index
     */
    public Integer getIndex() {
        return index;
    }

    /**
     * Getter method for property abbr.
     *
     * @return property value of abbr
     */
    public String getAbbr() {
        return abbr;
    }

    public static WeekEnums getByIndex(Integer index){
        for(WeekEnums week: WeekEnums.values()){
            if(index.equals(week.getIndex())){
                return week;
            }
        }
        return null;
    }

    public static WeekEnums getByDate(Date date){
        return WeekEnums.getByIndex(getWeekIndex(date));
    }

    private static Integer getWeekIndex(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }
}