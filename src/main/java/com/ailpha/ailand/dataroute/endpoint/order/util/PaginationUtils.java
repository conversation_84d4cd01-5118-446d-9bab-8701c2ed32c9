package com.ailpha.ailand.dataroute.endpoint.order.util;

import java.util.Collections;
import java.util.List;

/**
 * 内存分页工具类
 */
public class PaginationUtils {

    /**
     * 对列表进行分页
     * @param list 原始列表
     * @param pageNum 页码(从1开始)
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 分页后的子列表
     */
    public static <T> List<T> paginate(List<T> list, int pageNum, int pageSize) {
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }

        // 参数校验
        if (pageNum < 1) {
            pageNum = 1;
        }

        if (pageSize < 1) {
            pageSize = 10; // 默认每页10条
        }

        int totalItems = list.size();
        int totalPages = (int) Math.ceil((double) totalItems / pageSize);

        // 如果请求的页码大于总页数，返回最后一页
        if (pageNum > totalPages) {
            pageNum = totalPages;
        }

        int fromIndex = (pageNum - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalItems);

        // 防止fromIndex越界
        if (fromIndex >= totalItems) {
            return Collections.emptyList();
        }

        return list.subList(fromIndex, toIndex);
    }

    /**
     * 获取总页数
     * @param list 原始列表
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 总页数
     */
    public static <T> int getTotalPages(List<T> list, int pageSize) {
        if (list == null || list.isEmpty()) {
            return 0;
        }

        if (pageSize < 1) {
            pageSize = 10;
        }

        return (int) Math.ceil((double) list.size() / pageSize);
    }
}