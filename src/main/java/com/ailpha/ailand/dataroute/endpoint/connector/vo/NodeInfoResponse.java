package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import com.ailpha.ailand.dataroute.endpoint.configuration.vo.ConfigurationInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.RouteStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "连接器信息")
public class NodeInfoResponse {
    @Schema(description = "激活状态")
    RouteStatus status = RouteStatus.not_activate;
    @Schema(description = "激活时间")
    Long activeTime;
    @Schema(description = "平台ID")
    String platformId;
    @Schema(description = "公钥")
    String publicKey;
    @Schema(description = "csr")
    @JsonIgnore
    String certificateCSR;
    String name;
    String description;

    List<RegisterToHubRequest.HubInfo> hubNodeList;

    @Schema(description = "系统配置")
    ConfigurationInfoVO configuration;
}
