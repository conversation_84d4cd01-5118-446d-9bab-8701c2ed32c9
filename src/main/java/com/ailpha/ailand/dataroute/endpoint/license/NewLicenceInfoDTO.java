package com.ailpha.ailand.dataroute.endpoint.license;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/8/22 16:14
 * @description
 */
@Data
public class NewLicenceInfoDTO {

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 行业
     */
    private String industry;

    /**
     * 许可类型 正式许可/测试许可/内部许可
     */
    private int licenseType;

    /**
     * 首次激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstActiveDate;

    /**
     * 产品过保时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productExpireTime;

    /**
     * 产品使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productLiveTime;

    private String productName;

    private String productModel;

    private String productSn;

    private String sdkVersion;

    private String machineCode;

    private List<UnitLicenceDTO> unitLicenseList;

    @Data
    public static class UnitLicenceDTO {
        private Integer count;
        private String data;
        private String unitId;
        private String unitName;
    }
}
