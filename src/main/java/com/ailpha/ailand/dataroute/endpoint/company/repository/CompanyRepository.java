package com.ailpha.ailand.dataroute.endpoint.company.repository;

import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.dto.CompanyListResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long>, JpaSpecificationExecutor<Company> {
    
    Optional<Company> findByIdAndDeletedFalse(Long id);
    
    Optional<Company> findByCreditCodeAndDeletedFalse(String creditCode);
    
    List<Company> findByStatusAndDeletedFalse(CompanyStatus status);
    
    boolean existsByCreditCodeAndDeletedFalse(String creditCode);
    
    boolean existsByDelegateInstitutionCodeAndDeletedFalse(String institutionCode);

    Company findByNodeId(String nodeId);
}