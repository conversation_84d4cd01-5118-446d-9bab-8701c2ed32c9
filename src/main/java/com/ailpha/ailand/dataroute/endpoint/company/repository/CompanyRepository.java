package com.ailpha.ailand.dataroute.endpoint.company.repository;

import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long>, JpaSpecificationExecutor<Company> {
    
    Optional<Company> findByIdAndDeletedFalse(Long id);

    Company findByNodeId(String nodeId);

    boolean existsByNodeId(String nodeId);
}