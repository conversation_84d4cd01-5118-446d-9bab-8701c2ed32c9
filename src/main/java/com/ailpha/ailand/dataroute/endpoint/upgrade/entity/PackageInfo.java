package com.ailpha.ailand.dataroute.endpoint.upgrade.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "upgrade_package_info")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PackageInfo implements Serializable {

    @Id
    @Schema(description = "ID")
    @Column(name = "id", updatable = false)
    private String id;

    @Schema(description = "名称")
    @Column(name = "name")
    private String name;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    @Enumerated(EnumType.STRING)
    @Column(name = "module")
    private UpgradeModule module;

    @Schema(description = "版本")
    @Column(name = "version")
    private String version;

    @Schema(description = "描述")
    @Column(name = "description")
    private String description;

    @Schema(description = "MD5")
    @Column(name = "md5")
    private String md5;

    @Schema(description = "来源：DATA_ROUTE（连接器）")
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    private UpgradeSource source;

    @Schema(description = "文件路径")
    @Column(name = "file_path")
    private String filePath;

    @Schema(description = "上传时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "upload_time")
    private Date uploadTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    private Date updateTime;
}
