package com.ailpha.ailand.dataroute.endpoint.connector;

public enum RouteStatus {

    /**
     * 未激活
     */
    not_activate(0L),
    /**
     * 已上传license
     */
    import_license(1L),
    /**
     * license 验证通过
     */
    license_pass(2L),
    /**
     * 组网成功
     */
    network_link_pass(3L),
    /**
     * 已激活
     */
    activated(4L);

    private final Long order;

    RouteStatus(Long order) {
        this.order = order;
    }

    public Long getOrder() {
        return order;
    }
}
