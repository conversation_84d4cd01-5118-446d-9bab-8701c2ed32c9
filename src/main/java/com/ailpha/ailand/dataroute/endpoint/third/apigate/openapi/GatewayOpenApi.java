package com.ailpha.ailand.dataroute.endpoint.third.apigate.openapi;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.*;
import com.ailpha.ailand.dataroute.endpoint.third.config.GatewayConfig;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

@Component
public class GatewayOpenApi {
    private final RestClient restClient;

    private final GatewayConfig gatewayConfig;

    public GatewayOpenApi(RestClient.Builder restClientBuilder, GatewayConfig gatewayConfig, BaseCapabilityManager baseCapabilityManager) {
        this.gatewayConfig = gatewayConfig;
        this.restClient = restClientBuilder.baseUrl(baseCapabilityManager.getCapabilityConfig(null, BaseCapabilityType.API_GATE).getBaseUrl()).build();
    }

    /**
     * 根据服务名称查询API服务
     *
     * @param serviceName 服务名称
     * @return API服务详情
     */
    public DescribeServicesResponse describeServicesWithOpenAPI(String serviceName) {
        return restClient.get()
                .uri(
                        uriBuilder -> uriBuilder.path("DescribeApigService")
                                .queryParam("name", serviceName)
                                .queryParam("currentPage", "1")
                                .queryParam("pageSize", "1")
                                .build()
                )
                .header("apig-access-token", SignUtils.accessToken(gatewayConfig.getAccessKeyId(), gatewayConfig.getAccessKeySecret(), "DescribeApigService"))
                .retrieve()
                .body(DescribeServicesResponse.class);
    }

    /**
     * 创建API服务
     * 用户名 -> API服务名称
     * 数据资产 -> API
     * 一个用户对应一个API网关服务，同一个用户的API数据资产挂载一个网关服务下
     */
    public CreateServiceWithOpenAPIResponse createServiceWithOpenAPI(CreateServiceRequest.InvokeParam invokeParam) {
        return restClient.post()
                .uri("/openapi/apig/24.7/api")
                .header("apig-access-token", SignUtils.accessToken(gatewayConfig.getAccessKeyId(), gatewayConfig.getAccessKeySecret(), "CreateApigService"))
                .body(invokeParam)
                .retrieve()
                .body(CreateServiceWithOpenAPIResponse.class);
    }

    /**
     * 创建API调用者
     */
    public CreateConsumerResponse createConsumerWithOpenAPI(CreateConsumerRequest createConsumerRequest) {
        // TODO
        return null;
    }

    /**
     * 创建API路由
     */
    public CreateRouteResponse createRoute(CreateRouteRequest createRouteRequest) {
        // TODO
        return null;
    }

    /**
     * 创建API路由
     */
    public CreateRouteWithOpenAPIResponse createRouteWithOpenApi(CreateRouteRequest.InvokeParam invokeParam) {
        return restClient.post()
                .uri("/openapi/apig/24.7/api")
                .header("apig-access-token", SignUtils.accessToken(gatewayConfig.getAccessKeyId(), gatewayConfig.getAccessKeySecret(), "CreateApi"))
                .body(invokeParam)
                .retrieve()
                .body(CreateRouteWithOpenAPIResponse.class);
    }

}
