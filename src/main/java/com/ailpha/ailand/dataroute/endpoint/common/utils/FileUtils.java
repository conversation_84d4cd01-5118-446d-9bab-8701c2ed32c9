package com.ailpha.ailand.dataroute.endpoint.common.utils;

import info.monitorenter.cpdetector.io.ASCIIDetector;
import info.monitorenter.cpdetector.io.CodepageDetectorProxy;
import info.monitorenter.cpdetector.io.JChardetFacade;
import info.monitorenter.cpdetector.io.ParsingDetector;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;

@Slf4j
public class FileUtils extends org.apache.commons.io.FileUtils {

    public static boolean createNewFile(String filePath) {
        File file = new File(filePath);
        if (file.exists() && !file.delete()) {
            return false;
        }
        //判断文件所在目录是否存在
        if (!file.getParentFile().exists() && !file.getParentFile().mkdirs()) {
            return false;
        }
        try {
            return file.createNewFile();
        } catch (IOException e) {
            return false;
        }
    }

    public static String readStreamAsString(InputStream inputStream) throws IOException {
        StringWriter writer = new StringWriter();
        if (inputStream.available() > 1024 * 1024 * 10) {
            throw new RuntimeException("文件过大，读取失败");
        }
        IOUtils.copy(inputStream, writer, "utf-8");
        inputStream.close();
        return writer.toString();
    }

    public static Charset getFileEncode(String path) throws Exception {
        /*
         * detector是探测器，它把探测任务交给具体的探测实现类的实例完成。
         * cpDetector内置了一些常用的探测实现类，这些探测实现类的实例可以通过add方法 加进来，如ParsingDetector、
         * JChardetFacade、ASCIIDetector、UnicodeDetector。
         * detector按照“谁最先返回非空的探测结果，就以该结果为准”的原则返回探测到的
         * 字符集编码。使用需要用到三个第三方JAR包：antlr.jar、chardet.jar和cpdetector.jar
         * cpDetector是基于统计学原理的，不保证完全正确。
         */
        CodepageDetectorProxy detector = CodepageDetectorProxy.getInstance();
        /*
         * ParsingDetector可用于检查HTML、XML等文件或字符流的编码,构造方法中的参数用于
         * 指示是否显示探测过程的详细信息，为false不显示。
         */
        detector.add(new ParsingDetector(false));
        /*
         * JChardetFacade封装了由Mozilla组织提供的JChardet，它可以完成大多数文件的编码
         * 测定。所以，一般有了这个探测器就可满足大多数项目的要求，如果你还不放心，可以
         * 再多加几个探测器，比如下面的ASCIIDetector、UnicodeDetector等。
         */
        detector.add(JChardetFacade.getInstance());// 用到antlr.jar、chardet.jar
        // ASCIIDetector用于ASCII编码测定
        detector.add(ASCIIDetector.getInstance());
        Charset charset = null;
        File f = new File(path);
        FileInputStream in = new FileInputStream(f);
        return getCharset(in, detector, charset);
    }

    public static Charset getFileEncode(InputStream inputStream) throws Exception {
        /*
         * detector是探测器，它把探测任务交给具体的探测实现类的实例完成。
         * cpDetector内置了一些常用的探测实现类，这些探测实现类的实例可以通过add方法 加进来，如ParsingDetector、
         * JChardetFacade、ASCIIDetector、UnicodeDetector。
         * detector按照“谁最先返回非空的探测结果，就以该结果为准”的原则返回探测到的
         * 字符集编码。使用需要用到三个第三方JAR包：antlr.jar、chardet.jar和cpdetector.jar
         * cpDetector是基于统计学原理的，不保证完全正确。
         */
        CodepageDetectorProxy detector = CodepageDetectorProxy.getInstance();
        /*
         * ParsingDetector可用于检查HTML、XML等文件或字符流的编码,构造方法中的参数用于
         * 指示是否显示探测过程的详细信息，为false不显示。
         */
        detector.add(new ParsingDetector(false));
        /*
         * JChardetFacade封装了由Mozilla组织提供的JChardet，它可以完成大多数文件的编码
         * 测定。所以，一般有了这个探测器就可满足大多数项目的要求，如果你还不放心，可以
         * 再多加几个探测器，比如下面的ASCIIDetector、UnicodeDetector等。
         */
        detector.add(JChardetFacade.getInstance());// 用到antlr.jar、chardet.jar
        // ASCIIDetector用于ASCII编码测定
        detector.add(ASCIIDetector.getInstance());
        Charset charset = null;
        return getCharset(inputStream, detector, charset);
    }

    private static Charset getCharset(InputStream inputStream, CodepageDetectorProxy detector, Charset charset) throws IOException {
        byte[] bytes = new byte[1024 * 1024];
        int i = inputStream.read(bytes);
        if (i == 0) {
            return StandardCharsets.UTF_8;
        }
        try {
            charset = detector.detectCodepage(new ByteArrayInputStream(bytes), bytes.length);
            inputStream.close();
        } catch (Exception ignore) {
        }
        return charset;
    }

    public static void inputStream2File(InputStream ins, File file) throws Exception {
        boolean flag = createNewFile(file.getAbsolutePath());
        if (!flag) {
            throw new RuntimeException("创建新文件失败");
        }
        OutputStream os = new FileOutputStream(file);
        IOUtils.copy(ins, os);
    }

    public static List<String> readAndRemoveFirstLines(File file, int lineNum) throws IOException {
        List<String> strList = new ArrayList<String>();
        try (RandomAccessFile raf = new RandomAccessFile(file, "rw")) {
            //Initial write position
            long writePosition = raf.getFilePointer();
            for (int i = 0; i < lineNum; i++) {
                //解决中文乱码
                String line = new String(raf.readLine().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                strList.add(line);
            }
            // Shift the next lines upwards.
            long readPosition = raf.getFilePointer();
            byte[] buff = new byte[1024];
            int n;
            while (-1 != (n = raf.read(buff))) {
                raf.seek(writePosition);
                raf.write(buff, 0, n);
                readPosition += n;
                writePosition += n;
                raf.seek(readPosition);
            }
            raf.setLength(writePosition);
        } catch (IOException e) {
            throw e;
        }
        return strList;
    }

    public static List<String> readAheadLines(File file, int lineNum) throws IOException {
        List<String> strList = new ArrayList<String>();
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            for (int i = 0; i < lineNum; i++) {
                //解决中文乱码
                String rawStr = raf.readLine();
                if (StringUtils.isBlank(rawStr)) {
                    continue;
                }
                String line = new String(rawStr.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                strList.add(line);
            }
        } catch (IOException e) {
            log.error("读取文件失败：", e);
            throw e;
        }
        return strList;
    }

    public static String getLineSeparator(File f) throws IllegalArgumentException {
        if (f == null || !f.isFile() || !f.exists()) {
            throw new IllegalArgumentException("file must exists!");
        }

        try (RandomAccessFile raf = new RandomAccessFile(f, "r")) {
            String line = raf.readLine();
            if (line == null) {
                return "";
            }

            // 必须执行这一步，因为 RandomAccessFile 的 readLine() 会自动忽略并跳过换行符，所以需要先回退文件指针位置
            // "ISO-8859-1" 为 RandomAccessFile 使用的字符集，此处必须指定，否则中文 length 获取不对
            raf.seek(line.getBytes(StandardCharsets.ISO_8859_1).length);

            byte nextByte = raf.readByte();
            if (nextByte == '\n') {
                return new String(new byte[]{'\n'});
            }

            if (nextByte != '\r') {
                return "";
            }

            try {
                nextByte = raf.readByte();
                if (nextByte == '\n') {
                    return new String(new byte[]{'\r', '\n'});
                }
                return new String(new byte[]{'\r'});
            } catch (EOFException e) {
                return new String(new byte[]{'\r'});
            }
        } catch (IOException e) {
            log.error("读取文件换行符失败：", e);
        }
        return "";
    }

    public static String readFileAsString(File file) throws IOException {
        return readFileToString(file, StandardCharsets.UTF_8);
    }

    public static boolean isShellFile(File file) {
        try {
            List<String> top2 = readAheadLines(file, 2);
            return top2.stream().anyMatch(s -> s != null && s.startsWith("#!"));
        } catch (IOException e) {
            return false;
        }
    }


    /**
     * 获取数据
     *
     * @param decryptFile
     * @param hasHeader
     * @param lines       采样条数
     * @return
     */
    public static List<String> getSamplingRows(File decryptFile, boolean hasHeader, int lines) {
        return getSamplingRows(decryptFile, hasHeader, lines, null, -1, null, false);
    }

    /**
     * 获取调试数据
     *
     * @param decryptFile
     * @param hasHeader
     * @param lines           采样条数
     * @param fieldValues     调试数据过滤值
     * @param filedIndex      调试数据匹配列 index
     * @param separator       分隔符
     * @param filterDebugData 是否需要过滤调试数据
     * @return
     */
    public static List<String> getSamplingRows(File decryptFile, boolean hasHeader, int lines, String fieldValues,
                                               int filedIndex, String separator, boolean filterDebugData) {
        String[] filedValueArray = null;
        if (!StringUtils.isBlank(fieldValues)) {
            filedValueArray = fieldValues.split(",");
        }

        try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(Files.newInputStream(decryptFile.toPath()), StandardCharsets.UTF_8));) {
            String lineValue;
            List<String> rows = new ArrayList<>();
            if (hasHeader) {
                bufferedReader.readLine();
            }
            while ((lineValue = bufferedReader.readLine()) != null) {
                if (!filterDebugData || containFiledValue(lineValue, filedValueArray, filedIndex, separator)) {
                    if (--lines < 0) {
                        break;
                    }
                    rows.add(lineValue);
                }
            }
            if (rows.isEmpty()) {
                log.warn("调试数据采样为空，采样数：{}", lines);
            }
            return rows;
        } catch (Exception e) {
            log.warn("调试数据采样失败", e);
            return new ArrayList<>();
        }

    }

    /**
     * 调试数据不足，进行补数据
     *
     * @param decryptFile
     * @param hasHeader
     * @param sampleLine  采样数
     * @param rows        采样列表
     * @return
     */
    public static List<String> supplementSamplingRows(File decryptFile, boolean hasHeader, int sampleLine, List<String> rows) {
        log.debug("调试数据采样数不足，进行调试数据补充采样，需要补充 {} 条", sampleLine - rows.size());
        // 防止重复数据
        Set<String> rowsSet = new HashSet<>(rows);
        try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(Files.newInputStream(decryptFile.toPath()), StandardCharsets.UTF_8));) {
            String lineValue;
            if (hasHeader) {
                bufferedReader.readLine();
            }
            while ((lineValue = bufferedReader.readLine()) != null && rowsSet.size() < sampleLine) {
                rowsSet.add(lineValue);
            }
            if (rows.isEmpty()) {
                log.warn("调试数据补充采样为空，请检查原始数据");
            }
            return new ArrayList<>(rowsSet);
        } catch (Exception e) {
            log.warn("调试数据采样失败", e);
            return rows;
        }
    }

    /**
     * 过滤调试环境数据，如果传入 index ，则拿到对应的列进行匹配
     *
     * @param lineValue
     * @param filedValueArray
     * @param filedIndex      filedIndex 为 -1 表示不进行具体列的过滤判断，为正数时表示需要取对应的 index 列进行调试数据值过滤
     * @param separator       分隔符
     * @return
     */
    private static boolean containFiledValue(String lineValue, String[] filedValueArray, int filedIndex, String separator) {
        if (filedValueArray == null || filedValueArray.length == 0) {
            return true;
        }
        // 全行匹配
        if (filedIndex == -1) {
            for (String fieldValue : filedValueArray) {
                if (lineValue.contains(fieldValue)) {
                    return true;
                }
            }
        } else {
            // 特定列匹配
            String[] split = lineValue.split(separator);
            return Arrays.stream(filedValueArray).anyMatch(a -> a.equals(split[filedIndex]));
        }

        return false;
    }

    public static String readFirstLine(File file) throws IOException {
        List<String> strList = readAheadLines(file, 1);
        if (strList.isEmpty()) {
            throw new IOException("第一行不能为空");
        }
        return strList.get(0);
    }

    public static void moveFile(String sourceDirPath, String orgFileName, String destDirPath, String destFileName) {
        File startFile = new File(sourceDirPath + File.separator + orgFileName);

        File endDirection = new File(destDirPath);
        if (!endDirection.exists()) {
            endDirection.mkdirs();
        }

        File endFile = new File(endDirection + File.separator + destFileName);

        try {
            if (startFile.renameTo(endFile)) {
                log.info("文件移动成功！目标路径：{}", endFile.getAbsolutePath());
            } else {
                log.error("文件移动失败！起始路径：{}", startFile.getAbsolutePath());
            }
        } catch (Exception e) {
            log.error("文件移动出现异常！起始路径：{}", startFile.getAbsolutePath());
        }
    }

    public static String getFileExtension(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null;
        }
        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1 || dotIndex == 0) {
            return null;
        }
        return fileName.substring(dotIndex + 1);
    }

}
