package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

/**
 * 数据资产接入方式
 *
 * <AUTHOR>
 * @date 2024-11-13
 */
public enum SourceType {
    // API、数据库、文件、平台生成(MPC)
    API, DATABASE, FILE, FROM_MPC;

    public static SourceType fromName(String name) {
        for (SourceType dataType : values()) {
            if (dataType.name().equalsIgnoreCase(name)) {
                return dataType;
            }
        }
        return API;
    }
}
