package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import cn.hutool.json.JSONObject;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.CatalogQueryDataResource;
import com.dbapp.rest.openapi.AppToken;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;

public interface HubShuHanDataItemApi {
    /**
     * 通过ak换取token
     */
    @GetExchange("/gateway/auth/api/route/openapi/token")
    ShuhanResponse<AppToken> token();

    /**
     * 获取行业分类
     *
     * @param id 字典id（不传获取第一级，传递则获取其子节点）
     * @return 行业分类
     */
    @GetExchange("/gateway/shuhan-business-service/api/inner/industry-dict/getClassify")
    ShuhanResponse<List<IndustryDictVO>> getClassify(@RequestParam(required = false) String id);

    /**
     * 获取所有行业分类
     *
     * @return 行业分类
     */
    @GetExchange("/gateway/shuhan-business-service/api/inner/industry-dict/getClassifyAll")
    ShuhanResponse<List<IndustryDictVO>> getClassifyAll();

    /**
     * 数据目录查询接口
     *
     * @param catalogQuery 数据资源目录查询参数
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataCatalogQuery")
    ShuhanResponse<IPage<JSONObject>> catalogQuery(@RequestBody CatalogQueryVM catalogQuery);

}
