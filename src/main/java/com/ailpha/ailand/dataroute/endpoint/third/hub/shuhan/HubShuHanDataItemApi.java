package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import cn.hutool.json.JSONObject;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.GetNonceResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.TokenResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.FormStatusQuery;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.FormStatusQueryResponse;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface HubShuHanDataItemApi {

    @PostExchange("/identityVerify")
    ShuhanResponse<GetNonceResponse> nonce(@RequestBody GenerateUuidRequest request);

    /**
     * 通过ak换取token
     */
    @PostExchange("/identityVerifyNonce")
    ShuhanResponse<TokenResponse> token(@RequestBody GenerateTokenRequest request);

    /**
     * 数据目录查询接口
     *
     * @param catalogQuery 数据资源目录查询参数
     * @return <pre>
     *   {
     *     "statusCode": 200,
     *     "data": {
     *         "data": [],
     *         "pagination": {
     *             "total": 2,
     *             "size": 10,
     *             "page": 1
     *         }
     *     },
     *     "message": "ok",
     *     "metaData": null,
     *     "timestamp": 175297169809,
     *     "isFail": false,
     *     "success": true,
     *     "isSuccess": true
     *   }
     * </pre>
     */
    @PostExchange("/dataCatalogQuery")
    ShuhanResponse<JSONObject> catalogQuery(@RequestBody CatalogQueryVM catalogQuery);

    /**
     * 业务状态查询接口
     *
     * @param formStatusQuery 业务状态查询参数
     */
    @PostExchange("/formStatusQuery")
    ShuhanResponse<FormStatusQueryResponse> formStatusQuery(@RequestBody FormStatusQuery formStatusQuery);
}
