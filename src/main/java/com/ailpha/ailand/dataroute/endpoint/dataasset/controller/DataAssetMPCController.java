package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "数据资产(MPC相关)")
@RequiredArgsConstructor
@RequestMapping("data-asset")
public class DataAssetMPCController {

    private final MPCRemote mpcRemote;
    private final DataResourceRepository dataResourceRepository;

    @PostMapping("mpc/openapi-list")
    @Operation(summary = "mpc openapi 列表")
    public SuccessResponse<List<OpenApiListVO>> mpcOpenapiList(@RequestBody OpenApiListRequest openApiListRequest) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        openApiListRequest.setStatus(APIStatusEnum.NORMAL);
        openApiListRequest.setOnline(true);
        if ("COMPANY_ADMIN".equals(userDTO.getRoleName())) {
            Assert.notNull(openApiListRequest.getAssetId(), "assetId 不能为空");
            DataResource dataResource = dataResourceRepository.getReferenceById(openApiListRequest.getAssetId());
            openApiListRequest.setUserId(dataResource.getUserId());
        } else {
            openApiListRequest.setUserId(userDTO.getId());
        }
        PageResult<OpenApiListVO> openApiListVOPageResult = mpcRemote.openapiList(openApiListRequest);
        return ApiResponse.success(openApiListVOPageResult.getData())
                .page(Page.of(openApiListRequest.getNum(), openApiListRequest.getSize()))
                .total(openApiListVOPageResult.getTotal())
                .build();
    }

    @PostMapping("mpc/openapi-detail")
    @Operation(summary = "mpc openapi 详情")
    public SuccessResponse<CommonResult<OpenApiDetailVOForDataRoute>> mpcOpenapiDetail(@RequestBody OpenApiDetailRequest openApiDetailRequest) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        if ("COMPANY_ADMIN".equals(userDTO.getRoleName())) {
            Assert.notNull(openApiDetailRequest.getAssetId(), "assetId 不能为空");
            DataResource dataResource = dataResourceRepository.getReferenceById(openApiDetailRequest.getAssetId());
            openApiDetailRequest.setUserId(dataResource.getUserId());
        } else {
            openApiDetailRequest.setUserId(userDTO.getId());
        }
        return ApiResponse.success(mpcRemote.openapiDetail(openApiDetailRequest)).build();
    }

}
