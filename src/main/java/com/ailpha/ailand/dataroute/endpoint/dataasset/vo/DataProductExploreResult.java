package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("数据产品登记分析结果")
public class DataProductExploreResult{
    @Schema(description = "产品名称")
    @Alias("productNameCN")
    String dataProductName;
    @Schema(description = "产品英文名称")
    @Alias("productNameEN")
    String dataProductNameCN;
    @Schema(description = "产品类型")
    @Alias("productType")
    String type;
    @Schema(description = "覆盖时间范围")
    String timeRange;
    String dataCoverageTimeStart;
    String dataCoverageTimeEnd;
    String industry1;
    String region1;
    @Schema(description = "地域分类")
    String productRegion;
    @Schema(description = "资源名")
    List<String> industryCode;
    String industryName;
    @Schema(description = "资源名称-中文")
    @Alias("productSource")
    String source;
    @Schema(description = "数据类型")
    String dataType;
    @Schema(description = "数据类型")
    String dataType1;
    @Schema(description = "是否涉及个人信息")
    String personalInformation;
    @Schema(description = "数据规模")
    String dataSize;
    @Schema(description = "更新频率")
    String updateFrequency;
    @Schema(description = "交付方式")
    String deliveryMethod;
    @Schema(description = "使用限制")
    String limitations;
    @Schema(description = "授权使用")
    String authorize;
    @Schema(description = "产品简介")
    String description;
    @Alias("reprocessing")
    String isSecondaryProcessed;
}
