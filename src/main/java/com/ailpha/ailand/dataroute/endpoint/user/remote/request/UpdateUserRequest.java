package com.ailpha.ailand.dataroute.endpoint.user.remote.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UpdateUserRequest {
    @NotEmpty
    String id;
    String name;
    String mobile;
    String email;
//    String password;
    @Schema(description = "旧密码")
    String opassword;
    @Schema(description = "新密码")
    String npassword;
    @Schema(description = "重复新密码")
    String cpassword;
    // 过期时间
    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date expireDate;

    Boolean updateExpireDate = false;
}
