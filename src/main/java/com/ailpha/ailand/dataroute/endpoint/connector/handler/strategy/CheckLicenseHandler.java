package com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.*;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.DataRouteActivateHandler;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.LicenseRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ImportLicenseResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.ImportLicenseRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.LicenseDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.RouteDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

@Slf4j
@Component
@Order(1001)
@RequiredArgsConstructor
public class CheckLicenseHandler implements DataRouteActivateHandler {

    private final AiLandProperties aiLandProperties;
    private final LicenseRemoteService licenseRemoteService;
    private final NetworkLinkHandler networkLinkHandler;

    @Override
    public void handler(RouteActivateContext context) {
        RouterService routerService = SpringUtil.getBean(RouterService.class);
        RouteDTO route = context.getRoute();
        LicenseDTO license = context.getLicense();
        String licensePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + RouterService.LICENSE_FILENAME;
        if (context.getRoute().getStatus().getOrder() >= RouteStatus.license_pass.getOrder()) {
            networkLinkHandler.handler(context);
            return;
        }
        Assert.isTrue(RouteStatus.import_license.equals(context.getRoute().getStatus()), "请先上传证书");
        if (ObjectUtil.equals(aiLandProperties.getDeploy().getMode(), DeployMode.standalone)) {
            ImportLicenseRequest importLicenseReq = new ImportLicenseRequest();
            importLicenseReq.setCaCertificate(license.getCaCertificate());
            importLicenseReq.setRouteCertificate(license.getRouteCertificate());
            importLicenseReq.setEncryptedBusinessInfo(license.getEncryptedBusinessInfo());
            CommonResult<ImportLicenseResponse> importLicense = licenseRemoteService.importLicense(importLicenseReq);
            Assert.isTrue(importLicense.isSuccess(), "校验证书失败:" + importLicense.getMessage());
            // 解析企业信息
            CompanyDTO company = JSONUtil.toBean(Base64.decodeStr(importLicense.getData().getBusinessInfo()), CompanyDTO.class);
            license.setCompany(company);
            FileUtil.writeUtf8String(JSONUtil.toJsonStr(license), licensePath);
        }

        routerService.updateRouterCache(RouteStatus.license_pass);
        route.setStatus(RouteStatus.license_pass);
        context.setRoute(route);
    }
}
