package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@Builder
@AllArgsConstructor
public class DataAssetSavedVO {

    @ApiModelProperty("执行结果状态-0:数据登记成功1:执行失败，接入主体身份不存在，2:执行失败，数据产品已存在。")
    private String registrationFlag;

    @ApiModelProperty("精确到毫秒-格式为 13位UNIX时间戳")
    private Long registrationTime;

    @ApiModelProperty("数据标识-相关规范和内容见 NDI-TR-2025-04")
    private String registrationId;

    @ApiModelProperty("登记所属区域/行业功能节点标识-相关规范和内容见 NDI-TR-2025-04")
    private String regionNodeId;
}
