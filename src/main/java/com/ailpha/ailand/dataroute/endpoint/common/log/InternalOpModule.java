package com.ailpha.ailand.dataroute.endpoint.common.log;

import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;

public enum InternalOpModule implements OpModule {
    OTHER("其他操作", null),

    USER_CENTER("用户中心", null),
    ROUTE_REGISTER("连接器激活入网", RoleEnums.SUPER_ADMIN),
    LICENSE_MANGER("license管理", RoleEnums.SUPER_ADMIN),
    DATA_ASSET("数据资产", RoleEnums.TRADER),
    USER_MANAGER("用户管理", RoleEnums.SUPER_ADMIN),

    ORDER_MANAGER("订单管理", RoleEnums.TRADER),
    SCENE_MANAGER("交付场景管理", RoleEnums.TRADER),

    DATA_ASSET_APPROVAL("数据资产审批", RoleEnums.SUPER_ADMIN),

    PLUGIN_MANAGER("插件管理", RoleEnums.SUPER_ADMIN),

    CONFIGURATION("系统配置", RoleEnums.SUPER_ADMIN),
    ;
    private final String bizName;

    private final RoleEnums roleType;

    InternalOpModule(String bizName, RoleEnums roleType) {
        this.bizName = bizName;
        this.roleType = roleType;
    }

    @Override
    public String bizName() {
        return bizName;
    }

    @Override
    public boolean hasPermission(RoleEnums roleType) {
        if (OTHER.equals(this)) {
            return false;
        }
        if (RoleEnums.SUPER_ADMIN.equals(roleType)) {
            return true;
        }
        return this.roleType == null || roleType.equals(this.roleType);
    }

    public static OpModule getByName(String bizName) {
        for (InternalOpModule module : InternalOpModule.values()) {
            if (module.bizName.equals(bizName)) {
                return module;
            }
        }
        return null;
    }
}
