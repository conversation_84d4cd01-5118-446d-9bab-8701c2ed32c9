package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AgentTaskType;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据探查任务分页查询请求")
public class AgentTaskPageRequest extends Page {
    @Schema(description = "任务ID")
    String taskId;
    
    @Schema(description = "任务类型", defaultValue = "DATA_EXPLORE")
    AgentTaskType taskType = AgentTaskType.DATA_RESOURCE_EXPLORE;
}
