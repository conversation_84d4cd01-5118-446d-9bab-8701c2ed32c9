package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * 2023/9/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MPCDataSchemasVO {

    @Schema(description = "数据集用途: PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）PRIVATE_SET_INTERSECTION（隐私求交）CIPHER_TEXT_COMPUTE（密文计算）", example = "PRIVATE_INFORMATION_RETRIEVAL")
    MPCPurpose purpose;

    @Schema(description = "数据集 schema")
    List<MPCDataSchemaVO> datasourceSchema;
}
