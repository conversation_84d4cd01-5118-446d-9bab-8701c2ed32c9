package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DescribeRouteResponse extends GatewayResponse<DescribeRouteResponse.InvokeResultWrapper> {
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {
        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult extends CreateRouteRequest.InvokeParam {
        String id;
        @JsonProperty("create_time")
        Long createTime;
        @JsonProperty("update_time")
        Long updateTime;
    }
}
