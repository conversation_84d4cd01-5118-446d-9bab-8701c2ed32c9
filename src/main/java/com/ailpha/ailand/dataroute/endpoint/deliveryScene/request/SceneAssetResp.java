package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.ailpha.ailand.dataroute.endpoint.third.request.SceneAssetApiReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneAssetResp {

    @Schema(description = "状态 RUNNING（进行中）TERMINATED（已终止）COMPLETED（已完成）")
    String sceneStatus;

    String deliverId;

    @Schema(description = "资产关联订单状态")
    String orderStatus;

    @Schema(description = "资产关联订单")
    String orderId;

    boolean isTeeOnline = false;

    SceneAssetApiReq.ExtData ext;

}
