package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneAssetApiReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneAssetResp {

    @Schema(description = "ID")
    String id;

    @Schema(description = "状态 RUNNING（进行中）TERMINATED（已终止）COMPLETED（已完成）")
    String sceneStatus;

    @Schema(description = "资产关联订单状态")
    String orderStatus;

    String apiId;

    SceneAssetApiReq.ExtData jsonExt;

    String ext;

    public SceneAssetApiReq.ExtData getJsonExt() {
        return StringUtils.isBlank(ext) ? null : JSONUtil.toBean(ext, SceneAssetApiReq.ExtData.class);
    }
}
