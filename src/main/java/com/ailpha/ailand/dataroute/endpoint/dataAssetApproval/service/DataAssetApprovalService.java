package com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.enums.ApproveStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetSavedVO;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataAssetSaveRequest;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.deliveryModeMapping;

/**
 * <AUTHOR>
 * 2024/11/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataAssetApprovalService {

    private final TraderService traderService;

    private final DataResourceService dataResourceService;

    private final DataProductService dataProductService;

    private final DataProductRepository dataProductRepository;

    private final BaseCapabilityManager baseCapabilityManager;

    private final DataHubRemoteService dataHubRemote;

    private final RouterService routerService;
    private final DataResourceRepository dataResourceRepository;

    private final HubShuHanApiClient shuHanApiClient;

    /**
     * 更新数据资产状态
     */
    @Transactional
    public void updateStatus(String dataAssetId, AssetType type, String itemStatus, ApproveStatus approveStatus) {
        if (AssetType.PRODUCT.equals(type)) {
            DataProduct product = dataProductRepository.getReferenceById(dataAssetId);
            Assert.isTrue("item_status1".equals(product.getItemStatus()), "非待审批状态，无法进行审批操作");
            dataProductService.checkNameExists(product.getId(), product.getDataProductName());
            product.setItemStatus(itemStatus);
            if ("item_status2".equals(itemStatus)) {
                JSONObject others = new JSONObject();
                others.set("assetId", product.getId());
                others.set("productNameCN", product.getDataExt().getAssetNameCN());
                others.set("industry1", product.getDataExt().getIndustry1());
                others.set("region1", product.getDataExt().getRegion1());
                others.set("deliveryModes", product.getDeliveryExt().getDeliveryModes());
                CompanyDTO company = LoginContextHolder.currentUser().getCompany();
                others.set("clientPlatformUniqueNo", company.getNodeId());
                others.set("userId", product.getUserId());
                others.set("username", product.getUsername());
                others.set("organizationName", company.getOrganizationName());
                others.set("routerId", company.getNodeId());
                others.set("platformId", product.getPlatformId());
                others.set("companyId", company.getId());
                others.set("deliveryModes", product.getDeliveryExt().getDeliveryModes());
                others.set("billingMethod", product.getDeliveryExt().getBillingMethod());
                others.set("purchaseUnit", product.getDeliveryExt().getPurchaseUnit());
                others.set("price", product.getDeliveryExt().getPrice());
                DataAssetSavedVO dataAssetSavedVO = shuHanApiClient.dataProductInfoRegist(DataProductSaveVM.builder()
                        .productName(product.getDataProductName())
                        .productType(product.getDataExt().getType())
                        .timeRange(product.getDataExt().getDataCoverageTimeStart() == null || product.getDataExt().getDataCoverageTimeEnd() == null ? null :
                                String.format("%s 至 %s", product.getDataExt().getDataCoverageTimeStart(), product.getDataExt().getDataCoverageTimeEnd()))
                        .industry(product.getIndustry())
                        .productRegion(product.getDataExt().getRegion())
                        .personalInformation("1".equals(product.getDataExt().getPersonalInformation()))
                        .description(product.getDescription())
                        .deliveryMethod(product.getDeliveryExt().getDeliveryMethod() != null ? product.getDeliveryExt().getDeliveryMethod() : (
                                !CollectionUtils.isEmpty(product.getDeliveryExt().getDeliveryModes()) ? deliveryModeMapping.apply(product.getDeliveryExt().getDeliveryModes().getFirst()) : null
                        ))
                        .limitations(product.getDeliveryExt().getLimitations())
                        .authorize("1".equals(product.getDeliveryExt().getAuthorize()))
                        .dataSubject(product.getDataExt().getDataSubject() == null ? "01" : product.getDataExt().getDataSubject())
                        .dataSize(product.getCapacity() == null ? null : String.valueOf(product.getCapacity()))
                        .updateFrequency(product.getUpdateFrequency())
                        .resourceId(product.getDataExt().getResourceId())
                        .others(others)
                        .providerName(company.getOrganizationName())
                        .providerType(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "02" : "03")
                        .entityInformation(JSONUtil.parseObj(company))
                        .identifyId(company.getCreditCode())
                        .providerDesc(company.getAccessType().getDesc())
                        .operatorName(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeName() : company.getDelegateName())
                        .operatorTelephone(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "" : company.getDelegateContact())
                        .operatorIdCard(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeIdNumber() : company.getDelegateIdNumber())
                        .commission(company.getAuthorizationLetter())
                        .dataSample(product.getDataExt().getQualificationDoc().getDataSampleAttach())
                        .complianceAndLegalStatement(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                        .dataSourceStatement(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                        .safeLevel(product.getDataExt().getQualificationDoc().getSafeLevelAttach())
                        .evaluationReport(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                        .dataVersion(String.valueOf(product.getDataExt().getDataVersion()))
                        .clientPlatformUniqueNo(company.getNodeId())
                        .build());
                Assert.isTrue("0".equals(dataAssetSavedVO.getRegistrationFlag()), "登记数据产品到基础平台失败");
                product.setDataProductPlatformId(dataAssetSavedVO.getRegistrationId());
                product.getDataExt().setPlatformResourceId(dataAssetSavedVO.getRegistrationId());
                product.getDataExt().setRegistrationTime(System.currentTimeMillis());
            }
            dataProductRepository.saveAndFlush(product);
        } else {
            DataResource dataResource = dataResourceRepository.getReferenceById(dataAssetId);
            Assert.isTrue("item_status1".equals(dataResource.getItemStatus()), "非待审批状态，无法进行审批操作");
            dataResourceService.checkNameExists(dataResource.getId(), dataResource.getDataResourceName());
            dataResource.setItemStatus(itemStatus);
            if ("item_status2".equals(itemStatus)) {
                JSONObject others = new JSONObject();
                others.set("assetId", dataResource.getId());
                others.set("resourceNameCN", dataResource.getDataExt().getAssetNameCN());
                others.set("industry1", dataResource.getDataExt().getIndustry1());
                others.set("region1", dataResource.getDataExt().getRegion1());
                others.set("clientPlatformUniqueNo", dataResource.getProvider().getCompany().getNodeId());
                others.set("userId", dataResource.getUserId());
                others.set("username", dataResource.getUsername());
                others.set("organizationName", dataResource.getProvider().getCompany().getOrganizationName());
                others.set("routerId", dataResource.getProvider().getCompany().getNodeId());
                others.set("platformId", dataResource.getPlatformId());
                others.set("companyId", dataResource.getProvider().getCompany().getId());
                others.set("company", dataResource.getProvider().getCompany());
                try {
                    DataAssetSavedVO dataAssetSavedVO = shuHanApiClient.dataResourceRegistry(DataResourceSaveVM.builder()
                            .resourceName(dataResource.getDataResourceName())
                            .resourceFormat(dataResource.getDataExt().getResourceFormat())
                            .resourceId(dataResource.getDataExt().getResourceId() == null ? dataResource.getId() : dataResource.getDataExt().getResourceId())
                            .industry(dataResource.getIndustry())
                            .resourceOwner(dataResource.getProvider().getCompany().getOrganizationName())
                            .contacter(dataResource.getUsername())
                            .contactInformation(dataResource.getProvider().getPhone())
                            .resourceAbstract(dataResource.getDescription())
                            .itemName(dataResource.getDataExt().getDataSchema() != null ? dataResource.getDataExt().getDataSchema().stream().map(DataSchemaBO::getFieldName).collect(Collectors.joining(",")) : "")
                            .itemType(dataResource.getDataExt().getDataSchema() == null ? "[]" : JSONUtil.toJsonStr(dataResource.getDataExt().getDataSchema()))
                            .dataSource(dataResource.getDataExt().getSource())
                            .personalInformation("1".equals(dataResource.getDataExt().getPersonalInformation()) ? 1 : 0)
//                        .resourceStatus("01")
                            .others(others)
                            .dataVersion(String.valueOf(dataResource.getDataExt().getDataVersion()))
                            .clientPlatformUniqueNo(dataResource.getProvider().getCompany().getNodeId())
                            .build());
                    Assert.isTrue("0".equals(dataAssetSavedVO.getRegistrationFlag()), "登记数据资源到基础平台失败");
                    dataResource.getDataExt().setPlatformResourceId(dataAssetSavedVO.getRegistrationId());
                    dataResource.setDataResourcePlatformId(dataAssetSavedVO.getRegistrationId());
                    dataResource.getDataExt().setRegistrationTime(System.currentTimeMillis());
                } catch (Exception e) {
                    throw new RestfulApiException("登记数据资源到基础平台失败", e);
                }
            }
            dataResourceRepository.saveAndFlush(dataResource);
        }
    }

    private final ApplicationContext context;

    public void approve(String dataAssetId, AssetType type) {
        DataAssetApprovalService dataAssetApprovalService = context.getBean(DataAssetApprovalService.class);
        dataAssetApprovalService.updateStatus(dataAssetId, type, "item_status2", ApproveStatus.AUDIT_PASS);
        if (AssetType.PRODUCT.equals(type)) {
            traderService.dataAssetRegisterTrader(dataAssetId);
        } else {
            DataAsset dataAsset = traderService.dataAssetRegisterTrader(dataAssetId);
            DataAssetSaveRequest dataAssetSaveRequest = DataAssetSaveRequest.builder()
                    .id(dataAsset.getAssetId())
                    .type(AssetType.RESOURCE)
                    .platformId(dataAsset.getPlatformId())
                    .routerId(dataAsset.getRouterId())
                    .dataProductPlatformId(dataAsset.getDataAssetPlatformId())
                    .assetName(dataAsset.getAssetName())
//                    .provider(dataAsset.)
//                    .providerOrg(product.getProvider().getCompany().getOrganizationName())
                    .industry(dataAsset.getIndustry())
                    .describeMessage(dataAsset.getDescribeMessage())
                    .dataType(dataAsset.getDataType())
                    .source(dataAsset.getSource())
                    .userId(dataAsset.getUserId())
                    .userName(dataAsset.getUserName())
                    .extraData(JSONUtil.toJsonStr(dataAsset.getExtraData()))
                    .build();
            dataHubRemote.add(dataAssetSaveRequest);
        }
    }

    public void reject(String dataAssetId, AssetType type) {
        DataAssetApprovalService dataAssetApprovalService = context.getBean(DataAssetApprovalService.class);
        dataAssetApprovalService.updateStatus(dataAssetId, type, "item_status3", ApproveStatus.AUDIT_REFUSE);
    }

    /**
     * 资产发布审批
     *
     * @param dataAssetId 数据资产id
     * @param type        数据资产类型
     * @param pass        是否通过审批
     */
    public void processPublishApproval(String dataAssetId, AssetType type, boolean pass) {
        if (AssetType.PRODUCT.equals(type)) {
            DataProduct dataProduct = dataProductRepository.getReferenceById(dataAssetId);
            Assert.isTrue("1".equals(dataProduct.getDataExt().getPublishStatus()), "非待审批状态，无法进行审批操作");
            dataProductService.updateDataExt(dataAssetId, dataProductExt -> {
                dataProductExt.setPublishStatus(pass ? "2" : "3");
                if (pass) {
                    dataProductExt.setPublishTime(System.currentTimeMillis());
                }
                return dataProductExt;
            });
            if (pass) {
                try {
                    if (baseCapabilityManager.platformEnable(BaseCapabilityType.TRADE_PLATFORM)) {
                        DataProduct product = dataProductRepository.getReferenceById(dataAssetId);
                        DataAssetSaveRequest dataAssetSaveRequest = DataAssetSaveRequest.builder()
                                .id(product.getId())
                                .type(AssetType.PRODUCT)
                                .platformId(routerService.currentNode().getPlatformId())
                                .routerId(product.getProvider().getCompany().getNodeId())
                                .dataProductPlatformId(product.getDataProductPlatformId())
                                .assetName(product.getDataProductName())
                                .provider(product.getProvider().getCompany().getIndustryType())
                                .providerOrg(product.getProvider().getCompany().getOrganizationName())
                                .industry(product.getIndustry())
                                .describeMessage(product.getDescription())
                                .dataType(product.getDataExt().getDataType())
                                .source(product.getSourceType())
                                .userId(product.getUserId())
                                .userName(product.getUsername())
                                .extraData(JSONUtil.toJsonStr(product.getDeliveryExt()))
                                .build();
                        dataAssetSaveRequest.setDataProductPlatformId(product.getDataProductPlatformId());
                        dataHubRemote.add(dataAssetSaveRequest);
                    }
                } catch (Exception e) {
                    throw new RestfulApiException("登记数据产品到基础平台失败", e);
                }
            }
        } else {
            DataResource dataResource = dataResourceRepository.getReferenceById(dataAssetId);
            Assert.isTrue("1".equals(dataResource.getDataExt().getPublishStatus()), "非待审批状态，无法进行审批操作");
            dataResourceService.updateDataExt(dataAssetId, dataProductExt -> {
                dataProductExt.setPublishStatus(pass ? "2" : "3");
                return dataProductExt;
            });
        }
    }
}
