package com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.service;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.ApproveStatus;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataUpdateTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetSavedVO;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;

/**
 * <AUTHOR>
 * 2024/11/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataAssetApprovalService {

    private final TraderService traderService;

    private final DataResourceService dataResourceService;

    private final DataProductService dataProductService;

    private final DataProductRepository dataProductRepository;

    private final DataResourceRepository dataResourceRepository;

    private final HubShuHanApiClient shuHanApiClient;
    private final UserService userService;

    private final FilesStorageServiceImpl filesStorageService;

    /**
     * 更新数据资产状态
     */
    @Transactional
    public void updateStatus(String dataAssetId, AssetType type, String itemStatus, ApproveStatus approveStatus) {
        if (AssetType.PRODUCT.equals(type)) {
            DataProduct product = dataProductRepository.getReferenceById(dataAssetId);
            Assert.isTrue("item_status1".equals(product.getItemStatus()), "非待审批状态，无法进行审批操作");
            dataProductService.checkNameExists(product.getId(), product.getDataProductName());
            product.setItemStatus(itemStatus);
            product.getDataExt().addProcessLog("接入主体登记审批" + ("item_status2".equals(itemStatus) ? "通过" : "拒绝"), System.currentTimeMillis(), null, LoginContextHolder.currentUser().getUsername());
            if ("item_status2".equals(itemStatus)) {
                JSONObject others = new JSONObject();
                CompanyDTO company = product.getProvider().getCompany();
                others.set("routerId", company.getNodeId());
                others.set("companyId", company.getId());
                others.set("dataType", product.getDataExt().getDataType());
                UserDetailsResponse userDetail = userService.userDetail(product.getUserId());
                DataAssetSavedVO dataAssetSavedVO = shuHanApiClient.dataProductInfoRegist(DataProductSaveVM.builder()
                        .productName(product.getDataProductName())
                        .productType(product.getDataExt().getType())
                        .timeRange(product.getDataExt().getDataCoverageTimeStart() == null || product.getDataExt().getDataCoverageTimeEnd() == null ? null :
                                String.format("%s 至 %s", product.getDataExt().getDataCoverageTimeStart(), product.getDataExt().getDataCoverageTimeEnd()))
                        .industry(product.getIndustry().substring(0, 1))
                        .productRegion(product.getDataExt().getRegion())
                        .personalInformation("1".equals(product.getDataExt().getPersonalInformation()) ? 1 : 0)
                        .description(product.getDescription())
                        .deliveryMethod(product.getDeliveryExt().getDeliveryMethod())
                        .limitations(product.getDeliveryExt().getLimitations())
                        .authorize("1".equals(product.getDeliveryExt().getAuthorize()) ? 1 : 0)
                        .dataSubject(product.getDataExt().getDataSubject() == null ? "01" : product.getDataExt().getDataSubject())
                        .dataSize(product.getDataExt().getDataSize() == null ? 0 : product.getDataExt().getDataSize())
                        .dataSizeUnit(product.getDataExt().getDataSizeUnit())
                        .updateFrequency(product.getDataExt().getUpdateFrequency() == null ? 0 : product.getDataExt().getUpdateFrequency())
                        .updateFrequencyUnit(product.getDataExt().getUpdateFrequencyUnit() == null ? "次/天" : product.getDataExt().getUpdateFrequencyUnit())
// TODO                       .resourceId(List.of(product.getDataExt().getResourceId() == null ? product.getId() : product.getDataExt().getResourceId()))
                        .resourceId(Collections.emptyList())
                        .others(JSONUtil.toJsonStr(others))
                        .providerName(company.getOrganizationName())
                        .providerType("02")
                        .entityInformation(DataProductSaveVM.entityInformation(company))
                        .identityId(company.getCreditCode())
                        .providerDesc(userDetail.getDelegateInfo().getDelegateName())
                        .operatorName(userDetail.getRealName())
                        .operatorTelephone(userDetail.getPhone())
                        .operatorIdCard(userDetail.getDelegateInfo().getDelegateIdNumber())
                        .commission(company.getAuthorizationLetter())
                        .commissionFileName(company.getAuthorizationLetter())
                        .dataSample(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                                .resolve(product.getUserId())
                                .resolve(product.getDataExt().getQualificationDoc().getDataSampleAttach())))
                        .dataSampleFileName(product.getDataExt().getQualificationDoc().getDataSampleAttach())
                        .complianceAndLegalStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                                .resolve(product.getUserId())
                                .resolve(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())))
                        .complianceAndLegalStatementFileName(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                        .dataSourceStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                                .resolve(product.getUserId())
                                .resolve(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach())))
                        .dataSourceStatementFileName(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                        .safeLevel(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                                .resolve(product.getUserId())
                                .resolve(product.getDataExt().getQualificationDoc().getSafeLevelAttach())))
                        .safeLevelFileName(product.getDataExt().getQualificationDoc().getSafeLevelAttach())
                        .evaluationReport(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                                .resolve(product.getUserId())
                                .resolve(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())))
                        .evaluationReportFileName(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                        .dataVersion(String.valueOf(product.getDataExt().getDataVersion()))
                        .build());
                product.setDataProductPlatformId(dataAssetSavedVO.getRegistrationId());
                product.getDataExt().setProcessId(dataAssetSavedVO.getProcessId());
                product.getDataExt().setPlatformResourceId(dataAssetSavedVO.getRegistrationId());
                product.getDataExt().setRegistrationTime(System.currentTimeMillis());
                product.setItemStatus("item_status5");
                product.getDataExt().addProcessLog("提交功能节点登记审批", System.currentTimeMillis(), null, null);
            }
            dataProductRepository.saveAndFlush(product);
        } else {
            DataResource dataResource = dataResourceRepository.getReferenceById(dataAssetId);
            Assert.isTrue("item_status1".equals(dataResource.getItemStatus()), "非待审批状态，无法进行审批操作");
            dataResourceService.checkNameExists(dataResource.getId(), dataResource.getDataResourceName());
            dataResource.setItemStatus(itemStatus);
            dataResource.getDataExt().addProcessLog("接入主体登记审批" + ("item_status2".equals(itemStatus) ? "通过" : "拒绝"), System.currentTimeMillis(), null, LoginContextHolder.currentUser().getUsername());
            if ("item_status2".equals(itemStatus)) {
                JSONObject others = new JSONObject();
                others.set("routerId", dataResource.getProvider().getCompany().getNodeId());
                others.set("companyId", dataResource.getProvider().getCompany().getId());
                others.set("dataType", dataResource.getDataExt().getDataType());
                try {
                    DataAssetSavedVO dataAssetSavedVO = shuHanApiClient.dataResourceRegistry(DataResourceSaveVM.builder()
                            .resourceName(dataResource.getDataResourceName())
                            .resourceFormat(dataResource.getDataExt().getResourceFormat())
                            .industry(dataResource.getIndustry().substring(0, 1))
                            .resourceOwner(dataResource.getProvider().getCompany().getOrganizationName())
                            .identityId(dataResource.getProvider().getCompany().getCreditCode())
                            .contacter(dataResource.getUsername())
                            .contactInformation(dataResource.getProvider().getPhone())
                            .resourceAbstract(dataResource.getDescription())
                            .itemList(dataResource.getDataExt().getDataSchema() == null ? Collections.emptyList() :
                                    dataResource.getDataExt().getDataSchema().stream().map(schema -> new DataResourceSaveVM.Item(schema.getName(), schema.getType())).toList())
                            .dataSource(dataResource.getDataExt().getSource())
                            .personalInformation("1".equals(dataResource.getDataExt().getPersonalInformation()) ? 1 : 0)
//                        .resourceStatus("01")
                            .others(JSONUtil.toJsonStr(others))
                            .dataVersion(String.valueOf(dataResource.getDataExt().getDataVersion()))
                            .build());
                    dataResource.getDataExt().setPlatformResourceId(dataAssetSavedVO.getRegistrationId());
                    dataResource.getDataExt().setProcessId(dataAssetSavedVO.getProcessId());
                    dataResource.setDataResourcePlatformId(dataAssetSavedVO.getRegistrationId());
                    dataResource.getDataExt().setRegistrationTime(System.currentTimeMillis());
                    dataResource.setItemStatus("item_status5");
                    dataResource.getDataExt().addProcessLog("提交功能节点登记审批", System.currentTimeMillis(), null, null);
                } catch (Exception e) {
                    throw new RestfulApiException("登记数据资源到基础平台失败", e);
                }
            }
            dataResourceRepository.saveAndFlush(dataResource);
        }
    }

    private final ApplicationContext context;

    public void approve(String dataAssetId, AssetType type) {
        DataAssetApprovalService dataAssetApprovalService = context.getBean(DataAssetApprovalService.class);
        dataAssetApprovalService.updateStatus(dataAssetId, type, "item_status2", ApproveStatus.AUDIT_PASS);
        if (AssetType.PRODUCT.equals(type)) {
            traderService.dataAssetRegisterTrader(dataAssetId);
        } else {
            DataAsset dataAsset = traderService.dataAssetRegisterTrader(dataAssetId);
            /*DataAssetSaveRequest dataAssetSaveRequest = DataAssetSaveRequest.builder()
                    .id(dataAsset.getAssetId())
                    .type(AssetType.RESOURCE)
                    .platformId(dataAsset.getPlatformId())
                    .routerId(dataAsset.getRouterId())
                    .dataProductPlatformId(dataAsset.getDataAssetPlatformId())
                    .assetName(dataAsset.getAssetName())
//                    .provider(dataAsset.)
//                    .providerOrg(product.getProvider().getCompany().getOrganizationName())
                    .industry(dataAsset.getIndustry())
                    .describeMessage(dataAsset.getDescribeMessage())
                    .dataType(dataAsset.getDataType())
                    .source(dataAsset.getSource())
                    .userId(dataAsset.getUserId())
                    .userName(dataAsset.getUserName())
                    .extraData(JSONUtil.toJsonStr(dataAsset.getExtraData()))
                    .build();*/
            // dataHubRemote.add(dataAssetSaveRequest);
        }
    }

    public void reject(String dataAssetId, AssetType type) {
        DataAssetApprovalService dataAssetApprovalService = context.getBean(DataAssetApprovalService.class);
        dataAssetApprovalService.updateStatus(dataAssetId, type, "item_status3", ApproveStatus.AUDIT_REFUSE);
    }

    /**
     * 资产发布审批
     *
     * @param dataAssetId 数据资产id
     * @param type        数据资产类型
     * @param pass        是否通过审批
     */
    public void processPublishApproval(String dataAssetId, AssetType type, boolean pass) {
        if (AssetType.PRODUCT.equals(type)) {
            DataProduct dataProduct = dataProductRepository.getReferenceById(dataAssetId);
            Assert.isTrue("1".equals(dataProduct.getDataExt().getPublishStatus()), "非待审批状态，无法进行审批操作");
            long publishTime = System.currentTimeMillis();
            dataProductService.updateDataExt(dataAssetId, dataProductExt -> {
                dataProductExt.setPublishStatus(pass ? "2" : "3");
                dataProductExt.addProcessLog("接入主体上架审批" + (pass ? "通过" : "拒绝"), System.currentTimeMillis(), null, LoginContextHolder.currentUser().getUsername());
                if (pass) {
                    dataProductExt.setPublishTime(publishTime);
                }
                return dataProductExt;
            });
            if (pass) {
/*                try {
                    String localCompanyId = null;
                    if (LoginContextHolder.isLogin()) {
                        UserDTO currentUser = LoginContextHolder.currentUser();
                        localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
                    }
                    if (baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TRADE_PLATFORM)) {
                        DataProduct product = dataProductRepository.getReferenceById(dataAssetId);
                        DataAssetSaveRequest dataAssetSaveRequest = DataAssetSaveRequest.builder()
                                .id(product.getId())
                                .type(AssetType.PRODUCT)
                                .platformId(routerService.currentNode().getPlatformId())
                                .routerId(product.getProvider().getCompany().getNodeId())
                                .dataProductPlatformId(product.getDataProductPlatformId())
                                .assetName(product.getDataProductName())
                                .provider(product.getProvider().getCompany().getIndustryType())
                                .providerOrg(product.getProvider().getCompany().getOrganizationName())
                                .industry(product.getIndustry())
                                .describeMessage(product.getDescription())
                                .dataType(product.getDataExt().getDataType())
                                .source(product.getSourceType())
                                .userId(product.getUserId())
                                .userName(product.getUsername())
                                .extraData(JSONUtil.toJsonStr(product.getDeliveryExt()))
                                .build();
                        dataAssetSaveRequest.setDataProductPlatformId(product.getDataProductPlatformId());
                        dataHubRemote.add(dataAssetSaveRequest);
                    }
                } catch (Exception e) {
                    throw new RestfulApiException("登记数据产品到基础平台失败", e);
                }*/

                // 执行更新任务,根据产品id查询更新任务id，然后执行
                DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
                DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());
                dataUpdateTaskService.executeTask(dataUpdateTask.getId());
            }
        } else {
            DataResource dataResource = dataResourceRepository.getReferenceById(dataAssetId);
            Assert.isTrue("1".equals(dataResource.getDataExt().getPublishStatus()), "非待审批状态，无法进行审批操作");
            dataResourceService.updateDataExt(dataAssetId, dataResourceExt -> {
                dataResourceExt.setPublishStatus(pass ? "2" : "3");
                dataResourceExt.addProcessLog("接入主体上架审批" + (pass ? "通过" : "拒绝"), System.currentTimeMillis(), null, LoginContextHolder.currentUser().getUsername());
                return dataResourceExt;
            });
        }
    }
}
