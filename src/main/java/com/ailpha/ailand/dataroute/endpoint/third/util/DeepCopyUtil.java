package com.ailpha.ailand.dataroute.endpoint.third.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.SerializationUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class DeepCopyUtil {

    /**
     * 使用序列化方式深拷贝
     */
    public static <T extends Serializable> List<T> deepCopyBySerialize(List<T> src) {
        return SerializationUtils.clone(new ArrayList<>(src));
    }

    /**
     * 使用JSON方式深拷贝
     */
    public static <T> List<T> deepCopyByJson(List<T> src, Class<T> clazz) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            String jsonStr = mapper.writeValueAsString(src);
            return mapper.readValue(jsonStr,
                    mapper.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            log.error("JSON深拷贝失败", e);
            throw new RuntimeException("深拷贝失败", e);
        }
    }

    /**
     * 使用Stream方式深拷贝（需要对象实现Clone接口）
     */
    public static <T extends Cloneable> List<T> deepCopyByStream(List<T> src) {
        return src.stream()
                .map(item -> {
                    try {
                        return (T) item.getClass().getMethod("clone").invoke(item);
                    } catch (Exception e) {
                        throw new RuntimeException("克隆失败", e);
                    }
                })
                .collect(Collectors.toList());
    }
}