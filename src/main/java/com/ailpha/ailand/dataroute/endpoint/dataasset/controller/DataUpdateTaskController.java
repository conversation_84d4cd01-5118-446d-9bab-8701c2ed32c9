package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataUpdateTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductAccessConfigUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogVO;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Tag(name = "数据产品-接入更新")
@RequiredArgsConstructor
@RequestMapping("data-product/update")
@PreAuthorize("hasAuthority('TRADER')")
@Slf4j
public class DataUpdateTaskController {

    private final DataUpdateTaskService dataUpdateTaskService;

    private final DataProductService dataProductService;

//    /**
//     * 创建数据接入任务
//     *
//     * @param task 任务信息
//     * @return 创建的任务
//     */
//    @PostMapping
//    public ResponseEntity<DataUpdateTask> createTask(@RequestBody DataUpdateTask task) {
//        return ResponseEntity.ok(taskService.createTask(task));
//    }

//    /**
//     * 更新数据接入任务
//     *
//     * @param taskId 任务ID
//     * @param task   任务信息
//     * @return 更新后的任务
//     */
//    @PutMapping("/{taskId}")
//    public ResponseEntity<DataUpdateTask> updateTask(@PathVariable String taskId,
//                                                     @RequestBody DataUpdateTask task) {
//        task.setId(taskId);
//        return ResponseEntity.ok(dataUpdateTaskService.updateTask(task));
//    }

//    /**
//     * 删除数据接入任务
//     *
//     * @param taskId 任务ID
//     * @return 无内容
//     */
//    @DeleteMapping("/{taskId}")
//    public ResponseEntity<Void> deleteTask(@PathVariable String taskId) {
//        dataUpdateTaskService.deleteTask(taskId);
//        return ResponseEntity.noContent().build();
//    }

//    /**
//     * 获取任务详情
//     *
//     * @param taskId 任务ID
//     * @return 任务详情
//     */
//    @GetMapping("/{taskId}")
//    public ResponseEntity<DataUpdateTask> getTask(@PathVariable String taskId) {
//        DataUpdateTask task = dataUpdateTaskService.getTask(taskId);
//        return task != null ? ResponseEntity.ok(task) : ResponseEntity.notFound().build();
//    }

//    /**
//     * 获取数据产品关联的任务列表
//     *
//     * @param dataProductId 数据产品ID
//     * @return 任务列表
//     */
//    @GetMapping("/by-data-product/{dataProductId}")
//    public ResponseEntity<DataUpdateTask> getTasksByDataProduct(@PathVariable String dataProductId) {
//        return ResponseEntity.ok(taskService.getTasksByDataProduct(dataProductId));
//    }

    @GetMapping("/execute/{dataProductId}")
    @Operation(summary = "手动执行数据产品更新任务")
    public SuccessResponse<Boolean> executeTask(@PathVariable String dataProductId) {
        log.debug("手动执行数据产品更新任务");
        dataUpdateTaskService.executeTask(dataUpdateTaskService.getTaskByDataProductId(dataProductId).getId());
        return SuccessResponse.success(true).build();
    }

    @PostMapping("/history")
    @Operation(summary = "获取数据产品更新任务历史记录")
    public SuccessResponse<List<DataUpdateTaskLogVO>> getTaskHistory(@RequestBody @Valid DataUpdateTaskLogPageRequest dataUpdateTaskLogPageRequest) {
        return dataUpdateTaskService.getTaskHistory(dataUpdateTaskLogPageRequest);
    }

    @PostMapping("/config")
    @Operation(summary = "更新数据产品数据接入信息")
    public SuccessResponse<Boolean> accessConfigUpdate(@RequestBody DataProductAccessConfigUpdateRequest dataProductAccessConfigUpdateRequest) {
        dataProductService.accessConfigUpdate(dataProductAccessConfigUpdateRequest);
        return SuccessResponse.success(true).build();
    }

    @GetMapping("/execute/schedule/{dataProductId}")
    @Operation(summary = "手动触发数据产品定时更新任务")
    public SuccessResponse<Boolean> scheduleTask(@PathVariable String dataProductId) {
        dataUpdateTaskService.triggerTask(dataUpdateTaskService.getTaskByDataProductId(dataProductId).getId());
        return SuccessResponse.success(true).build();
    }
}