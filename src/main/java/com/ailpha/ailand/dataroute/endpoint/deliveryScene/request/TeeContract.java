package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TeeContract {
    String sceneId;
    String contractName;              // 合约名
    List<String> datasetIds;         // 数据集
    List<String> profitedUserIdList;   // 获益人
    String sandboxType;               // 合约类型
    String businessType;              // 业务类型
    String pythonModelTrainDebugType;// 建模方法
    List<String> resultFetchWays;     // 结果获取方式
    String algorithmDesc;             // 合约描述
    int debugMemorySize;             // 调试环境资源配置
    int memorySize;                  // 资源配置
    boolean enableGPU;                // 启用GPU
    String executeType;               // 计算频率
    String expireDate;                // 过期时间，不传表示不过期
    String selectDate;         // 执行日期
    String selectHour;                // 执行时间
    String updateFreq;                // 更新频率 DAY WEEK MONTH SINGLE HOUR MULTI
    int driverMemory;                 // 正式环境 pyspark 参数
    int driverCores;                  // 正式环境 pyspark 参数
    int debugDriverMemory;            // 调试环境 pyspark 参数
    int debugDriverCores;             // 调试环境 pyspark 参数
    int executeTimesLimit;            // 执行次数限制
    String flinkJobType;                 // 流式任务类型
    FlinkJobConfig flinkJobConfigs;   // 流式任务合约资源配置
    String key;                       // 流式合约key，执行时放入header
    String userId;                      // 创建者
    String userName;                      // 创建者名
    String routerId;                  // 连接器id
    String orderId;                   // 交付订单id
    String modelId;
    String companyId;

    List<DatasetApiInfo> apiInfos;
    @Data
    public static class DatasetApiInfo {
        String dataAssetId;
        String apikey;
        String onlineApiUrl;
    }


    @Data
    public static class FlinkJobConfig {
        int slots = 1;
        int taskManagerNums = 1;
        int jobManagerMaxMemSize = 1024;
        int taskManagerMaxMemSize = 1024;
        int parallelism = 1;
    }
}
