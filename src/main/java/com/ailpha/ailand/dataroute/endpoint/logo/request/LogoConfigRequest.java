package com.ailpha.ailand.dataroute.endpoint.logo.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/1 15:27
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogoConfigRequest {

    private Boolean showLogo;

    private LogoConfig logo;

    private String productName;

    private String companyName;

    private LogoConfig loginBg;

    private String headerBgType;

    private LogoConfig headerBg;

    private String headerBgColor;

    private String siderBgType;

    private LogoConfig siderBg;

    private String siderBgColor;

    private LogoConfig screenBg;

    @Data
    public static class LogoConfig {
        private String url;
        private String fileName;
    }

}
