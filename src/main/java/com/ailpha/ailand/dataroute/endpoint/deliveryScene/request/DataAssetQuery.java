package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "数据集市数据资产列表筛选条件")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetQuery extends Page {
    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "数源单位")
    String providerOrg;

    @Schema(description = "行业分类")
    String industry;

    @Schema(description = "交付场景")
    DeliveryMode deliveryMode;


}
