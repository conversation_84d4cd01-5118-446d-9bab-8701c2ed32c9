package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.APISourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AiSortMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DatabaseSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.FileSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.FileTypeConstants;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAsset {
    String assetId;
    /**
     * 数据资源全局（连接器空间）唯一标识
     */
    String dataAssetPlatformId;
    AssetType assetType;
    /**
     * 连接器ID
     */
    String routerId;
    /**
     * 连接器名称 根据连接器id获取连接器信息然后填充
     */
    String routerName;
    /**
     * 资产名称
     */
    String assetName;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 数据容量（单位 MB）
     */
    String capacity;
    /**
     * 敏感等级
     */
    String sensitiveLevel;
    /**
     * 更新频率：数源的更新迭代不代表接入平台的数据是否更新
     */
    String updateFrequency;
    /**
     * 标签
     */
    List<String> tag;
    /**
     * 数据覆盖周期开始时间（精确到2024.1 ）
     */
    String dataCoverageTimeStart;
    /**
     * 数据覆盖周期结束时间（精确到2024.12）
     */
    String dataCoverageTimeEnd;
    /**
     * 资源摘要
     */
    String describeMessage;
    // <<< 以上为数翰平台数据资产信息

    /**
     * 数据类型：结构化数据、非结构化数据、模型
     */
    DataType dataType;
    /**
     * 数据接入方式：API、数据库、文件
     */
    SourceType source;
    /**
     * 审核状态 item_status1 item_status2 item_status3
     */
    String itemStatus;
    /**
     * 上下线状态
     */
    PushStatus pushStatus;
    /**
     * 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销 5 业务节点待审批 6 业务节点通过 7 业务节点拒绝
     */
    @Builder.Default
    String publishStatus = "0";
    /**
     * 用户id
     */
    String userId;
    /**
     * 用户名
     */
    String userName;

    String createTime;
    String updateTime;

    Boolean isDelete;

    ExtraData extraData;

    String platformId;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ExtraData {
        /**
         * 数据资产预处理状态：已创建、已处理
         */
        @Builder.Default
        DataAssetPrepareStatus dataAssetPrepareStatus = DataAssetPrepareStatus.CREATED;
        /**
         * 数据预处理失败原因
         */
        String processResultMessage;
        /**
         * 数据库数据资产元数据
         */
        @Builder.Default
        Map<DeliveryMode, DataAssetExt.ProcessResult> processResult = new HashMap<>();
        /**
         * API查询方式
         */
        APIQueryWay apiQueryWay;
        /**
         * 交付方式：原数据、TEE、MPC
         */
        @Builder.Default
        List<DeliveryMode> deliveryModes = new ArrayList<>();
        /**
         * MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）
         */
        @Builder.Default
        List<MPCPurpose> mpcPurpose = new ArrayList<>();
        /**
         * 调试数据来源
         */
        DebugDataSourceEnum debugDataSource;
        /**
         * 调试数据路径
         */
        String debugDataPath;
        /**
         * 调试数据分隔符
         */
        String separator;
        /**
         * 调试数据是否包含表头
         */
        Integer hasHeader;
        /**
         * 数据结构
         */
        List<DataSchemaBO> dataSchema;
        /**
         * 网关服务路由ID
         */
        String gatewayServiceRouteId;
        /**
         * API数据资产元数据
         */
        @Builder.Default
        APISourceMetadata apiSourceMetadata = new APISourceMetadata();
        /**
         * 文件数据资产元数据
         */
        @Builder.Default
        FileSourceMetadata fileSourceMetadata = new FileSourceMetadata();
        /**
         * 数据库数据资产元数据
         */
        @Builder.Default
        DatabaseSourceMetadata databaseSourceMetadata = new DatabaseSourceMetadata();
        /**
         * AiSort元数据
         */
        @Builder.Default
        AiSortMetadata aiSortMetadata = new AiSortMetadata();

        /**
         * 绑定交易所插件
         */
        List<Long> exchangePluginIds;

        /**
         * 绑定数字证书插件
         */
        List<Long> certificatePluginIds;
        /**
         * 连接器名称
         */
        String routerName;
        String phone;
        String email;

        String createIp;
        /**
         * 产品血缘
         */
        String lineage;
        /**
         * 平台生成(MPC) 结果集(openapiId)
         */
        String mpcOpenAPIId;
        /**
         * 行业分类（前端回显用）
         */
        String industry1;
        /**
         * 是否改写响应体
         */
        Boolean extractResponse;
        // 声明信息
        QualificationDoc qualificationDoc;
        /**
         * 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销 5 业务节点待审批 6 业务节点通过 7 业务节点拒绝
         */
        @Builder.Default
        String publishStatus = "0";
        Long companyId;
    }


    public static final List<String> SUPPORT_SUFFIX = List.of(FileTypeConstants.CSV, FileTypeConstants.DATA, FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ,
            FileTypeConstants.GZ, FileTypeConstants.TAR, FileTypeConstants.TXT, FileTypeConstants.PDF, FileTypeConstants.DOC, FileTypeConstants.DOCX, FileTypeConstants.XLSX);

    public static final List<String> SUPPORT_SUFFIX_FOR_MODEL = List.of(FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ, FileTypeConstants.GZ, FileTypeConstants.TAR);

    public static boolean validateFilenameUsingRegex(String filename, List<String> supportedSuffix) {
        if (filename == null) {
            return false;
        }
        return filename.matches("^.+(" + String.join("|", supportedSuffix) + ")$");
    }

    public static void main(String[] args) {
        System.out.println(validateFilenameUsingRegex("test.csv", SUPPORT_SUFFIX));
        System.out.println(validateFilenameUsingRegex("测试.csv", SUPPORT_SUFFIX));
        System.out.println(validateFilenameUsingRegex("测试.hah", SUPPORT_SUFFIX));
        System.out.println("支持的文件格式为：" + DataAsset.SUPPORT_SUFFIX);
    }

}
