package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DemandSideDTO {
    @Schema(description = "需求主体名称")
    String demandSideName;
    @Schema(description = "需求主体性质")
    String species;
    @Schema(description = "连接器名称")
    String routerName;
    @Schema(description = "连接器ID")
    String routerId;
    @Schema(description = "需求方")
    String username;
    @Schema(description = "电话")
    String phone;
    @Schema(description = "邮箱")
    String email;
}
