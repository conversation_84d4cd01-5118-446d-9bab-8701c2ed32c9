package com.ailpha.ailand.dataroute.endpoint.user.security;

import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
public class LoginContextHolder {

    public static void clearContext() {
        SecurityContextHolder.clearContext();
    }

    public static UserDTO currentUser() {
        return getPrincipal();
    }

    public static RoleEnums currentUserRole() {
        return RoleEnums.valueOf(getPrincipal().getRoleName());
    }

    private static UserDTO getPrincipal() {
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (ObjectUtil.isNull(principal)) {
            throw new RestfulApiException("未登录");
        }

        if (principal instanceof UserDTO) {
            return (UserDTO) principal;
        } else if (principal instanceof String) {
            // 如果是字符串（通常是用户名），需要重新获取用户信息
            // 这里需要根据你的业务逻辑来实现，比如从数据库或缓存中获取用户信息
            log.error("异常的用户信息：{}", principal);
//            if (principal == "anonymousUser")
            return null;
            // todo: 异常的用户信息：32b8b76f29630b3ebb24c595fd09d8c9742e64f6bf61bcfc34e96b3e5db106613c399b3e1b2e285fa874271107640763
//            throw new RestfulApiException("用户信息不完整，请重新登录");
        } else {
            log.error("不支持的认证对象类型：" + principal.getClass().getName());
            throw new RestfulApiException("获取登录信息异常，请重新登录");
        }
    }

    public static Boolean isLogin() {
        return ObjectUtil.isNotNull(getPrincipal());
    }
}
