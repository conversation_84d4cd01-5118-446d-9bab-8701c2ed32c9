package com.ailpha.ailand.dataroute.endpoint.entity;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginApiTypeEnum;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.BlockchainParamsBO;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 插件api配置地址表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_blockchain_plugin_api_detail")
public class BlockchainPluginApiDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(length = 255)
    private String url;

    @Enumerated(EnumType.STRING)
    private MethodEnum method;

    @JdbcTypeCode(SqlTypes.JSON)
    private List<BlockchainParamsBO> params;

    @Column(length = 500)
    private String body;

    @Enumerated(EnumType.STRING)
    @Column(name = "body_type")
    private BodyTypeEnum bodyType;

    @JdbcTypeCode(SqlTypes.JSON)
    private List<BlockchainParamsBO> headers;

    @Column(updatable = false, name = "create_time")
    private LocalDateTime createTime;

    @Enumerated(EnumType.STRING)
    private BlockchainPluginApiTypeEnum type;

    private String ext;

    @Column(name = "up_data_field")
    String upDataField;

    @Column(name = "plugin_id")
    Long pluginId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plugin_id", referencedColumnName = "id", insertable = false, updatable = false)
    BlockchainPluginDetail blockchainPluginDetail;
}