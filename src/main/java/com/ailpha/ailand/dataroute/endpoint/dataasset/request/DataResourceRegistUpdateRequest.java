package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资源登记请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceRegistUpdateRequest {
    @NotEmpty(message = "产品ID不能为空")
    @Schema(description = "数据标识")
    String id;
    @Schema(description = "资源名称")
    String resourceNameCN;
    @Schema(description = "资源格式")
    String resourceFormat;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是", examples = {"0", "1"})
    String personalInformation;
    @Schema(description = "资源简介")
    String description;
    @Schema(description = "资源来源: 01 原始取得 02 收集取得 03 交易取得 04 其他")
    String source;
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "其他")
    String other;

    @Schema(description = "数据项")
    List<DataSchemaBO> dataSchema;
}
