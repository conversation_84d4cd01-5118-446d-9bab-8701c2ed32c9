package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataProductUpdateVM extends DataProductSaveVM {

    @ApiModelProperty("数据唯一标识")
    @NotBlank(message = "数据唯一标识为空")
    private String registrationId;
}
