package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2022/6/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeployInfo implements Serializable {

    @ApiModelProperty(value = "是否启用邮箱服务", example = "true")
    private Boolean enableMail;
    @ApiModelProperty(value = "是否启用数据集审批", example = "true")
    private Boolean enableSdoAudit;
    @ApiModelProperty(value = "是否启用浙江政务钉钉扫码登录", example = "false")
    private Boolean enableZwDingDingQRLogin = false;
    @ApiModelProperty(value = "合约名称自定义", example = "false")
    private String contractName = "合约";
    @ApiModelProperty(value = "交易名称自定义", example = "false")
    private String traderName = "交易";

    /**
     * sdo 审批配置细化
     */
    @ApiModelProperty(value = "是否启用数据集审批", example = "true")
    private Boolean enableSdoDatasetAudit;
    @ApiModelProperty(value = "是否启用模型发布审批", example = "true")
    private Boolean enableSdoModelAudit;
    @ApiModelProperty(value = "是否启用模型在线推理审批", example = "true")
    private Boolean enableSdoArgumentAudit;
}
