package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;


import cn.hutool.json.JSONObject;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RouterInfoReportRequest {

    Integer type;

    List<JSONObject> value;

    String timestamp;

    String remark;
}
