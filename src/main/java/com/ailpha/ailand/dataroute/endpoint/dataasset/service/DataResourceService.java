package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticAssetDTO;
import com.dbapp.rest.response.SuccessResponse;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;
import java.util.function.Function;

public interface DataResourceService {

    /**
     * 获取数据资产
     *
     * @param dataAssetId 数据资产ID
     * @return DataAssetVO
     */
    DataResourceVO getDataResource(String dataAssetId);


    /**
     * 获取数据资产
     *
     * @param dataResourcePlatformId 数据资产全局ID
     * @return DataResourceVO
     */
    DataResourceVO getDataResourceByDataResourcePlatformId(String dataResourcePlatformId, String currentNodeId);

    DataResourceVO getDataResourceByDataResourcePlatformId(String dataResourcePlatformId, String currentNodeId, String targetNodeId);

    DataResourceVO getDataResourceByDataResourcePlatformIdFromRemote(String dataResourcePlatformId, String targetNodeId);

    /**
     * 获取所有数据资产
     *
     * @param dataAssetQuery 查询条件
     * @return 所有符合条件的数据资产
     */
    SuccessResponse<List<DataResourceListVO>> allDataAssets(int page, int size, Function<Specification<DataResource>, Specification<DataResource>> specificationFunction);

    void updateDataExt(String dataAssetId, Function<DataResource.DataResourceExt, DataResource.DataResourceExt> dataExtUpdate);

    void checkNameExists(String dataResourceId, String dataResourceName);

    /**
     * 数据产品登记暂存
     *
     * @param request
     */
    void temporarySave(DataResourceRegistRequest request);

    /**
     * 数据产品登记
     *
     * @param request
     */
    void registration(DataResourceRegistRequest request);

    /**
     * 数据产品登记更新
     *
     * @param request
     */
    void updateRegistration(DataResourceRegistUpdateRequest request);

    /**
     * 数据产品登记撤销
     *
     * @param productId
     */
    void revokeRegistration(String productId);

    void delete(String dataResourceId);

    StatisticAssetDTO.StatisticResource statistics();
}
