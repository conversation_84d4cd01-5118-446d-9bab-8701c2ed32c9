package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.dbapp.rest.exception.RestfulApiException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:20
 */
@Slf4j
public class IpUtils {

    public static String getIP(String url) {
        //使用正则表达式过滤，
        String re = "((http|ftp|https)://)(([a-zA-Z0-9._-]+)|([0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}))";
        String str = "";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(re);
        // 忽略大小写的写法
        Matcher matcher = pattern.matcher(url);
        //若url==http://127.0.0.1:9040或www.baidu.com的，正则表达式表示匹配
        if (matcher.matches()) {
            str = url;
        } else {
            String[] split2 = url.split(re);
            if (split2.length > 1) {
                String substring = url.substring(0, url.length() - split2[1].length());
                str = substring;
            } else {
                str = split2[0];
            }
        }
        str = str.startsWith("http://") || str.startsWith("https://") ? str.split("//")[1] : str;
        if (isDomain(url)) {
            try {
                InetAddress inetAddress = InetAddress.getByName(str);
                str = inetAddress.getHostAddress();
            } catch (UnknownHostException e) {
                log.error("解析域名异常 ", e);
                throw new RestfulApiException("无法解析域名：" + str);
            }
        }

        return str;
    }

    public static boolean isLoopBackAddress(String ipAddress) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ipAddress);
            return inetAddress.isLoopbackAddress();
        } catch (Exception ignore) {
            return false;
        }
    }

    public static String getClientIp(HttpServletRequest request) {
        try {
            String clientIp = request.getHeader("X-Forwarded-For");
            if (clientIp == null || clientIp.isEmpty()) {
                // 如果没有代理头，则返回直接连接的 IP 地址
                clientIp = request.getRemoteAddr();
            }
            // X-Forwarded-For 可能会包含多个 IP 地址，取第一个
            if (clientIp != null && clientIp.indexOf(",") > 0) {
                clientIp = clientIp.split(",")[0]; // 获取最前面那个 IP 地址
            }
            return clientIp;
        } catch (Exception e) {
            log.error("获取客户端ip异常：", e);
            return "127.0.0.1";
        }
    }

    // 正则表达式，用于匹配域名的基本特征
    private static final Pattern DOMAIN_PATTERN = Pattern.compile(
            "([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}"
    );

    public static boolean isDomain(String url) {
        try {
            // 使用URL类解析输入的URL字符串
            URL parsedUrl = new URL(url);

            // 获取URL的主机部分
            String host = parsedUrl.getHost();

            // 使用正则表达式判断主机部分是否符合域名特征
            return DOMAIN_PATTERN.matcher(host).matches();
        } catch (MalformedURLException e) {
            // 若输入的字符串不是有效的URL格式，捕获异常并处理
            log.error("输入的字符串不是一个有效的URL");
            return false;
        }
    }

    public static void main(String[] args) {
//        String url = "http://127.0.0.1:8501";
//        String url = "www.baidu.com";
//        String url = "https://***********/digital-certificate";
        String url = "kimi.moonshot.cn";
        System.out.println(getIP(url));
    }

}
