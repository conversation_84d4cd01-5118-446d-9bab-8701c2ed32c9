package com.ailpha.ailand.dataroute.endpoint.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "操作日志返回")
public class LogResponse {
    @Schema(description = "操作用户")
    String username;
    @Schema(description = "IP")
    String ip;
    @Schema(description = "操作模块")
    String opModule;
    @Schema(description = "结果")
    String result;
    @Schema(description = "操作类型")
    String opType;
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "归档时段（结束）", example = "2020.03.03 11:15:25")
    Date createTime;
    @Schema(description = "备注")
    String desc;
    String message;
}
