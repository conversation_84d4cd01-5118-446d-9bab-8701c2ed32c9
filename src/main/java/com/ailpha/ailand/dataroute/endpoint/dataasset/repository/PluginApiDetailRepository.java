package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:48
 * @Description:
 */
public interface PluginApiDetailRepository extends JpaRepository<PluginApiDetail, Long>, QuerydslPredicateExecutor<PluginApiDetail> {

}
