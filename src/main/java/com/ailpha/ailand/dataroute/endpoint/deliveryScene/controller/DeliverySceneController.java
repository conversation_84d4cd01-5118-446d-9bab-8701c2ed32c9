package com.ailpha.ailand.dataroute.endpoint.deliveryScene.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.*;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.order.util.PageUtils;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.third.response.SystemConfigureInfo;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/17 16:02
 */
@RestController
@Tag(name = "交付场景")
@RequestMapping("delivery")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('TRADER')")
public class DeliverySceneController {

    private final DigitalCertificateRemote digitalCertificateRemote;

    private final DeliveryService deliveryService;

    private final DataHubRemoteService dataHubRemoteService;


    @Operation(summary = "合规场景列表-场景分页")
    @PostMapping({"/scene-list"})
    public PageResult<SceneBasicInfoResp> traderComplianceSceneList(@RequestBody @Valid ThirdSceneInfoPageReq thirdSceneInfoPageReq) {
        thirdSceneInfoPageReq.setPage((int) thirdSceneInfoPageReq.getNum());
        return digitalCertificateRemote.traderComplianceSceneList(thirdSceneInfoPageReq);
    }

    private final BaseCapabilityManager baseCapabilityManager;

    @Operation(summary = "合规场景列表-场景详情")
    @GetMapping({"/scene-info"})
    public CommonResult<SceneInfoResp> traderSceneInfo(@RequestParam String sceneId) {
        if (baseCapabilityManager.platformEnable(BaseCapabilityType.DATA_INVOICE))
            return digitalCertificateRemote.traderSceneInfo(sceneId);
        else
            return CommonResult.SUCCESS(null);
    }


    @Operation(summary = "当前用户可用数据资产")
    @PostMapping({"/asset/list"})
    public PageResult<DataAssetResp> dataAssetApproveList(@RequestBody DataAssetQuery query) {
        return deliveryService.dataAssetApproveList(query);
    }


    @Operation(summary = "交付场景新增")
    @OpLog(message = "创建交付场景")
    @PostMapping({"/scene/save"})
    public CommonResult<Boolean> deliverySceneAdd(@RequestBody SceneSaveReq sceneSaveReq) {
        OPLogContext.putOpType(InternalOpType.CREATE_SCENE);
        deliveryService.deliverySceneAdd(sceneSaveReq);
        return CommonResult.SUCCESS(Boolean.TRUE);
    }


    @Operation(summary = "场景交付列表")
    @PostMapping({"/scene/list"})
    public PageResult<SceneListResp> deliverySceneList(@RequestBody SceneListReq sceneListReq) {
        return deliveryService.deliverySceneList(sceneListReq);
    }


    @Operation(summary = "场景交付详情")
    @GetMapping({"/scene/{id}"})
    public CommonResult<SceneDetailResp> detail(@PathVariable String id) {
        return CommonResult.SUCCESS(deliveryService.detail(id));
    }


    @PostMapping("/scene/asset")
    @Operation(summary = "场景交付关联资产——分页")
    public SuccessResponse<List<DataAssetResp>> sceneAssetPage(@Valid @RequestBody SceneListReq sceneListReq) {
        String sceneId = sceneListReq.getSceneId();
        AssetType type = sceneListReq.getType();
        Assert.isTrue(StringUtils.isNotBlank(sceneId), "场景id不能为空");
        Assert.isTrue(type != null, "资产类型不能为空");

        SceneDetailResp resp = deliveryService.detail(sceneId);
        List<DataAssetResp> assetRespList = resp.getDataAssetSceneRefList();
        if (assetRespList == null) {
            assetRespList = Collections.emptyList();
        }
        List<DataAssetResp> respList = assetRespList.stream().filter(dataAssetResp -> dataAssetResp.getType() == type).toList();
        if (CollectionUtil.isEmpty(assetRespList) || CollectionUtil.isEmpty(respList)) {
            return SuccessResponse.success(Collections.EMPTY_LIST).page(new Page(sceneListReq.getNum(), sceneListReq.getSize())).total(0L).build();
        } else {
            int num = Math.toIntExact(sceneListReq.getNum());
            int size = Math.toIntExact(sceneListReq.getSize());
            PageUtils.PageResult<DataAssetResp> result = PageUtils.page(respList, num, size);
            return SuccessResponse.success(result.getRecords())
                    .page(new Page(num, size)).total((long) respList.size()).build();
        }
    }

    @Operation(summary = "场景交付详情关联存证记录")
    @PostMapping({"/scene/certificateRecord/{id}"})
    public PageResult<DeliveryListResponse> certificateRecord(@PathVariable String id, @RequestBody Page page) {
        if (!baseCapabilityManager.platformEnable(BaseCapabilityType.DATA_INVOICE))
            return new PageResult<>(new ArrayList<>(), 0);
        SceneDetailResp detail = deliveryService.detail(id);

        // ID 使用的是 场景ID+资产ID
        List<String> thirdDeliveryIds = detail.getDataAssetSceneRefList().stream().map(dataAssetSceneRef ->
                StrUtil.join(":", id, dataAssetSceneRef.getAssetId())).collect(Collectors.toList());

        DeliveryListRequest deliveryListRequest = DeliveryListRequest.builder()
                .thirdDeliveryIds(thirdDeliveryIds).page(page.getNum()).size(page.getSize()).build();

        PageResult<DeliveryListResponse> result = digitalCertificateRemote.deliveryList(deliveryListRequest);
        List<DeliveryListResponse> resultData = result.getData().stream().peek(x -> {
            try {
                DataAssetListRequest dataAssetListRequest = new DataAssetListRequest();
                dataAssetListRequest.setDataAssetIds(List.of(x.getThirdBusinessId().split(":")[1]));
                CommonResult<List<DataAssetDTO>> dataAssets = dataHubRemoteService.dataAssets(dataAssetListRequest);
                Assert.isTrue(dataAssets.isSuccess(), "获取资产信息异常");
                x.setDataAssetName(dataAssets.getData().getFirst().getAssetName());
                x.setType(AssetType.valueOf(dataAssets.getData().getFirst().getType()));
            } catch (Exception ignore) {
                x.setDataAssetName("找不到数据资产");
            }
        }).collect(Collectors.toList());
        return new PageResult<>(resultData, result.getTotal());
    }


    @Operation(summary = "文件下载")
    @OpLog(message = "创建交付场景")
    @PostMapping({"/download/success"})
    public CommonResult<Boolean> recordDownload() {
        OPLogContext.putOpType(InternalOpType.FILE_DOWNLOAD_INVOKE);
        return CommonResult.SUCCESS(Boolean.TRUE);
    }

    @PostMapping("pre-check")
    @Operation(summary = "交付场景新增前的检查")
    public CommonResult<Boolean> preCheck(@RequestBody PreCheckRequest request) {
        return CommonResult.SUCCESS(deliveryService.contractPreCheck(request));
    }

    private final TeeRemote teeRemote;

    @GetMapping("tee/systemConfig/info")
    public CommonResult<SystemConfigureInfo> teeSystemConfigInfo() {
        if (baseCapabilityManager.platformEnable(BaseCapabilityType.TEE)) {
            return teeRemote.systemConfigInfo();
        }
        return CommonResult.SUCCESS(new SystemConfigureInfo());
    }


    @GetMapping("mpc/baseCapabilityConfig/check/{id}")
    public CommonResult<Void> checkBaseCapabilityConfig(@PathVariable String id) {
        deliveryService.checkBaseCapabilityConfig(id);
        return CommonResult.SUCCESS();
    }

}
