package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderRecordsReq implements Serializable {

    @Schema(description = "连接器ID（买家或卖家）")
    String routerId;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "交付方式——订单自带的交付")
    DeliveryMode deliveryMode;

    @Schema(description = "交付方式——国标筛选 01、02、03")
    String deliveryMethod;

    @Schema(description = "支持的交付使用场景")
    DeliveryMode deliverySceneMode;

    @Schema(description = "获益人方企业名称")
    String beneficiaryEnterpriseName;

    @Schema(description = "状态")
    String status;

    @Schema(description = "非以下状态")
    List<String> notStatus;

    @Schema(description = "获益人用户ID")
    String beneficiaryId;

    @Schema(description = "审批人用户ID")
    String approverId;

    Long page;
    Long offset;
    Long size;
}
