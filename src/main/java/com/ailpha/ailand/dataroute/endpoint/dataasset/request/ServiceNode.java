package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceNode {
    @Schema(description = "业务节点id")
    String serviceNodeId;

    @Schema(description = "业务节点名称")
    String serviceNodeName;

    @Schema(description = "业务节点位置")
    String serviceNodeLocation;

    @Builder.Default
    @Schema(description = "上架状态")
    PushStatus pushStatus = PushStatus.OFFLINE;
}
