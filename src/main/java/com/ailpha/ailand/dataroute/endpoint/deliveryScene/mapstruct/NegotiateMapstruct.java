package com.ailpha.ailand.dataroute.endpoint.deliveryScene.mapstruct;

import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.NegotiateTransfer;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Slf4j
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class NegotiateMapstruct {

    @Mapping(target = "transactionExecutionStrategy", ignore = true)
    @Mapping(target = "serviceNodeId", source = "nodeId")
    public abstract NegotiateTransfer negotiateDataTransferTo(NegotiateDataTransferDTO transferDTO);

}
