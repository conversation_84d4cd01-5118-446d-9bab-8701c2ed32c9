package com.ailpha.ailand.dataroute.endpoint.upgrade.repository;

import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.PackageInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/6/5
 */
public interface PackageInfoRepository extends JpaRepository<PackageInfo, String>, QuerydslPredicateExecutor<PackageInfo> {

    List<PackageInfo> findAllByVersion(String version);
}
