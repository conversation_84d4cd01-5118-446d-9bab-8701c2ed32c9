package com.ailpha.ailand.dataroute.endpoint.plugin.strategy;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yuwenping
 * @date: 2025/5/9 17:13
 * @Description:
 */
public class BlockchainStrategyFactory {
    private static final Map<BlockchainPluginTypeEnum, BlockchainProcessingStrategy> STRATEGIES = new HashMap<>();

    static {
        STRATEGIES.put(BlockchainPluginTypeEnum.API, new APIBlockchainStrategy());
        STRATEGIES.put(BlockchainPluginTypeEnum.JAR, new JarBlockchainStrategy());
        STRATEGIES.put(BlockchainPluginTypeEnum.GROOVY, new GroovyBlockchainStrategy());
    }

    public static BlockchainProcessingStrategy getStrategy(BlockchainPluginTypeEnum type) {
        return STRATEGIES.getOrDefault(type, STRATEGIES.get(BlockchainPluginTypeEnum.API));
    }
}
