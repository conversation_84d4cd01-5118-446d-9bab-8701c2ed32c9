package com.ailpha.ailand.dataroute.endpoint.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OderRecordExtend implements Serializable {

    @Schema(description = "调用价格", allowableValues = {"面议", "自定义"}, example = "自定义")
    String callPrice;

    @Schema(description = "下拉值：包月、包年、单次价格、总价")
    String cycleWay;

    @Schema(description = "价格")
    Double price;

    @Schema(description = "获益方统一社会信用代码")
    String beneficiaryCreditCode;

    @Schema(description = "审批方统一社会信用代码")
    String approverCreditCode;

    @Schema(description = "获益方企业ID")
    String beneficiaryCompanyId;

    @Schema(description = "审批方企业ID")
    String approverCompanyId;

    String dataProductPlatformId;
}
