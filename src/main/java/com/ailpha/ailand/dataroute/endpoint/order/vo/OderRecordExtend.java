package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OderRecordExtend implements Serializable {

    @Schema(description = "调用价格", allowableValues = {"面议", "自定义"}, example = "自定义")
    String callPrice;

    @Schema(description = "下拉值：包月、包年、单次价格、总价")
    String cycleWay;

    @Schema(description = "订单价格")
    String price;

    @Schema(description = "获益方统一社会信用代码")
    String beneficiaryCreditCode;

    @Schema(description = "审批方统一社会信用代码")
    String approverCreditCode;

    @Schema(description = "获益方企业ID")
    String beneficiaryCompanyId;

    @Schema(description = "审批方企业ID")
    String approverCompanyId;

    String dataProductPlatformId;

    // -------扩展字段--------
    String deliveryModes;
    String mpcPurposes;
    String teePurposes;

    String productionType;
    String dataType;
    String dataType1;
    Boolean isLLM;

    String summary;

    String providerOrg;

    @Schema(description = "国标交付 01，02，03")
    String deliveryModeStandards;

    @Schema(description = "数据产品价格")
    String productPrice;

    /**
     * 合同编号
     */
    private String tradingStrategyCode;

    /**
     * 合同名称—没有的话可规则生成
     */
    private String tradingStrategyName;

    /**
     * 合同描述—没有的话可传空
     */
    private String tradingStrategyContent;

    /**
     * 合同生效时间（13位时间戳）
     */
    private Long tradingStrategyTime;

    /**
     * 业务节点id
     */
    String serviceNodeId;

    String serviceNodeUrl;

    String serviceNodeName;

    String transferMode;

    String deliveryInfo;
}
