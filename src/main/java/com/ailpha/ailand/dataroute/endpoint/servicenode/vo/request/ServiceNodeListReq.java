package com.ailpha.ailand.dataroute.endpoint.servicenode.vo.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/7/29
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeListReq extends Page implements Serializable {

    @Schema(description = "业务节点登记名称（模糊查询）")
    private String entryName;

    @Schema(description = "业务节点标识编码（精确匹配）")
    private String serviceNodeId;
}
