package com.ailpha.ailand.dataroute.endpoint.common.config.session;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpSession;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description: 自定义session上下文: 暂时：仅用于管理员重置密码后销毁指定用户session
 * @date 2023/6/26 14:45
 */
@Slf4j
public class CustomSessionContext {

    private static CustomSessionContext instance;

    public static ConcurrentHashMap<String, HttpSession> sessionMap = new ConcurrentHashMap<>();

    public static CustomSessionContext getInstance() {
        if (instance == null) {
            instance = new CustomSessionContext();
        }
        return instance;
    }

    public synchronized void addSession(HttpSession session) {
        if (session != null) {
            log.debug("session 创建: {}", session.getId());
            sessionMap.put(session.getId(), session);
        }
    }

    public synchronized void delSession(HttpSession session) {
        if (session != null) {
            log.debug("session 销毁: {}", session.getId());
            sessionMap.remove(session.getId());
        }
    }

    public synchronized HttpSession getSession(String sessionID) {
        if (sessionID == null) {
            return null;
        }
        return sessionMap.get(sessionID);
    }

}
