package com.ailpha.ailand.dataroute.endpoint.third.constants;

/**
 * @author: liufei
 * @Date: 2020-04-23
 * 项目中使用到的一些常量
 */
public class OperatorConstants {

    /**
     * hdfs存储加密结果集的目录
     */
    public static final String HDFS_ENCRYPT_RESULT_PATH_PREFIX = "/minio/result-data/encrypt/";

    /**
     * hdfs存储明文结果集的目录
     */
    public static final String HDFS_DECRYPT_RESULT_PATH_PREFIX = "/minio/result-data/compute/";

    /**
     * hdfs存储明文结果集采样数据的目录
     */
    public static final String HDFS_RESULT_SAMPLING_PATH_PREFIX = "/minio/result-data/sampling/";

    /**
     * hdfs存储结果集加密密码的目录
     */
    public static final String HDFS_RESULT_ENCRYPT_PWD_PATH_PREFIX = "/minio/result-data/encryptPwd/";

    /**
     * 存储DMZ区MINIO上传的密文文件的目录
     */
    public static final String MINIO_ENCRYPT_DATA_PREFIX = "/minio/source-data/encrypt/";

    /**
     * 存储DMZ区已经合约授权，明文解密产生的数据
     */
    public static final String DECRYPT_DATASET_PREFIX = "/minio/source-data/compute/";

    /**
     * 存储调试数据
     */
    public static final String DEBUG_DATA_FILE_PREFIX = "/minio/debug-data/decrypt/";


    public static final String RESULT_BUCKET_SUFFIX = "-result";

    public static final String HDFS_REDUCE_TEMP_DIR = "/minio/tmp-result/";

    public static final String TAR_FILENAME_SUFFIX = ".tar.gz";

    /**
     * API接口导入批量文件ID
     */
    public static final String API_IMPORT_BATCH_PARAMS_FILE_ID = "API_IMPORT_BATCH_PARAMS_FILE_PATH";

    public static final String BACKUP_ENCRYPT_DATA_PATH_PREFIX = "/minio/backup-data/encrypt/";
}
