package com.ailpha.ailand.dataroute.endpoint.hengnao;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.hengnao.interceptor.HengNaoSignInterceptor;
import com.ailpha.ailand.dataroute.endpoint.hengnao.request.AgentTaskMessagesRequest;
import com.ailpha.ailand.dataroute.endpoint.hengnao.request.HengNaoAsyncTaskSubmitRequest;
import com.ailpha.ailand.dataroute.endpoint.hengnao.response.AgentMessages;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import okhttp3.MultipartBody;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "${ailand.heng-nao.base-url}", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = HengNaoSignInterceptor.class)
public interface HengNaoRemoteService {

    /**
     * 异步任务提交接口
     */
    @POST("/open/api/v2/agent/task/submit")
    CommonResult<String> submitAsyncTask(@Body HengNaoAsyncTaskSubmitRequest requestBody);

    /**
     * 上传文件接口
     */
    @Multipart
    @POST("/open/api/v2/agent/file/upload")
    CommonResult<String> uploadFile(@Part MultipartBody.Part file);

    /**
     * 异步任务状态查询接口
     */
    @POST("/open/api/v2/agent/task/status")
    CommonResult<Integer> queryAsyncTaskStatus(@Body QueryAsyncTaskStatusRequest request);

    /**
     * 异步任务执行结果查询接口
     */
    @POST("/open/api/v2/agent/task/messages")
    CommonResult<AgentMessages> queryAsyncTaskMessages(@Body AgentTaskMessagesRequest request);

}
