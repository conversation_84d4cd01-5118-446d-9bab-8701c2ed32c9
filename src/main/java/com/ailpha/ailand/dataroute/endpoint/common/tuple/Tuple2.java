package com.ailpha.ailand.dataroute.endpoint.common.tuple;

public final class Tuple2<T, D> {

    public T first;
    public D second;

    public Tuple2() {
    }

    public Tuple2(T first, D second) {
        this.first = first;
        this.second = second;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Tuple2<?, ?> tuple2 = (Tuple2<?, ?>) o;

        if (!first.equals(tuple2.first)) return false;
        return second.equals(tuple2.second);
    }

    @Override
    public int hashCode() {
        int result = first.hashCode();
        result = 31 * result + second.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "Tuple2{" +
                "first=" + first +
                ", second=" + second +
                '}';
    }
}
