package com.ailpha.ailand.dataroute.endpoint.third.constants;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DbType {
    ORACLE(1),
    MYSQL(2),
    SQLSERVER(3),
    SYBASE(4),
    DB2(5),
    INFORMIX(6),
    OSCAR(7),
    DM(8),
    <PERSON>CH<PERSON>(9),
    POSTGRESQL(10),
    TERADATA(12),
    KINGBASE(13),
    GBASE(14),
    MARIADB(15),
    HTTP(16),
    TELNET(17),
    FTP(18),
    HANA(22),
    MONGODB(23),
    HBASEPROTOBUF(24),
    HBASETHRIFT(25),
    HIV<PERSON>(26),
    REDIS(27),
    ELASTICSEARCH(28),
    CASSANDRA(29),
    HDFS(30),
    IMPALA(31),
    GAUSSDB(32),
    LIBRA(33),
    GRAPHBASE(34),
    GREENPLUM(35),
    SPARKSQLTHRIFT(36),
    <PERSON>AR<PERSON><PERSON>RE<PERSON><PERSON><PERSON>L(37),
    TID<PERSON>(46),
    <PERSON><PERSON><PERSON><PERSON>(48),
    <PERSON><PERSON><PERSON>G<PERSON>(58),
    CLICKHOUSEJDBC(61),
    GOLDENDB(66),
    PRESTO(67),
    UXDB(70),
    TDH(72),
    TELEDB_MYSQL(73),
    TELEDB_POSTGRESQL(74),
    ODPS(75),
    DORIS(76),
    TRINO(77),
    XUGU(78);

    private final int code;

    DbType(int code) {
        this.code = code;
    }

    @JsonValue
    public String getValue() {
        return String.valueOf(this.code);
    }
}
