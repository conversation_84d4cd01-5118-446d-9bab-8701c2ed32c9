package com.ailpha.ailand.dataroute.endpoint.demand;

import com.ailpha.ailand.dataroute.endpoint.demand.request.LatestDemandNegotiationDTO;
import com.ailpha.ailand.dataroute.endpoint.home.HomeResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DemandStatisticDTO {
    @Schema(description = "发布需求")
    List<HomeResponse.DemandNegotiationTop10DTO> demandTop10;
    @Schema(description = "交易磋商")
    List<LatestDemandNegotiationDTO> latestDemandNegotiations;
    @Schema(description = "发布需求数")
    Long demandCount;
    @Schema(description = "需求被响应")
    Long demandNegotiationCount;
}
