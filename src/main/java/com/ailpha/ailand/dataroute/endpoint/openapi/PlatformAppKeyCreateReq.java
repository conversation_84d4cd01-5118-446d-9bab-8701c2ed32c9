package com.ailpha.ailand.dataroute.endpoint.openapi;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PlatformAppKeyCreateReq implements Serializable {

    @NotBlank(message = "平台名称不能为空")
    @Schema(description = "平台名称", required = true)
    String platformName;

    @NotNull(message = "平台类型不能为空")
    @Schema(description = "平台类型：DATA_ROUTE_HUB（连接器枢纽）DATA_ROUTE_ENDPOINT（连接器终端）OTHER（其他）", required = true)
    PlatformType platformType;
}
