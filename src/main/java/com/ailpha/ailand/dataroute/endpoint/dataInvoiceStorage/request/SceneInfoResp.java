package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneInfoResp {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "场景类别")
    private String type;

    @Schema(description = "场景名称")
    private String name;

    @Schema(description = "买方行业")
    private String buyerIndustry;

    @Schema(description = "卖方行业")
    private String sellerIndustry;

    @Schema(description = "买方性质")
    private String buyerNature;

    @Schema(description = "卖方性质")
    private String sellerNature;

    @JsonFormat(
            pattern = "yyyy/MM/dd",
            timezone = "GMT+8"
    )
    @DateTimeFormat(
            pattern = "yyyy/MM/dd"
    )
    @Schema(description = "失效时间")
    private Date expireDate;

    @Schema(description = "交付方式")
    private String deliveryMode;

    @Schema(description = "可交易权限")
    private String tradableAuthority;

    @Schema(description = "场景描述")
    private String description;

    @Schema(description = "数据字段")
    private String dataSchema;

    @Schema(description = "数据字段文件路径")
    private String dataSchemaFilePath;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description = "场景创建时间")
    private Date sceneCreateTime;

    @Schema(description = "场景审核记录")
    private List<SceneAuditRecordResp> sceneAuditRecords;

    @Schema(description = "地域信息")
    private List<SceneRegionResp> regions;

}
