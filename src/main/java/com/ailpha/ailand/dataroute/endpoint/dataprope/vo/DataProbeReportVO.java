package com.ailpha.ailand.dataroute.endpoint.dataprope.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/2/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProbeReportVO implements Serializable {

    @Schema(description = "资产概况")
    AssetProfile assetProfile;

    @Schema(description = "资产/梳理新增趋势")
    List<AssetCombingTrend> assetCombingTrends;

    @Schema(description = "分类分级梳理统计")
    ClassificationGrading classificationGrading;

    /**
     * 资产概况
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class AssetProfile implements Serializable {
        @Schema(description = "数据源（个）")
        Integer datasource;
        @Schema(description = "数据库（个）")
        Integer database;
        @Schema(description = "schema（个）")
        Integer schema;
        @Schema(description = "数据库表（张）")
        Integer table;
        @Schema(description = "字段（个）")
        Integer column;
        @Schema(description = "未梳理字段（个）")
        Integer uncombedColumn;
        @Schema(description = "梳理率（%）")
        Double cardingRate;
        @Schema(description = "数据量（万行）")
        Double dataVolume;
    }

    /**
     * 资产/梳理新增趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class AssetCombingTrend implements Serializable {
        @Schema(description = "月份")
        Integer month;
        @Schema(description = "新增字段数")
        Integer newColumn;
        @Schema(description = "新增梳理字段数")
        Integer newCombedColumn;
    }

    /**
     * 分类分级梳理统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ClassificationGrading implements Serializable {
        @Schema(description = "已梳理")
        Integer combed;
        @Schema(description = "未梳理")
        Integer uncombed;
    }
}
