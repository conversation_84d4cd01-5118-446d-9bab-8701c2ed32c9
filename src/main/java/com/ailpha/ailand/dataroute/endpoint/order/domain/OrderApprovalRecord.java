package com.ailpha.ailand.dataroute.endpoint.order.domain;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/11/16
 */
@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_order_approval_record")
@AllArgsConstructor
public class OrderApprovalRecord implements Serializable {

    /**
     * 订单编号
     */
    @Id
    @Schema(description = "订单编号")
    @Length(max = 32, message = "订单编号长度不能超过32")
    @Column(name = "id")
    private String id;


    /**
     * 资产类型
     */
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    AssetType type;

    /**
     * 资产ID
     */
    @Schema(description = "资源ID | 产品id")
    @Length(max = 32, message = "资产ID长度不能超过32")
    @Column(name = "asset_id")
    private String assetId;

    /**
     * 资产名称
     */
    @Schema(description = "资产名称")
    @Length(max = 100, message = "资产名称长度不能超过100")
    @Column(name = "asset_name")
    private String assetName;

    /**
     * 交付方式
     */
    @Schema(description = "交付方式")
    @Length(max = 100, message = "交付方式长度不能超过100")
    @Column(name = "delivery_mode")
    private String deliveryMode;

    /**
     * 获益人用户ID
     */
    @Schema(description = "获益人用户ID")
    @Length(max = 32, message = "获益人用户ID长度不能超过32")
    @Column(name = "beneficiary_id")
    private String beneficiaryId;

    @Schema(description = "获益人用户名")
    @Length(max = 100, message = "获益人用户名长度不能超过100")
    @Column(name = "beneficiary_username")
    private String beneficiaryUsername;

    /**
     * 获益方数由器ID
     */
    @Schema(description = "获益方数由器ID")
    @Length(max = 100, message = "获益方数由器ID长度不能超过100")
    @Column(name = "beneficiary_router_id")
    private String beneficiaryRouterId;

    /**
     * 获益方企业名称
     */
    @Schema(description = "获益方企业名称")
    @Length(max = 100, message = "获益方企业名称长度不能超过100")
    @Column(name = "beneficiary_enterprise_name")
    private String beneficiaryEnterpriseName;

    /**
     * 获益人方企业性质
     */
    @Schema(description = "获益方企业性质")
    @Length(max = 100, message = "获益方企业性质长度不能超过100")
    @Column(name = "beneficiary_enterprise_property")
    private String beneficiaryEnterpriseProperty;

    /**
     * 审批人用户ID
     */
    @Schema(description = "审批人用户ID")
    @Length(max = 32, message = "审批人用户ID长度不能超过32")
    @Column(name = "approver_id")
    private String approverId;

    /**
     * 审批人用户名
     */
    @Schema(description = "审批人用户名")
    @Length(max = 100, message = "审批人用户名长度不能超过100")
    @Column(name = "approver_username")
    private String approverUsername;

    /**
     * 获益方数由器ID
     */
    @Schema(description = "审批方数由器ID")
    @Length(max = 100, message = "审批方数由器ID长度不能超过100")
    @Column(name = "approver_router_id")
    private String approverRouterId;

    /**
     * 获益方企业名称
     */
    @Schema(description = "审批方企业名称")
    @Length(max = 100, message = "审批方企业名称长度不能超过100")
    @Column(name = "approver_enterprise_name")
    private String approverEnterpriseName;

    /**
     * 计费方式：预付费、后付费
     */
    @Schema(description = "计费方式：预付费、后付费")
    @Length(max = 100, message = "计费方式：预付费、后付费长度不能超过100")
    @Column(name = "charging_way")
    private String chargingWay;

    /**
     * 计量方式：按次、按时间
     */
    @Schema(description = "计量方式：按次、按时间")
    @Length(max = 100, message = "计量方式：按次、按时间长度不能超过100")
    @Column(name = "metering_way")
    private String meteringWay;

    /**
     * 使用次数上限
     */
    @Schema(description = "使用次数上限")
    @Column(name = "allowance")
    private BigInteger allowance;

    /**
     * 成功使用次数
     */
    @Schema(description = "成功使用次数")
    @Column(name = "successful_usage")
    private BigInteger successfulUsage;

    /**
     * 失败使用次数
     */
    @Schema(description = "失败使用次数")
    @Column(name = "unsuccessful_usage")
    private BigInteger unsuccessfulUsage;

    /**
     * 状态
     */
    @Schema(description = "状态")
    @Length(max = 100, message = "状态长度不能超过100")
    @Column(name = "status")
    private String status;

    /**
     * 扩展字段json
     */
    @Schema(description = "扩展字段json")
    @Column(name = "extend")
    String extend;

    /**
     * 有效期
     */
    @Schema(description = "有效期")
    @Column(name = "expire_date")
//    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private Date expireDate;

    /**
     * 审批通过时间
     */
    @Schema(description = "审批通过时间")
    @Column(name = "approve_time")
//    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否变更状态
     */
    @Transient
    private boolean changeStatus = false;

    public OderRecordExtend getOderRecordExtend() {
        if (StringUtils.isNotBlank(extend)) {
            return JSONUtil.toBean(extend, OderRecordExtend.class);
        } else {
            return null;
        }
    }

}