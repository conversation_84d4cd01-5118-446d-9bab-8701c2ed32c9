package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AgentTaskType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.HengNaoAgentTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.QHengNaoAgentTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.HengNaoAgentTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.hengnao.HengNaoAdapterService;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class HengNaoAgentTaskService {
    private final AiLandProperties aiLandProperties;
    private final HengNaoAgentTaskRepository hengNaoAgentTaskRepository;
    private final HengNaoAdapterService hengNaoAdapterService;
    private final JPAQueryFactory queryFactory;

    @Transactional(rollbackFor = Exception.class)
    public String createDataExploreTask(MultipartFile debugDateFile, MultipartFile dataSourcePdfFile, String agentTaskType) {
        String agentId;
        switch (AgentTaskType.valueOf(agentTaskType)) {
            case DATA_PRODUCT_EXPLORE -> agentId = aiLandProperties.getHengNao().getDataProductExploreAgentId();
            case DATA_RESOURCE_EXPLORE -> agentId = aiLandProperties.getHengNao().getDataResourceExploreAgentId();
            default -> throw new RestfulApiException(String.format("非法的参数：[taskType=%s]", agentTaskType));
        }
        // 1. 上传文件到恒脑
        String fileId = hengNaoAdapterService.uploadFile(debugDateFile);
        // 2. 提交异步任务
        DataExploreInputs inputs = new DataExploreInputs();
        inputs.setFile(fileId);
        if (ObjectUtil.isNotNull(dataSourcePdfFile) && StringUtils.equals(agentTaskType, AgentTaskType.DATA_PRODUCT_EXPLORE.name())) {
            String dataSourcePdfFileId = hengNaoAdapterService.uploadFile(dataSourcePdfFile);
            inputs.setProdSourcePdf(dataSourcePdfFileId);
        }
        MultipartFile multipartFile;
        try {
            multipartFile = new MockMultipartFile("industry.json", ResourceUtil.getStream("conf/industry.json"));
        } catch (IOException e) {
            throw new RestfulApiException("上传行业文件异常：" + e.getMessage());
        }
        String industryFileId = hengNaoAdapterService.uploadFile(multipartFile);
        inputs.setIndustryFile(industryFileId);
        inputs.setReturnLogs(log.isDebugEnabled());
        DataExploreInputs.EnterpriseInfo enterpriseInfo = new DataExploreInputs.EnterpriseInfo();
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        enterpriseInfo.setEnterpriseName(company.getOrganizationName());
        enterpriseInfo.setEnterpriseAddress(company.getRegistrationAddress());
        enterpriseInfo.setIndustryCategory(company.getIndustryType());
        enterpriseInfo.setBusinessScope(company.getBusinessScope());
        inputs.setEnterpriseInfo(enterpriseInfo);
        String taskId = hengNaoAdapterService.submitAsyncTask(agentId, BeanUtil.beanToMap(inputs));
        // 3. 持久化任务记录
        HengNaoAgentTask task = new HengNaoAgentTask();
        task.setTaskId(taskId);
        task.setTaskType(AgentTaskType.valueOf(agentTaskType));
        task.setUserId(LoginContextHolder.currentUser().getId());
        task.setFileName(debugDateFile.getOriginalFilename());
        HengNaoAgentTask.Ext ext = new HengNaoAgentTask.Ext();
        if (StringUtils.equals(agentTaskType, AgentTaskType.DATA_PRODUCT_EXPLORE.name())) {

            ext.setDataSourcePdfFileName(dataSourcePdfFile.getOriginalFilename());
        }
        task.setExt(ext);
        task.setStatus(1); // 执行中
        hengNaoAgentTaskRepository.saveAndFlush(task);
        // todo 缺少事务补偿机制：加入这里失败了应该取消智能体任务
        return taskId;
    }

    public SuccessResponse<List<HengNaoAgentTaskDTO>> listTasks(AgentTaskPageRequest request) {
        QHengNaoAgentTask hengNaoAgentTask = QHengNaoAgentTask.hengNaoAgentTask;
        BooleanBuilder queryBuilder = new BooleanBuilder();
        queryBuilder.and(hengNaoAgentTask.userId.eq(LoginContextHolder.currentUser().getId()));
        // 过滤未删除的记录
        queryBuilder.and(hengNaoAgentTask.ext.deleted.isNull().or(hengNaoAgentTask.ext.deleted.eq(false)));
        if (ObjectUtil.isNotNull(request.getTaskType()))
            queryBuilder.and(hengNaoAgentTask.taskType.eq(request.getTaskType()));
        if (StringUtils.isNotEmpty(request.getTaskId())) {
            queryBuilder.and(hengNaoAgentTask.taskId.eq(request.getTaskId()));
        }
        Long count = queryFactory.select(hengNaoAgentTask.id.countDistinct()).from(hengNaoAgentTask).where(queryBuilder).fetchOne();
        List<HengNaoAgentTask> agentTasks = queryFactory.selectFrom(hengNaoAgentTask)
                .where(queryBuilder)
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .orderBy(hengNaoAgentTask.createdAt.desc())
                .fetch();
        return SuccessResponse.success(agentTasks.stream().map(this::toResponse).toList()).total(count).build();
    }

    public HengNaoAgentTaskDTO getTaskById(Long id) {
        QHengNaoAgentTask hengNaoAgentTask = QHengNaoAgentTask.hengNaoAgentTask;
        HengNaoAgentTask hengNaoAgentTask1 = queryFactory.selectFrom(hengNaoAgentTask)
                .where(hengNaoAgentTask.id.eq(id)
                        .and(hengNaoAgentTask.userId.eq(LoginContextHolder.currentUser().getId()))
                        .and(hengNaoAgentTask.ext.deleted.isNull().or(hengNaoAgentTask.ext.deleted.eq(false))))
                .fetchOne();
        Assert.isTrue(hengNaoAgentTask1 != null, "数据探查任务不存在");
        return toResponse(hengNaoAgentTask1);
    }

    public HengNaoAgentTaskDTO toResponse(HengNaoAgentTask task) {
        HengNaoAgentTaskDTO resp = new HengNaoAgentTaskDTO();
        resp.setTaskId(task.getTaskId());
        resp.setFilename(task.getFileName());
        if (task.getTaskType().equals(AgentTaskType.DATA_PRODUCT_EXPLORE)) {
            resp.setPdfName(task.getExt().getDataSourcePdfFileName());
        }
        resp.setStatus(task.getStatus());
        JSONObject content = JSONUtil.parseObj(JSONUtil.parseObj(task.getResultJson()).getByPath("messages[0].content"));

        if (StringUtils.equals(content.getStr("status"), "error")) {
            content.remove("metadata");
            content.remove("logs");
            resp.setResult(content);
            return resp;
        }
        JSONObject entries = new JSONObject();
        switch (task.getTaskType()) {
            case DATA_RESOURCE_EXPLORE -> {
                DataResourceExploreResult metadata = content.getByPath("metadata", DataResourceExploreResult.class);
                if (ObjectUtil.isNull(metadata))
                    return resp;
                List<DataResourceExploreResult.DataSchema> fields = content.getBeanList("fields", DataResourceExploreResult.DataSchema.class);
                entries.set("industryCode", metadata.getIndustryCode());
                entries.set("industryName", metadata.getIndustryName());
                metadata.setPersonalInformation(StringUtils.equals(metadata.getPersonalInformation(), "true") ? "1" : "0");
                metadata.setIndustry1(entries.toString());
                metadata.setDataSchema(fields);
                resp.setResult(metadata);
            }
            case DATA_PRODUCT_EXPLORE -> {
                DataProductExploreResult metadata = content.getByPath("metadata", DataProductExploreResult.class);
                if (ObjectUtil.isNull(metadata))
                    return resp;
                entries.set("industryCode", metadata.getIndustryCode());
                entries.set("industryName", metadata.getIndustryName());
                metadata.setIndustry1(entries.toString());
                metadata.setDataCoverageTimeStart(StringUtils.substringBefore(metadata.getTimeRange(), " 至 "));
                metadata.setDataCoverageTimeEnd(StringUtils.substringAfter(metadata.getTimeRange(), " 至 "));
                metadata.setPersonalInformation(StringUtils.equals(metadata.getPersonalInformation(), "true") ? "1" : "0");
                metadata.setAuthorize(StringUtils.equals(metadata.getAuthorize(), "true") ? "1" : "0");
                metadata.setIsSecondaryProcessed(StringUtils.equals(metadata.getIsSecondaryProcessed(), "true") ? "1" : "0");
                resp.setResult(metadata);
            }

        }
        return resp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        QHengNaoAgentTask hengNaoAgentTask = QHengNaoAgentTask.hengNaoAgentTask;
        HengNaoAgentTask task = queryFactory.selectFrom(hengNaoAgentTask)
                .where(hengNaoAgentTask.taskId.eq(taskId)
                        .and(hengNaoAgentTask.userId.eq(LoginContextHolder.currentUser().getId())))
                .fetchOne();

        if (task == null) {
            throw new RestfulApiException("数据探查任务不存在");
        }

        if (task.getStatus() == 1) {
            throw new RestfulApiException("任务正在执行中，无法删除");
        }

        if (task.getExt() == null) {
            task.setExt(new HengNaoAgentTask.Ext());
        }
        task.getExt().setDeleted(true);
        hengNaoAgentTaskRepository.saveAndFlush(task);
    }

    public static void main(String[] args) {
        String s = FileUtil.readUtf8String("D:\\test.txt");
        JSONObject content = JSONUtil.parseObj(JSONUtil.parseObj(s).getByPath("messages[0].content"));
        System.out.println(content.toStringPretty());
    }
}
