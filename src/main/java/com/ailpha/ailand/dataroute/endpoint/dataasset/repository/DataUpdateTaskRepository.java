package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTaskLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 数据接入任务数据访问层
 */
public interface DataUpdateTaskRepository extends JpaRepository<DataUpdateTask, String>, JpaSpecificationExecutor<DataUpdateTask> {
    /**
     * 根据数据产品ID查询任务，一对一的关系，一个数据产品对应一个更新任务，一个更新任务对应多条更新任务记录
     *
     * @param dataProductId 数据产品ID
     * @return 任务
     */
    DataUpdateTask findByDataProductId(String dataProductId);

    /**
     * 根据更新方式查询任务列表
     * @param dataUpdateType
     * @return
     */
    List<DataUpdateTask> findByDataUpdateType(UpdateWay dataUpdateType);

    // 悲观读锁（共享锁，其他事务可读不可改）
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Query("SELECT u FROM DataUpdateTask u WHERE u.id = :id")
    DataUpdateTask findByIdWithPessimisticReadLock(@Param("id") String id);

    @Transactional
    @Modifying
    @Query("update DataUpdateTask dut set dut.taskLogNum = :taskLogNum where dut.id = :id")
    void updateTaskLogNum(@Param(value = "id") String id, @Param(value = "taskLogNum") Integer taskLogNum);

    @Transactional
    @Modifying
    @Query("update DataUpdateTask dut set dut.latestTaskLogId = :latestTaskLogId where dut.id = :id")
    void updateLatestTaskLogId(@Param(value = "id") String id, @Param(value = "latestTaskLogId") String latestTaskLogId);
}