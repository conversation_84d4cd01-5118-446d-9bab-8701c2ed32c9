package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogVO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.dbapp.rest.response.SuccessResponse;

import java.util.List;

/**
 * 数据接入任务服务层
 */
public interface DataUpdateTaskService {
    /**
     * 创建数据接入任务
     *
     * @param task 任务信息
     * @return 创建的任务
     */
    void createTask(DataUpdateTask task);

    /**
     * 更新数据接入任务
     *
     * @param task 任务信息
     * @return 更新后的任务
     */
    DataUpdateTask updateTask(DataUpdateTask task);

    /**
     * 删除数据接入任务
     *
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    DataUpdateTask getTask(String taskId);

    /**
     * 根据数据产品ID查询任务，一对一的关系，一个数据产品对应一个更新任务，一个更新任务对应多条更新任务记录
     *
     * @param dataProductId 数据产品ID
     * @return 任务信息
     */
    DataUpdateTask getTaskByDataProductId(String dataProductId);

    /**
     * 执行任务
     *
     * @param taskId 任务ID
     */
    void executeTask(String taskId);

    /**
     * 获取单条任务记录信息
     * @param taskLogId
     * @return
     */
    DataUpdateTaskLog getTaskLog(String taskLogId);

    /**
     * 获取任务执行历史记录
     *
     * @return 历史记录分页数据
     */
    SuccessResponse<List<DataUpdateTaskLogVO>> getTaskHistory(DataUpdateTaskLogPageRequest dataUpdateTaskLogPageRequest);

    /**
     * 更新任务信息
     * @param dataUpdateTask
     */
    void saveAndFlushDataUpdateTask(DataUpdateTask dataUpdateTask);

    /**
     * 更新任务记录信息
     * @param dataUpdateTaskLog
     */
    void saveAndFlushDataUpdateTaskLog(DataUpdateTaskLog dataUpdateTaskLog);

    /**
     * 检查最近的一次任务执行状态
     * true：表示任务执行结束,如果是第一次执行也为true
     * false：任务执行中
     * @param taskLogId
     * @return
     */
    Boolean checkLatestTaskLogStatus(String taskLogId);

    /**
     * 根据定时信息，构建 DataUpdateTaskBaseInfo
     * @param updateWay
     * @param updateFreq
     * @param selectDate
     * @param selectHour
     * @return
     */
    DataUpdateTaskBaseInfo buildDataUpdateBaseInfo(UpdateWay updateWay, SchedulerPeriodEnum updateFreq, Integer selectDate, Integer selectHour);

    /**
     * 根据起始时间和周期计算cron表达式
     *
     * @param dataUpdateBaseInfo
     * @return
     */
    String dataUpdateBaseInfo2CronExpression(DataUpdateTaskBaseInfo dataUpdateBaseInfo);

    /**
     * 获取所有的定时任务
     * @return
     */
    List<DataUpdateTask> getScheduleUpdateTasks();

    /**
     * grep任务日志，根据任务历史id
     * @param taskLogId
     * @return
     * @throws Exception
     */
    String grepTaskLog(String taskLogId);

    /**
     * 任务信息统计
     * @param logId
     * @param total
     */
    void taskLogStatistics(String logId, Integer total);

    /**
     * 根据taskId查询任务历史状态列表
     * @param taskId
     * @return
     */
    List<DataUpdateStatus> getDataUpdateTaskStatus(String taskId);

    /**
     * 手动触发一次定时任务
     * @param taskId
     */
    void triggerTask(String taskId);
}