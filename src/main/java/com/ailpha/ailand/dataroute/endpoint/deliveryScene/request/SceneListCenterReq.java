package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneListCenterReq {

    @Schema(description = "用户")
    private String createUser;

    @Schema(description = "ID")
    private String sceneId;

    @Schema(description = "交付方式")
    private String deliveryType;

    @Schema(description = "数字证书 —— 合规场景名称")
    private String digitalSceneName;

    private String sceneStatus;
}