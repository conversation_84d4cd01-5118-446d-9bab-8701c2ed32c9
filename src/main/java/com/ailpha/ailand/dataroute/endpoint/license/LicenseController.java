package com.ailpha.ailand.dataroute.endpoint.license;


import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.utils.CookieUtils;
import com.dbapp.licence.licsdkjava.bean.dto.ProductSnInfoDTO;
import com.dbapp.licence.licsdkjava.exceptions.LicException;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/8/23 10:27
 * @description
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("license")
@Tag(name = "许可证相关接口")
@PreAuthorize("hasAuthority('SUPER_ADMIN')")
public class LicenseController {

    private final DasLicenceService licenceService;

    @Value("${ailand.hotLine:400 6059 110}")
    private String hotLine;

    @Value("${ailand.version}")
    private String version;

    @Data
    public static class ProductSnOrMachineCodeVO {
        @Schema(description = "产品识别码(新许可证用)")
        String productSn;
        @Schema(description = "客户热线")
        String hotLine;
    }

    /**
     * 获取产品识别码 or 机器码
     *
     * @return
     */
    @GetMapping("productSn")
    @Operation(summary = "获取获取产品识别码")
    public SuccessResponse<ProductSnOrMachineCodeVO> getProductSn() {
        ProductSnOrMachineCodeVO productSnOrMachineCodeVO = new ProductSnOrMachineCodeVO();
        productSnOrMachineCodeVO.setHotLine(hotLine);
        ProductSnInfoDTO productSnInfo = licenceService.getProductSnInfo();
        productSnOrMachineCodeVO.setProductSn(productSnInfo.getProductSn());
        return SuccessResponse.success(productSnOrMachineCodeVO).build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class LicenseInfoVO {
        @Schema(description = "产品型号")
        String productModel;
        @Schema(description = "授权对象(客户名称)")
        String customName;
        @Schema(description = "许可类型")
        String licenseType;
        @Schema(description = "许可期限(月)")
        String licensePeriod;
        @Schema(description = "签发日期")
        @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
        Date issueDate;
        @Schema(description = "过期日期")
        @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
        Date expireDate;
        @Schema(description = "许可证版本")
        private String sdkVersion;
        @Schema(description = "软件版本")
        private String version;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class OldLicenseInfoVO {
        @Schema(description = "证书编号")
        String licenseNo;
        @Schema(description = "最终用户")
        String client;
        @Schema(description = "使用类型")
        String licenseType;
        @Schema(description = "维保期限(月)")
        String licensePeriod;
        @Schema(description = "签发日期")
        @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
        Date issueDate;
        @Schema(description = "过期日期")
        @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
        Date expireDate;
        @Schema(description = "软件版本")
        private String version;
    }

    @Data
    public static class LicenseInfoVOWrapper {
        LicenseInfoVO n3w;
        OldLicenseInfoVO old;
    }

    /**
     * 获取许可信息
     *
     * @return 许可证信息
     */
    @GetMapping("info")
    @Operation(summary = "获取许可证详情")
    public SuccessResponse<LicenseInfoVOWrapper> getLicenceInfo() {
        LicenseInfoVOWrapper licenseInfoVOWrapper = new LicenseInfoVOWrapper();
        LicenceInfoDTO licenceInfo = licenceService.getLicenceInfo();
        if (licenceInfo.getProductSn() != null) {
            licenseInfoVOWrapper.setN3w(LicenseInfoVO.builder()
                    .productModel(licenceInfo.getProductModel())
                    .customName(licenceInfo.getCustomName())
                    .licenseType(licenceInfo.getLicenseType() == 1 ? "正式许可" : (licenceInfo.getLicenseType() == 2 ? "测试许可" : "内部许可"))
                    .licensePeriod(
                            ChronoUnit.MONTHS.between(
                                    licenceInfo.getFirstActiveDate().toInstant()
                                            .atZone(ZoneId.systemDefault())
                                            .toLocalDate().withDayOfMonth(1),
                                    licenceInfo.getProductExpireTime().toInstant()
                                            .atZone(ZoneId.systemDefault())
                                            .toLocalDate().withDayOfMonth(1)
                            ) + ""
                    )
                    .issueDate(licenceInfo.getFirstActiveDate())
                    .expireDate(licenceInfo.getProductExpireTime())
                    .sdkVersion(licenceInfo.getSdkVersion())
                    .version(version)
                    .build());
        }
        return SuccessResponse.success(licenseInfoVOWrapper).build();
    }

    /**
     * 上传许可证文件
     *
     * @param file 许可证文件
     * @param flag 1方式一(新 License)  2方式二(旧 License)  3 延期
     * @throws IOException
     */
    @PostMapping("upload")
    @Operation(summary = "上传许可证文件")
    @OpLog
    public SuccessResponse<Boolean> uploadLicenseFile(MultipartFile file, String flag) throws IOException {
        OPLogContext.putOpType(InternalOpType.UPLOAD_LIC);
        if (Objects.isNull(file)) {
            throw new RestfulApiException("许可证不能为空");
        }
        return SuccessResponse.success(licenceService.uploadLicenceFile(file.getInputStream(), UploadLicenseFlag.getFlag(flag))).build();
    }

    /**
     * 在线激活
     *
     * @param productSn 产品识别码
     * @param email     邮箱
     */
    @GetMapping("activation/online")
    @Operation(summary = "在线激活")
    public SuccessResponse<Integer> activationOnline(String productSn, String email) {
        try {
            return SuccessResponse.success(licenceService.activeOnline(productSn, email)).build();
        } catch (LicException e) {
            log.error("在线激活失败: ", e);
            throw new RestfulApiException("在线激活失败: " + e.getMessage());
        }
    }

    private static final String OFFLINE_ACTIVATION_FILE = "act-off-file";

    @GetMapping("activation/offline")
    @Operation(description = "离线激活")
    public void downloadOffLineActivationFile(HttpServletRequest request, HttpServletResponse response) throws IOException {
        OPLogContext.putOpType(InternalOpType.EXPORT_LICENSE_REQ_FILE);
        try {
            String fileName = CookieUtils.getCookie(request, OFFLINE_ACTIVATION_FILE).getValue();
//            String fileNameSign = CookieUtils.getCookie(request, OFFLINE_ACTIVATION_FILE_SIGN).getValue();

            Assert.isTrue(fileName != null, "操作超时，请重试");
//            RSAUtil.verify(fileName.getBytes(StandardCharsets.UTF_8), keyPair.getPublic(), fileNameSign);
            licenceService.downloadOffLineActivationFile(response, fileName);
        } catch (Exception e) {
            log.error("下载离线激活请求包失败(文件名签名异常): ", e);
            try (ServletOutputStream outputStream = response.getOutputStream()) {
                outputStream.write(("下载离线激活请求包失败: 文件签名异常").getBytes());
            }
        }
    }

    private static final String OFFLINE_ACTIVATION_FILE_SIGN = "act-off-file-sign";

    /**
     * 下载离线请求包
     *
     * @param productSn 产品识别码
     * @param email     用户用于接收激活信息的邮箱地址
     */
    @GetMapping("activation/offlinePreCheck")
    @Operation(description = "离线激活")
    public SuccessResponse<Void> downloadOffLineActivationFilePreCheck(HttpServletResponse response, String productSn, String email) {
        try {
            String fileName = licenceService.downloadOffLineActivationFilePreCheck(productSn, email);
//            String fileNameSign = RSAUtil.sign(fileName.getBytes(StandardCharsets.UTF_8), keyPair.getPrivate());
            CookieUtils.setCookie(response, OFFLINE_ACTIVATION_FILE, fileName, true, TimeUnit.SECONDS.toSeconds(30));
//            CookieUtils.setCookie(response, OFFLINE_ACTIVATION_FILE_SIGN, fileNameSign, true, TimeUnit.SECONDS.toSeconds(30));

            return SuccessResponse.success(null).build();
        } catch (LicException e) {
            log.error("下载离线激活请求包失败: ", e);
            throw new RestfulApiException("下载离线激活请求包失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("下载离线激活请求包失败(文件名签名异常): ", e);
            throw new RestfulApiException("下载离线激活请求包失败: 文件名签名异常");
        }
    }


    /**
     * 下载人工激活请求包
     *
     * @param email 用户用于接收激活信息的邮箱地址
     */
    @GetMapping("activation/manual")
    @Operation(description = "人工激活")
    public SuccessResponse<Void> manual(HttpServletResponse response, String email) {
        try {
            licenceService.downloadArtificialPackage(email, response);
            return SuccessResponse.success(null).build();
        } catch (LicException e) {
            log.error("下载人工激活请求包失败: ", e);
            throw new RestfulApiException("下载人工激活请求包失败: " + e.getMessage());
        }
    }

    /**
     * 许可证备份
     *
     */
    @GetMapping("backup")
    @Operation(description = "许可证备份")
    public SuccessResponse<Void> backUpLicence(HttpServletResponse response) {
        licenceService.backupLicence(response);
        return SuccessResponse.success(null).build();
    }
}
