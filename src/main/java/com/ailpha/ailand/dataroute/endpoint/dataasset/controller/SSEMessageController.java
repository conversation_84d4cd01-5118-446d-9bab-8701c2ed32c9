package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageReadStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.SSEMessageService;
import com.ailpha.ailand.dataroute.endpoint.sse.entity.SSEMessageRecord;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 11:32
 * @Description:
 */
@RestController
@Tag(name = "消息管理")
public class SSEMessageController {

    @Resource
    private SSEMessageService sseMessageService;

    @GetMapping(value = "/sse/notifications", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "创建sse连接")
    public Flux<String> streamNotifications(@RequestParam(value = "uid", required = true) String uid, HttpServletResponse response) {
        // 设置响应头
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Cache-Control", "no-cache");

        return sseMessageService.streamNotifications(uid); // 返回 Sink 的 Flux
    }

    @GetMapping(value = "/sse/markNotice")
    @Operation(summary = "标记消息为已读")
    public ApiResponse<Void> markNotice(@RequestParam(value = "id", required = false) Long id,
                                        @RequestParam(value = "status", required = false, defaultValue = "READ") SSEMessageReadStatus status) {
        sseMessageService.markNotice(id, status);
        return SuccessResponse.success(null).build();
    }

    @GetMapping(value = "/sse/userList")
    public ApiResponse<Void> userList() {
        sseMessageService.logSinkInfo();
        return SuccessResponse.success(null).build();
    }

    @GetMapping(value = "/sse/mockData")
    public ApiResponse<Void> mockData(@RequestParam(value = "message") String message) {
        sseMessageService.mockData(message);
        return SuccessResponse.success(null).build();
    }

    @PostMapping(value = "/sse/receiveDataRouteMessage")
    public ApiResponse<Void> receiveDataRouteMessage(@RequestBody List<SSEMessageRequest> sseMessageRequests) {
        sseMessageService.receiveDataRouteMessage(sseMessageRequests);
        return SuccessResponse.success(null).build();
    }

    @PostMapping(value = "/sse/messageList")
    @Operation(summary = "消息列表")
    public ApiResponse<List<SSEMessageRecord>> messageList(@RequestBody SSEMessageListRequest sseMessageListRequest) {
        return sseMessageService.messageList(sseMessageListRequest);
    }

    @GetMapping(value = "/sse/totalCountMessage")
    @Operation(summary = "消息总数")
    public ApiResponse<Void> totalCountMessage() {
        sseMessageService.totalCountMessage();
        return SuccessResponse.success(null).build();
    }

    @GetMapping(value = "/sse/unReadCount")
    @Operation(summary = "消息总数")
    public ApiResponse<Integer> unReadCount() {
        return SuccessResponse.success(sseMessageService.unReadCount()).build();
    }

}
