package com.ailpha.ailand.dataroute.endpoint.plugin.aop;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yuwenping
 * @date: 2025/5/28 15:36
 * @Description:
 */
public class TrackingContext {
    private static final ThreadLocal<Map<String, Object>> context =
            ThreadLocal.withInitial(HashMap::new);

    public static final String DATA = "data";

    public static void put(String key, Object value) {
        context.get().put(key, value);
    }

    public static Object get(String key) {
        return context.get().get(key);
    }

    public static void clear() {
        context.remove();
    }

    public static Map<String, Object> getContext() {
        return context.get();
    }
}
