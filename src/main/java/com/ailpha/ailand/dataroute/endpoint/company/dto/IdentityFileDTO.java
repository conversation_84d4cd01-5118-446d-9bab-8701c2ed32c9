package com.ailpha.ailand.dataroute.endpoint.company.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class IdentityFileDTO {
    @Schema(description = "证书")
    String crt;
    String entityCrt;
    String entityPrivateKey;
    @Schema(description = "接入主体ID")
    String entityId;
    @Schema(description = "接入连接器ID")
    String nodeId;
    @Schema(description = "功能节点url")
    String functionNodeUrl;
    @Schema(description = "业务节点标识编码（业务节点ID）")
    String serviceNodeId;
    @Schema(description = "业务节点地址")
    String serviceNodeUrl;
    @Schema(description = "类型")
    String type;
    @Schema(description = "id")
    String id;
    @Schema(description = "文件id")
    String fileId;
    @Schema(description = "内部主体ID")
    Long companyId;
}
