package com.ailpha.ailand.dataroute.endpoint.company.cache;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.PersistentUserManagedCache;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.builders.UserManagedCacheBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.impl.config.persistence.DefaultPersistenceConfiguration;
import org.ehcache.impl.config.persistence.UserManagedPersistenceContext;
import org.ehcache.impl.persistence.DefaultLocalPersistenceService;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;

@Component
@RequiredArgsConstructor
@Slf4j
public class CompanyCacheConfig {

    private final AiLandProperties aiLandProperties;

    @Bean
    public PersistentUserManagedCache<Long, CompanyDTO> companyCache() {
        File cacheDir = new File(aiLandProperties.getFileStorage().getBasePath(), "company-cache");
        log.info("Initializing cache in directory: {}", cacheDir.getAbsolutePath());

        // 确保目录存在
        if (!cacheDir.exists() && !cacheDir.mkdirs()) {
            throw new IllegalStateException("Could not create cache directory: " + cacheDir);
        }
        
        // 检查目录权限
        if (!cacheDir.canRead() || !cacheDir.canWrite()) {
            log.error("Cache directory permissions issue: read={}, write={}", cacheDir.canRead(), cacheDir.canWrite());
            throw new IllegalStateException("Cache directory has insufficient permissions: " + cacheDir);
        }
        
        log.info("Cache files: {}", Arrays.toString(cacheDir.list()));
        
        // 创建持久化服务
        DefaultLocalPersistenceService persistenceService = new DefaultLocalPersistenceService(
                new DefaultPersistenceConfiguration(cacheDir));
        
        try {
            // 配置缓存，使用默认序列化器
            PersistentUserManagedCache<Long, CompanyDTO> cache = UserManagedCacheBuilder
                    .newUserManagedCacheBuilder(Long.class, CompanyDTO.class)
                    .with(new UserManagedPersistenceContext<>("company-cache", persistenceService))
                    .withResourcePools(ResourcePoolsBuilder.newResourcePoolsBuilder()
                            .heap(200L, EntryUnit.ENTRIES)
                            .disk(500L, MemoryUnit.MB, true))
                    .build(true);
            
            // 注册关闭钩子，确保应用关闭时缓存正确关闭
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                try {
                    log.info("Closing cache during shutdown...");
                    // 直接关闭缓存，PersistentUserManagedCache没有flush方法
                    cache.close();
                    // 最后停止持久化服务
                    persistenceService.stop();
                    log.info("Cache closed successfully");
                } catch (Exception e) {
                    log.error("Error closing cache", e);
                }
            }));
            
            return cache;
        } catch (Exception e) {
            log.error("Failed to initialize cache", e);
            persistenceService.stop();
            throw new RuntimeException("Failed to initialize cache", e);
        }
    }
}
