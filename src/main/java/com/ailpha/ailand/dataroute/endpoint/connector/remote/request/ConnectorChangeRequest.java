package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;

import lombok.Data;

import java.io.Serializable;

@Data
public class ConnectorChangeRequest implements Serializable {

    private static final long serialVersionUID = -5973025159130465950L;
    /**
     * 接入连接器标识
     * length = 32
     */
    private String connectorId;

    /**
     * 接入连接器所有者主体身份标识
     * length = 18
     */
    private String connectorOwnerId;

    /**
     * 接入连接器物理部署地址
     */
    private String connectorDeploymentAddress;

    /**
     * 接入连接器地址A
     * length = 15
     */
    private String connectorAddressA;

    /**
     * 接入连接器地址AAAA
     * length = 39
     */
    private String connectorAddressAAAA;

    /**
     * 接入连接器接入方式说明
     */
    private String connectorAccessMethod;

    /**
     * 接入连接器地址更新原因
     */
    private String addressChangeExplanation;

    /**
     * 备注
     */
    private String changeNotes;
}
