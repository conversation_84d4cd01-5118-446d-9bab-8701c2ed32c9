package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetPublishDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetReportDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.BusinessReportDTO;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: sunsas.yu
 * @date: 2024/11/22 19:15
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/ailand/trader")
@Tag(name = "交易所管理")
@RequiredArgsConstructor
public class TraderController {

    private final TraderService traderService;

    @PostMapping("/manualActiveTrader")
    public ApiResponse<Boolean> manualActiveTrader(@RequestBody BusinessReportDTO businessReportDTO) {
        return SuccessResponse.success(traderService.manualActiveTrader(businessReportDTO)).build();
    }

    @PostMapping("/manualDataAssetPublishTrader")
    public ApiResponse<Boolean> manualDataAssetPublishTrader(@RequestBody AssetPublishDTO assetPublishDTO) {
        return SuccessResponse.success(traderService.manualDataAssetPublishTrader(assetPublishDTO)).build();
    }

    @PostMapping("/manualDataAssetRegisterTrader")
    public ApiResponse<Boolean> manualDataAssetRegisterTrader(@RequestBody AssetReportDTO reportDTO) {
        return SuccessResponse.success(traderService.manualDataAssetRegisterTrader(reportDTO)).build();
    }

    @PostMapping("/manualDataAssetUpdateTrader")
    public ApiResponse<Boolean> manualDataAssetUpdateTrader(@RequestBody AssetReportDTO reportDTO) {
        return SuccessResponse.success(traderService.manualDataAssetUpdateTrader(reportDTO)).build();
    }

}
