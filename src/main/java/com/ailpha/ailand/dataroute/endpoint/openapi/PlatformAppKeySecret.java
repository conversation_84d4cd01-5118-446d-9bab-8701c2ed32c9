package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/12/24
 */
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "platform_app_key_secret")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PlatformAppKeySecret implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "bigint COMMENT 'id'", updatable = false, nullable = false, unique = true)
    Long id;

    @Schema(description = "平台名称")
    @Column(name = "platform_name", columnDefinition = "VARCHAR(5000) COMMENT '平台名称'")
    String platformName;

    @Column(name = "platform_type", columnDefinition = "VARCHAR(64) COMMENT '平台类型'")
    @Enumerated(EnumType.STRING)
    PlatformType platformType;

    @Schema(description = "appKey")
    @Column(name = "app_key", columnDefinition = "VARCHAR(255) COMMENT 'appKey'")
    String appKey;

    @Schema(description = "appSecret")
    @Column(name = "app_secret", columnDefinition = "VARCHAR(255) COMMENT 'appSecret'")
    String appSecret;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    Date updateTime;
}
