package com.ailpha.ailand.dataroute.endpoint.common.config;

import sun.security.x509.*;

import java.io.IOException;
import java.security.*;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/30 9:54
 * @description https://stackoverflow.com/questions/64442156/how-to-create-a-create-a-pkcs12-keystore?noredirect=1&lq=1
 */
public class CertificateAndKeyGenerate {

    private SecureRandom prng;
    private final String sigAlg;
    private KeyPairGenerator keyGen;
    private PublicKey publicKey;
    private PrivateKey privateKey;

    /**
     * @param keyType keyType type of key, e.g. "RSA", "DSA"
     * @param sigAlg  sigAlg name of the signature algorithm, e.g. "MD5WithRSA", "MD2WithRSA", "SHAwithDSA".
     * @throws NoSuchAlgorithmException
     */
    public CertificateAndKeyGenerate(String keyType, String sigAlg) throws NoSuchAlgorithmException {
        this.keyGen = KeyPairGenerator.getInstance(keyType);
        this.sigAlg = sigAlg;
    }

    public CertificateAndKeyGenerate(String keyType, String sigAlg, String providerName) throws NoSuchAlgorithmException {
        if (providerName == null) {
            keyGen = KeyPairGenerator.getInstance(keyType);
        } else {
            try {
                keyGen = KeyPairGenerator.getInstance(keyType, providerName);
            } catch (Exception e) {
                // try first available provider instead
                keyGen = KeyPairGenerator.getInstance(keyType);
            }
        }
        this.sigAlg = sigAlg;
    }

    /**
     * Sets the source of random numbers used when generating keys.
     * If you do not provide one, a system default facility is used.
     * You may wish to provide your own source of random numbers
     * to get a reproducible sequence of keys and signatures, or
     * because you may be able to take advantage of strong sources
     * of randomness/entropy in your environment.
     */
    public void setRandom(SecureRandom generator) {
        prng = generator;
    }

    // want "public void generate (X509Certificate)" ... inherit DSA/D-H param

    /**
     * Generates a random public/private key pair, with a given key
     * size.  Different algorithms provide different degrees of security
     * for the same key size, because of the "work factor" involved in
     * brute force attacks.  As computers become faster, it becomes
     * easier to perform such attacks.  Small keys are to be avoided.
     *
     * <P>Note that not all values of "keyBits" are valid for all
     * algorithms, and not all public key algorithms are currently
     * supported for use in X.509 certificates.  If the algorithm
     * you specified does not produce X.509 compatible keys, an
     * invalid key exception is thrown.
     *
     * @param keyBits the number of bits in the keys.
     * @throws InvalidKeyException if the environment does not
     *                             provide X.509 public keys for this signature algorithm.
     */
    public void generate(int keyBits) throws InvalidKeyException {
        KeyPair pair;
        try {
            if (prng == null) {
                prng = new SecureRandom();
            }
            keyGen.initialize(keyBits, prng);
            pair = keyGen.generateKeyPair();
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage());
        }

        publicKey = pair.getPublic();
        privateKey = pair.getPrivate();

        // publicKey's format must be X.509 otherwise
        // the whole CertGen part of this class is broken.
        if (!"X.509".equalsIgnoreCase(publicKey.getFormat())) {
            throw new IllegalArgumentException("publicKey's is not X.509, but " + publicKey.getFormat());
        }
    }


    /**
     * Returns the public key of the generated key pair if it is of type
     * <code>X509Key, or null if the public key is of a different type.
     * <p>
     * XXX Note: This behaviour is needed for backwards compatibility.
     * What this method really should return is the public key of the
     * generated key pair, regardless of whether or not it is an instance of
     * <code>X509Key. Accordingly, the return type of this method
     * should be <code>PublicKey.
     */
    public X509Key getPublicKey() {
        if (!(publicKey instanceof X509Key)) {
            return null;
        }
        return (X509Key) publicKey;
    }

    /**
     * Always returns the public key of the generated key pair. Used
     * by KeyTool only.
     * <p>
     * The publicKey is not necessarily to be an instance of
     * X509Key in some JCA/JCE providers, for example SunPKCS11.
     */
    public PublicKey getPublicKeyAnyway() {
        return publicKey;
    }

    /**
     * Returns the private key of the generated key pair.
     *
     * <P>Be extremely careful when handling private keys.
     * When private keys are not kept secret, they lose their ability
     * to securely authenticate specific entities ... that is a huge
     * security risk!</em>
     */
    public PrivateKey getPrivateKey() {
        return privateKey;
    }

    /**
     * Returns a self-signed X.509v3 certificate for the public key.
     * The certificate is immediately valid. No extensions.
     *
     * <P>Such certificates normally are used to identify a "Certificate
     * Authority" (CA).  Accordingly, they will not always be accepted by
     * other parties.  However, such certificates are also useful when
     * you are bootstrapping your security infrastructure, or deploying
     * system prototypes.
     *
     * @param myname    X.500 name of the subject (who is also the issuer)
     * @param firstDate the issue time of the certificate
     * @param validity  how long the certificate should be valid, in seconds
     * @throws CertificateException     on certificate handling errors.
     * @throws InvalidKeyException      on key handling errors.
     * @throws SignatureException       on signature handling errors.
     * @throws NoSuchAlgorithmException on unrecognized algorithms.
     * @throws NoSuchProviderException  on unrecognized providers.
     */
    public X509Certificate getSelfCertificate(X500Name myname, Date firstDate, long validity)
            throws CertificateException, InvalidKeyException, SignatureException,
            NoSuchAlgorithmException, NoSuchProviderException {
        return getSelfCertificate(myname, firstDate, validity, null);
    }

    // Like above, plus a CertificateExtensions argument, which can be null.

    public X509Certificate getSelfCertificate(X500Name myname, Date firstDate, long validity, CertificateExtensions ext)
            throws CertificateException, InvalidKeyException, SignatureException,
            NoSuchAlgorithmException, NoSuchProviderException {
        X509CertImpl cert;
        Date lastDate;
        try {
            lastDate = new Date();
            lastDate.setTime(firstDate.getTime() + validity * 1000);

            CertificateValidity interval = new CertificateValidity(firstDate, lastDate);

            X509CertInfo info = new X509CertInfo();
            // Add all mandatory attributes
            info.setVersion(new CertificateVersion(CertificateVersion.V3));
            info.setSerialNumber(new CertificateSerialNumber(new java.util.Random().nextInt() & 0x7fffffff));
            AlgorithmId algID = AlgorithmId.get(sigAlg);
            info.setAlgorithmId(new CertificateAlgorithmId(algID));
            info.setSubject(myname);
            info.setKey(new CertificateX509Key(publicKey));
            info.setValidity(interval);
            info.setIssuer(myname);
            if (ext != null) {
                info.setExtensions(ext);
            }

            cert = X509CertImpl.newSigned(info, privateKey, this.sigAlg);

            return cert;
        } catch (IOException e) {
            throw new CertificateEncodingException("getSelfCert: " +
                    e.getMessage());
        }
    }

    // Keep the old method

    public X509Certificate getSelfCertificate(X500Name myname, long validity)
            throws CertificateException, InvalidKeyException, SignatureException,
            NoSuchAlgorithmException, NoSuchProviderException {
        return getSelfCertificate(myname, new Date(), validity);
    }

    /**
     * Returns a PKCS #10 certificate request.  The caller uses either
     * <code>PKCS10.print or PKCS10.toByteArray
     * operations on the result, to get the request in an appropriate
     * transmission format.
     *
     * <P>PKCS #10 certificate requests are sent, along with some proof
     * of identity, to Certificate Authorities (CAs) which then issue
     * X.509 public key certificates.
     *
     * @param myname X.500 name of the subject
     * @throws InvalidKeyException on key handling errors.
     * @throws SignatureException  on signature handling errors.
     */
//    public PKCS10 getCertRequest(X500Name myname) throws InvalidKeyException, SignatureException {
//        PKCS10 req = new PKCS10(publicKey);
//
//        try {
//            Signature signature = Signature.getInstance(sigAlg);
//            signature.initSign(privateKey);
//            req.encodeAndSign(myname, signature);
//        } catch (CertificateException e) {
//            throw new SignatureException(sigAlg + " CertificateException");
//
//        } catch (IOException e) {
//            throw new SignatureException(sigAlg + " IOException");
//
//        } catch (NoSuchAlgorithmException e) {
//            // "can't happen"
//            throw new SignatureException(sigAlg + " unavailable?");
//        }
//        return req;
//    }
}
