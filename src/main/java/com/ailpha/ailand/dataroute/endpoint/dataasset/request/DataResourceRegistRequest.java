package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资源登记请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceRegistRequest {
    @Schema(description = "数据标识，首次添加不用传")
    String id;
    @Schema(description = "资源英文名称")
    @NotBlank(message = "资源名称不能为空")
    String resourceName;
    @Schema(description = "资源名称")
    String resourceNameCN;
    @Schema(description = "资源格式")
    @NotBlank(message = "资源格式（如OFD、xlsx、jpg等）不能为空")
    String resourceFormat;
    @Schema(description = "行业分类")
    @NotBlank(message = "行业分类（GB/T 4754-2017门类代码）不能为空")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是", examples = {"0", "1"})
    @NotNull(message = "是否涉及个人信息：0-否,1-是不能为空")
    String personalInformation;
    @Schema(description = "资源简介")
    @NotBlank(message = "资源摘要不能为空")
    String description;
    @Schema(description = "资源来源: 01 原始取得 02 收集取得 03 交易取得 04 其他")
    @NotBlank(message = "数据来源：01-原始取得,02-收集取得,03-交易取得不能为空")
    String source;
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "其他")
    String other;

    @Schema(description = "数据项")
    List<DataSchemaBO> dataSchema;

    
    @Schema(description = "资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据")
    String resourceType;
}
