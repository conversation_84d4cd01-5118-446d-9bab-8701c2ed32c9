package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URL;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiSortInterceptor extends BasePathMatchInterceptor {

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        if (baseCapabilityManager.isCompanyCapability(BaseCapabilityType.AI_SORT) && ObjectUtils.isEmpty(localCompanyId)) {
            localCompanyId = request.header(SystemConstants.LOCAL_COMPANY_ID);
            // TODO：注释-非登录态接口调用前，需要拿到localCompanyId，获取基础能力管理配置
            Assert.isTrue(!ObjectUtil.isEmpty(localCompanyId), "企业级读取基础能力管理配置：缺少参数【LOCAL_COMPANY_ID】");
        }
        URL url = URLUtil.url(baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.AI_SORT).getBaseUrl());
        request = chain.request();
        HttpUrl httpUrl = request.url().newBuilder()
                .host(url.getHost())
                .port(url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        return chain.proceed(request.newBuilder().url(httpUrl).method(request.method(), request.body()).tag(request.tag()).build());
    }
}
