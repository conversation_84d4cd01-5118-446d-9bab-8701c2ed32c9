package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URL;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AiSortInterceptor extends BasePathMatchInterceptor {

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        URL url = URLUtil.url(baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.AI_SORT).getBaseUrl());
        Request request = chain.request();
        HttpUrl httpUrl = request.url().newBuilder()
                .host(url.getHost())
                .port(url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        return chain.proceed(request.newBuilder().url(httpUrl).method(request.method(), request.body()).tag(request.tag()).build());
    }
}
