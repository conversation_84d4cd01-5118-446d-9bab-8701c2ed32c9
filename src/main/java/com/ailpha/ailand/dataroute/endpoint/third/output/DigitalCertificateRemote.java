package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.DATA_INVOICE, tokenUrl = "/_digital-certificate/third/app/token")
public interface DigitalCertificateRemote {
    @POST("/_digital-certificate/third/data-route/delivery-list")
    PageResult<DeliveryListResponse> deliveryList(@Body DeliveryListRequest request);

    /**
     * 分页查询场景信息
     *
     * @param thirdSceneInfoPageReq req
     * @return 分页查询场景信息
     */
    @POST("/_digital-certificate/third/data-route/compliance-scene-list")
    PageResult<SceneBasicInfoResp> traderComplianceSceneList(@Body ThirdSceneInfoPageReq thirdSceneInfoPageReq);

    /**
     * 场景详情
     *
     * @param sceneId id
     * @return 场景详情
     */
    @GET("/_digital-certificate/third/data-route/compliance-scene-info")
    CommonResult<SceneInfoResp> traderSceneInfo(@Query("sceneId") String sceneId);


    /**
     * 交易登记
     *
     * @param request req
     * @return
     */
    @POST("/_digital-certificate/third/data-route/transaction-register")
    CommonResult<Boolean> traderRegister(@Body TransactionRegisterRequest request);

    /**
     * 交付登记
     *
     * @param request req
     * @return
     */
    @POST("/_digital-certificate/third/data-route/delivery-register")
    CommonResult<String> deliveryRegister(@Header(SystemConstants.LOCAL_COMPANY_ID) String localCompanyId, @Body DeliveryRegisterRequest request);


}
