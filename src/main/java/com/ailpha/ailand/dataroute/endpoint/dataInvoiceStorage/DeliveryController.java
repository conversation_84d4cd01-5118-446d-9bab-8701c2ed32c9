package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderDeliveryRelation;
import com.ailpha.ailand.dataroute.endpoint.order.domain.QOrderDeliveryRelation;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderDeliveryRepository;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("delivery")
@RequiredArgsConstructor
@Tag(name = "数据发票存证")
@Slf4j
@PreAuthorize("hasAuthority('COMPANY_ADMIN')")
public class DeliveryController {

    private final DigitalCertificateRemote digitalCertificateRemote;
    private final DataProductService dataProductService;
    private final RouterService routerService;
    private final OrderDeliveryRepository orderDeliveryRepository;
    private final JPAQueryFactory queryFactory;

    @Data
    public static class DeliveryListReqVO extends Page {
        @Schema(description = "资源名称")
        String dataName;
        @Schema(description = "交付ID")
        String deliveryId;
        @Schema(description = "交付类型")
        String deliveryType;
    }

    @PostMapping("page")
    @Operation(summary = "列表查询")
    public SuccessResponse<List<DeliveryListResponse>> list(@RequestBody DeliveryListReqVO reqVO) {
        DeliveryListRequest deliveryListRequest = BeanUtil.copyProperties(reqVO, DeliveryListRequest.class);
        CompanyDTO companyDTO = routerService.currentCompany();
        deliveryListRequest.setThirdEnterpriseId(String.valueOf(companyDTO.getThirdBusinessId()));
        // 根据资产名称查询交付场合ID
        if (StringUtils.isNotEmpty(reqVO.getDataName())) {
            List<OrderDeliveryRelation> relationList = orderDeliveryRepository.findDistinctByAssetNameLike("%" + reqVO.getDataName() + "%");
            if (CollectionUtil.isEmpty(relationList)) {
                List<DeliveryListResponse> list = new ArrayList<>();
                return SuccessResponse.success(list).total(0L)
                        .page(new Page(reqVO.getNum(), 0L))
                        .build();
            } else {
                deliveryListRequest.setThirdDeliveryIds(relationList.stream().map(x -> String.join(":", x.getDeliverySceneId(), x.getAssetId())).collect(Collectors.toList()));
            }
        }
        deliveryListRequest.setPage(reqVO.getNum());
        deliveryListRequest.setSize(reqVO.getSize());
        PageResult<DeliveryListResponse> deliveryListResponsePageResult = digitalCertificateRemote.deliveryList(deliveryListRequest);
        List<String> dataAssetIds = deliveryListResponsePageResult.getData().stream().map(x -> x.getThirdBusinessId().split(":")[1]).collect(Collectors.toList());
        DataAssetListRequest dataAssetListRequest = new DataAssetListRequest();
        dataAssetListRequest.setDataAssetIds(dataAssetIds);

        List<String> assetIdList = deliveryListResponsePageResult.getData().stream().distinct().map(x -> x.getThirdBusinessId().split(":")[1]).toList();
        QOrderDeliveryRelation relation = QOrderDeliveryRelation.orderDeliveryRelation;

        Map<String, String> assetIdNameMap = new HashMap<>();
        queryFactory.selectDistinct(relation.assetId, relation.assetName).from(relation)
                .where(relation.assetId.in(assetIdList)).fetch().forEach(tuple -> {
                    String assetId = tuple.get(relation.assetId);
                    String assetName = tuple.get(relation.assetName);
                    assetIdNameMap.put(assetId, assetName);
                });

        List<DeliveryListResponse> resultData = deliveryListResponsePageResult.getData().stream().peek(x -> {
            try {
                String assetId = x.getThirdBusinessId().split(":")[1];
                String assetName = assetIdNameMap.get(assetId);
                if (StringUtils.isEmpty(assetName)) {
                    DataProductVO dataProductVO = null;
                    try {
                        dataProductVO = dataProductService.getDataProductByDataProductPlatformId(assetId);
                    } catch (Exception e) {
                        log.error("查询数据产品异常：", e);
                    }
                    assetName = dataProductVO == null ? "" : dataProductVO.getDataProductName();
                }
                x.setDataAssetName(StringUtils.isEmpty(assetName) ? "找不到数据资产" : assetName);
                x.setType(AssetType.PRODUCT);
            } catch (Exception ignore) {
                x.setDataAssetName("找不到数据资产");
            }
        }).collect(Collectors.toList());
        return SuccessResponse.success(resultData).total(deliveryListResponsePageResult.getTotal())
                .page(new Page(reqVO.getNum(), deliveryListResponsePageResult.getTotal()))
                .build();
    }
}
