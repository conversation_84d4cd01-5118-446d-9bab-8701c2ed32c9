package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage;

import cn.hutool.core.bean.BeanUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListResponse;
import com.ailpha.ailand.dataroute.endpoint.third.output.HubDeliverySceneRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.SelectDeliverySceneRequest;
import com.ailpha.ailand.dataroute.endpoint.third.response.DeliverySceneResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("delivery")
@RequiredArgsConstructor
@Tag(name = "数据发票存证")
@PreAuthorize("hasAuthority('COMPANY_ADMIN')")
public class DeliveryController {

    private final DigitalCertificateRemote digitalCertificateRemote;
    private final HubDeliverySceneRemote hubDeliverySceneRemote;
    private final DataHubRemoteService dataHubRemoteService;
    private final RouterService routerService;

    @Data
    public static class DeliveryListReqVO extends Page {
        @Schema(description = "资源名称")
        String dataName;
        @Schema(description = "交付ID")
        String deliveryId;
        @Schema(description = "交付类型")
        String deliveryType;
    }

    @PostMapping("page")
    @Operation(summary = "列表查询")
    public SuccessResponse<List<DeliveryListResponse>> list(@RequestBody DeliveryListReqVO reqVO) {
        DeliveryListRequest deliveryListRequest = BeanUtil.copyProperties(reqVO, DeliveryListRequest.class);
        CompanyDTO companyDTO = routerService.currentCompany();
        deliveryListRequest.setThirdEnterpriseId(String.valueOf(companyDTO.getThirdBusinessId()));
        // 根据资产名称查询交付场合ID
        if (StringUtils.isNotEmpty(reqVO.getDataName())) {
            SelectDeliverySceneRequest selectDeliverySceneRequest = new SelectDeliverySceneRequest();
            selectDeliverySceneRequest.setDataAssetName(reqVO.dataName);
            CommonResult<List<DeliverySceneResponse>> deliverySceneListResult = hubDeliverySceneRemote.selectByCondition(selectDeliverySceneRequest);
            if (deliverySceneListResult.isSuccess()) {
                if (deliverySceneListResult.getData() != null) {
                    deliveryListRequest.setThirdDeliveryIds(deliverySceneListResult.getData().stream().map(x -> String.join(":", x.getDeliverySceneId(), x.getDataAssetId())).collect(Collectors.toList()));
                }
            } else
                throw new RestfulApiException("查询交付场景信息异常：" + deliverySceneListResult.getMessage());
        }
        deliveryListRequest.setPage(reqVO.getNum());
        deliveryListRequest.setSize(reqVO.getSize());
        PageResult<DeliveryListResponse> deliveryListResponsePageResult = digitalCertificateRemote.deliveryList(deliveryListRequest);
        List<String> dataAssetIds = deliveryListResponsePageResult.getData().stream().map(x -> x.getThirdBusinessId().split(":")[1]).collect(Collectors.toList());
        DataAssetListRequest dataAssetListRequest = new DataAssetListRequest();
        dataAssetListRequest.setDataAssetIds(dataAssetIds);
        CommonResult<List<DataAssetDTO>> dataAssets = dataHubRemoteService.dataAssets(dataAssetListRequest);
        Assert.isTrue(dataAssets.isSuccess(), "获取资产信息异常");
        // 根据资产ID 分组
        Map<String, DataAssetDTO> dataAssetMap = dataAssets.getData().stream().collect(Collectors.toMap(DataAssetDTO::getId, x -> x));
        List<DeliveryListResponse> resultData = deliveryListResponsePageResult.getData().stream().peek(x -> {
            try {
                DataAssetDTO dataAssetDTO = dataAssetMap.get(x.getThirdBusinessId().split(":")[1]);
                x.setDataAssetName(dataAssetDTO.getAssetName());
                x.setType(AssetType.valueOf(dataAssetDTO.getType()));
            } catch (Exception ignore) {
                x.setDataAssetName("找不到数据资产");
            }
        }).collect(Collectors.toList());
        return SuccessResponse.success(resultData).total(deliveryListResponsePageResult.getTotal())
                .page(new Page(reqVO.getNum(), deliveryListResponsePageResult.getTotal()))
                .build();
    }
}
