package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DeliveryListRequest implements Serializable {
    @Schema(description = "第三方平台企业ID（卖家）")
    String thirdEnterpriseId;

    @Schema(description = "第三方交付ID列表")
    List<String> thirdDeliveryIds;

    @Schema(description = "交付ID")
    String deliveryId;

    @Schema(description = "交付类型", allowableValues = "数据文件,API,平台", example = "API")
    String deliveryType;

    Long page;
    Long size;
}
