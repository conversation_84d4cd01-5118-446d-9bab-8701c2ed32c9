package com.ailpha.ailand.dataroute.endpoint.tenant.config;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.jdbc.connections.spi.AbstractDataSourceBasedMultiTenantConnectionProviderImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

@Slf4j
@Component
public class MultiTenantConnectionProviderImpl extends AbstractDataSourceBasedMultiTenantConnectionProviderImpl<String> {
    
    @Autowired
    private DataSource defaultDataSource;
    
    @Override
    protected DataSource selectAnyDataSource() {
        return defaultDataSource;
    }

    @Override
    protected DataSource selectDataSource(String tenantIdentifier) {
        try (Connection connection = defaultDataSource.getConnection()) {
            connection.createStatement().execute("SET search_path TO " + tenantIdentifier);
        } catch (SQLException e) {
            log.error("无法切换到租户schema: {}", tenantIdentifier, e);
            throw new RuntimeException("无法切换到租户schema: " + tenantIdentifier, e);
        }
        return defaultDataSource;
    }
}