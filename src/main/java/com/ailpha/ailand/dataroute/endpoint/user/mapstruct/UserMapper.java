package com.ailpha.ailand.dataroute.endpoint.user.mapstruct;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserListResponse;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.Date;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface UserMapper {
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(source = "name", target = "realName")
    @Mapping(source = "mobile", target = "phone")
    @Mapping(target = "companyId", ignore = true)
    User toDo(AddUserRequest request);

    @AfterMapping
    default void afterMapping(@MappingTarget User user, AddUserRequest request) {
        user.setCompanyId(Long.valueOf(request.getLocalCompanyId()));
    }

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "userId", source = "id")
    @Mapping(target = "delegateInfo", source = "ext.delegateInfo")
    @Mapping(target = "createTime", source = "createTime")
    @Mapping(target = "authStatus", source = "ext.authStatus")
    UserDetailsResponse toDetails(User user);

    @AfterMapping
    default void afterMapping(@MappingTarget UserDetailsResponse response, User user) {
        response.setAuthTime(ObjectUtil.isNotNull(user.getExt().getReviewTime()) ? DateUtil.format(new Date(user.getExt().getReviewTime()), "yyyy-MM-dd HH:mm:ss") : "");
        if (response.getDelegateInfo().getDelegateIdType().equals("身份证")) {
            response.getDelegateInfo().setDelegateIdNumber(StringUtils.substring(response.getDelegateInfo().getDelegateIdNumber(), 0, 6) + "****" + StringUtils.substring(response.getDelegateInfo().getDelegateIdNumber(), -4));
        } else
            response.getDelegateInfo().setDelegateIdNumber(StringUtils.repeat("*", response.getDelegateInfo().getDelegateIdNumber().length()));
    }

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(source = "ext.delegateInfo.delegateName", target = "name")
    @Mapping(source = "phone", target = "mobile")
    @Mapping(target = "endpointUrl", ignore = true)
    @Mapping(target = "createTime", source = "createTime")
    @Mapping(target = "authStatus", source = "ext.authStatus")
    @Mapping(target = "expireDate", source = "ext.expireDate")
    UserListResponse toListRsp(User user);

    @AfterMapping
    default void afterMapping(@MappingTarget UserListResponse response, User user) {
        JSONObject entries = JSONUtil.parseObj(user.getExt());
        response.setEndpointUrl(ObjectUtil.isNotNull(user.getExt()) ? entries.getStr("url") : "");
        response.setAuthTime(ObjectUtil.isNotNull(user.getExt().getReviewTime()) ? DateUtil.format(new Date(user.getExt().getReviewTime()), "yyyy-MM-dd HH:mm:ss") : "");
    }
}
