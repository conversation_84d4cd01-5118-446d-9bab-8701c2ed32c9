package com.ailpha.ailand.dataroute.endpoint.sse.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageReadStatus;
import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 10:19
 * @Description:
 */
@Data
@Entity
@Table(name = "t_sse_message_record")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SSEMessageRecord {
    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Enumerated(EnumType.STRING)
    SSEMessageTypeEnum type;

    /**
     * TraderRoleEnums
     */
    @Column(name = "trader_role_type")
    String traderRoleType;

    @Column(columnDefinition = "text COMMENT '消息内容，json串格式存储发送'")
    String message;

    @Column(name = "data_id")
    String dataId;

    @Column(name = "user_id")
    String userId;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    @Column(name = "create_time")
    Date createTime;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    @Column(name = "send_time")
    Date sendTime;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    @Column(name = "update_time")
    Date updateTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "read_status")
    SSEMessageReadStatus readStatus;

    @Column(columnDefinition = "text COMMENT '扩展字段'")
    String ext;

    @Data
    public static class SSEMessage {
        private String message;

        private String type;

        private String userId;

    }
}
