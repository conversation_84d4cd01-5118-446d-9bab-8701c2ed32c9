package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response;


import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.ResourceItem;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
//@JsonIgnoreProperties(ignoreUnknown = true)
public class DataResourceInfo {
    /**
     * 资源名称
     */
    String resourceName;
    /**
     * 来源平台内部标识
     */
    String outerResourceId;
    /**
     * 数据类型：1 个人 2 企业 3 公共
     */
    String dataType;
    /**
     * 数据资源类型
     */
    String resourceType;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 资源持有方
     */
    String resourceOwner;
    /**
     * 资源持有方ID
     */
    String resourceOwnerId;
    /**
     * 资源持有方编号
     */
    String resourceOwnerCode;
    /**
     * 存储容量
     */
    String capacity;
    /**
     * 联系人
     */
    String contacter;
    /**
     * 联系方式
     */
    String contactInformation;
    /**
     * 资源摘要
     */
    String resourceAbstract;
    /**
     * 资源格式
     */
    String resourceFormat;
    /**
     * 数据来源
     */
    String dataSource;
    /**
     * 是否涉及个人信息： 0 否 1 是
     */
    String personalInformation;
    /**
     * 资源状态
     */
    String resourceStatus;
    /**
     * 其他
     */
    String others;
    /**
     * 版本
     */
    Integer version;
    /**
     * 资源编号
     */
    String resourceCode;
    /**
     * 平台ID
     */
    String platformId;
    /**
     * 查询资源列表
     */
    List<ResourceItem> itemList;
    /**
     * 使用限制
     */
    String limitations;
    /**
     * 授权使用：0 否 1 是
     */
    String authorize;
    /**
     * 创建时间
     */
    String createTime;
    /**
     * 更新时间
     */
    String updateTime;
    String dpe;
}
