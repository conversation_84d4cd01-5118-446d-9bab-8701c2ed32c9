package com.ailpha.ailand.dataroute.endpoint.common.config;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "ailand")
public class AiLandProperties {
    String version = "0.0.1";

    /**
     * 安全配置
     */
    Security security;

    /**
     * 文件存储
     */
    FileStorage fileStorage;

    /**
     * 证书
     */
    LicenseServer licenseServer;

    /**
     * 连接器组网agent
     */
    RouteAgent agent;

    ExecutorServer executorServer;

    /**
     * lic
     */
    NewLic newLic;

    IamServer iamServer;

    KeyPair keyPair;

    Deploy deploy;

    @Data
    public static class Deploy {
        /**
         * share
         * standalone
         */
        DeployMode mode;
    }

    @Data
    public static class Security {
        /**
         * 白名单接口
         */
        List<String> whiteApis;
        List<String> csrfWhiteApis;
        LoginConfig login;
    }

    @Data
    @ConfigurationProperties(prefix = "ailand.security.login")
    @Configuration
    public static class LoginConfig {
        int maximumSessions;
        boolean exceptionIfMaximumExceeded;
    }

    @Data
    public static class KeyPair {

        Boolean enable;
        String alias;
        String storeType;
        String storePath;
        String keyPassword;
        String keyStorePassword;
    }

    @Data
    public static class FileStorage {
        /**
         * 磁盘地址
         */
        String basePath;
        /**
         * 证书支持的文件类型
         */
        List<String> licenseFileSuffix;
        /**
         * 证书支持的文件类型
         */
        List<String> logoFileSuffix;
    }

    @Data
    public static class LicenseServer {
        /**
         * 访问地址
         */
        String baseUrl;
        /**
         * 接口版本
         */
        String version = "v1";
    }

    /**
     * 连接器终端 零信任客户端
     */
    @Data
    public static class RouteAgent {
        /**
         * 访问地址
         */
        String baseUrl;
        /**
         * 虚拟网卡名称
         */
        String networkName;
    }

    @Data
    public static class ExecutorServer {
        String port = "6112";
        String submitJobUrl = "/api/collector/async/job";
    }

    @Data
    public static class DataHubServer {
        String baseUrl;
    }

    @Data
    public static class NewLic {
        String localCacheDir;
        String additional;
        String productName;
        String productModel;

    }

    @Data
    public static class IamServer {
        String baseUrl = "https://127.0.0.1";
        // todo 可能需要提取为公共变量
        String clientId;
        String clientSecret;
        String externalUrl;
    }
}
