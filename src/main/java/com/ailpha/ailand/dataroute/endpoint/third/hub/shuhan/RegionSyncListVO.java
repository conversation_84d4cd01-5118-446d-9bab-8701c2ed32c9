package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/7/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RegionSyncListVO implements Serializable {

    @Schema(description = "总数据量")
    Long total;

    @Schema(description = "当前查询的页码数，从1开始")
    Long offset;

    @Schema(description = "每页返回的数据数量")
    Long size;

    @Schema(description = "业务节点列表")
    List<ServiceNode> serviceNodeList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ServiceNode implements Serializable {
        @Schema(description = "业务节点登记名称")
        String entryName;

        @Schema(description = "业务节点标识编码")
        String serviceNodeId;

        @Schema(description = "业务节点地址")
        List<Addresses> addresses;

        @Schema(description = "业务功能简介")
        String introduction;

        @Schema(description = "业务节点版本")
        String version;

        @Schema(description = "备注")
        String reserveNotes;

        @Schema(description = "所属法人或其他组织名称")
        String enterpriseName;

        @Schema(description = "所属法人或其他组织身份标识码")
        String enterpriseIdentityId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Addresses implements Serializable {
        /**
         * 业务节点六种类型，单选：
         * 1－应用侧基础设施
         * 2－数据交易类
         * 3－数据开发利用类
         * 4－公共数据授权运营平台类
         * 5－公共服务平台类
         */
        @Schema(description = "业务功能类型")
        String type;

        @Schema(description = "业务节点IP地址")
        String ip;

        @Schema(description = "业务节点域名")
        String domainName;

        @Schema(description = "业务节点接口地址")
        String apiUrl;
    }

    public static String getTypeDescription(String type) {
        return switch (type) {
            case "1" -> "应用侧基础设施";
            case "2" -> "数据交易类";
            case "3" -> "数据开发利用类";
            case "4" -> "公共数据授权运营平台类";
            case "5" -> "公共服务平台类";
            default -> "其他业务平台类型";
        };
    }
}
