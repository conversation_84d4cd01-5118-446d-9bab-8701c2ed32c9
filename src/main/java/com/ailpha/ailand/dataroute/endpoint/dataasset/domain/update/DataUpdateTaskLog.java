package com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetPrepareStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据接入任务历史记录实体
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_data_update_task_log")
public class DataUpdateTaskLog {
    /**
     * 历史记录ID
     */
    @Id
    private String id;

    /**
     * 关联的任务ID
     */
    @Column(name = "task_id", nullable = false)
    private String taskId;

    /**
     * 任务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "task_status", nullable = false)
    private DataUpdateStatus taskStatus;

    /**
     * 任务日志
     */
    @Column(name = "log")
    private String log;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 任务开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 任务结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 扩展字段
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "ext", columnDefinition = "json")
    DataUpdateTaskLogExt ext;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataUpdateTaskLogExt {
        /**
         * 任务处理中间状态
         */
//        @Builder.Default
//        Map<DeliveryMode, DataAssetPrepareStatus> processStatus = new HashMap<>();
        /**
         * 任务failed的失败原因
         */
        String processResultMessage;
    }

    /**
     * 数据更新方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "data_update_type", nullable = false)
    private UpdateWay dataUpdateType;
}