package com.ailpha.ailand.dataroute.endpoint.common.utils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class CommandSecurityFilter {

    // 危险字符
    private static final Set<Character> DANGEROUS_CHARS = new HashSet<>(Arrays.asList(
            '`', ';', '&', '|', '$', '(', ')', '{', '}', '<', '\n', '"', '!', '*'
    ));

    public static Boolean validateSafety(String input) {
        if (input == null) {
            return true;
        }

        for (char c : input.toCharArray()) {
            if (DANGEROUS_CHARS.contains(c)) {
                return false;
            }
        }

        return true;
    }

    public static void main(String[] args) {
        if (!validateSafety("sed -i 's/\\[16312593525199267e6748754452a9031040f0f4d42df\\]//g' test.log")) {
            System.out.println("检测到危险字符");
        }

        if (!validateSafety("grep -a '\\[16312593525199267e6748754452a9031040f0f4d42df\\]'  > test.log")) {
            System.out.println("检测到危险字符");
        }

        if (!validateSafety("dangerous;text")) {
            System.out.println("检测到危险字符");
        }

        System.out.println("pass");
    }
}