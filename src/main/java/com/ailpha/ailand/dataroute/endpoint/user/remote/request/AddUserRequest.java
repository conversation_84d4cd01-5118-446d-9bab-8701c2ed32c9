package com.ailpha.ailand.dataroute.endpoint.user.remote.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AddUserRequest extends BaseRemoteRequest {
    @Schema(description = "账号")
    String account;
    @Schema(description = "姓名")
    String name;
    @Schema(description = "手机号")
    String mobile;
    @Schema(description = "邮箱")
    String email;
    String password;
//    @JsonIgnore
//    Long companyId;
    @JsonIgnore
    String schema;
    @JsonIgnore
    String loginUrl;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date expireDate;
    @JsonIgnore
    Boolean companyAdmin = false;

    String userType;
    String entityId;
    String nodeId;
}
