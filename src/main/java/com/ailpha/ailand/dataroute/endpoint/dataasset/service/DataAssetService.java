package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DataAssetService {

    /**
     * 在不知道数据资产是资源还是产品的情况下，根据id获取dataAsset
     *
     * @param assetId
     * @return
     */
    DataAsset getLocalDataAssetById(String assetId);

    /**
     * 上传数据资产文件
     *
     * @param file 数据资产文件
     * @return 数据资产文件ID
     */
    DataAssetFileUploadResponse uploadFile(MultipartFile file) throws IOException;

    DataAssetFileUploadResponse uploadModelFile(MultipartFile file);

    DataAssetFileUploadResponse exploreFile(String filepath) throws IOException;

    /**
     * 上传调试数据集
     *
     * @param datasetId         数据集ID
     * @param file              文件流
     * @param separator         文件分割符号
     * @param hasHeader         是否含有表头
     * @param dataStructureType 数据结构类型
     */
    DebugFileUploadResponse uploadDebugDataReturnSchemaAndExampleData(String datasetId, MultipartFile file, String separator, Integer hasHeader, String dataStructureType) throws Exception;

    /**
     * 将数据资产作为文件下载
     *
     * @param dataProductPlatformId 数据资产ID
     * @param accessKey
     * @param secretKey
     * @param response    响应
     * @return 是否是当前连接器的数据资产
     */
    boolean download(AssetType assetType, String apiId, String companyId,String dataProductPlatformId, String accessKey, String secretKey, boolean dispatch, HttpServletResponse response);

    /**
     * 获取数据源配置字段
     *
     * @return 数据源配置字段
     */
    String datasourceProperties() throws IOException;

    ApiImportFileVO apiImportUploadBatchParamsFile(MultipartFile file, String separator, Integer hasHeader) throws Exception;

    String datasourceFileType() throws Exception;

    void checkApiImportUrl(String url);

    void downloadBatchParamsFile(AssetType assetType, String dataAssetId, String routerId, HttpServletRequest request, HttpServletResponse response) throws IOException;

    List<List<String>> previewBatchParams(AssetType assetType, String dataAssetId, String routerId);

    SimpleLocalFileVO uploadKerberosKeytabFile(MultipartFile file);

    SimpleLocalFileVO uploadKerberosConfFile(MultipartFile file);

    void downloadDownloadDebugFile(AssetType assetType, String assetId, HttpServletResponse response);

    String uploadAttachFile(MultipartFile file);

    String callDataApiProcess(AssetType assetType, String deliverId, String companyId, String dataAssetId, ApiImportTestVO apiImportTestVO, boolean dispatch);

    String callApi(ApiImportTestVO apiImportTestVO, boolean dispatch);
}
