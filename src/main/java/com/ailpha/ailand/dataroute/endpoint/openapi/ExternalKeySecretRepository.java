package com.ailpha.ailand.dataroute.endpoint.openapi;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * 2022/8/30
 */
public interface ExternalKeySecretRepository extends JpaRepository<ExternalPlatformAppKeySecret, Long>, QuerydslPredicateExecutor<ExternalPlatformAppKeySecret> {

    List<ExternalPlatformAppKeySecret> findByPlatformType(PlatformType platformType);

    List<ExternalPlatformAppKeySecret> findByPlatformId(String platformId);

    ExternalPlatformAppKeySecret findFirstByAppKeyAndPlatformType(String appKey, PlatformType platformType);

}
