package com.ailpha.ailand.dataroute.endpoint.logo.entity;

import com.ailpha.ailand.dataroute.endpoint.logo.request.LogoConfigRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;

import java.util.Date;

@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_custom_logo")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CustomLogo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "bigint COMMENT 'id'", updatable = false, nullable = false, unique = true)
    Long id;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "config_ext", columnDefinition = "json")
    LogoConfigRequest configExt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @CreatedDate
    @Column(name = "update_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    Date updateTime;

    /**
     CREATE TABLE "t_custom_logo" (
     "id" SERIAL	PRIMARY KEY,
     "config_ext" json NOT NULL DEFAULT '{}'::json,
     "create_time"   TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
     "update_time" timestamp
     );

     -- 添加注释
     COMMENT ON TABLE "t_custom_logo" IS 'Logo配置表';
     COMMENT ON COLUMN "t_custom_logo"."id" IS '主键ID';
     COMMENT ON COLUMN "t_custom_logo"."create_time" IS '创建时间';
     COMMENT ON COLUMN "t_custom_logo"."update_time" IS '更新时间';
     */
}