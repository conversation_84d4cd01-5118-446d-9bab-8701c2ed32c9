package com.ailpha.ailand.dataroute.endpoint.entity;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginApiTypeEnum;
import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_blockchain_plugin_detail")
public class BlockchainPluginDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 插件配置名称
     */
    @Column(length = 200)
    private String name;

    /**
     * 插件配置描述
     */
    @Column(length = 200)
    private String description;

    /**
     * 插件文件路径 groovy 脚本内容, jar 文件的地址
     */
    @Column
    private String ext;

    @Column(columnDefinition = "INTEGER COMMENT '0-未启用，1-启用，-1-删除'")
    private Integer status;

    @Column(updatable = false, name = "create_time")
    private LocalDateTime createTime;

    @Enumerated(EnumType.STRING)
    private BlockchainPluginTypeEnum type;

    /**
     * TrackingModuleType
     */
    @Column(length = 200, name = "module_type")
    private String moduleType;

    @OneToMany(mappedBy = "blockchainPluginDetail")
    @JsonIgnore
    @ToString.Exclude
    @EqualsAndHashCode.Exclude
    List<BlockchainPluginApiDetail> blockchainPluginApiDetails;

    public String doGetUrl() {
        if (CollectionUtils.isEmpty(blockchainPluginApiDetails))
            return null;
        Optional<BlockchainPluginApiDetail> optional = blockchainPluginApiDetails.stream().filter(item -> BlockchainPluginApiTypeEnum.UP_BLOCKCHAIN.equals(item.getType())).findFirst();
        return optional.orElse(new BlockchainPluginApiDetail()).getUrl();
    }
}