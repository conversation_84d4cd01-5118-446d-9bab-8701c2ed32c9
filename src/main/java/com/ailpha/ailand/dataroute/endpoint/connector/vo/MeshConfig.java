package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

@Data
public class MeshConfig {
    Client client;
    Mesh mesh;

    @Data
    public static class Mesh {
        @Alias("registry_info")
        RegistryInfo registry_info;
        @Alias("local_registry_info")
        LocalRegistryInfo local_registry_info;
    }

    @Data
    public static class Client {
        Logger logger;
    }

    @Data
    public static class Logger {
        String level;
        String path;
        String name;
    }

    @Data
    public static class RegistryInfo {
        String type = "组网";
        String vip;
        String mask;
        String all_sub_net;
        List<Pop> pops;
    }

    @Data
    public static class Pop {
        String id;
        String ip;
        String port;
    }

    @Data
    public static class LocalRegistryInfo {
        String companyId;
        String certId;
        String devId;
        String centralIP;
    }
}
