package com.ailpha.ailand.dataroute.endpoint.order.vo.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderIdReq implements Serializable {

    @NotEmpty(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    String orderId;
}
