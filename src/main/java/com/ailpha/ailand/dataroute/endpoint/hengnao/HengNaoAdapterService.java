package com.ailpha.ailand.dataroute.endpoint.hengnao;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.hengnao.request.AgentTaskMessagesRequest;
import com.ailpha.ailand.dataroute.endpoint.hengnao.request.HengNaoAsyncTaskSubmitRequest;
import com.ailpha.ailand.dataroute.endpoint.hengnao.response.AgentMessages;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.RequiredArgsConstructor;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class HengNaoAdapterService {

    private final HengNaoRemoteService hengNaoRemoteService;

    /**
     * 上传文件到恒脑
     */
    public String uploadFile(MultipartFile file) {
        try {
            RequestBody requestBody = RequestBody.create(file.getBytes(), MediaType.parse("text/csv"));
            MultipartBody.Part part = MultipartBody.Part.createFormData("file", file.getOriginalFilename(), requestBody);
            CommonResult<String> result = hengNaoRemoteService.uploadFile(part);
            if (result == null || !"0".equals(String.valueOf(result.getCode()))) {
                throw new RuntimeException("恒脑文件上传失败: " + (result != null ? result.getMessage() : "null"));
            }
            return result.getData();
        } catch (Exception e) {
            throw new RestfulApiException("上传文件到恒脑失败", e);
        }
    }

    /**
     * 提交异步任务
     */
    public String submitAsyncTask(String agentId, Map<String, Object> inputs) {
        HengNaoAsyncTaskSubmitRequest req = new HengNaoAsyncTaskSubmitRequest();
        req.setId(agentId); // 这里id为智能体ID，需根据业务传递
        req.setInputs(inputs);
        CommonResult<String> result = hengNaoRemoteService.submitAsyncTask(req);
        if (result == null || !"0".equals(String.valueOf(result.getCode()))) {
            throw new RestfulApiException("恒脑任务提交失败: " + (result != null ? result.getMessage() : "null"));
        }
        return result.getData();
    }

    /**
     * 查询异步任务状态
     */
    public Integer queryAsyncTaskStatus(String taskId) {
        QueryAsyncTaskStatusRequest req = new QueryAsyncTaskStatusRequest();
        req.setTaskId(taskId);
        CommonResult<Integer> result = hengNaoRemoteService.queryAsyncTaskStatus(req);
        if (result == null || !"0".equals(String.valueOf(result.getCode()))) {
            throw new RestfulApiException("恒脑任务状态查询失败: " + (result != null ? result.getMessage() : "null"));
        }
        return result.getData();
    }

    /**
     * 查询异步任务结果
     */
    public AgentMessages queryAsyncTaskMessages(String taskId) {
        AgentTaskMessagesRequest req = new AgentTaskMessagesRequest();
        req.setTaskId(taskId);
        req.setFull(true);
        CommonResult<AgentMessages> result = hengNaoRemoteService.queryAsyncTaskMessages(req);
        if (result == null || !"0".equals(String.valueOf(result.getCode()))) {
            throw new RuntimeException("恒脑任务结果查询失败: " + (result != null ? result.getMessage() : "null"));
        }
        return result.getData();
    }
}
