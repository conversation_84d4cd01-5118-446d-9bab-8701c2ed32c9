package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataAssetRevokeVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceUpdateVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.CatalogQueryDataResource;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetRevokeVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetSavedVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetUpdateVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface HubShuHanDataResourceApi {

    /**
     * 数据资源目录查询接口
     *
     * @param catalogQuery 数据资源目录查询参数
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataCatalogQuery")
    ShuhanResponse<IPage<CatalogQueryDataResource>> dataCatalogQuery(@RequestBody CatalogQueryVM catalogQuery);

    /**
     * 数据资源登记接口
     *
     * @param registRequest
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataResourceRegistry")
    ShuhanResponse<DataAssetSavedVO> dataResourceRegistry(@RequestBody DataResourceSaveVM registRequest);

    /**
     * 数据资源登记更新接口
     *
     * @param dataResourceUpdate
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataResourceRegistryUpdate")
    ShuhanResponse<DataAssetUpdateVO> dataResourceRegistryUpdate(@RequestBody DataResourceUpdateVM dataResourceUpdate);

    /**
     * 数据资源登记撤销接口
     *
     * @param dataAssetRevoke 数据标识
     * @return
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/dataResource/dataResourceRegistryRevoke")
    ShuhanResponse<DataAssetRevokeVO> dataResourceRegistryRevoke(@RequestBody DataAssetRevokeVM dataAssetRevoke);

}
