package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataAssetRevokeVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceUpdateVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.ResolutionRequest;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetRevokeVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetSavedVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetUpdateVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.ResolutionResponseDataResource;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface HubShuHanDataResourceApi {

    /**
     * 数据资源登记接口
     *
     * @param registRequest
     * @return
     */
    @PostExchange("/dataResourceRegistry")
    ShuhanResponse<DataAssetSavedVO> dataResourceRegistry(@RequestBody DataResourceSaveVM registRequest);

    /**
     * 数据资源登记更新接口
     *
     * @param dataResourceUpdate
     * @return
     */
    @PostExchange("/dataResourceRegistryUpdate")
    ShuhanResponse<DataAssetUpdateVO> dataResourceRegistryUpdate(@RequestBody DataResourceUpdateVM dataResourceUpdate);

    /**
     * 数据资源登记撤销接口
     *
     * @param dataAssetRevoke 数据标识
     * @return
     */
    @PostExchange("/dataResourceRegistryRevoke")
    ShuhanResponse<DataAssetRevokeVO> dataResourceRegistryRevoke(@RequestBody DataAssetRevokeVM dataAssetRevoke);

    /**
     * 数据资源标识解析
     *
     * @param resolutionRequest 标识
     * @return 数据资源信息
     */
    @PostExchange("/regionNodeResolution")
    ShuhanResponse<ResolutionResponseDataResource> regionNodeResolutionDataResource(@RequestBody ResolutionRequest resolutionRequest);
}
