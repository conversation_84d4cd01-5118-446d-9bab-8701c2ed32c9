package com.ailpha.ailand.dataroute.endpoint.openapi;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.dbapp.rest.openapi.AppInfo;
import com.dbapp.rest.openapi.AppToken;
import com.dbapp.rest.openapi.IOpenApiToken;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.utils.DasApiUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("openapi")
@Deprecated
public class OpenAPITokenController implements IOpenApiToken {

    @Value("${data-route.endpoint.appKey}")
    private String endpointKey;

    @Value("${data-route.endpoint.appSecret}")
    private String endpointSecret;

    @GetMapping("/token")
    public ApiResponse<AppToken> appTokens(@RequestHeader String appKey,
                                           @RequestHeader("timestamp") String timestamp,
                                           @RequestHeader("sign") String sign) {
        return defaultAppTokens(appKey, timestamp, sign);
    }

    @Override
    public Long getRequestTimeExpire() {
        return IOpenApiToken.super.getRequestTimeExpire();
    }

    @Override
    public AppInfo getAppInfo(String appId) {
        if (StringUtils.equals(endpointKey, appId)) {
            return new AppInfo(endpointKey, endpointSecret);
        }
        String appSecret = BaseCapabilityManager.PLATFORM_APP_KEY_SECRET_UNWRAPPED_MAP.get(appId);
        return ObjectUtils.isEmpty(appSecret) ? null : new AppInfo(appId, appSecret);
    }

    public static final byte[] key = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();

    @Override
    public AppToken getAndSaveToken(String appId) {
        String token = DasApiUtil.generateRandomString(32);
        long expireTime = System.currentTimeMillis() + this.getRequestTimeExpire();
        String realToken = SecureUtil.aes(key).encryptHex(token + "_" + expireTime);
        return new AppToken(realToken, expireTime);
    }
}
