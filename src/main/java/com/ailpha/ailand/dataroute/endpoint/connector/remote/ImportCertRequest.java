package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ImportCertRequest {
    ConnectorIdentity connectorIdentity;
    SubjectIdentity subjectIdentity;

    @Data
    public static class ConnectorIdentity {
        String keyId;
        String routeCertificate;
    }

    @Data
    public static class SubjectIdentity {
        String keyId;
        String certificate;
        String privateKey;
    }
}
