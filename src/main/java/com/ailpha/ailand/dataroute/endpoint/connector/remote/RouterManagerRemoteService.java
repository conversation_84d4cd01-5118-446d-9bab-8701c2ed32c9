package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.DataRouterManagerInterceptor;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.*;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorChangeResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorRegionNodeResolutionResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.IdentityProviderListResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.LoginResponse;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = DataRouterManagerInterceptor.class)
public interface RouterManagerRemoteService {
    @POST("/SyncConnectorInfo")
    CommonResult<ConnectorChangeResponse> activate(@Body RegisterConnectorToHubRequest request);

    @PUT("/gateway/shuhan-business-service/api/drClientInfo/remote/edit")
    CommonResult<Boolean> updateRouter(@Body UpdateRouterInfoRequest request);

    @POST("/regionNodeResolution")
    CommonResult<ConnectorRegionNodeResolutionResponse> regionNodeResolution(@Body RegionNodeResolutionRequest request, @Header("metaData") String metaData);

    @GET("/gateway/file-center-service/api/pub/remote/getHttpsFileUrl")
    CommonResult<String> getHttpsFileUrl(@Query("filepath") String filepath);

    @POST("/heartBeat")
    CommonResult<Void> heartbeat(@Body StandardBaseRequest<RouterHeartBeatRequest> request);

    @POST("/infoReport")
    CommonResult<Void> infoReport(@Body StandardBaseRequest<List<RouterInfoReportRequest>> request, @Header("metaData") String metaData);

    @GET("/gateway/auth/api/route/verifyToken")
    CommonResult<Boolean> checkToken(@Query("token") String token, @Header("hubInfo") String hubInfo);

    @POST("/gateway/api/ljqLogin")
    CommonResult<LoginResponse> login(@Body LoginRequest request);

    @POST("/GetIdentityProviderList")
    CommonResult<IdentityProviderListResponse> getIdentityProviderList(@Body GetIdentityProviderListRequest request);

    @POST("/identityVerify")
    CommonResult<GetNonceResponse> getNonce(@Body GenerateUuidRequest request, @Header("metaData") String metaData);

    @POST("/identityVerifyNonce")
    CommonResult<TokenResponse> getToken(@Body GenerateTokenRequest request, @Header("metaData") String metaData);
}
