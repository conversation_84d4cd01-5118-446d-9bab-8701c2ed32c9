package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.DataRouterManagerInterceptor;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.LegalIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.OperatorIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.PersonIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.*;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorChangeResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.LoginResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ReginNodeResolution;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = DataRouterManagerInterceptor.class)
public interface RouterManagerRemoteService {
    @POST("/gateway/shuhan-business-service/api/drClientInfo/remote/SyncConnectorInfo")
    CommonResult<ConnectorChangeResponse> activate(@Body RegisterConnectorToHubRequest request);

    @PUT("/gateway/shuhan-business-service/api/drClientInfo/remote/edit")
    CommonResult<Boolean> updateRouter(@Body UpdateRouterInfoRequest request);

    @GET("/gateway/shuhan-business-service/api/drClientInfo/remote/getByClientNo/{clientNo}")
    CommonResult<DrClientInfoVO> getByClientNo(@Path("clientNo") String clientNo);

    @GET("/gateway/file-center-service/api/pub/remote/getHttpsFileUrl")
    CommonResult<String> getHttpsFileUrl(@Query("filepath") String filepath);

    @POST("/gateway/shuhan-business-service/api/heartbeat/remote/reportMonitorInfo")
    CommonResult<Boolean> heartbeat(@Body RouterHeartBeatRequest request);

    @GET("/gateway/auth/api/route/verifyToken")
    CommonResult<Boolean> checkToken(@Query("token") String token);

    //    @POST("/gateway/api/ljqLogin")
    @POST("/prod-api/data-base/open/entity/getUserToken")
    CommonResult<LoginResponse> login(@Body LoginRequest request);

    @POST("/prod-api/data-base/open/equipment/regionNodeResolution")
    CommonResult<ReginNodeResolution> reginNodeResolution(@Body ReginNodeResolutionRequest request);

    @POST("/prod-api/data-base/open/equipment/connectorChange")
    CommonResult<ConnectorChangeResponse> connectorChangeReport(@Body ConnectorChangeRequest request);

    @POST("/prod-api/data-base/open/entity/legalIdentity/query")
    CommonResult<LegalIdentityQuery> queryLegalIdentity(@Body QueryIdentityRequest request);

    @POST("/prod-api/data-base/open/entity/operatorIdentity/query")
    CommonResult<OperatorIdentityQuery> operatorIdentityQuery(@Body QueryIdentityRequest request);

    @POST("/prod-api/data-base/open/entity/personIdentity/query")
    CommonResult<PersonIdentityQuery> personIdentityQuery(@Body QueryIdentityRequest request);
}
