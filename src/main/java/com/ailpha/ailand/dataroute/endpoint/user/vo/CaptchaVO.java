package com.ailpha.ailand.dataroute.endpoint.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2022/2/10
 * @description
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CaptchaVO {
    @Schema(description = "验证码 id")
	String captchaId;

    @Schema(description = "验证码图片 BASE64")
	String captchaBASE64;
}
