package com.ailpha.ailand.dataroute.endpoint.util;

import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/27 13:34
 */
@Slf4j
public class DockerNetworkUtils {


    /**
     * docker容器ip
     *
     * @param containerName 容器名称
     */
    public static String dockerVirtualIp(String containerName) {
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (!osInfo.isLinux()) {
            log.warn("当前系统非 Linux OS");
            return "127.0.0.1";
        } else {
            String localIp;
            try {
                localIp = RuntimeUtil.execForStr("docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' " + containerName);
            } catch (Exception e) {
                log.error("获取当前API网关IP地址异常：" + e);
                localIp = "127.0.0.1";
            }
            return localIp.replaceAll("\\n", "").replaceAll("'","");
        }
    }
}