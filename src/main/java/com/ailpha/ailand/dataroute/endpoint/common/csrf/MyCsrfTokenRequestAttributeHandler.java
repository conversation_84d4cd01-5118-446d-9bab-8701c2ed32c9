package com.ailpha.ailand.dataroute.endpoint.common.csrf;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.security.web.csrf.CsrfTokenRequestAttributeHandler;
import org.springframework.util.Assert;

public class MyCsrfTokenRequestAttributeHandler extends CsrfTokenRequestAttributeHandler {
    @Override
    public String resolveCsrfTokenValue(HttpServletRequest request, CsrfToken csrfToken) {
        Assert.notNull(request, "request cannot be null");
        Assert.notNull(csrfToken, "csrfToken cannot be null");
        CsrfToken token = (CsrfToken) request.getAttribute(csrfToken.getParameterName());
        if (token == null)
            return null;
        return token.getToken();
    }
}
