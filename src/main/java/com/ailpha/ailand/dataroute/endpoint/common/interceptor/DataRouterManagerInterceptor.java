package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.third.input.TokenBaseRequest;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataRouterManagerInterceptor extends BasePathMatchInterceptor {
    private final List<String> EXCLUDE_GET_TOKEN_URLS = List.of("/identityVerify", "/identityVerifyNonce");

    private final List<String> old_interface_urls = List.of("/gateway/api/ljqLogin", "/**/saveDataRouteUser", "/**/reset", "/**/remote/updateDataRouteUser");

    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        NodeDTO.HubInfo hubInfo;
        if (LoginContextHolder.isLogin() && !ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN)) {
            hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo(LoginContextHolder.currentUser().getCompany().getId());
        } else {
            hubInfo = getHubInfo(request);
        }
        Assert.isTrue(ObjectUtil.isNotNull(hubInfo) && StringUtils.isNotEmpty(hubInfo.getUrl()), "请求功能节点异常：功能节点配置参数不能为空");
        URL url = URLUtil.url(hubInfo.getUrl());

        Boolean skipGetToken = EXCLUDE_GET_TOKEN_URLS.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()));
        Request.Builder newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request, BaseCapabilityType.SHU_HAN, hubInfo.getCertificate(), skipGetToken, hubInfo);

        String newPath = url.getPath() + request.url().encodedPath();
        if (old_interface_urls.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()))) {
            newPath = newPath.replace("/gateway/shuhan-business-service/api/openapi", "");
        }

        HttpUrl httpUrl = request.url().newBuilder()
                .encodedPath(newPath)
                .host(url.getHost())
                .port(url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        newRequest.url(httpUrl)
                .method(request.method(), request.body())
                .tag(request.tag());
        Response response = chain.proceed(newRequest.build());
        return OpenApiHttpUtil.doInterceptForResp(chain, newRequest, response, skipGetToken, hubInfo.getCertificate(), BaseCapabilityType.SHU_HAN, BaseCapabilityType.SHU_HAN.name(), "", "", hubInfo);
    }

    private NodeDTO.HubInfo getHubInfo(Request request) {
        if (StringUtils.equals(request.method(), "POST")) {
            if (request.body() == null)
                return null;
            try {
                Buffer buffer = new Buffer();
                request.body().writeTo(buffer);
                if (EXCLUDE_GET_TOKEN_URLS.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()))) {
                    return JSONUtil.toBean(buffer.readUtf8(), TokenBaseRequest.class).getHubInfo();
                } else
                    return JSONUtil.toBean(buffer.readUtf8(), BaseRemoteRequest.class).getHubInfo();
            } catch (IOException e) {
                return null;
            }
        } else if (StringUtils.equals(request.method(), "GET")) {
            String hubInfo = request.header("hubInfo");
            if (StringUtils.isEmpty(hubInfo)) {
                return SpringUtil.getBean(CompanyService.class).getHubInfo();
            }
            return JSONUtil.toBean(new String(Base64.getDecoder().decode(hubInfo), StandardCharsets.UTF_8), NodeDTO.HubInfo.class);
        } else
            throw new RestfulApiException("暂不支持该请求类型获取功能节点信息");

    }
}
