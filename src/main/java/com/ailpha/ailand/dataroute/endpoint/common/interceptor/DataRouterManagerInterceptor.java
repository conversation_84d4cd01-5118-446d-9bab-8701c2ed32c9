package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SignUtil;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URL;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataRouterManagerInterceptor extends BasePathMatchInterceptor {

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        NodeDTO.HubInfo hubInfo;
        if (request.url().toString().contains("/prod-api/data-base/open/equipment")) {
            if (LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN)) {
                hubInfo = LoginContextHolder.currentUser().getCompany().getServiceNode().getHubInfo();
            } else {
                hubInfo = JSONUtil.toBean(MDC.get("hubInfo"), NodeDTO.HubInfo.class);
            }
        } else {
            NodeDTO serviceFirstNode = SpringUtil.getBean(NodeService.class).getFirstNode();
            hubInfo = serviceFirstNode.getHubInfo();
        }
        Assert.isTrue(StringUtils.isNotEmpty(hubInfo.getUrl()), "请先添加功能节点");
        URL url = URLUtil.url(hubInfo.getUrl());

//        Request.Builder newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request,
//                String.format("%s://%s:%s", url.getProtocol(), url.getHost(), url.getPort()) + "/gateway/auth/api/route/openapi/token",
//                BaseCapabilityType.SHU_HAN, hubInfo.getAk(), hubInfo.getSk());

        Map<String, Object> paramMap = new LinkedHashMap<>();
        if (request.body() != null) {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            String bodyStr = buffer.readUtf8();

            // 将请求体JSON转为Map并合并到paramMap
            Map<String, Object> bodyMap = JSONUtil.toBean(bodyStr, new TypeReference<Map<String, Object>>() {
            }.getType(), true);
            paramMap.putAll(bodyMap);
        }
        String timeMillis = String.valueOf(System.currentTimeMillis());
        String nonceStr = SignUtil.getRandomString(32);
        paramMap.put(SignUtil.ACCESS_KEY, hubInfo.getAk());
        paramMap.put(SignUtil.TIMESTAMP, timeMillis);
        paramMap.put(SignUtil.NONCE, nonceStr);

        // 计算签名
        String sign = SignUtil.createSign(paramMap, hubInfo.getSk());
        paramMap.put(SignUtil.SIGN, sign);

        // 创建新的请求体
        String newBodyJson = JSONUtil.toJsonStr(paramMap);
        RequestBody newBody = RequestBody.create(
                newBodyJson,
                MediaType.parse("application/json; charset=utf-8")
        );
        Request.Builder newRequest = request.newBuilder();
        HttpUrl httpUrl = request.url().newBuilder()
                .host(url.getHost())
                .port(url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        newRequest.url(httpUrl)
//                .header("hubInfo", Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(hubInfo).getBytes(StandardCharsets.UTF_8)))
//                .header("nodeId", routerService.currentNode().getPlatformId())
                .method(request.method(), newBody)
                .header("Content-Type", "application/json")
                .tag(request.tag());
        return chain.proceed(newRequest.build());
    }
}
