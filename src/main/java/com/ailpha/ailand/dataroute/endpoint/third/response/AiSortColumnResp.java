package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiSortColumnResp implements Serializable {

    @Schema(description = "AiGuard平台数据源id")
    String aiguardId;
    @Schema(description = "识别字段id")
    Long catalogColumnId;
    @Schema(description = "识别字段名称")
    String catalogColumnName;
    @Schema(description = "分类id")
    Long classifyId;
    @Schema(description = "分类名称")
    String classifyName;
    @Schema(description = "是否折叠, 暂时不起作用")
    Boolean collapse;
    @Schema(description = "别名")
    String columnAlias;
    @Schema(description = "列长度")
    Long columnLength;
    @Schema(description = "字段名")
    String columnName;
    @Schema(description = "字段类型")
    String columnType;
    @Schema(description = "是否梳理")
    Boolean combing;
    @Schema(description = "恒脑识别说明")
    String combingExplain;
    @Schema(description = "注释")
    String comment;
    @Schema(description = "-")
    String comments;
    @Schema(description = "字段确认状态")
    Boolean confirmStatus;
    @Schema(description = "数据库名")
    String dbName;
    @Schema(description = "-")
    String defaultValue;
    @Schema(description = "金融数据")
    String financialDataLevel;
    @Schema(description = "id")
    Long id;
    @Schema(description = "重要数据定级")
    String importantDataLevel;
    @Schema(description = "是否命中")
    Boolean isHit;
    @Schema(description = "-")
    Boolean isNew;
    @Schema(description = "是否敏感")
    Boolean isSensitive;
    @Schema(description = "-")
    String length;
    @Schema(description = "级别名称")
    String level;
    @Schema(description = "级别id")
    Long levelId;
    @Schema(description = "敏感等级标签")
    String levelLabel;
    @Schema(description = "-")
    Boolean majorKey;
    @Schema(description = "是否延迟打标")
    Boolean markingDelay;
    @Schema(description = "-")
    Boolean nullable;
    @Schema(description = "-")
    Long ordinalPosition;
    @Schema(description = "规则id")
    Long ruleId;
    @Schema(description = "规则名称")
    String ruleName;
    @Schema(description = "schema名")
    String schemaName;
    @Schema(description = "敏感数据标签")
    String sensitiveTag;
    @Schema(description = "敏感数据标签id")
    Long sensitiveTagId;
    @Schema(description = "-")
    Long sourceId;
    @Schema(description = "-")
    String systemCode;
    @Schema(description = "-")
    String systemName;
    @Schema(description = "表注释")
    String tableDesc;
    @Schema(description = "-")
    Long tableId;
    @Schema(description = "表名")
    String tableName;
    @Schema(description = "最后梳理时间")
    String updateTime;
}
