package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataAssetFileUploadResponse {
    @Schema(description = "数据资产文件临时id")
    String dataAssetFileId;
    @Schema(description = "数据集结构")
    List<DataSchemaBO> dataSchema;
}
