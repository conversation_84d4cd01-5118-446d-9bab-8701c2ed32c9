package com.ailpha.ailand.dataroute.endpoint.order.remote.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:22
 */
@Data
public class OrderInfo {
    private String orderNo;           // 订单标识
    private String srcPlatformId;     // 来源平台标识
    private String srcPlatformOrderNo;// 来源平台订单标识
    private String itemName;          // 商品名称
    private String srcProductId;       // 来源产品标识 ⭐️ 确定订单绑定的产品id 和 连接器平台的id有什么映射关系 ⭐️
    private String srcProductName;     // 来源产品名称
    private String industryType;       // 行业分类，详见附录《行业代码表》
    private String region;            // 地域分类，详见附录《全国区划表》
    private String productType;       // 产品类型
    private String producerId;       // 提供方标识
    private String consumerId;        // 使用方标识
    private String contractId;        // 合同 ID ⭐️ 一个订单一个合同： 一个订单一个场景交付 ⭐️
    private String dpe;               // 提供方连接器
    private String dce;               // 使用方连接器
    private String sku;               // 商品规格信息
    private Long amount;              // 金额,单位:分
    private String dataStrategy;       // 数据策略
    private String contractNo;        // 合同编号
    private String orderStatus;        // 订单状态
    private String createTime;        // 创建时间
    private String updateTime;        // 更新时间
    private String effectiveTime;     // 生效时间
    private String producerName;      // 提供主体名称
    private String consumerName;       // 使用主体名称
    private String platformId;        // 平台 id
    private String producerCode;      // 提供方编码
    private String consumerCode;      // 使用方编码


}
