package com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface DeliverySceneRepository extends JpaRepository<DeliveryScene, String>, QuerydslPredicateExecutor<DeliveryScene>, JpaSpecificationExecutor<DeliveryScene> {
    long countAllByDeliveryTypeIn(List<DeliveryType> deliveryTypeList);
}
