package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;


@EqualsAndHashCode(callSuper = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiListVO extends ContractOpenApiRequest {

    @Schema(description = "api id")
    String id;

    @Schema(description = "api url")
    String apiUrl;

    @Schema(description = "api 状态")
    APIStatusEnum status;

}
