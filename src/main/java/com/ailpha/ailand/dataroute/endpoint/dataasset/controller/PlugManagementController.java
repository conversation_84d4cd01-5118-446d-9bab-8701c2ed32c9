package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpModule;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.mapper.PluginDetailMapper;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.PlugApiService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.PlugDetailService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailPageVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailVO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiType;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import com.ailpha.ailand.dataroute.endpoint.user.service.LogService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.LogResponse;
import com.ailpha.ailand.dataroute.endpoint.user.vo.QueryLogRequest;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RequiredArgsConstructor
@RestController
@RequestMapping("/dataRoute/plug")
@Tag(name = "连接器-插件")
public class PlugManagementController {

    private final PlugDetailService plugDetailService;

    private final PlugApiService plugApiService;

    private final PluginDetailMapper pluginDetailMapper;

    private final LogService logService;

    @Operation(summary = "查询插件详情")
    @GetMapping(path = "/getById")
    public ApiResponse<PluginDetailVO> getById(@RequestParam(value = "id") Long id) {
        PluginDetailVO detail = plugDetailService.getById(id);
        return SuccessResponse.success(detail).build();
    }

    @Operation(summary = "分页查询插件")
    @PostMapping(path = "/pageByParam")
    public ApiResponse<List<PluginDetailPageVO>> pageByParam(
            @RequestBody @Validated PluginDetailPageRequest request) {
        return plugDetailService.pageByParam(request);
    }

    @Operation(summary = "查询插件列表")
    @GetMapping(path = "/listByParam")
    public ApiResponse<List<PluginDetailPageVO>> listByParam(
            @RequestParam("type") PluginApiTypeEnums type, @RequestParam("status") boolean status) {
        List<PluginDetail> pluginDetails = plugDetailService.listByParam(status, type);
        List<PluginDetailPageVO> res = pluginDetails.stream().map(pluginDetailMapper::toPluginDetailPageVO).toList();
        return SuccessResponse.success(res).build();
    }


    @Operation(summary = "获取插件接口清单列表")
    @GetMapping(path = "getPlugApiList")
    public ApiResponse<List<PluginApiType>> getPlugApiList(
            @RequestParam("type") PluginApiTypeEnums type) {
        return SuccessResponse.success(plugApiService.getPlugApiList(type)).build();
    }

    @Operation(summary = "查询数据资产插件列表")
    @PostMapping(path = "/getByDataAssertIds")
    public ApiResponse<List<PluginDetailPageVO>> getByDataAssertIds(
            @RequestBody PluginListRequest request) {
        List<PluginDetail> pluginDetails = plugDetailService.getByDataAssertIds(request.getDataAssertIds());
        List<PluginDetailPageVO> res = pluginDetails.stream().map(pluginDetailMapper::toPluginDetailPageVO).toList();
        return SuccessResponse.success(res).build();
    }

    @Operation(summary = "启用插件")
    @PostMapping(path = "enable")
    @OpLog(message = "启用插件，插件名称：{name}")
    public ApiResponse<Boolean> enable(@RequestBody @Validated PluginDetailRequest request) {
        OPLogContext.putOpType(InternalOpType.ENABLE_PLUGIN);
        OPLogContext.put("name", request.getName());
        request.setStatus(Boolean.TRUE);
        return ApiResponse.success(plugDetailService.updateStatus(request)).build();
    }

    @Operation(summary = "禁用插件")
    @PostMapping(path = "forbidden")
    @OpLog(message = "禁用插件，插件名称：{name}")
    public ApiResponse<Boolean> forbidden(@RequestBody @Validated PluginDetailRequest request) {
        OPLogContext.putOpType(InternalOpType.FORBIDDEN_PLUGIN);
        OPLogContext.put("name", request.getName());
        request.setStatus(Boolean.FALSE);
        return ApiResponse.success(plugDetailService.updateStatus(request)).build();
    }

    @Operation(summary = "新增插件")
    @PostMapping(path = "savePlug")
    @OpLog(message = "新增插件，插件名称：{name}")
    public ApiResponse<Boolean> savePlug(@RequestBody PluginDetailRequest request) {
        OPLogContext.putOpType(InternalOpType.ADD_PLUGIN);
        OPLogContext.put("name", request.getName());
        return ApiResponse.success(plugDetailService.savePlug(request)).build();
    }

    @Operation(summary = "编辑插件")
    @PostMapping(path = "updatePlug")
    public ApiResponse<Boolean> updatePlug(@RequestBody @Validated PluginDetailRequest request) {
        return ApiResponse.success(plugDetailService.updatePlug(request)).build();
    }

    @PostMapping("history")
    @Operation(summary = "操作历史")
    public SuccessResponse<List<LogResponse>> list(@RequestBody QueryLogRequest request) {
        request.setOpModule(String.valueOf(InternalOpModule.PLUGIN_MANAGER));
        return logService.query(request);
    }
}
