package com.ailpha.ailand.dataroute.endpoint.common.utils;

import cn.hutool.cache.CacheUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ConnectorMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.FunctionNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.third.input.TokenBaseRequest;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.constant.OpenApiConstant;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.AppToken;
import com.dbapp.rest.utils.DasApiUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import org.apache.commons.lang3.StringUtils;

import java.io.EOFException;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class OpenApiHttpUtil {
    private OpenApiHttpUtil() {
    }

    private static final cn.hutool.cache.Cache<String, AppToken> tokenCache = CacheUtil.newLRUCache(8);

    /**
     * 获取 token
     *
     * @param tokenUrl  获取token的接口地址
     * @param timestamp 时间辍
     * @param ak        服务端ak
     * @param sk        服务端sk
     * @return appToken
     */
    public static AppToken appToken(BaseCapabilityType type, String tokenUrl, Long timestamp, String ak, String sk) {
        //判断token是否在有效期内
        // todo：暂时移除校验
//        if (Objects.nonNull(appToken.getExpireTime()) && appToken.getExpireTime() - 5000 > timestamp) {
//            log.debug("使用原有的 token");
//            return appToken;
//        }
//        log.debug("重新获取 token");
        // 生成token需要的sign
        String sign = DasApiUtil.getSHA256Hash(timestamp + ak + sk);
        try (HttpResponse res = HttpRequest.get(tokenUrl)
                .header(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp))
                .header(OpenApiConstant.SIGN, sign)
                .header("appKey", ak)
                .execute()) {
            JSONObject jsonObject = JSONUtil.parseObj(res.body());
            log.debug("获取的 token = {}", jsonObject);
            if ((jsonObject.containsKey("code") && jsonObject.getInt("code") == -1) || jsonObject.containsKey("error")) {
                log.error(jsonObject.toString());
                switch (type) {
                    case MPC -> throw new RestfulApiException("MPC调用异常：请检查APPKey、APPSecret");
                    case TEE -> throw new RestfulApiException("TEE调用异常：请检查APPKey、APPSecret");
                    case SHU_HAN -> throw new RestfulApiException("数由空间管理服务调用异常：请检查APPKey、APPSecret");
                    case END_POINT -> throw new RestfulApiException("连接器调用异常：请检查APPKey、APPSecret");
                    case DATA_INVOICE -> throw new RestfulApiException("数据发票调用异常，请检查APPKey、APPSecret");
                }


            }
            return JSONUtil.toBean(jsonObject.getStr("data"), AppToken.class);
        }
    }

    public static synchronized AppToken appToken(BaseCapabilityType type, String certificate, NodeDTO.HubInfo hubInfo) {
        AppToken appToken = tokenCache.get(type.name());
        if (appToken == null || appToken.getExpireTime() < System.currentTimeMillis()) {
            appToken = SpringUtil.getBean(ThirdService.class).generateToken(type, certificate, hubInfo, "");
            tokenCache.put(type.name(), appToken);
        }
        return appToken;
    }

    public static synchronized AppToken appToken(BaseCapabilityType type, String clientId, String certificate, NodeDTO.HubInfo hubInfo, String metaData) {
        AppToken appToken = tokenCache.get(type.name() + "_" + clientId);
        if (appToken == null || appToken.getExpireTime() < System.currentTimeMillis()) {
            appToken = SpringUtil.getBean(ThirdService.class).generateToken(type, certificate, hubInfo, metaData);
            tokenCache.put(type.name() + "_" + clientId, appToken);
        }
        return appToken;
    }

    public static Request.Builder openApiHeadersForRetrofit(Request newRequest, String tokenUrl, BaseCapabilityType type, String ak, String sk) {
        RouterService routerService = SpringUtil.getBean(RouterService.class);
        long timestamp = System.currentTimeMillis();
        AppToken appToken = OpenApiHttpUtil.appToken(type, tokenUrl, timestamp, ak, sk);
        String nonce = DasApiUtil.generateRandomString(6);
        // 生成 openApi 需要的sign
        String signature = DasApiUtil.generateSign(DasApiUtil.concatSignString(newRequest.url().query()), String.valueOf(timestamp), appToken.getToken(), nonce);
        Request.Builder requestBuilder = newRequest.newBuilder()

                .header(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp))
                .header("akToken", appToken.getToken())
                .header(OpenApiConstant.NONCE, nonce)
                .header(OpenApiConstant.TOKEN, appToken.getToken())
                .header(OpenApiConstant.SIGN, signature);
//        if (LoginContextHolder.isLogin()) {
//            Cache<String, String> userCache = SpringUtil.getBean("userCache", Cache.class);
//            String loginToken = userCache.get(String.format(LoginController.HUB_LOGIN_TOKEN, LoginContextHolder.currentUser().getUsername()));
//            if (StringUtils.isNotEmpty(loginToken)) {
//                requestBuilder.header("Authorization", loginToken);
//            }
//            if (!ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.SUPER_ADMIN))
//                requestBuilder.header("routerId", LoginContextHolder.currentUser().getCompany().getNodeId());

//        }
        return requestBuilder;
    }

    private static TokenBaseRequest transformBodyToTokenRequest(Request request) {
        if (request.body() == null)
            return null;
        try {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            return JSONUtil.toBean(buffer.readUtf8(), TokenBaseRequest.class);
        } catch (IOException | JSONException e) {
            return null;
        }
    }

    public static Request.Builder openApiHeadersForRetrofit(Request newRequest, BaseCapabilityType type, String nodeId,
                                                            NodeDTO.HubInfo hubInfo, Boolean skipGetToken, String serviceNodeUrl,
                                                            String targetSchema) {
        long timestamp = System.currentTimeMillis();
        String metaData = "";
        if (type == BaseCapabilityType.END_POINT) {
            ConnectorMetaData connectorMetaData = new ConnectorMetaData();
            connectorMetaData.setSchema(targetSchema);
            connectorMetaData.setTargetNodeId(serviceNodeUrl);
            metaData = connectorMetaData.toBase64();
        } else if (type == BaseCapabilityType.SERVICE_NODE) {
            ServiceNodeMetaData serviceNodeMetaData = new ServiceNodeMetaData();
            serviceNodeMetaData.setUrl(serviceNodeUrl);
            serviceNodeMetaData.setNodeId(nodeId);
            metaData = serviceNodeMetaData.toBase64();
        }

        Request.Builder builder = newRequest.newBuilder();
        builder.header("nodeId", nodeId)
                .header("xTimestamp", String.valueOf(timestamp))
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("metaData", metaData);

        if (skipGetToken) {
            TokenBaseRequest tokenBaseRequest = transformBodyToTokenRequest(newRequest);
            return builder
                    .header("xRequestId", tokenBaseRequest == null || StringUtils.isEmpty(tokenBaseRequest.getRequestId()) ? UuidUtils.uuid32() : tokenBaseRequest.getRequestId())
                    ;
        }
        AppToken appToken = OpenApiHttpUtil.appToken(type, nodeId, hubInfo.getCertificate(), hubInfo, metaData);
        if (log.isTraceEnabled()) {
            log.trace("appToken = {}", appToken);
        }

        return builder
                .header("xRequestId", UuidUtils.uuid32())
                .header("authorization", "Bearer " + appToken.getToken())
                .header("Content-Type", "application/json;charset=UTF-8");
    }

    public static Request.Builder openApiHeadersForRetrofit(Request newRequest, BaseCapabilityType type, String certificate, Boolean skipGetToken, NodeDTO.HubInfo hubInfo) {
        long timestamp = System.currentTimeMillis();
        String nodeId;
        String metaData = newRequest.header("metaData");
        if (LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            nodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        } else {
            if (StringUtils.isNotEmpty(metaData)) {
                nodeId = ServiceNodeMetaData.fromBase64(metaData).getNodeId();
            } else
                nodeId = SpringUtil.getBean(CompanyService.class).getFirst().getNodeId();
        }
        if (skipGetToken) {
            TokenBaseRequest tokenBaseRequest = transformBodyToTokenRequest(newRequest);
            return newRequest.newBuilder()
                    .header("metaData", metaData == null ? "" : metaData)
                    .header("nodeId", nodeId)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("xRequestId", tokenBaseRequest == null || StringUtils.isEmpty(tokenBaseRequest.getRequestId()) ? UuidUtils.uuid32() : tokenBaseRequest.getRequestId())
                    .header("xTimestamp", String.valueOf(timestamp));
        }
        AppToken appToken = OpenApiHttpUtil.appToken(type, certificate, hubInfo);
        if (log.isTraceEnabled())
            log.trace("appToken = {}", appToken);
        // 生成 openApi 需要的sign
//        String signature = DasApiUtil.generateSign(DasApiUtil.concatSignString(newRequest.url().query()), String.valueOf(timestamp), appToken.getToken(), nonce);
        Request.Builder requestBuilder = newRequest.newBuilder();
//        MetaData metaData = new MetaData();
//        if (LoginContextHolder.isLogin()) {
//            metaData.setUserId(LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN) ? LoginContextHolder.currentUser().getIdShuhan() : "");

//        }
        return requestBuilder
                .header("metaData", metaData == null ? "" : metaData)
                .header("nodeId", nodeId)
                .header("Content-Type", "application/json;charset=UTF-8")
                .header("xRequestId", UuidUtils.uuid32())
                .header("xTimestamp", String.valueOf(timestamp))
                .header("authorization", "Bearer " + appToken.getToken());
//        return requestBuilder;
    }

    public static void main(String[] args) {
        FunctionNodeMetaData functionNodeMetaData = new FunctionNodeMetaData();
        functionNodeMetaData.setUserId("1937397850409304066");
        System.out.println(Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(functionNodeMetaData).getBytes(StandardCharsets.UTF_8)));
        String s = "eyJ1cmwiOiJodHRwOi8vMTAuNTAuMy4yMTk6ODEzMi9nYXRld2F5L2RhdGEtcm91dGUtYnVzaW5lc3Mtc2VydmljZS9hcGkvb3BlbmFwaSJ9";
        System.out.println(new String(Base64.getDecoder().decode(s)));
    }

    /**
     * Headers：生成 openApi 需要的sign
     */
    public static Map<String, String> generateSignHeaders(String tokenUrl, Map<String, String> params, String appKey, String appSecret) {
        long timestamp = System.currentTimeMillis();
        AppToken appToken = OpenApiHttpUtil.appToken(BaseCapabilityType.SHU_HAN, tokenUrl, timestamp, appKey, appSecret);
        String parameterConcat = DasApiUtil.concatSignString(params);
        String nonce = DasApiUtil.generateRandomString(6);
        String signString = parameterConcat + timestamp + appToken.getToken() + nonce;
        String signature = DasApiUtil.getSHA256Hash(signString);
        Map<String, String> headers = new HashMap<>();
        headers.put(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp));
        headers.put(OpenApiConstant.TOKEN, appToken.getToken());
        headers.put(OpenApiConstant.NONCE, nonce);
        headers.put(OpenApiConstant.SIGN, signature);
        return headers;
    }

    public static synchronized void clearTokenCache(String tokenKey) {
        tokenCache.remove(tokenKey);
    }

    public static Response doInterceptForResp(Interceptor.Chain chain, Request.Builder requestBuilder, Response response,
                                              Boolean skipGetToken, String certificate, BaseCapabilityType type,
                                              String tokenKey, String currentNodeId, String targetNodeId, NodeDTO.HubInfo hubInfo) throws IOException {
        ResponseBody responseBody = response.body();
        long contentLength = responseBody.contentLength();
        BufferedSource source = responseBody.source();
        source.request(Long.MAX_VALUE); // Buffer the entire body.
        Buffer buffer = source.buffer();
        if (!isPlaintext(buffer)) {
            log.info("<-- END HTTP (binary " + buffer.size() + "-byte body omitted)");
            return response;
        }
        if (contentLength != 0) {
            String result = buffer.clone().readString(Charset.defaultCharset());
            CommonResult<?> commonResult = JSONUtil.toBean(result, new TypeReference<>() {
            }, false);
            if (commonResult.unauthorized() && !skipGetToken) {
                OpenApiHttpUtil.clearTokenCache(tokenKey);
                // 重新获取token
                AppToken appToken;
                if (type == BaseCapabilityType.END_POINT || type == BaseCapabilityType.SERVICE_NODE) {
                    ConnectorMetaData connectorMetaData = new ConnectorMetaData();
                    connectorMetaData.setTargetNodeId(targetNodeId);
                    String metaDataBase64 = Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(connectorMetaData).getBytes(StandardCharsets.UTF_8));
                    appToken = OpenApiHttpUtil.appToken(BaseCapabilityType.END_POINT, currentNodeId, certificate, hubInfo, metaDataBase64);
                } else
                    appToken = OpenApiHttpUtil.appToken(type, certificate, hubInfo);
                requestBuilder.header("authorization", "Bearer " + appToken.getToken());
                response = chain.proceed(requestBuilder.build());
            }
        }
        return response;
    }

    static boolean isPlaintext(Buffer buffer) {
        try {
            Buffer prefix = new Buffer();
            long byteCount = buffer.size() < 64 ? buffer.size() : 64;
            buffer.copyTo(prefix, 0, byteCount);
            for (int i = 0; i < 16; i++) {
                if (prefix.exhausted()) {
                    break;
                }
                int codePoint = prefix.readUtf8CodePoint();
                if (Character.isISOControl(codePoint) && !Character.isWhitespace(codePoint)) {
                    return false;
                }
            }
            return true;
        } catch (EOFException e) {
            return false; // Truncated UTF-8 sequence.
        }
    }
}
