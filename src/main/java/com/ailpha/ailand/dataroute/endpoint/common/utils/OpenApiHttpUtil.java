package com.ailpha.ailand.dataroute.endpoint.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.user.controller.LoginController;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.constant.OpenApiConstant;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.AppToken;
import com.dbapp.rest.utils.DasApiUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class OpenApiHttpUtil {
    private OpenApiHttpUtil() {
    }

    /**
     * 获取 token
     *
     * @param tokenUrl  获取token的接口地址
     * @param timestamp 时间辍
     * @param ak        服务端ak
     * @param sk        服务端sk
     * @return appToken
     */
    public static AppToken appToken(BaseCapabilityType type, String tokenUrl, Long timestamp, String ak, String sk) {
        //判断token是否在有效期内
        // todo：暂时移除校验
//        if (Objects.nonNull(appToken.getExpireTime()) && appToken.getExpireTime() - 5000 > timestamp) {
//            log.debug("使用原有的 token");
//            return appToken;
//        }
//        log.debug("重新获取 token");
        // 生成token需要的sign
        String sign = DasApiUtil.getSHA256Hash(timestamp + ak + sk);
        try (HttpResponse res = HttpRequest.get(tokenUrl)
                .header(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp))
                .header(OpenApiConstant.SIGN, sign)
                .header("appKey", ak)
                .execute()) {
            JSONObject jsonObject = JSONUtil.parseObj(res.body());
            log.debug("获取的 token = {}", jsonObject);
            if ((jsonObject.containsKey("code") && jsonObject.getInt("code") == -1) || jsonObject.containsKey("error")) {
                log.error(jsonObject.toString());
                switch (type) {
                    case MPC -> throw new RestfulApiException("MPC调用异常：请检查APPKey、APPSecret");
                    case TEE -> throw new RestfulApiException("TEE调用异常：请检查APPKey、APPSecret");
                    case TRADE_PLATFORM -> throw new RestfulApiException("交易平台调用异常：请检查APPKey、APPSecret");
                    case SHU_HAN -> throw new RestfulApiException("数由空间管理服务调用异常：请检查APPKey、APPSecret");
                    case END_POINT -> throw new RestfulApiException("连接器调用异常：请检查APPKey、APPSecret");
                    case DATA_INVOICE -> throw new RestfulApiException("数据发票调用异常，请检查APPKey、APPSecret");
                }


            }
            return JSONUtil.toBean(jsonObject.getStr("data"), AppToken.class);
        }
    }

    public static Request.Builder openApiHeadersForRetrofit(Request newRequest, String tokenUrl, BaseCapabilityType type, String ak, String sk) {
//        RouterService routerService = SpringUtil.getBean(RouterService.class);
        long timestamp = System.currentTimeMillis();
//        AppToken appToken = OpenApiHttpUtil.appToken(type, tokenUrl, timestamp, ak, sk);
        String nonce = DasApiUtil.generateRandomString(6);
        // 生成 openApi 需要的sign
//        String signature = DasApiUtil.generateSign(DasApiUtil.concatSignString(newRequest.url().query()), String.valueOf(timestamp), "", nonce);
        Request.Builder requestBuilder = newRequest.newBuilder()

                .header(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp))
//                .header("akToken", appToken.getToken())
                .header(OpenApiConstant.NONCE, nonce);
//                .header(OpenApiConstant.TOKEN, appToken.getToken())
//                .header(OpenApiConstant.SIGN, signature);
        if (LoginContextHolder.isLogin()) {
            Cache<String, String> userCache = SpringUtil.getBean("userCache", Cache.class);
            String loginToken = userCache.get(String.format(LoginController.HUB_LOGIN_TOKEN, LoginContextHolder.currentUser().getUsername()));
            if (StringUtils.isNotEmpty(loginToken)) {
                requestBuilder.header("Authorization", loginToken);
            }
            if (LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN))
                requestBuilder.header("routerId", LoginContextHolder.currentUser().getCompany().getNodeId());

        }
        return requestBuilder;
    }

    /**
     * Headers：生成 openApi 需要的sign
     */
    public static Map<String, String> generateSignHeaders(String tokenUrl, Map<String, String> params, String appKey, String appSecret) {
        long timestamp = System.currentTimeMillis();
        AppToken appToken = OpenApiHttpUtil.appToken(BaseCapabilityType.SHU_HAN, tokenUrl, timestamp, appKey, appSecret);
        String parameterConcat = DasApiUtil.concatSignString(params);
        String nonce = DasApiUtil.generateRandomString(6);
        String signString = parameterConcat + timestamp + appToken.getToken() + nonce;
        String signature = DasApiUtil.getSHA256Hash(signString);
        Map<String, String> headers = new HashMap<>();
        headers.put(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp));
        headers.put(OpenApiConstant.TOKEN, appToken.getToken());
        headers.put(OpenApiConstant.NONCE, nonce);
        headers.put(OpenApiConstant.SIGN, signature);
        return headers;
    }
}
