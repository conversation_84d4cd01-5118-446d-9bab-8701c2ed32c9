package com.ailpha.ailand.dataroute.endpoint.common.aspect;

import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
@Order(1) // 确保该切面在其他切面之前执行
public class GlobalAspect {
    
    /**
     * 定义切点，拦截所有控制器方法
     * 这里使用了execution表达式，匹配所有controller包下的所有类的所有方法
     */
    @Pointcut("execution(* com.ailpha.ailand.dataroute.endpoint..*.controller..*.*(..))")
    public void allControllerMethods() {
    }
    
    /**
     * 环绕通知，在方法执行前后都可以处理
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 可能抛出的异常
     */
    @Around("allControllerMethods()")
    public Object aroundAdvice(ProceedingJoinPoint joinPoint) throws Throwable {
        try {
            // 执行目标方法
            return joinPoint.proceed();
        } finally {
            // 无论方法是否正常执行完成或抛出异常，都会执行clear方法
            TenantContext.clear();
            log.debug("TenantContext已清理");
        }
    }
}
