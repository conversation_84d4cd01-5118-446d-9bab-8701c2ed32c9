package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/12/24
 */
@Tag(name = "平台appKey管理")
@RestController
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('SUPER_ADMIN')")
@RequestMapping(value = "platform-app-key")
public class PlatformAppKeyController {

    private final PlatformAppKeyService platformAppKeyService;

    @Operation(summary = "接口授权管理-创建appKey")
    @PostMapping(value = "/interface/create")
    public SuccessResponse<PlatformAppKeySecret> interfaceCreate(@Valid @RequestBody PlatformAppKeyCreateReq platformAppKeyCreateReq) {
        PlatformAppKeySecret appKey = platformAppKeyService.interfaceCreate(null, platformAppKeyCreateReq.getPlatformName(), platformAppKeyCreateReq.getPlatformType());
        return ApiResponse.success(appKey).build();
    }

    @Operation(summary = "接口授权管理-创建appKey-appName重名校验")
    @PostMapping(value = "/interface/name-repeat-check")
    public SuccessResponse<Boolean> nameRepeatCheck(@RequestParam String appName) {
        return ApiResponse.success(platformAppKeyService.nameRepeatCheck(appName)).build();
    }

    @Operation(summary = "接口授权管理-删除appKey")
    @DeleteMapping(value = "/interface/delete")
    public SuccessResponse<Boolean> interfaceDelete(@RequestParam Long id) {
        boolean success = platformAppKeyService.interfaceDelete(id);
        return ApiResponse.success(success).build();
    }

    @Operation(summary = "接口授权管理-编辑appKey")
    @PutMapping(value = "/interface/update")
    public SuccessResponse<Boolean> interfaceUpdate(@Valid @RequestBody PlatformAppKeyUpdateReq platformAppKeyUpdateReq) {
        boolean success = platformAppKeyService.interfaceUpdate(platformAppKeyUpdateReq.getId(), platformAppKeyUpdateReq.getPlatformName(), platformAppKeyUpdateReq.getPlatformType());
        return ApiResponse.success(success).build();
    }

    @Operation(summary = "接口授权管理-重置appSecret")
    @PutMapping(value = "/interface/reset")
    public SuccessResponse<Boolean> interfaceResetAppSecret(@Valid @RequestBody PlatformAppSecretResetReq platformAppSecretResetReq) {
        boolean success = platformAppKeyService.interfaceResetAppSecret(platformAppSecretResetReq.getId());
        return ApiResponse.success(success).build();
    }

    @Operation(summary = "接口授权管理-appKey列表")
    @PostMapping(value = "/interface/list")
    public SuccessResponse<List<PlatformAppKeySecret>> interfaceList(@Valid @RequestBody PlatformAppKeyPageReq platformAppKeyPageReq) {
        return platformAppKeyService.interfaceList(platformAppKeyPageReq);
    }

    @Operation(summary = "外部接口管理-创建appKey")
    @PostMapping(value = "/external-interface/create")
    public SuccessResponse<String> externalCreate(@Valid @RequestBody ExternalPlatformAppKeyCreateReq externalPlatformAppKeyCreateReq) {
        String appKey = platformAppKeyService.externalCreate(externalPlatformAppKeyCreateReq.getPlatformId(), externalPlatformAppKeyCreateReq.getPlatformDomain(), externalPlatformAppKeyCreateReq.getPlatformType(), externalPlatformAppKeyCreateReq.getAppKey(), externalPlatformAppKeyCreateReq.getAppSecret());
        return ApiResponse.success(appKey).build();
    }

    @Operation(summary = "外部接口管理-删除appKey")
    @DeleteMapping(value = "/external-interface/delete")
    public SuccessResponse<Boolean> externalDelete(@RequestParam Long id) {
        boolean success = platformAppKeyService.externalDelete(id);
        return ApiResponse.success(success).build();
    }

    @Operation(summary = "外部接口管理-编辑appKey")
    @PutMapping(value = "/external-interface/update")
    public SuccessResponse<Boolean> externalUpdate(@Valid @RequestBody ExternalPlatformAppKeyUpdateReq externalPlatformAppKeyUpdateReq) {
        boolean success = platformAppKeyService.externalUpdate(externalPlatformAppKeyUpdateReq.getId(), externalPlatformAppKeyUpdateReq.getPlatformId(), externalPlatformAppKeyUpdateReq.getPlatformDomain(), externalPlatformAppKeyUpdateReq.getPlatformType(), externalPlatformAppKeyUpdateReq.getAppKey(), externalPlatformAppKeyUpdateReq.getAppSecret());
        return ApiResponse.success(success).build();
    }

    @Operation(summary = "外部接口管理-appKey列表")
    @PostMapping(value = "/external-interface/list")
    public SuccessResponse<List<ExternalPlatformAppKeySecret>> externalList(@Valid @RequestBody ExternalPlatformAppKeyPageReq externalPlatformAppKeyPageReq) {
        return platformAppKeyService.externalList(externalPlatformAppKeyPageReq);
    }
}
