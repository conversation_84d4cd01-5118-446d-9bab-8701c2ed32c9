package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CatalogQueryVM extends ServiceNodeRequest {

    @NotEmpty(message = "过滤器为必填项")
    @ApiModelProperty(value = "过滤器（object） /多个过滤器条件是 and 的关系")
    private List<FilterVM> filters;

    @NotEmpty(message = "排序器为必填项")
    @ApiModelProperty(value = "排序器（object） 数据的排序方法。不指定将采用默认排序。排序项一般为1个。")
    private List<OrderVM> orders;

    @ApiModelProperty(value = "返回结果的分页 码，从1开始")
    private Long page;

    @ApiModelProperty(value = "返回结果的数据条数")
    private Long size;

    /**
     * 查询类型：1-数据资源登记、2-数据产品登记、3-数据产品上架
     */
    @ApiModelProperty(value = "查询类型：1-数据资源登记、2-数据产品登记、3-数据产品上架")
    private Integer type;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterVM {
        @ApiModelProperty(value = "过滤属性 参见NDI-TR-2025-06 数据资源目录字典和数据产品目录字典：（加一个字段type：PRODUCT(查询数据产品)，RESOURCE(查询数据资源)PRODUCT_PUBLISH（已上架数据产品））")
        @NotBlank(message = "过滤属性为空")
        private String filterProperty;

        @ApiModelProperty(value = "过滤属性/参见默认操作是=，根据 NDI-TR-2025-06 具体描述使用>、<，between等操作")
        @NotBlank(message = "过滤操作为空")
        private String filterOperation;

        @ApiModelProperty(value = "过滤值（json） 根据过滤操作而定:过滤操作为=或 like 时过滤值为简单类型(String,Boolean 或 Int);过滤操作为 in或 contain 时，过滤值为数组类型;过滤操作为 between 时，过滤值为包含两个元素的数组(>=并且<=)")
        @NotBlank(message = "过滤值为空")
        private String filterValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderVM {
        @ApiModelProperty(value = "排序属性 系统级:OVERALL 表示综合排序，MATCH 表示按关键词匹配度排序。属性级: 指定具体的属性按属性值排序宜支持 createTime(按创建时间排序)updateTime(按更新时间排序)。")
        @NotBlank(message = "排序属性为空")
        private String orderProperty;

        @ApiModelProperty(value = "排序方向 升序：ASC 降序：DESC")
        @NotBlank(message = "排序方向为空")
        private String orderDirection;
    }

}
