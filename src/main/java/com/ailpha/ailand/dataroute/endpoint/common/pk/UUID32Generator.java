package com.ailpha.ailand.dataroute.endpoint.common.pk;

import com.ailpha.ailand.invoke.api.utils.UuidUtils;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import java.io.Serializable;
import java.lang.reflect.Method;

/**
 * @author: yuwenping
 * @date: 2025/3/11 20:22
 * @Description:
 */
public class UUID32Generator implements IdentifierGenerator {

    public static final String TYPE = "com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32Generator";

    @Override
    public Serializable generate(SharedSessionContractImplementor sharedSessionContractImplementor, Object o) throws HibernateException {
        return UuidUtils.uuid32();
    }


    private Object getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[]{});
            Object value = method.invoke(o, new Object[]{});
            return value;
        } catch (Exception e) {
            return null;
        }
    }
}
