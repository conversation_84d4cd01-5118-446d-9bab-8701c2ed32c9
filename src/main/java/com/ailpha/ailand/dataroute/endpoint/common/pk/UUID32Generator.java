package com.ailpha.ailand.dataroute.endpoint.common.pk;

import com.ailpha.ailand.invoke.api.utils.UuidUtils;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import java.io.Serializable;

/**
 * @author: yuwenping
 * @date: 2025/3/11 20:22
 * @Description:
 */
public class UUID32Generator implements IdentifierGenerator {

    public static final String TYPE = "com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32Generator";

    @Override
    public Serializable generate(SharedSessionContractImplementor sharedSessionContractImplementor, Object o) throws HibernateException {
        return UuidUtils.uuid32();
    }
}
