package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.third.constants.State;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobInfo {
    /**
     * 作业id
     */
    private String jobId;

    /**
     * 作业当前状态
     */
    private State state;

    private String jobType;

    private String errorMessage;

}
