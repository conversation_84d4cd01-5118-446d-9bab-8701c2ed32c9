package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.APIQueryWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品登记请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductRegistRequest {
    @Schema(description = "数据标识")
    String id;
    @Schema(description = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    String productName;
    @Schema(description = "产品中文名称")
    String productNameCN;
    @Schema(description = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他长度不能超过2")
    @NotBlank(message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他不能为空")
    String type;
    @Schema(description = "数据覆盖周期开始时间 YYYY-MM-DD")
    String dataCoverageTimeStart;
    @Schema(description = "数据覆盖周期结束时间 YYYY-MM-DD")
    String dataCoverageTimeEnd;
    @Schema(description = "行业分类")
    @NotBlank(message = "行业分类（GB/T 4754-2017门类代码）不能为空")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是")
    @NotNull(message = "是否涉及个人信息：0-否,1-是不能为空")
    String personalInformation;
    @Schema(description = "产品简介")
    @NotBlank(message = "产品简介不能为空")
    String description;
    @Schema(description = "产品来源")
    String source;
    @Schema(description = "数据规模（单位 MB GB TB）")
    String dataSize;
    @Schema(description = "更新频率")
    @NotBlank(message = "更新频率（次/天、次/周等）不能为空")
    String updateFrequency;
    @Schema(description = "交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC")
    List<DeliveryMode> deliveryModes;
//    @Schema(description = "交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）")
//    String deliveryMethod;
    @Schema(description = "使用限制：数据产品在使用过程中的任何限制或条件，标注使用限制场景")
    @NotBlank(message = "使用限制不能为空")
    String limitations;
    @Schema(description = "授权使用：数据产品使用时是否需要授权： 0：否 1：是")
    @NotNull(message = "授权使用：0-否,1-是不能为空")
    String authorize;
    @Schema(description = "数据主体：01-个人信息,02-企业数据,03-公共数据")
    @Length(max = 2, message = "数据主体：01-个人信息,02-企业数据,03-公共数据长度不能超过2")
    String dataSubject;
    @Schema(description = "是否允许二次加工：0：不允许 1：允许")
    String isSecondaryProcessed;
    @Schema(description = "一个或多个数据资源标识码，产品关联数据资源统一标识，基于那些数据资源形成的数据产品。", examples = "[{\"resourceId\":\"resourceId1\"},{\"resourceId\":\"resourceId2\"}]")
    String resourceId;
    @Schema(description = "产品血缘")
    String lineage;
    @Schema(description = "其他")
    String other;

    // 声明信息
    @Schema(description = "声明信息")
    QualificationDoc qualificationDoc;

    // >>> 接入配置
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "数据接入方式")
    SourceType accessWay;
    @Schema(description = "API查询方式")
    APIQueryWay apiQueryWay;
    @Schema(description = "MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）")
    List<MPCPurpose> mpcPurpose;

    @jakarta.validation.constraints.NotNull
    @Schema(description = "调试数据来源")
    DebugDataSourceEnum debugDataSource;
    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "临时调试文件id")
    String tempDebugFileId;
    @Schema(description = "调试数据分隔符")
    String separator;
    @Schema(description = "调试数据是否包含表头 1:是")
    Integer hasHeader;
    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;
    @Schema(description = "文件数据资产元数据")
    FileSourceMetadata fileSourceMetadata;
    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
    @Schema(description = "AiSort元数据")
    AiSortMetadata aiSortMetadata;

    @Schema(description = "绑定交易所插件")
    List<Long> exchangePluginIds;
    @Schema(description = "绑定数字证书插件")
    List<Long> certificatePluginIds;

    @Schema(description = "平台生成(MPC) 结果集(openapiId)")
    String mpcOpenAPIId;

    @Schema(description = "是否改写响应体")
    Boolean extractResponse;
}
