package com.ailpha.ailand.dataroute.endpoint.common.utils;

import cn.hutool.core.util.StrUtil;

import java.nio.charset.StandardCharsets;

public class SmUtil {

    /**
     * sm4 加密
     *
     * @param data 待加密数据
     * @param password 秘钥字符串
     * @return 加密后字符串, 采用 Base64 编码
     */
    public static String encryptBySm4(String data, String password) {
        if (StrUtil.isBlank(password)) {
            throw new IllegalArgumentException("SM4 需要传入秘钥信息");
        }
        // sm4 算法的密钥要求是 16 位长度
        int sm4PasswordLength = 16;
        if (sm4PasswordLength != password.length()) {
            throw new IllegalArgumentException("SM4 秘钥长度要求为 16 位");
        }
        return cn.hutool.crypto.SmUtil.sm4(password.getBytes(StandardCharsets.UTF_8)).encryptBase64(data, StandardCharsets.UTF_8);
    }
}
