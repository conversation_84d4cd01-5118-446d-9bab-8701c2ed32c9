package com.ailpha.ailand.dataroute.endpoint.demand.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class LatestDemandNegotiationDTO {
    @Schema(description = "需求标题")
    String title;
    @Schema(description = "数源主体名称")
    String providerCompany;
    @Schema(description = "数源方")
    String providerName;
    @Schema(description = "电话")
    String phone;
    @Schema(description = "报价")
    String price;
    @Schema(description = "关联数据资产")
    String dataAssets;
    @Schema(description = "最新提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date submitDate;
}
