package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024/11/18 15:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "API网关日志上报")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class LogReportReq {

    String apiId;

    String dataHash;

    /**
     * 1:成功，-1:失败
     */
    Integer status;

    /**
     * 手动填充 —— 计算次数
     */
    @JsonIgnore
    String orderId;

    /**
     * 资产所在数由器id
     */
    String assetRouteId;
}
