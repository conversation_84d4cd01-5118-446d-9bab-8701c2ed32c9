package com.ailpha.ailand.dataroute.endpoint.order.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.TimeUnit;

@Slf4j
public class OrderTimeUtil {

    public static String formatUsageTime(String status, Date approveTime, Date updateTime, Date expireDate) {
        try {
            if (approveTime == null) {
                log.error("订单审批时间为空...");
                return null;
            }

            if (status.equals("COMPLETED")) {
                return OrderTimeUtil.formatUsageTime(approveTime.getTime(), expireDate.getTime());
            } else if (status.equals("TERMINATED")) {
                return OrderTimeUtil.formatUsageTime(approveTime.getTime(), updateTime.getTime());
            } else if (status.equals("APPROVED")) {
                return OrderTimeUtil.formatUsageTime(approveTime.getTime(), null);
            }
        } catch (Exception e) {
            log.error("订单使用时长转换异常：", e);
        }

        return null;
    }

    private static String formatUsageTime(long startTimeMillis, Long endTimeMillis) {
        if (endTimeMillis == null) {
            endTimeMillis = System.currentTimeMillis();
        }
        long diffMillis = endTimeMillis - startTimeMillis;

        // 转换成各个时间单位
        long days = TimeUnit.MILLISECONDS.toDays(diffMillis);
        long years = days / 365;
        days = days % 365;
        long months = days / 30;
        days = days % 30;

        long hours = TimeUnit.MILLISECONDS.toHours(diffMillis) % 24;
        long minutes = TimeUnit.MILLISECONDS.toMinutes(diffMillis) % 60;

        StringBuilder result = new StringBuilder();
        if (years > 0) {
            result.append(years).append("年");
        }
        if (months > 0) {
            result.append(months).append("月");
        }
        if (days > 0) {
            result.append(days).append("天");
        }
        if (hours > 0) {
            result.append(hours).append("时");
        }
        if (minutes > 0) {
            result.append(minutes).append("分");
        }

        return !result.isEmpty() ? result.toString() : "0分";
    }
}