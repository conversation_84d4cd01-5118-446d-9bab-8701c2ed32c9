package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 11:43
 * @Description:
 */
@Data
public class SSEMessageVO {
    /**
     * 消息id
     */
    private Long id;

    private String message;

    private SSEMessageTypeEnum type;

    private String userId;

    private String dataId;

    private Date sendTime;

    private Integer count;
}
