package com.ailpha.ailand.dataroute.endpoint.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiMarkTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:49
 * @Description:
 */
@Data
@Entity
@Table(name = "t_plugin_api_type")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PluginApiType {
    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    /**
     * 接口名称
     */
    private String name;

    /**
     * 接口标识
     */
    @Enumerated(EnumType.STRING)
    private PluginApiMarkTypeEnums mark;

    @Enumerated(EnumType.STRING)
    private PluginApiTypeEnums type;

}
