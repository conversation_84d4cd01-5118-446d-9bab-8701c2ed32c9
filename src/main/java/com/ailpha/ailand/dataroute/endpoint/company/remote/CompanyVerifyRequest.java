package com.ailpha.ailand.dataroute.endpoint.company.remote;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Schema(description = "企业认证请求")
public class CompanyVerifyRequest {

    @Schema(description = "经办人信息")
    private AgentPerson agentPerson;

    @Schema(description = "法人信息")
    private LegalPerson legalPerson;

    @Schema(description = "认证类型")
    private String verifyType;

    @Data
    @Schema(description = "经办人信息")
    public static class AgentPerson {
        @Schema(description = "经办人身份证号")
        private String applyIdCard;

        @Schema(description = "证件有效期结束时间")
        private String applyIdCardEndTime;

        @Schema(description = "证件有效期开始时间")
        private String applyIdCardStartTime;

        @Schema(description = "经办人姓名")
        private String applyName;

        @Schema(description = "经办人手机号")
        private String applyPhone;

        @Schema(description = "营业执照URL")
        private String businessLicenseUrl;
        String businessLicenseUrlName;

        @Schema(description = "统一社会信用代码")
        private String companyCode;

        @Schema(description = "企业名称")
        private String companyName;

        @Schema(description = "授权书URL")
        private String impowerUrl;

        @Schema(description = "行业类型")
        private String industry;

        @Schema(description = "注册地址")
        private String registerAddress;

        @Schema(description = "备注")
        private String remark;
    }

    @Data
    @Schema(description = "法人信息")
    public static class LegalPerson {
        @Schema(description = "法人身份证号")
        private String applyIdCard;

        @Schema(description = "法人姓名")
        private String applyName;

        @Schema(description = "申请类型")
        private String applyType;

        @Schema(description = "营业执照URL")
        private String businessLicenseUrl;

        String businessLicenseUrlName;

        @Schema(description = "统一社会信用代码")
        private String companyCode;

        @Schema(description = "企业名称")
        private String companyName;

        @Schema(description = "认证机构")
        private String examineInstitution;

        @Schema(description = "行业类型")
        private String industry;

        @Schema(description = "经营期限结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date operateEndTime;

        @Schema(description = "经营范围")
        private String operateRange;

        @Schema(description = "经营期限开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date operateStartTime;

        @Schema(description = "注册地址")
        private String registerAddress;

        @Schema(description = "注册资本")
        private String registerAmount;

        @Schema(description = "注册时间")
        private String registerTime;

        @Schema(description = "经营范围")
        private String scope;
        //法定代表人证件类型：[身份证/护照/军官证]
        String documentType;
        // 实名认证方式：[0-公安认证/1-政务平台/2-其他]
        String authenticationMode;
        //身份状态：[可用/不可用]
        String identityState;
        //    法定代表人实名认证等级：[0-未实名/1-身份证核验/2-生物识别]
        String certificationLevel;
        //完成实名认证时间：YYYY-MM-DD HH:mm:ss
        String authenticationTime;
    }
}
