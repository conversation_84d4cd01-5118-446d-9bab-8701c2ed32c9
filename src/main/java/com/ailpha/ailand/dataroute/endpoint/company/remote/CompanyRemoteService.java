package com.ailpha.ailand.dataroute.endpoint.company.remote;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.DataRouterManagerInterceptor;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.EnterpriseInfoResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import okhttp3.MultipartBody;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = DataRouterManagerInterceptor.class)
public interface CompanyRemoteService {
    @POST("/gateway/shuhan-business-service/api/drCompanyPersonalApplyInfo/remote/submitCompanyVerify")
    CommonResult<Boolean> companyVerify(@Body CompanyVerifyRequest request);

    @POST("/gateway/shuhan-business-service/api/drCompanyPersonalApplyInfo/remote/getCompanyVerifyInfo")
    CommonResult<GetCompanyVerifyResultResponse> getCompanyVerifyInfo(@Query("companyCode") String companyCode);

    @POST("/gateway/file-center-service/api/pub/remote/ljqUploadFile")
    @Multipart
    CommonResult<String> uploadFile(@Part MultipartBody.Part file);

    @POST("/gateway/shuhan-business-service/api/drCompanyPersonalApplyInfo/remote/updateCompanyLegalInfo")
    CommonResult<String> edit(@Body UpdateCompanyLegalInfoReq req);

    @POST("/GetEnterpriseInfo")
    CommonResult<EnterpriseInfoResponse> getEnterpriseInfo(@Body GetEnterpriseInfoRequest request, @Header("metaData") String metaData);
}
