package com.ailpha.ailand.dataroute.endpoint.common.config.session;

import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionEvent;
import javax.servlet.http.HttpSessionListener;

/**
 * <AUTHOR>
 * @description: session 创建监听器
 * @date 2023/6/26 14:52
 */
@WebListener
public class SessionListener implements HttpSessionListener {

    private final CustomSessionContext context = CustomSessionContext.getInstance();

    @Override
    public void sessionCreated(HttpSessionEvent se) {
        final HttpSession httpSession = se.getSession();
        context.addSession(httpSession);
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent se) {
        final HttpSession httpSession = se.getSession();
        context.delSession(httpSession);
    }
}
