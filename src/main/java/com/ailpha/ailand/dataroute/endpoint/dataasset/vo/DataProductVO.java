package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductVO {
    @Schema(description = "资产id", requiredMode = Schema.RequiredMode.REQUIRED)
    String id;
    @Schema(description = "数据资源全局（连接器空间）唯一标识")
    String dataProductPlatformId;
    String platformId;

    /**
     * 用户id
     */
    String userId;

    @Schema(description = "资源提供方")
    ProviderExt provider;

    // 基本信息
    @Schema(description = "资产名称")
    String dataProductName;
    @Schema(description = "资产中文名称")
    String dataProductNameCN;
    @Schema(description = "产品类型")
    String type;
    @Schema(description = "数据覆盖周期开始时间")
    String dataCoverageTimeStart;
    @Schema(description = "数据覆盖周期结束时间")
    String dataCoverageTimeEnd;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是")
    String personalInformation;
    @Schema(description = "产品简介")
    String description;
    @Schema(description = "交付方式")
    List<DeliveryMode> deliveryModes;
    @Schema(description = "交付方式")
    String deliveryMethod;
    @Schema(description = "使用限制")
    String limitations;
    @Schema(description = "授权使用")
    String authorize;
    @Schema(description = "产品来源")
    String source;
    @Schema(description = "数据规模（单位 MB GB TB）")
    String dataSize;
    @Schema(description = "更新频率")
    String updateFrequency;
    @Schema(description = "是否允许二次加工")
    String isSecondaryProcessed;
    @Schema(description = "登记提交时间")
    Long registrationSubmitTime;
    @Schema(description = "登记时间")
    Long registrationTime;
    @Schema(description = "最近登记更新时间")
    Long registrationUpdateTime;
    @Schema(description = "上架提交时间")
    Long publishSubmitTime;
    @Schema(description = "上架时间")
    Long publishTime;
    @Schema(description = "最近上架更新时间")
    Long publishUpdateTime;

    @Schema(description = "资源统一标识列表")
    String resourceId;
    @Schema(description = "产品血缘")
    String lineage;
    @Schema(description = "其他")
    String other;


    // 声明信息
    @Schema(description = "声明信息")
    QualificationDoc qualificationDoc;


    @Schema(description = "业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;

    // 定价信息
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价")
    String price;

    // >>> 接入配置
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "数据接入方式")
    SourceType accessWay;
    @Schema(description = "API查询方式")
    APIQueryWay apiQueryWay;
    @Schema(description = "MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）")
    List<MPCPurpose> mpcPurpose;

    @Schema(description = "调试数据来源")
    DebugDataSourceEnum debugDataSource;
    @Schema(description = "调试数据路径")
    String debugDataPath;
    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "调试数据分隔符")
    String separator;
    @Schema(description = "调试数据是否包含表头 1:是")
    Integer hasHeader;
    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;
    @Schema(description = "文件数据资产元数据")
    FileSourceMetadata fileSourceMetadata;
    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
    @Schema(description = "AiSort元数据")
    AiSortMetadata aiSortMetadata;

    @Schema(description = "绑定交易所插件")
    List<Long> exchangePluginIds;
    @Schema(description = "绑定数字证书插件")
    List<Long> certificatePluginIds;

    @Schema(description = "平台生成(MPC) 结果集(openapiId)")
    String mpcOpenAPIId;

    @Schema(description = "是否改写响应体")
    Boolean extractResponse;

    @Schema(description = "调试数据")
    List<List<String>> dataList;

    @Schema(description = "API网关跳转参数routeId的值")
    String gatewayServiceRouteId;

    @Schema(description = "数据资产预处理状态")
    DataAssetPrepareStatus prepareStatus;

    @Schema(description = "数据资产发布状态：0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销")
    String publishStatus;
    @Schema(description = "登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
}
