package com.ailpha.ailand.dataroute.endpoint.common.utils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class CookieUtils {
    public static void setCookie(
            HttpServletResponse response, String cookieName, String cookieValue,
            boolean enableSecure, long expireSeconds, String path
    ) {
        //为了过研究院的安全测试，需要手动序列化cookie为响应头，添加SameSite属性
        String httpHeaderValue = cookieName + '=' + cookieValue +
                "; Max-Age=" + expireSeconds +
                "; Path=" + path +
                "; HttpOnly" +
                (enableSecure ? "; Secure" : "") +
                // 标准的cookie序列化类不包含SameSite属性，所以需要手动设置响应头
                "; SameSite=Lax";
        response.addHeader("Set-Cookie", httpHeaderValue);
    }

    public static void setCookie(
            HttpServletResponse response, String cookieName, String cookieValue,
            boolean enableSecure, long expireSeconds
    ) {
        setCookie(response, cookieName, cookieValue, enableSecure, expireSeconds, "/");
    }

    public static Cookie getCookie(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (name.equals(cookie.getName())) {
                    return cookie;
                }
            }
        }
        return null;
    }
}
