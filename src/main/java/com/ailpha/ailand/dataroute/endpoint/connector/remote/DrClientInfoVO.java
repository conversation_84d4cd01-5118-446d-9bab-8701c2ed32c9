package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * @author: sunsas.yu
 * @date: 2024/11/19 19:41
 * @Description:
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DrClientInfoVO {
    /**
     * 连接器管理员名称
     */
    String clientAdminName;

    /**
     * 连接器客户端描述
     */
    String clientDesc;

    /**
     * 连接器客户端ip
     */
    String clientIp;

    /**
     * 连接器客户端名称
     */
    String clientName;

    /**
     * 连接器客户端编号
     */
    String clientNo;

    /**
     * 企业 - 统一社会信用代码
     */
    String companyCode;
}
