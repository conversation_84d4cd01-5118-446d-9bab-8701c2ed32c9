package com.ailpha.ailand.dataroute.endpoint.user.vo;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

@Data
@Schema(description = "当前用户信息")
public class CurrentUserResponse {
    @Schema(description = "用户")
    UserDTO userDTO;
    @Schema(description = "角色")
    RoleEnums role;
    @Schema(description = "菜单")
    Set<String> menu;
    @Schema(description = "license")
    License license;

    @Schema(description = "部署模式: standalone 独享模式 share sass模式")
    DeployMode deployMode;

    @Data
    public static class License {
        String message;
        LicenseStatus status = LicenseStatus.license_pass;
    }

    public enum LicenseStatus {
        /**
         * 没有导出过license
         */
        not_import_license,
        /**
         * 正式license已过期
         */
        official_license_expired,
        /**
         * 非正式license已过期
         */
        not_official_license_expired,
        /**
         * 正式license 临期且小于90天
         */
        official_license_expired_lt_90_days,
        /**
         * 非正式license 临期且小于90天
         */
        not_official_license_expired_lt_90_days,
        /**
         * license正常
         */
        license_pass;
    }
}
