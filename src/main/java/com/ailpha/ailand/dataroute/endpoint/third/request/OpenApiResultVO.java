package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiResultVO {

    @Schema(description = "结果路径")
    String resultPath;

    @Schema(description = "结果字段信息")
    List<OpenApiResultSchemaVO> resultSchemas;
}
