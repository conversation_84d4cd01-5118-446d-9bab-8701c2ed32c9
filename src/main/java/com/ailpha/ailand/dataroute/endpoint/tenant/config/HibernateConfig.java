package com.ailpha.ailand.dataroute.endpoint.tenant.config;

import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
public class HibernateConfig {

    @Bean
    public JpaVendorAdapter jpaVendorAdapter() {
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        vendorAdapter.setDatabasePlatform("org.hibernate.dialect.PostgreSQLDialect");
        return vendorAdapter;
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            DataSource dataSource,
            MultiTenantConnectionProviderImpl multiTenantConnectionProvider) {

        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("com.ailpha.ailand.dataroute.endpoint");
        em.setJpaVendorAdapter(jpaVendorAdapter());

        Properties properties = new Properties();
        // 多租户配置
        properties.put("hibernate.multiTenancy", "SCHEMA");
        properties.put("hibernate.tenant_identifier_resolver", new TenantIdentifierResolver());
        properties.put("hibernate.multi_tenant_connection_provider", multiTenantConnectionProvider);
        
        // 连接池配置
        properties.put("hibernate.connection.provider_class", "org.hibernate.hikaricp.internal.HikariCPConnectionProvider");
        properties.put("hibernate.hikari.maximumPoolSize", "10");
        properties.put("hibernate.hikari.minimumIdle", "5");
        properties.put("hibernate.hikari.idleTimeout", "300000");
        
        // 数据源配置
        properties.put("hibernate.hikari.dataSource.cachePrepStmts", "true");
        properties.put("hibernate.hikari.dataSource.prepStmtCacheSize", "250");
        properties.put("hibernate.hikari.dataSource.prepStmtCacheSqlLimit", "2048");

        // 启用二级缓存
        properties.put("hibernate.cache.use_second_level_cache", "true");
        // 指定缓存提供者为 jcache
        properties.put("hibernate.cache.region.factory_class", "org.hibernate.cache.jcache.JCacheRegionFactory");
        // 启用查询缓存
        properties.put("hibernate.cache.use_query_cache", "true");
        // 指定 ehcache 作为 jcache 的实现
        properties.put("hibernate.javax.cache.provider", "org.ehcache.jsr107.EhcacheCachingProvider");

        properties.put("hibernate.enable_lazy_load_no_trans", "true");

        properties.put("hibernate.show_sql", "true");
        properties.put("hibernate.format_sql", "true");

        em.setJpaProperties(properties);
        return em;
    }
}