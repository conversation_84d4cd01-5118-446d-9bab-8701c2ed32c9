package com.ailpha.ailand.dataroute.endpoint.dict;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.IndustryDictVO;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;

@Slf4j
@RestController
@Tag(name = "数据字典")
@RequiredArgsConstructor
@RequestMapping("dict")
public class DictController {

    @GetMapping("codes")
    @Operation(summary = "字典值")
    public SuccessResponse<Map<String, Map<String, String>>> codes(@RequestParam List<String> codes) {
        return ApiResponse.success(JSONUtil.toBean("{\n" +
                "        \"industryClassify\": {\n" +
                "            \"industryClassify1\": \"制造业\",\n" +
                "            \"industryClassify2\": \"电力、热力、燃气及水生产和供应业\",\n" +
                "            \"industryClassify3\": \"建筑业\",\n" +
                "            \"industryClassify4\": \"批发和零售业\",\n" +
                "            \"industryClassify5\": \"交通运输、仓储和邮政业\",\n" +
                "            \"industryClassify6\": \"农、林、牧、渔业\",\n" +
                "            \"industryClassify7\": \"采矿业\",\n" +
                "            \"industryClassify8\": \"金融业\",\n" +
                "            \"industryClassify9\": \"房地产业\",\n" +
                "            \"industryClassify10\": \"租赁和商务服务业\",\n" +
                "            \"industryClassify11\": \"卫生和社会工作\",\n" +
                "            \"industryClassify12\": \"文化、体育和娱乐业\",\n" +
                "            \"industryClassify13\": \"住宿和餐饮业\",\n" +
                "            \"industryClassify14\": \"信息传输、软件和信息技术服务业\",\n" +
                "            \"industryClassify15\": \"科学研究和技术服务业\",\n" +
                "            \"industryClassify16\": \"水利、环境和公共设施管理业\",\n" +
                "            \"industryClassify17\": \"居民服务、修理和其他服务业\",\n" +
                "            \"industryClassify18\": \"教育\",\n" +
                "            \"industryClassify19\": \"公共管理、社会保障和社会组织\",\n" +
                "            \"industryClassify20\": \"国际组织\"\n" +
                "        },\n" +
                "        \"safeLevel\": {\n" +
                "            \"safeLevel1\": \"不敏感\",\n" +
                "            \"safeLevel2\": \"低敏感\",\n" +
                "            \"safeLevel3\": \"较敏感\",\n" +
                "            \"safeLevel4\": \"敏感\"\n" +
                "        },\n" +
                "        \"update_frequency\": {\n" +
                "            \"1\": \"每小时\",\n" +
                "            \"2\": \"每天\",\n" +
                "            \"3\": \"每周\",\n" +
                "            \"4\": \"每两周\",\n" +
                "            \"5\": \"每月\",\n" +
                "            \"6\": \"每年\"\n" +
                "        },\n" +
                "        \"dataRange\": {\n" +
                "            \"dataRange1\": \"全国\",\n" +
                "            \"dataRange2\": \"全省\",\n" +
                "            \"dataRange3\": \"省本级\",\n" +
                "            \"dataRange4\": \"全市\",\n" +
                "            \"dataRange5\": \"市本级\",\n" +
                "            \"dataRange6\": \"县（市、区）\"\n" +
                "        },\n" +
                "        \"accessWay\": {\n" +
                "            \"accessWay1\": \"数据库\",\n" +
                "            \"accessWay2\": \"API接口\",\n" +
                "            \"accessWay3\": \"文件\"\n" +
                "        },\n" +
                "        \"provider\": {\n" +
                "            \"provider1\": \"大数据局\"\n" +
                "        }\n" +
                "    }", Map.class)).build();
    }

    private static final Map<String, IndustryDictVO> CLASSIFY_BY_ID = new HashMap<>();

    @GetMapping("getClassify")
    @Operation(summary = "获取行业分类")
    public SuccessResponse<List<IndustryDictVO>> getClassify(@RequestParam(required = false) String id) {
        cacheClassifyData();
        return ApiResponse.success(List.of(CLASSIFY_BY_ID.get(id))).build();
    }

    private static List<IndustryDictVO> ALL_CLASSIFYS = new ArrayList<>();

    @GetMapping("getAllClassify")
    @Operation(summary = "获取所有行业分类")
    public SuccessResponse<List<IndustryDictVO>> getAllClassify() {
        cacheClassifyData();
        return ApiResponse.success(ALL_CLASSIFYS).build();
    }

    private void cacheClassifyData() {
        try {
            if (CollectionUtils.isEmpty(ALL_CLASSIFYS)) {
                String allClassify = Files.readString(ResourceUtils.getFile("dict/classify.json").toPath(), StandardCharsets.UTF_8);
                ALL_CLASSIFYS = JSONUtil.toBean(allClassify, new TypeReference<List<IndustryDictVO>>() {
                }, true);
                flatmapClassifies(ALL_CLASSIFYS);
                ALL_CLASSIFYS = JSONUtil.toBean(allClassify, new TypeReference<List<IndustryDictVO>>() {
                }, true);
            }
        } catch (Exception ignore) {
        }
    }

    private void flatmapClassifies(List<IndustryDictVO> allClassifys) {
        for (IndustryDictVO classify : allClassifys) {
            if (!CollectionUtils.isEmpty(classify.getChildren())) {
                flatmapClassifies(classify.getChildren());
            }
            classify.setChildren(null);
            CLASSIFY_BY_ID.put(classify.getId(), classify);
        }
    }
}
