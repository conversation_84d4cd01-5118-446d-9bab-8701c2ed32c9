
package com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.TransactionRegisterRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.APIQueryWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.QDeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.SceneAssetRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.*;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDelivery;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryOderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.*;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.PageResponse;
import com.ailpha.ailand.dataroute.endpoint.third.util.PageConvertUtil;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/18 14:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryServiceImpl implements DeliveryService {

    private final DigitalCertificateRemote digitalCertificateRemote;

    private final HubDeliverySceneRemote hubDeliverySceneRemote;

    private final HubOrderRemote hubOrderRemote;

    private final GatewayWebApi gatewayWebApi;

    private final RouterService routerService;

    private final StatisticDeliveryRepository statisticDeliveryRepository;

    private final DataProductService dataProductService;

    private final JPAQueryFactory queryFactory;

    private final SceneAssetRepository sceneAssetRepository;

    private final DeliverySceneRepository deliverySceneRepository;

    private final OrderRecordRepository orderRecordRepository;

    @Value("${ailand.endpoint.ip}")
    private String endPointIp;

    private static final String targetApiUri = "/deliver_%s/asset_%s";

    private static final String deliveryTargetApiUri = "/_data-route/data-asset/api/%s/%s/%s";
    private static final String targetDownLoadUri = "%s/_data-route/data-asset/download/%s/%s/%s";

    @Override
    public PageResult<DataAssetResp> dataAssetApproveList(DataAssetQuery query) {
        UserDataAssetReq userDataAssetReq = BeanUtil.copyProperties(query, UserDataAssetReq.class, "deliveryMode");
        String userId = LoginContextHolder.currentUser().getId();
        userDataAssetReq.setCurrentRouter(query.getCurrentRouter());
        userDataAssetReq.setUserId(userId);
        userDataAssetReq.setDeliveryMode(query.getDeliveryModes() == null ? "" : query.getDeliveryModes().name());
        userDataAssetReq.setMpcPurpose(ObjectUtil.isNull(query.getMpcPurpose()) ? "" : query.getMpcPurpose().name());

        PageRequest<UserDataAssetReq> request = new PageRequest<>();
        request.setModel(userDataAssetReq);
        request.setCurrent(query.getNum());
        request.setSize(query.getSize());
        CommonResult<PageResponse<DataAssetResp>> result = hubDeliverySceneRemote.userDataAssetApprove(request);
        return PageConvertUtil.convert(result);
    }

    @Override
    public void checkBaseCapabilityConfig(String id) {
        SceneDetailResp detail = detail(id);
        switch (DeliveryType.valueOf(detail.getDeliveryType())) {
            case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_SET_INTERSECTION, MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                String message = "选择的数据所在连接器未启用MPC多方安全计算，无法使用";
                checkBaseCapabilityConfig(BaseCapabilityType.MPC, message);
            }
            case TEE_ONLINE, TEE_OFFLINE -> {
                String message = "选择的数据所在连接器未启用TEE可信执行环境，无法使用";
                checkBaseCapabilityConfig(BaseCapabilityType.TEE, message);
            }
        }
    }

    public void checkBaseCapabilityConfig(BaseCapabilityType type, String message) {
        Assert.isTrue(baseCapabilityManager.platformEnable(type), message);
    }

    @Override
    public void deliverySceneAdd(SceneSaveReq sceneSaveReq) {
        switch (sceneSaveReq.getDeliveryType()) {
            case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_SET_INTERSECTION, MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                String message = "选择的数据所在连接器未启用MPC多方安全计算，无法选中";
                checkBaseCapabilityConfig(BaseCapabilityType.MPC, message);
            }
            case TEE_ONLINE, TEE_OFFLINE -> {
                String message = "选择的数据所在连接器未启用TEE可信执行环境，无法选中";
                checkBaseCapabilityConfig(BaseCapabilityType.TEE, message);
            }
        }
        // step 1：调用  —— 服务管理平台操作 新增场景
        log.info("新增交付场景：{}", JSONUtil.toJsonStr(sceneSaveReq));
        String userId = LoginContextHolder.currentUser().getId();
        sceneSaveReq.setCreateUser(userId);
        CommonResult<String> commonResult = hubDeliverySceneRemote.deliverySceneManagerAdd(sceneSaveReq);
        log.info("服务管理平台接口返回：{}", JSONUtil.toJsonStr(commonResult));
        if (!commonResult.isSuccess()) {
            throw new RestfulApiException("新增交付场景异常");
        }
        String sceneId = commonResult.getData();

        SceneAssetApiReq req = new SceneAssetApiReq();
        req.setSceneId(sceneId);
        List<SceneAssetApiReq.DataAssetApi> dataAssetApiList = new ArrayList<>();

        // 根据用户 + 数据资产 = 订单id
        AssetBeneficiaryRelDTOListReq relListReq = new AssetBeneficiaryRelDTOListReq();
        relListReq.setBeneficiaryId(userId);
        relListReq.setAssetIds(sceneSaveReq.getDataAssetIdList());
        SuccessResponse<List<AssetBeneficiaryOderDTO>> response = hubOrderRemote.selectAssetBeneficiaryOderDTOWhereAssetIdInAndBeneficiaryId(relListReq);
        Map<String, AssetBeneficiaryOderDTO> oderDTOMap = response.getData().stream().collect(Collectors.toMap(AssetBeneficiaryOderDTO::getAssetId, assetBeneficiaryOderDTO -> assetBeneficiaryOderDTO, (a, b) -> a));

        List<TeeContract.DatasetApiInfo> apiInfos = new ArrayList<>();
        // step 2：调用  —— 数字证书接口 交易登记
        try {
            sceneSaveReq.getDataAssetIdList().forEach(dataAssetId -> {
                // 绑定信息参数
                SceneAssetApiReq.DataAssetApi dataAssetApi = new SceneAssetApiReq.DataAssetApi();
                AssetBeneficiaryOderDTO beneficiaryOderDTO = oderDTOMap.get(dataAssetId);
                if (beneficiaryOderDTO == null) {
                    log.warn("userId:[{}]  AssetId:[{}]", userId, sceneSaveReq.getDataAssetIdList());
                    throw new RestfulApiException("根据用户id和数据资产查询有效订单异常");
                }
                // 交易登记
                AssetType assetType = beneficiaryOderDTO.getType();

                // 查资产详情
                DataProductVO dataProduct = dataProductService.getDataProductByOldAssetId(dataAssetId);

                String assetName = dataProduct.getDataProductName();
                List<DeliveryMode> deliveryModes = dataProduct.getDeliveryModes();
                SourceType accessWay = dataProduct.getAccessWay();
                APIQueryWay apiQueryWay = dataProduct.getApiQueryWay();
                String routerId = dataProduct.getProvider().getRouterId();

                transactionRegister(sceneSaveReq.getDigitalSceneId(), sceneSaveReq.getDeliveryType(), sceneId, assetName, dataAssetId, routerId);

                // 扩展字段
                SceneAssetApiReq.ExtData extData = buildExtendData(dataProduct, routerId, sceneSaveReq.getDeliveryType());

                // 获取订单授权key
                String orderId = beneficiaryOderDTO.getOrderId();
                String beneficiaryExtend = beneficiaryOderDTO.getBeneficiaryExtend();
                JSONObject jsonObject = JSONUtil.parseObj(beneficiaryExtend);
                String apiKey = jsonObject.getStr("apiKey", null);
                // 鉴权key
                dataAssetApi.setApiKey(apiKey);

                // 生成 买方 API 代理接口if
                if (deliveryModes.contains(DeliveryMode.API) || (ObjectUtil.equals(accessWay, SourceType.API) || ObjectUtil.equals(accessWay, SourceType.FROM_MPC)
                        && ObjectUtil.equals(apiQueryWay, APIQueryWay.REALTIME) && (deliveryModes.contains(DeliveryMode.TEE_ONLINE))
                )) {
                    dataAssetApi.setApiId(IdUtil.fastSimpleUUID());

                    TeeContract.DatasetApiInfo datasetApiInfo = new TeeContract.DatasetApiInfo();
                    datasetApiInfo.setDataAssetId(dataAssetId);
                    datasetApiInfo.setApikey(apiKey);

                    String proxyPath = String.format(deliveryTargetApiUri, assetType, sceneId, dataAssetId);
                    String onlineApiUrl = endPointIp + proxyPath;
                    log.info("API资产【{}】交付地址：【{}】", assetName, onlineApiUrl);
                    datasetApiInfo.setOnlineApiUrl(onlineApiUrl);
                    apiInfos.add(datasetApiInfo);
                } else if (sceneSaveReq.getDeliveryType() == DeliveryType.FILE_DOWNLOAD) {
                    // 保持逻辑一致
                    dataAssetApi.setApiId(IdUtil.fastSimpleUUID());
                    // 文件扩展名
                    String extension = "csv";
                    try {
                        extension = FileUtil.extName(dataProduct.getFileSourceMetadata().getDataAssetFilePath());
                    } catch (Exception ignore) {
                    }
                    extData.setFileExtend(extension);
                }
                String extJson = JSONUtil.toJsonStr(extData);
                log.info("创建场景交付扩展字段信息:{}", extJson);
                dataAssetApi.setDataAssetId(dataAssetId);
                dataAssetApi.setOrderId(orderId);
                dataAssetApi.setExt(extJson);
                dataAssetApiList.add(dataAssetApi);

            });
        } catch (Exception e) {
            log.error("交付场景创建异常，通知服务管理平台删除交付场景【{}】", sceneId, e);
            hubDeliverySceneRemote.deleteSceneById(new DeleteSceneByIdReq(sceneId, userId));
            throw e;
        }

        req.setDataAssetApiList(dataAssetApiList);
        log.info("更新绑定的场景信息req：{}", JSONUtil.toJsonStr(req));
        CommonResult<Boolean> result = hubDeliverySceneRemote.relateApi(req);
        log.info("更新绑定的场景信息结果返回：{}", JSONUtil.toJsonStr(result));
        try {
            deCreateContract(sceneId, sceneSaveReq.getDeliveryType(), sceneSaveReq.getContract(), apiInfos);
        } catch (Exception e) {
            log.error("交付场景创建异常，通知服务管理平台删除交付场景【{}】", sceneId, e);
            hubDeliverySceneRemote.deleteSceneById(new DeleteSceneByIdReq(sceneId, userId));
            throw e;
        }

        // 首页埋点：创建场景交付个数
        statisticDeliveryRepository.saveAndFlush(StatisticDelivery.builder().deliveryId(sceneId).deliveryMode(sceneSaveReq.getDeliveryType())
                .creatorId(userId).createTime(new Date()).updateTime(new Date()).build());
    }

    private static SceneAssetApiReq.ExtData buildExtendData(DataProductVO dataProduct, String routerId, DeliveryType deliveryType) {
        // 扩展字段
        SceneAssetApiReq.ExtData extData = new SceneAssetApiReq.ExtData();
        extData.setDataProductName(dataProduct.getDataProductName());

        extData.setPlatformId(dataProduct.getPlatformId());
        extData.setUserId(dataProduct.getUserId());
        extData.setRouteId(routerId);
        extData.setDataProductPlatformId(dataProduct.getDataProductPlatformId());
        extData.setBuyerCompanyId(String.valueOf(LoginContextHolder.currentUser().getCompany().getId()));
        extData.setSellerCompanyId(String.valueOf(dataProduct.getProvider().getCompany().getId()));
        extData.setDeliveryType(deliveryType.name());
        return extData;
    }

    @Lazy
    private final TeeRemote teeRemote;
    @Lazy
    private final MPCRemote mpcRemote;

    private void deCreateContract(String sceneId, DeliveryType deliveryType, String contract, List<TeeContract.DatasetApiInfo> apiInfos) {
        switch (deliveryType) {
            case MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION, MPC_CIPHER_TEXT_COMPUTE -> {
                MpcContract mpcContract = JSONUtil.toBean(contract, MpcContract.class);
                mpcContract.setSceneId(sceneId);
                com.ailpha.ailand.dataroute.endpoint.third.response.CommonResult<String> mpcContractRes = mpcRemote.createContract(mpcContract);
                Assert.isTrue(mpcContractRes.isSuccess(), "MPC 创建合约异常：" + mpcContractRes.getMessage());
            }
            case TEE_ONLINE, TEE_OFFLINE -> {
                TeeContract teeContract = JSONUtil.toBean(contract, TeeContract.class);
                teeContract.setSceneId(sceneId);
                teeContract.setApiInfos(apiInfos);
                teeContract.setUserId(LoginContextHolder.currentUser().getId());
                teeContract.setUserName(LoginContextHolder.currentUser().getUsername());
                teeContract.setRouterId(LoginContextHolder.currentUser().getCompany().getNodeId());
                CommonResult<Boolean> teeContractResult = teeRemote.createContract(teeContract);
                Assert.isTrue(teeContractResult.isSuccess(), "TEE 创建合约异常：" + teeContractResult.getMessage());
            }
        }
    }

    private final BaseCapabilityManager baseCapabilityManager;

    /**
     * 交易登记
     *
     * @param digitalSceneId 数字证书场景id
     * @param deliveryType   交付方式
     * @param sceneId        连接器场景id
     * @param dataAssetId    资产id
     */
    private void transactionRegister(String digitalSceneId, DeliveryType deliveryType, String sceneId, String assetName, String dataAssetId, String sellerRouteId) {

        if (!baseCapabilityManager.platformEnable(BaseCapabilityType.DATA_INVOICE)) {
            log.warn("数字发票基础能力未启用，跳过推送逻辑");
            return;
        }

        DrClientInfoVO clientInfoVO = routerService.getByClientNo(sellerRouteId);
        DrClientInfoVO currentNodeClientInfoVO = routerService.getByClientNo(LoginContextHolder.currentUser().getCompany().getNodeId());

        try {
            TransactionRegisterRequest request = new TransactionRegisterRequest();
            request.setThirdBusinessId(StrUtil.join(":", sceneId, dataAssetId));
            request.setSceneId(digitalSceneId);
            // 卖方、买方 注册连接器企业ID
            request.setThirdSellerEnterpriseId(clientInfoVO.getCompanyId());
            request.setThirdBuyerEnterpriseId(currentNodeClientInfoVO.getCompanyId());
            request.setName(assetName + "-" + DeliveryType.getDeliveryMode(deliveryType));

            request.setDeliveryMode(DeliveryType.getDeliveryMode(deliveryType));
            log.debug("交易登记信息：{}", JSONUtil.toJsonStr(request));
            CommonResult<Boolean> result = digitalCertificateRemote.traderRegister(request);
            log.info("交易登记返回：{}", result);
        } catch (Exception e) {
            log.error("场景交付【{}】资产【{}】交易登记异常", digitalSceneId, dataAssetId, e);
        }
    }


    /**
     * 创建买方网关api
     *
     * @param dataAssetId     数据资产
     * @param deliverySceneId 场景id
     * @param deliveryType    交付类型
     * @param sellerRouteId   卖方连接器id
     * @return api id
     */
    private Tuple2<String, String> createBuyerService(String dataAssetId, String deliverySceneId, DeliveryType deliveryType, String sellerRouteId) {
        log.info("创建创建买方API网关 .......");
        DrClientInfoVO byClientNo = routerService.getByClientNo(sellerRouteId);
        // 目标连接器网关地址:连接器网关服务端口
        // todo：需要确定目标连接器网关地址 —— 暂时写死
        String clientIp = byClientNo.getClientIp();
        String ip = clientIp.split(":")[0];
        // 目前的地址经过两层，原来地址不对
        String targetUri = String.format("https://%s:10443/data_%s", ip, dataAssetId);
        String proxyPath = String.format(targetApiUri, deliverySceneId, dataAssetId);

        // 第二层代理使用的是自己的ip
        String currentIp = routerService.currentRouteVirtualIp();

        CreateRouteResponse response;
        try {
            log.info("创建买方API网关: sellerRouteId [{}]  deliverySceneId [{}]  代理卖方targetUri地址 [{}]", sellerRouteId, deliverySceneId, targetUri);
            response = gatewayWebApi.createServiceAndRouteForDeliver(dataAssetId, "seller-" + sellerRouteId, deliverySceneId + "-" + dataAssetId, proxyPath, targetUri,
                    DeliveryType.needBuyerHeader(deliveryType));
        } catch (URISyntaxException e) {
            log.error("买方创建api网关失败", e);
            throw new RestfulApiException("买方创建api网关失败");
        }
        return new Tuple2<>(response.getData().getInvokeResult().getId(), String.format("https://%s:10443", currentIp) + proxyPath);
    }


    private PageResult<SceneListResp> sceneListRespList(SceneListReq sceneListReq) {
        QDeliveryScene qScene = QDeliveryScene.deliveryScene;

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotBlank(sceneListReq.getSceneId())) {
            booleanBuilder.and(qScene.id.like("%" + sceneListReq.getSceneId() + "%"));
        }
        if (StringUtils.isNotBlank(sceneListReq.getDeliveryType())) {
            booleanBuilder.and(qScene.deliveryType.eq(DeliveryType.getDeliveryTypeByName(sceneListReq.getDeliveryType())));
        }

        String userId = LoginContextHolder.currentUser().getId();
        booleanBuilder.and(qScene.createUser.eq(userId));

        Long total = queryFactory.select(qScene.countDistinct()).from(qScene)
                .where(booleanBuilder).fetch().getFirst();


        List<DeliveryScene> deliverySceneList = queryFactory.selectFrom(qScene)
                .where(booleanBuilder)
                .orderBy(qScene.createTime.desc())
                .offset((sceneListReq.getNum() - 1) * sceneListReq.getSize())
                .limit(sceneListReq.getSize())
                .fetch();

        List<String> sceneIdList = deliverySceneList.stream().map(DeliveryScene::getId).toList();
        List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(sceneIdList);
        Map<String, List<SceneAsset>> map = sceneAssetList.stream().collect(Collectors.groupingBy(SceneAsset::getDeliverySceneId));

        List<SceneListResp> listRespList = deliverySceneList.stream().map(deliveryScene -> {
            if (deliveryScene == null) {
                return null;
            }

            List<SceneListResp.DataAssetSceneRef> dataAssetSceneRefList = map.get(deliveryScene.getId()).stream().map(sceneAsset -> {
                // 解析的时候写入
                SceneAssetApiReq.ExtData extData = JSONUtil.toBean(sceneAsset.getExt(), SceneAssetApiReq.ExtData.class);

                SceneListResp.DataAssetSceneRef sceneRef = SceneListResp.DataAssetSceneRef.builder()
                        .assetType(AssetType.PRODUCT)
                        .assetId(sceneAsset.getId())
                        .dataProductPlatformId(extData.getDataProductPlatformId())
                        .assetName(extData.getDataProductName())
                        .routerId(extData.getRouteId())
                        .apiKey(sceneAsset.getApiKey())
                        .orderId(sceneAsset.getOrderId())
                        .apiId(sceneAsset.getApiId())
                        .ext(sceneAsset.getExt())
                        .build();

                return sceneRef;
            }).toList();
            log.debug("当前场景【{}】关联资产内容：{} ", deliveryScene.getId(), dataAssetSceneRefList);

            SceneListResp sceneListResp = SceneListResp.builder()
                    .id(deliveryScene.getId())
                    .deliveryType(deliveryScene.getDeliveryType().name())
                    .digitalSceneName(deliveryScene.getDigitalSceneName())
                    .sceneStatus(deliveryScene.getSceneStatus())
                    .createTime(deliveryScene.getCreateTime())
                    .build();
            sceneListResp.setDataAssetSceneRefList(dataAssetSceneRefList);

            return sceneListResp;
        }).toList();

        return new PageResult<>(listRespList, total);
    }

    @Override
    public PageResult<SceneListResp> deliverySceneList(SceneListReq sceneListReq) {
        SceneListCenterReq centerReq = BeanUtil.copyProperties(sceneListReq, SceneListCenterReq.class);
        String userId = LoginContextHolder.currentUser().getId();
        centerReq.setCreateUser(userId);

        PageRequest<SceneListCenterReq> request = new PageRequest<>();
        request.setModel(centerReq);
        request.setCurrent(sceneListReq.getNum());
        request.setSize(sceneListReq.getSize());

        log.info("分页查询当前登录人交付场景信息:{}", JSONUtil.toJsonStr(request));

//        CommonResult<PageResponse<SceneListResp>> result = hubDeliverySceneRemote.deliverySceneManagerList(request);
        PageResult<SceneListResp> convert = sceneListRespList(sceneListReq);
        List<SceneListResp> data = convert.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            data.forEach(item -> {
                String deliveryType = item.getDeliveryType();
                List<SceneListResp.DataAssetSceneRef> dataAssetSceneRefList = item.getDataAssetSceneRefList();
                dataAssetSceneRefList.forEach(dataAssetSceneRef -> {
                    // todo 端口问题
                    String url;
                    if (deliveryType.equals(DeliveryType.FILE_DOWNLOAD.toString())) {
                        url = String.format(targetDownLoadUri, endPointIp, dataAssetSceneRef.getAssetType(), item.getId(), dataAssetSceneRef.getDataProductPlatformId());
                    } else {
                        String proxyPath = String.format(deliveryTargetApiUri, dataAssetSceneRef.getAssetType(), item.getId(), dataAssetSceneRef.getDataProductPlatformId());
                        url = endPointIp + proxyPath;
                    }
                    dataAssetSceneRef.setUrl(url);
                    final SceneAssetApiReq.ExtData jsonExt = dataAssetSceneRef.getJsonExt();
                    dataAssetSceneRef.setDataProductPlatformId(jsonExt == null ? null : jsonExt.getDataProductPlatformId());
                });
            });
        }

        return convert;
    }


    @Override
    public SceneDetailResp detail(String id) {
//        CommonResult<SceneDetailResp> result = hubDeliverySceneRemote.sceneDetail(id);
//        String userId = LoginContextHolder.currentUser().getId();

        DeliveryScene deliveryScene = deliverySceneRepository.findById(id).orElseThrow(() -> new RestfulApiException("未找到指定交付信息【" + id + "】"));
        List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(Collections.singletonList(id));
        SceneDetailResp resp = SceneDetailResp.builder()
                .id(deliveryScene.getId())
                .deliveryType(deliveryScene.getDeliveryType().name())
                .digitalSceneName(deliveryScene.getDigitalSceneName())
                .sceneStatus(deliveryScene.getSceneStatus())
                .createTime(deliveryScene.getCreateTime())
                .build();

        List<String> orderIdList = sceneAssetList.stream().map(SceneAsset::getOrderId).distinct().toList();
        List<OrderApprovalRecord> recordList = orderRecordRepository.findAllById(orderIdList);
        Map<String, OrderApprovalRecord> orderMap = recordList.stream().collect(Collectors.toMap(OrderApprovalRecord::getId, orderApprovalRecord -> orderApprovalRecord, (a, b) -> a));

        List<DataAssetResp> respList = sceneAssetList.stream().map(sceneAsset -> {
            OrderApprovalRecord orderApprovalRecord = orderMap.get(sceneAsset.getOrderId());

            // todo
            DataAssetResp assetResp = DataAssetResp.builder()
                    .assetId(sceneAsset.getId())
                    .dataProductPlatformId(orderApprovalRecord.getOderRecordExtend().getDataProductPlatformId())
                    .deliveryType(deliveryScene.getDeliveryType().name())
                    .routerId(orderApprovalRecord.getApproverRouterId())
                    .type(AssetType.PRODUCT)
                    .assetName(orderApprovalRecord.getAssetName())
                    .provider(orderApprovalRecord.getApproverUsername())
                    .providerOrg(orderApprovalRecord.getApproverEnterpriseName())
                    .summary("desc")
                    .productionType("01")
                    .build();
            return assetResp;
        }).toList();

        resp.setDataAssetSceneRefList(respList);

/*        List<String> assetIdList = result.getData().getDataAssetSceneRefList().stream().map(DataAssetResp::getAssetId).toList();

        // 根据用户 + 数据资产 = 订单id
        AssetBeneficiaryRelDTOListReq relListReq = new AssetBeneficiaryRelDTOListReq();
        relListReq.setBeneficiaryId(userId);
        relListReq.setAssetIds(assetIdList);
        SuccessResponse<List<AssetBeneficiaryOderDTO>> response = hubOrderRemote.selectAssetBeneficiaryOderDTOWhereAssetIdInAndBeneficiaryId(relListReq);
        Map<String, AssetBeneficiaryOderDTO> oderDTOMap = response.getData().stream().collect(Collectors.toMap(AssetBeneficiaryOderDTO::getAssetId, assetBeneficiaryOderDTO -> assetBeneficiaryOderDTO, (a, b) -> a));

        result.getData().getDataAssetSceneRefList().forEach(dataAssetResp -> {
            AssetBeneficiaryOderDTO oderDTO = oderDTOMap.get(dataAssetResp.getAssetId());
            if (oderDTO != null) {
                dataAssetResp.setAllowance(oderDTO.getAllowance());
                dataAssetResp.setExtend(oderDTO.getExtend());
            }
        });*/

        return resp;
    }


    @Override
    public SceneAssetResp deliveryAsset(String deliverId, String dataAssetId) {
        final CommonResult<SceneAssetResp> result = hubDeliverySceneRemote.sceneAssetOrder(DeliveryAssetOrderRequest.builder().dataAssetId(dataAssetId).deliverId(deliverId).build());
        return result.getData();
    }


    private final DataHubRemoteService dataHubRemoteService;

    @Override
    public Boolean contractPreCheck(PreCheckRequest request) {
        switch (request.getDeliveryType()) {
            case MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                if (request.getDataAssetIds().size() == 1)
                    return true;
                DataAssetListRequest dataAssetListRequest = new DataAssetListRequest();
                dataAssetListRequest.setDataAssetIds(request.getDataAssetIds());
                CommonResult<List<DataAssetDTO>> dataAssets = dataHubRemoteService.dataAssets(dataAssetListRequest);
                Assert.isTrue(dataAssets.isSuccess(), "获取数据资产数据异常：" + dataAssets.getMessage());
                // 统计每个数据资产 主键数量是否相同
                long lastSize = -1;
                boolean sameSize = true;
                for (DataAssetDTO dataAssetDTO : dataAssets.getData()) {
                    long count = dataAssetDTO.getExtraData().getDataSchema().stream().filter(DataSchemaBO::isId).count();
                    if (lastSize == -1) {
                        lastSize = count;

                    } else if (lastSize != count) {
                        sameSize = false;
                        break;
                    }
                }
                Assert.isTrue(sameSize, "所选数据集的主键数量不同，无法生成匿踪查询合约");
            }
        }
        return true;
    }
}