
package com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ConnectorMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.TransactionRegisterRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.*;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.mapstruct.NegotiateMapstruct;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.NegotiateTransferRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.SceneAssetRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.*;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDelivery;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderRecordDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.QOrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.mapstruct.OrderMapstruct;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderDeliveryRelationRequest;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderSuccessDeliveryRequest;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.DeliveryTransferRequest;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneAssetApiReq;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import com.ailpha.ailand.dataroute.endpoint.third.response.OrderRecordsResp;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType.MPC_PRIVATE_INFORMATION_RETRIEVAL;
import static com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType.MPC_PRIVATE_SET_INTERSECTION;

/**
 * <AUTHOR>
 * @date 2024/11/18 14:42
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryServiceImpl implements DeliveryService {

    private final DigitalCertificateRemote digitalCertificateRemote;

    private final StatisticDeliveryRepository statisticDeliveryRepository;

    private final DataProductService dataProductService;

    private final JPAQueryFactory queryFactory;

    private final DeliverySceneRepository deliverySceneRepository;

    private final SceneAssetRepository sceneAssetRepository;

    private final OrderRecordRepository orderRecordRepository;

    private final AssetBeneficiaryRepository assetBeneficiaryRepository;

    private final OrderManagerService orderManagerService;

    private final OrderMapstruct orderMapstruct;

    private final EndpointRemote endpointRemote;

    private final NegotiateTransferRepository negotiateTransferRepository;

    private final NegotiateMapstruct negotiateMapstruct;

    @Value("${ailand.endpoint.ip}")
    private String endPointIp;

    private static final String deliveryTargetApiUri = "/_data-route/data-asset/api/%s/%s/%s/%s";
    private static final String targetDownLoadUri = "%s/_data-route/data-asset/download/%s/%s/%s/%s";

    @Override
    public PageResult<DataAssetResp> dataAssetApproveList(DataAssetQuery query) {

        // 通过有效订单查询 —— 拉取资产信息
        OrderRecordsResp recordsResp = getRecordResp(query);

        List<DataAssetResp> respList = recordsResp.getData().stream().map(orderApprovalRecord -> {

            OderRecordExtend oderRecordExtend = orderApprovalRecord.getExtend();
            DataAssetResp assetResp = null;
            try {
                assetResp = DataAssetResp.builder()
                        .orderId(orderApprovalRecord.getOrderId())
                        .assetId(orderApprovalRecord.getAssetId())
                        .deliveryMethod(oderRecordExtend.getDeliveryModeStandards())
                        .type(AssetType.PRODUCT)
                        .assetName(orderApprovalRecord.getAssetName())
                        .providerOrg(oderRecordExtend.getProviderOrg())
                        .summary(oderRecordExtend.getSummary())
                        .deliveryType(oderRecordExtend.getDeliveryModes())
                        .dataProductPlatformId(oderRecordExtend.getDataProductPlatformId())
                        .productionType(oderRecordExtend.getProductionType())
                        .isLLM(StringUtils.equals(oderRecordExtend.getDataType1(), "模型") && oderRecordExtend.getIsLLM())
                        .dataType1(oderRecordExtend.getDataType1())
                        .dataType(oderRecordExtend.getDataType())
                        .build();
            } catch (Exception e) {
                log.error("数据转换异常：{}", orderApprovalRecord.getExtend(), e);
            }

            return assetResp;
        }).filter(Objects::nonNull).toList();


        return new PageResult<>(respList, recordsResp.getTotal());
    }


    public OrderRecordsResp getRecordResp(DataAssetQuery query) {
        String currentUserId = LoginContextHolder.currentUser().getId();
        QOrderApprovalRecord orderApprovalRecord = QOrderApprovalRecord.orderApprovalRecord;
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(orderApprovalRecord.classify.eq(OrderClassify.NORMAL)).and(orderApprovalRecord.beneficiaryId.eq(currentUserId)).and(orderApprovalRecord.status.eq("APPROVED"));

        if (Objects.nonNull(query.getDeliveryModes())) {
            BooleanExpression contains = orderApprovalRecord.extend.deliveryModes.contains(query.getDeliveryModes().name());
            if (DeliveryMode.TEE_ONLINE.equals(query.getDeliveryModes())) {
                contains = contains.or(orderApprovalRecord.extend.deliveryModes.contains(DeliveryMode.TEE_OFFLINE.name()));
            }
            booleanBuilder.and(contains);
        }

        if (query.getAssetName() != null && !query.getAssetName().isEmpty()) {
            booleanBuilder.and(orderApprovalRecord.assetName.contains(query.getAssetName()));
        }

        if (CollectionUtil.isNotEmpty(query.getDataTypeFilters())) {
            BooleanExpression dataTypeFilterExpression = null;
            for (DataAssetQuery.DataTypeFilter dataTypeFilter : query.getDataTypeFilters()) {
                BooleanExpression dataTypeFilterExpression1 = orderApprovalRecord.extend.dataType.eq(dataTypeFilter.getDataType().name()).and(orderApprovalRecord.extend.dataType1.eq(dataTypeFilter.getDataType1()));
                dataTypeFilterExpression = dataTypeFilterExpression == null ? dataTypeFilterExpression1 : dataTypeFilterExpression.or(dataTypeFilterExpression1);
            }
            booleanBuilder.and(dataTypeFilterExpression);
        }

        if (ObjectUtil.isNotNull(query.getMpcPurpose())) {
            booleanBuilder.and(orderApprovalRecord.extend.mpcPurposes.contains(query.getMpcPurpose().name()));
        }

        List<OrderRecordDTO> list = queryFactory.selectFrom(orderApprovalRecord)
                .where(booleanBuilder)
                .orderBy(orderApprovalRecord.createTime.desc())
                .offset((query.getNum() - 1) * query.getSize())
                .limit(query.getSize()).fetch().stream().map(orderMapstruct::OrderApprovalRecordToOrderRecordDTO).toList();

        Long total = queryFactory.select(orderApprovalRecord.countDistinct()).from(orderApprovalRecord)
                .where(booleanBuilder).fetch().getFirst();

        return new OrderRecordsResp(list, total);
    }

    @Override
    public void checkBaseCapabilityConfig(String id) {
        SceneDetailResp detail = detail(id);
        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        switch (DeliveryType.valueOf(detail.getDeliveryType())) {
            case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_SET_INTERSECTION, MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                String message = "选择的数据所在连接器未启用MPC多方安全计算，无法使用";
                checkBaseCapabilityConfig(localCompanyId, BaseCapabilityType.MPC, message);
            }
            case TEE_ONLINE, TEE_OFFLINE -> {
                String message = "选择的数据所在连接器未启用TEE可信执行环境，无法使用";
                checkBaseCapabilityConfig(localCompanyId, BaseCapabilityType.TEE, message);
            }
        }
    }

    public void checkBaseCapabilityConfig(String companyId, BaseCapabilityType type, String message) {
        Assert.isTrue(baseCapabilityManager.platformEnable(companyId, type), message);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deliverySceneAdd(SceneSaveReq sceneSaveReq) {
        Assert.isTrue(CollectionUtil.isNotEmpty(sceneSaveReq.getSceneOrderAssetList()), "数据资产订单信息不能为空");

        String localCompanyId;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        } else {
            localCompanyId = null;
        }
        switch (sceneSaveReq.getDeliveryType()) {
            case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_SET_INTERSECTION, MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                String message = "选择的数据所在连接器未启用MPC多方安全计算，无法选中";
                checkBaseCapabilityConfig(localCompanyId, BaseCapabilityType.MPC, message);

                // MPC 合约选择产品要求
                if (sceneSaveReq.getDeliveryType().equals(MPC_PRIVATE_SET_INTERSECTION)) {
                    boolean anyMatch = sceneSaveReq.getSceneOrderAssetList().stream().anyMatch(sceneOrderAsset -> StringUtils.isEmpty(sceneOrderAsset.getOrderId()));
                    Assert.isTrue(anyMatch, "MPC隐私求交合约至少需要一个本主体下产品");
                }

                if (sceneSaveReq.getDeliveryType().equals(MPC_PRIVATE_INFORMATION_RETRIEVAL)) {
                    boolean anyMatch = sceneSaveReq.getSceneOrderAssetList().stream().allMatch(sceneOrderAsset -> StringUtils.isNotBlank(sceneOrderAsset.getOrderId()));
                    Assert.isTrue(anyMatch, "MPC匿踪查询合约不能选择本主体下产品");
                }

            }
            case TEE_ONLINE, TEE_OFFLINE, TEE_MODEL_OPTIMIZE, TEE_MODEL_PREDICT -> {
                String message = "选择的数据所在连接器未启用TEE可信执行环境，无法选中";
                checkBaseCapabilityConfig(localCompanyId, BaseCapabilityType.TEE, message);
            }
        }
        // step 1：调用  —— 服务管理平台操作 新增场景
        log.info("新增交付场景：{}", JSONUtil.toJsonStr(sceneSaveReq));
        UserDTO userDTO = LoginContextHolder.currentUser();
        String userId = LoginContextHolder.currentUser().getId();
        sceneSaveReq.setCreateUser(userId);

        // 走本地方法
        String sceneId = saveDeliveryScene(sceneSaveReq);

        List<TeeContract.DatasetApiInfo> apiInfos = new ArrayList<>();
        List<OrderDeliveryRelationRequest> relationRequests = new ArrayList<>();

        // step 2：调用  —— 数字证书接口 交易登记
        try {
            sceneSaveReq.getSceneOrderAssetList().forEach(sceneOrderAsset -> {

                // 这个id是数瀚全局id
                String dataAssetId = sceneOrderAsset.getAssetId();
                String orderIdSubmit = sceneOrderAsset.getOrderId();

                DataProductVO dataProduct = dataProductService.getDataProductByDataProductPlatformId(dataAssetId, userDTO.getCompany().getNodeId());

                // 如果订单数据信息是空的
                String thirdBusinessId = dataProduct.getProvider().getCompany().getThirdBusinessId();
                if (StringUtils.isBlank(orderIdSubmit)) {
                    // 判断是否是同一主体下的数据
                    Assert.isTrue(userDTO.getCompany().getThirdBusinessId().equals(thirdBusinessId), "使用非同一主体下数据产品，请在业务节点下单使用");

                    // 新增系统订单
                    orderIdSubmit = orderManagerService.addSystemOrder(dataProduct, userDTO);
                }

                // 查资产详情
                String finalOrderIdSubmit = orderIdSubmit;
                OrderApprovalRecord orderApprovalRecord = orderRecordRepository.findById(orderIdSubmit).orElseThrow(() -> new RestfulApiException("查询订单信息异常:[" + finalOrderIdSubmit + "]"));
                Assert.isTrue(StringUtils.equals(orderApprovalRecord.getStatus(), "APPROVED"), "订单状态变更，请刷新后重试");
                Assert.isTrue(StringUtils.equals(orderApprovalRecord.getBeneficiaryId(), userId), "非法请求");

                String assetName = dataProduct.getDataProductName();
                List<DeliveryMode> deliveryModes = dataProduct.getDeliveryModes();
                SourceType accessWay = dataProduct.getAccessWay();
                APIQueryWay apiQueryWay = dataProduct.getApiQueryWay();

                transactionRegister(sceneSaveReq.getDigitalSceneId(), sceneSaveReq.getDeliveryType(), sceneId, assetName, dataAssetId, thirdBusinessId);

                // 扩展字段
                SceneAssetApiReq.ExtData extData = buildExtendData(dataProduct, sceneSaveReq.getDeliveryType());

                // 获取订单授权key
                String orderId = orderApprovalRecord.getId();
                AssetBeneficiaryRel assetBeneficiaryRel = assetBeneficiaryRepository.findFirstByOrderIdOrderByCreateTimeDesc(orderId);

                SceneAsset sceneAsset = new SceneAsset();
                String apiKey = "";
                // 生成 买方 API 代理接口if
                if (deliveryModes.contains(DeliveryMode.API) || (ObjectUtil.equals(accessWay, SourceType.API) || ObjectUtil.equals(accessWay, SourceType.FROM_MPC)
                        && ObjectUtil.equals(apiQueryWay, APIQueryWay.REALTIME) && (deliveryModes.contains(DeliveryMode.TEE_ONLINE))
                ) && (sceneSaveReq.getDeliveryType() == DeliveryType.TEE_ONLINE || sceneSaveReq.getDeliveryType() == DeliveryType.API)) {
                    apiKey = keyInfo(dataProduct, assetBeneficiaryRel, DeliveryType.API);
                    TeeContract.DatasetApiInfo datasetApiInfo = new TeeContract.DatasetApiInfo();
                    datasetApiInfo.setDataAssetId(dataAssetId);
                    datasetApiInfo.setApikey(apiKey);

                    String proxyPath = String.format(deliveryTargetApiUri, AssetType.PRODUCT, sceneId, localCompanyId, dataAssetId);
                    String onlineApiUrl = endPointIp + proxyPath;
                    log.info("API资产【{}】交付地址：【{}】", assetName, onlineApiUrl);
                    datasetApiInfo.setOnlineApiUrl(onlineApiUrl);
                    apiInfos.add(datasetApiInfo);
                    sceneAsset.setTransfer(false);
                } else if (sceneSaveReq.getDeliveryType() == DeliveryType.FILE_DOWNLOAD) {
                    // 文件扩展名
                    String extension = "csv";
                    try {
                        extension = FileUtil.extName(dataProduct.getFileSourceMetadata().getDataAssetFilePath());
                    } catch (Exception ignore) {
                    }
                    extData.setFileExtend(extension);
                    // 绑定 api_key
                    apiKey = keyInfo(dataProduct, assetBeneficiaryRel, DeliveryType.FILE_DOWNLOAD);
                    sceneAsset.setTransfer(false);
                } else {
                    sceneAsset.setTransfer(true);
                }
                String extJson = JSONUtil.toJsonStr(extData);
                log.info("创建场景交付扩展字段信息:{}", extJson);

                // 除了API 都用这个日志上报
                String hash = dataProduct.getFileSourceMetadata() == null ? "" : dataProduct.getFileSourceMetadata().getDataAssetFileHash();
                extData.setDataAssetFileHash(hash);

                sceneAsset.setId(UuidUtils.uuid32());
                sceneAsset.setDeliverySceneId(sceneId);
                sceneAsset.setDataAssetId(dataAssetId);
                sceneAsset.setOrderId(orderIdSubmit);
                sceneAsset.setApiId(IdUtil.fastSimpleUUID());
                // 绑定 api_key
                sceneAsset.setApiKey(apiKey);
                // 新增一个扩展字段
                sceneAsset.setExt(extData);
                sceneAssetRepository.save(sceneAsset);

                OrderSuccessDeliveryRequest request = new OrderSuccessDeliveryRequest();
                request.setOrderId(orderId);
                request.setDeliverySceneId(sceneId);
                request.setAssetName(extData.getDataProductName());
                request.setAssetId(dataAssetId);
                request.setDeliveryType(sceneSaveReq.getDeliveryType());
                request.setIsSuccessDelivery(false);
                OrderDeliveryRelationRequest relationRequest = OrderDeliveryRelationRequest.builder()
                        .routeId(extData.getRouteId())
                        .sellerCompanyId(extData.getSellerCompanyId())
                        .request(request).build();
                relationRequests.add(relationRequest);
            });
        } catch (Exception e) {
            log.error("交付场景创建异常【{}】", sceneId, e);
            throw e;
        }

        try {
            deCreateContract(sceneId, sceneSaveReq.getDeliveryType(), sceneSaveReq.getContract(), apiInfos);
        } catch (Exception e) {
            log.error("交付场景创建合约异常【{}】", sceneId, e);
            throw e;
        }

        storeOrderDelivery(relationRequests);

        // 首页埋点：创建场景交付个数
        statisticDeliveryRepository.saveAndFlush(StatisticDelivery.builder().deliveryId(sceneId).deliveryMode(sceneSaveReq.getDeliveryType())
                .creatorId(userId).createTime(new Date()).updateTime(new Date()).build());
    }

    private final CompanyService companyService;

    /**
     * 通知卖方也关联场景信息
     */
    private void storeOrderDelivery(List<OrderDeliveryRelationRequest> relationRequests) {
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();

        relationRequests.forEach(relationRequest -> {
            // 创建 订单——交付场景
            OrderSuccessDeliveryRequest request = relationRequest.getRequest();
            if (!company.getNodeId().equals(relationRequest.getRouteId())) {
                // 通知卖方
                try {
                    request.setHubInfo(companyService.getHubInfo(Long.valueOf(relationRequest.getSellerCompanyId())));
                    request.setTargetNodeId(relationRequest.getRouteId());
                    request.setTargetCompanyId(Long.valueOf(relationRequest.getSellerCompanyId()));
                    com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<Boolean> response = endpointRemote.orderDelivery(request);
                    Assert.isTrue(response.isSuccess(), "通知卖方订单关联交付信息失败");
                } catch (Exception e) {
                    log.error("通知卖方订单关联交付信息失败 ", e);
                }
            } else {
                // 本地创建
                log.info("本地创建订单关联场景：orderId: {}  deliverySceneId: {}", request.getOrderId(), request.getDeliverySceneId());
                orderManagerService.orderDelivery(request);
            }
        });

    }

    private String keyInfo(DataProductVO dataProduct, AssetBeneficiaryRel assetBeneficiaryRel, DeliveryType deliveryType) {
        AssetBeneficiaryExtend beneficiaryExtend = assetBeneficiaryRel.getExtend();
        String routerId = dataProduct.getProvider().getRouterId();
        if (beneficiaryExtend == null) {
            beneficiaryExtend = new AssetBeneficiaryExtend();
        }

        String keyInfo;
        if (deliveryType == DeliveryType.FILE_DOWNLOAD) {
            if (StringUtils.isBlank(beneficiaryExtend.getAccessKey())) {
                try {
                    keyInfo = orderManagerService.generateApiKey(deliveryType, routerId, dataProduct, assetBeneficiaryRel.getOrderId());
                } catch (Exception e) {
                    throw new RestfulApiException("获取minio授权key异常");
                }
                beneficiaryExtend.setAccessKey(keyInfo);
            } else {
                return beneficiaryExtend.getAccessKey();
            }
        } else if (deliveryType == DeliveryType.API) {
            if (StringUtils.isBlank(beneficiaryExtend.getApiKey())) {
                try {
                    keyInfo = orderManagerService.generateApiKey(deliveryType, routerId, dataProduct, assetBeneficiaryRel.getOrderId());
                } catch (Exception e) {
                    throw new RestfulApiException("获取api授权key异常");
                }
                beneficiaryExtend.setApiKey(keyInfo);
            } else {
                return beneficiaryExtend.getApiKey();
            }
        } else {
            throw new RestfulApiException("不支持的操作");
        }

        assetBeneficiaryRel.setExtend(beneficiaryExtend);
        assetBeneficiaryRepository.save(assetBeneficiaryRel);

        return keyInfo;
    }


    private String saveDeliveryScene(SceneSaveReq sceneSaveReq) {
        // 保存场景信息
        DeliveryScene deliveryScene = BeanUtil.toBean(sceneSaveReq, DeliveryScene.class);
        // 场景ID生成规则：ds + 年月日 + 13位随机数
        String deliverySceneId = String.format("ds%s%s", LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS"), StringUtils.substring(IdUtil.fastSimpleUUID(), 0, 13));
        deliveryScene.setId(deliverySceneId);
        DeliveryScene.Ext ext = new DeliveryScene.Ext();
        String contract = sceneSaveReq.getContract();
        ext.setContract(contract);

        switch (sceneSaveReq.getDeliveryType()) {
            case MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION, MPC_CIPHER_TEXT_COMPUTE -> {
                MpcContract mpcContract = JSONUtil.toBean(contract, MpcContract.class);
                ext.setContractName(mpcContract.getName());
            }
            case TEE_ONLINE, TEE_OFFLINE, TEE_MODEL_OPTIMIZE, TEE_MODEL_PREDICT -> {
                TeeContract teeContract = JSONUtil.toBean(contract, TeeContract.class);
                ext.setContractName(teeContract.getContractName());
            }
        }

        deliveryScene.setExt(ext);
        //进行中 —— 包含文件下载、API、TEE在线就是磋商中
        if (DeliveryType.needNegotiate(sceneSaveReq.getDeliveryType())) {
            deliveryScene.setSceneStatus("NEGOTIATE");
            deliveryScene.setTransferAll(false);
        } else {
            deliveryScene.setSceneStatus("RUNNING");
            deliveryScene.setTransferAll(true);
        }
        deliveryScene.setDeliveryType(sceneSaveReq.getDeliveryType());
        deliveryScene.setCreateTime(new Date());
        deliverySceneRepository.save(deliveryScene);

        return deliverySceneId;
    }


    private static SceneAssetApiReq.ExtData buildExtendData(DataProductVO dataProduct, DeliveryType deliveryType) {
        // 扩展字段
        SceneAssetApiReq.ExtData extData = new SceneAssetApiReq.ExtData();
        extData.setDataProductName(dataProduct.getDataProductName());

        extData.setPlatformId(dataProduct.getPlatformId());
        extData.setUserId(dataProduct.getUserId());

        String routerId = dataProduct.getProvider().getRouterId();
        extData.setRouteId(routerId);
        extData.setDataProductPlatformId(dataProduct.getDataProductPlatformId());
        extData.setBuyerCompanyId(String.valueOf(LoginContextHolder.currentUser().getCompany().getId()));
        extData.setSellerCompanyId(String.valueOf(dataProduct.getProvider().getCompany().getId()));
        extData.setDeliveryType(deliveryType.name());
        return extData;
    }

    @Lazy
    private final TeeRemote teeRemote;
    @Lazy
    private final MPCRemote mpcRemote;

    private void deCreateContract(String sceneId, DeliveryType deliveryType, String contract, List<TeeContract.DatasetApiInfo> apiInfos) {
        switch (deliveryType) {
            case MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION, MPC_CIPHER_TEXT_COMPUTE -> {
                MpcContract mpcContract = JSONUtil.toBean(contract, MpcContract.class);
                mpcContract.setSceneId(sceneId);
                com.ailpha.ailand.dataroute.endpoint.third.response.CommonResult<String> mpcContractRes = mpcRemote.createContract(mpcContract);
                Assert.isTrue(mpcContractRes.isSuccess(), "MPC 创建合约异常：" + mpcContractRes.getMessage());
            }
            case TEE_ONLINE, TEE_OFFLINE, TEE_MODEL_OPTIMIZE, TEE_MODEL_PREDICT -> {
                TeeContract teeContract = JSONUtil.toBean(contract, TeeContract.class);
                teeContract.setSceneId(sceneId);
                teeContract.setApiInfos(apiInfos);
                teeContract.setUserId(LoginContextHolder.currentUser().getId());
                teeContract.setUserName(LoginContextHolder.currentUser().getUsername());
                teeContract.setRouterId(LoginContextHolder.currentUser().getCompany().getNodeId());
                teeContract.setCompanyId(LoginContextHolder.currentUser().getCompany().getId().toString());
                CommonResult<Boolean> teeContractResult = teeRemote.createContract(teeContract);
                Assert.isTrue(teeContractResult.isSuccess(), "TEE 创建合约异常：" + teeContractResult.getMessage());
            }
        }
    }

    private final BaseCapabilityManager baseCapabilityManager;

    /**
     * 交易登记
     *
     * @param digitalSceneId 数字证书场景id
     * @param deliveryType   交付方式
     * @param sceneId        连接器场景id
     * @param dataAssetId    资产id
     */
    private void transactionRegister(String digitalSceneId, DeliveryType deliveryType, String sceneId, String assetName, String dataAssetId, String sellerCompanyId) {

        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        if (!baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.DATA_INVOICE)) {
            log.warn("数字发票基础能力未启用，跳过推送逻辑");
            return;
        }

        try {
            TransactionRegisterRequest request = new TransactionRegisterRequest();
            request.setThirdBusinessId(StrUtil.join(":", sceneId, dataAssetId));
            request.setSceneId(digitalSceneId);
            // 卖方、买方 注册连接器企业ID
            request.setThirdSellerEnterpriseId(sellerCompanyId);
            request.setThirdBuyerEnterpriseId(LoginContextHolder.currentUser().getCompany().getThirdBusinessId());
            request.setName(assetName + "-" + DeliveryType.getDeliveryMode(deliveryType));

            request.setDeliveryMode(DeliveryType.getDeliveryMode(deliveryType));
            log.debug("交易登记信息：{}", JSONUtil.toJsonStr(request));
            CommonResult<Boolean> result = digitalCertificateRemote.traderRegister(request);
            log.info("交易登记返回：{}", result);
        } catch (Exception e) {
            log.error("场景交付【{}】资产【{}】交易登记异常", digitalSceneId, dataAssetId, e);
        }
    }


    @Override
    public void completeByOrderId(String orderId) {
        List<DeliveryScene> deliverySceneList = orderManagerService.findDeliverySceneByOrderId(orderId);
        if (CollectionUtil.isNotEmpty(deliverySceneList)) {
            List<String> list = deliverySceneList.stream().map(DeliveryScene::getId).toList();
            deliverySceneList.forEach(deliveryScene -> deliveryScene.setSceneStatus("COMPLETED"));
            deliverySceneRepository.saveAllAndFlush(deliverySceneList);
            log.info("订单到期完成场景交付状态信息:{}", list);
        }
    }


    @Override
    public SceneAssetResp sceneAssetResp(String deliverId, String dataAssetId) {
        QSceneAsset qSceneAsset = QSceneAsset.sceneAsset;

        SceneAsset sceneAsset = queryFactory.selectFrom(qSceneAsset)
                .where(qSceneAsset.deliverySceneId.eq(deliverId).and(qSceneAsset.ext.dataProductPlatformId.eq(dataAssetId))).fetchFirst();
        if (Objects.isNull(sceneAsset)) {
            return null;
        }

        DeliveryScene deliveryScene = deliverySceneRepository.getReferenceById(sceneAsset.getDeliverySceneId());

        String orderId = sceneAsset.getOrderId();
        OrderApprovalRecord referenceById = orderRecordRepository.getReferenceById(orderId);
        return SceneAssetResp.builder().sceneStatus(deliveryScene.getSceneStatus())
                .deliverId(deliverId)
                .orderStatus(referenceById.getStatus())
                .ext(sceneAsset.getExt())
                .orderId(orderId)
                .isTeeOnline(deliveryScene.getDeliveryType() == DeliveryType.TEE_ONLINE)
                .build();
    }

    private PageResult<SceneListResp> sceneListRespList(SceneListReq sceneListReq) {
        QDeliveryScene qScene = QDeliveryScene.deliveryScene;

        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotBlank(sceneListReq.getSceneId())) {
            booleanBuilder.and(qScene.id.like("%" + sceneListReq.getSceneId() + "%"));
        }
        if (StringUtils.isNotBlank(sceneListReq.getDeliveryType())) {
            booleanBuilder.and(qScene.deliveryType.stringValue().contains(sceneListReq.getDeliveryType()));
        }
        if (StringUtils.isNotBlank(sceneListReq.getSceneStatus())) {
            booleanBuilder.and(qScene.sceneStatus.eq(sceneListReq.getSceneStatus()));
        }

        String userId = LoginContextHolder.currentUser().getId();
        booleanBuilder.and(qScene.createUser.eq(userId));

        Long total = queryFactory.select(qScene.countDistinct()).from(qScene)
                .where(booleanBuilder).fetch().getFirst();


        List<DeliveryScene> deliverySceneList = queryFactory.selectFrom(qScene)
                .where(booleanBuilder)
                .orderBy(qScene.createTime.desc())
                .offset((sceneListReq.getNum() - 1) * sceneListReq.getSize())
                .limit(sceneListReq.getSize())
                .fetch();

        List<String> sceneIdList = deliverySceneList.stream().map(DeliveryScene::getId).toList();
        List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(sceneIdList);
        Map<String, List<SceneAsset>> map = sceneAssetList.stream().collect(Collectors.groupingBy(SceneAsset::getDeliverySceneId));

        List<SceneListResp> listRespList = deliverySceneList.stream().map(deliveryScene -> {
            if (deliveryScene == null) {
                return null;
            }

            List<SceneListResp.DataAssetSceneRef> dataAssetSceneRefList = map.get(deliveryScene.getId()).stream().map(sceneAsset -> {
                // 解析的时候写入
                SceneAssetApiReq.ExtData extData = sceneAsset.getExt();
                return SceneListResp.DataAssetSceneRef.builder()
                        .assetType(AssetType.PRODUCT)
                        .assetId(sceneAsset.getId())
                        .dataProductPlatformId(extData.getDataProductPlatformId())
                        .assetName(extData.getDataProductName())
                        .routerId(extData.getRouteId())
                        .apiKey(sceneAsset.getApiKey())
                        .orderId(sceneAsset.getOrderId())
                        .apiId(sceneAsset.getApiId())
                        .ext(sceneAsset.getExt())
                        .transfer(sceneAsset.getTransfer())
                        .build();
            }).toList();

            SceneListResp sceneListResp = SceneListResp.builder()
                    .id(deliveryScene.getId())
                    .deliveryType(deliveryScene.getDeliveryType().name())
                    .digitalSceneName(deliveryScene.getDigitalSceneName())
                    .sceneStatus(deliveryScene.getSceneStatus())
                    .createTime(deliveryScene.getCreateTime())
                    .contractName(deliveryScene.getExt().getContractName())
                    .build();
            sceneListResp.setDataAssetSceneRefList(dataAssetSceneRefList);

            return sceneListResp;
        }).toList();

        return new PageResult<>(listRespList, total);
    }

    @Override
    public PageResult<SceneListResp> deliverySceneList(SceneListReq sceneListReq) {
        UserDTO currentUser = LoginContextHolder.currentUser();

        PageResult<SceneListResp> convert = sceneListRespList(sceneListReq);
        List<SceneListResp> data = convert.getData();
        if (CollectionUtil.isNotEmpty(data)) {
            String localCompanyId = String.valueOf(currentUser.getCompany().getId());

            data.forEach(item -> {
                String deliveryType = item.getDeliveryType();
                List<SceneListResp.DataAssetSceneRef> dataAssetSceneRefList = item.getDataAssetSceneRefList();
                dataAssetSceneRefList.forEach(dataAssetSceneRef -> {
                    // todo 端口问题
                    String url;
                    if (deliveryType.equals(DeliveryType.FILE_DOWNLOAD.toString())) {
                        url = String.format(targetDownLoadUri, endPointIp, dataAssetSceneRef.getAssetType(), item.getId(), localCompanyId, dataAssetSceneRef.getDataProductPlatformId());
                    } else {
                        String proxyPath = String.format(deliveryTargetApiUri, dataAssetSceneRef.getAssetType(), item.getId(), localCompanyId, dataAssetSceneRef.getDataProductPlatformId());
                        url = endPointIp + proxyPath;
                    }
                    dataAssetSceneRef.setUrl(url);
                    final SceneAssetApiReq.ExtData jsonExt = dataAssetSceneRef.getExt();
                    dataAssetSceneRef.setDataProductPlatformId(jsonExt == null ? null : jsonExt.getDataProductPlatformId());
                });
            });
        }

        return convert;
    }


    /**
     * 传输协商
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfer(DeliveryTransferRequest request) {
        String orderId = request.getOrderId();
        String deliverySceneId = request.getDeliverySceneId();
        String transferMode = request.getTransferMode();
        Assert.isTrue(StringUtils.equals(transferMode, "2"), "当前仅支持主动拉取");

        DeliveryScene deliveryScene = deliverySceneRepository.getReferenceById(deliverySceneId);
        SceneAsset sceneAsset = sceneAssetRepository.findFirstByOrderIdAndDeliverySceneId(orderId, deliverySceneId);

        orderRecordRepository.findById(orderId).ifPresent(order -> {

            log.info("订单发起传输协商: deliverySceneId {} productName {} transferMode {}", deliverySceneId, order.getAssetName(), transferMode);
            OderRecordExtend extend = order.getExtend();
            String targetNodeId = order.getApproverRouterId();

            NegotiateDataTransferDTO transferDTO = NegotiateDataTransferDTO.builder()
                    .nodeId(extend.getServiceNodeId())
                    .tradingStrategyCode(extend.getTradingStrategyCode())
                    .tradingStrategyName(extend.getTradingStrategyName())
                    .ctrlInstructionId(orderId)
                    .transferMode(transferMode)
                    .build();

            NegotiateDataTransferDTO.ExecutionStrategy strategy = NegotiateDataTransferDTO.ExecutionStrategy.builder()
                    .targetConnectorId(targetNodeId)
                    .issuerConnectorId(order.getBeneficiaryRouterId())
                    .issuedTime(String.valueOf(System.currentTimeMillis()))
                    .productId(extend.getDataProductPlatformId())
                    .deliverySceneId(deliverySceneId)
                    .deliveryType(deliveryScene.getDeliveryType())
                    .buyerCompanyId(extend.getBeneficiaryCompanyId())
                    .sellerCompanyId(extend.getApproverCompanyId())
                    .build();

            transferDTO.setTransactionExecutionStrategy(JSONUtil.toJsonStr(strategy));
            transferDTO.setNodeId(extend.getServiceNodeId());
            try {
                ConnectorMetaData connectorMetaData = new ConnectorMetaData();
                connectorMetaData.setTargetNodeId(targetNodeId);
                com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<String> commonResult = endpointRemote.transferProcess(transferDTO, connectorMetaData.toBase64());
                String transferResp = commonResult.getData();
                Assert.isTrue(commonResult.isSuccess(), "场景【" + deliverySceneId + "】资产【" + extend.getDataProductPlatformId() + "】发起传输协商失败：" + commonResult.getMessage());

                log.info("订单[{}]传输协商返回: {}", orderId, commonResult);

                SceneAssetApiReq.ExtData ext = sceneAsset.getExt();
                sceneAsset.setTransfer(true);
                ext.setTransferResp(transferResp);

                sceneAssetRepository.saveAndFlush(sceneAsset);
                // 保存磋商信息 —— 会签状态
                mergeSceneStatus(deliverySceneId);

            } catch (Exception e) {
                log.error("根据 orderId【{}】发起传输协商异常：", orderId, e);
                throw new RestfulApiException("数据产品[" + order.getAssetName() + "]磋商失败，请联系管理员");
            }
        });
    }

    private void mergeSceneStatus(String deliverySceneId) {
        List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(Collections.singletonList(deliverySceneId));
        boolean allTransfer = sceneAssetList.stream().allMatch(SceneAsset::getTransfer);
        if (allTransfer) {
            DeliveryScene deliveryScene = deliverySceneRepository.getReferenceById(deliverySceneId);
            deliveryScene.setSceneStatus("RUNNING");
            deliveryScene.setTransferAll(true);
            deliverySceneRepository.saveAndFlush(deliveryScene);
        }
    }


    @Override
    public String transferProcess(NegotiateDataTransferDTO request) {
        NegotiateDataTransferDTO.ExecutionStrategy strategy = request.getStringToTransactionExecutionStrategy();
        log.info("场景数据传输协商：{}", JSONUtil.toJsonStr(strategy));

        // 存一下这些磋商的数据
        String transferId = AsyncManager.getInstance().executeFuture(() -> {
            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            String id = UUID.randomUUID().toString().replaceAll("-", "");
            NegotiateTransfer negotiateTransfer = negotiateMapstruct.negotiateDataTransferTo(request);
            negotiateTransfer.setId(id);
            negotiateTransfer.setExtend("{}");
            negotiateTransfer.setCreateTime(new Date());
            negotiateTransfer.setTransactionExecutionStrategy(strategy);
            negotiateTransferRepository.saveAndFlush(negotiateTransfer);
            return id;
        });
        log.info("磋商传输ID：{}", transferId);

        return transferId;
    }

    @Override
    public SceneDetailResp detail(String id) {

        DeliveryScene deliveryScene = deliverySceneRepository.findById(id).orElseThrow(() -> new RestfulApiException("未找到指定交付信息【" + id + "】"));
        List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(Collections.singletonList(id));
        SceneDetailResp resp = SceneDetailResp.builder()
                .id(deliveryScene.getId())
                .deliveryType(deliveryScene.getDeliveryType().name())
                .digitalSceneName(deliveryScene.getDigitalSceneName())
                .sceneStatus(deliveryScene.getSceneStatus())
                .createTime(deliveryScene.getCreateTime())
                .contract(deliveryScene.getExt().getContract())
                .build();

        List<String> orderIdList = sceneAssetList.stream().map(SceneAsset::getOrderId).distinct().toList();
        List<OrderApprovalRecord> recordList = orderRecordRepository.findAllById(orderIdList);
        Map<String, OrderApprovalRecord> orderMap = recordList.stream().collect(Collectors.toMap(OrderApprovalRecord::getId, orderApprovalRecord -> orderApprovalRecord, (a, b) -> a));

        List<DataAssetResp> respList = sceneAssetList.stream().map(sceneAsset -> {
            OrderApprovalRecord orderApprovalRecord = orderMap.get(sceneAsset.getOrderId());
            OderRecordExtend extend = orderApprovalRecord.getExtend();

            // todo 补充信息
            return DataAssetResp.builder()
                    .orderId(sceneAsset.getOrderId())
                    .assetId(sceneAsset.getDataAssetId())
                    .dataProductPlatformId(extend.getDataProductPlatformId())
                    .deliveryType(extend.getDeliveryModes())
                    .deliveryMethod(extend.getDeliveryModeStandards())
                    .meteringWay(orderApprovalRecord.getMeteringWay())
                    .chargingWay(orderApprovalRecord.getChargingWay())
                    .routerId(orderApprovalRecord.getApproverRouterId())
                    .type(AssetType.PRODUCT)
                    .assetName(orderApprovalRecord.getAssetName())
                    .provider(orderApprovalRecord.getApproverUsername())
                    .providerOrg(orderApprovalRecord.getApproverEnterpriseName())
                    .summary(extend.getSummary())
                    .price(extend.getPrice())
                    .productionType(extend.getProductionType())
                    .build();
        }).toList();

        resp.setDataAssetSceneRefList(respList);

        return resp;
    }

    @Override
    public Boolean contractPreCheck(PreCheckRequest request) {
        switch (request.getDeliveryType()) {
            case MPC_PRIVATE_INFORMATION_RETRIEVAL -> {
                if (request.getDataAssetIds().size() == 1)
                    return true;
                // 统计每个数据资产 主键数量是否相同
                long lastSize = -1;
                boolean sameSize = true;
                for (String assetId : request.getDataAssetIds()) {
                    DataProductVO dataProductByDataProductPlatformId = dataProductService.getDataProductByDataProductPlatformId(assetId);
                    long count = dataProductByDataProductPlatformId.getDataSchema().stream().filter(DataSchemaBO::isId).count();
                    if (lastSize == -1) {
                        lastSize = count;

                    } else if (lastSize != count) {
                        sameSize = false;
                        break;
                    }
                }
                Assert.isTrue(sameSize, "所选数据集的主键数量不同，无法生成匿踪查询合约");
            }
        }
        return true;
    }
}