package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataSchemaBO implements Serializable {
    @Builder.Default
    private Boolean apiOnline = false;

    /**
     * 字段中文名
     */
    @Schema(description = "中文字段名")
    String name;

    /**
     * 字段英文名
     */
    @Schema(description = "英文字段名")
    String fieldName;

    /**
     * 字段类型
     */
    @Schema(description = "字段类型")
    String type;

    /**
     * 字段敏感等级
     */
    @Schema(description = "字段敏感等级")
    String sensitiveLevel;

    /**
     * 字段注释
     */
    @Schema(description = "字段注释")
    String comment;

    // >>> 数据探查
    @Schema(description = "分类名称")
    String classifyName;
    @Schema(description = "级别名称")
    String level;
    @Schema(description = "是否敏感")
    Boolean isSensitive;
    @Schema(description = "恒脑识别说明")
    String combingExplain;

    @Builder.Default
    boolean allowQuery = false;

    @Builder.Default
    boolean isId = false;

    /**
     * 是否通过授权
     */
    @Builder.Default
    boolean authorization = false;

    /**
     * 是否更新此字段
     */
    @Builder.Default
    Boolean update = false;
    /**
     * 是否为新增字段
     */
    @Builder.Default
    Boolean create = false;

    String originalColumnName;

    String originalDataType;

    /**
     * hive不区分大小写子弹
     *
     * @param fieldName 统一转化为小写
     */
//    public void setFieldName(String fieldName) {
//        if (apiOnline != null && apiOnline) {
//            // 如果是在线API的形式 —— 不需要转换小写
//            this.fieldName = fieldName;
//        } else {
//            this.fieldName = fieldName == null ? null : fieldName.toLowerCase();
//        }
//    }

    /**
     * 防止插入 null
     *
     * @param comment
     */
    public void setComment(String comment) {
        this.comment = comment == null ? "" : comment;
    }


    public DataSchemaBO(
            String fieldName, String type, String comment, String sensitiveLevel, boolean allowQuery, boolean authorization,
            Boolean update, Boolean create, String originalColumnName, String originalDataType
    ) {
        this.fieldName = fieldName;
        this.type = type;
        this.comment = comment;
        this.sensitiveLevel = sensitiveLevel;
        this.allowQuery = allowQuery;
        this.authorization = authorization;
        this.update = update;
        this.create = create;
        this.originalColumnName = originalColumnName;
        this.originalDataType = originalDataType;
    }
}
