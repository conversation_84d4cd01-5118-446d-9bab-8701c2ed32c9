package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResItemPageVO {
    /**
     * 数据产品id
     */
    String dataResourceId;
    /**
     * 数据产品名称
     */
    String dataResourceName;
    /**
     * 数据产品平台唯一编号
     */
    String dataResPlatformUniqueNo;
    /**
     * 数据资源描述
     */
    String description;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 连接器ID
     */
    String platformId;
    /**
     * 连接器名称
     */
    String platformName;
    /**
     * 资源提供方信息
     */
    String provider;
    /**
     * 敏感等级
     */
    String sensitiveLevel;
    /**
     * 数据类型
     */
    String dataType;
    /**
     * 上下架状态
     */
    String pushStatus;
    /**
     * 交付状态
     */
    List<String> deliveryModes;
}
