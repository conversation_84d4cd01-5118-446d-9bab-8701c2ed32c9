package com.ailpha.ailand.dataroute.endpoint.order.domain;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetBeneficiaryOderTableDTO implements Serializable {
    /**
     * 订单合同审批记录
     */
    OrderApprovalRecord orderApprovalRecord;
    /**
     * 资产获益人信息记录
     */
    AssetBeneficiaryRel assetBeneficiaryRel;
}
