package com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/7/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApplyServiceNodeResp implements Serializable {

    @Schema(description = "业务ID")
    String processId;
}
