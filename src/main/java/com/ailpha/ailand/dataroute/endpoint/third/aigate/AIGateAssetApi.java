package com.ailpha.ailand.dataroute.endpoint.third.aigate;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.PostExchange;

public interface AIGateAssetApi {

    @PostExchange("/openapi/gateway/2.0/CreateAssetByRouter.json")
    AIGateResponse<CreateAssetByRouterResponse> createAsset(@RequestBody CreateAssetByRouterRequest createAssetByRouterRequest,
                                                            @RequestParam String accessKeyId, @RequestParam String accessTime, @RequestParam String accessSign);

}
