package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PlatformListVO {
    @Schema(description = "平台节点 Id")
    String platformId;
    @Schema(description = "平台节点名称")
    String platformName;
}
