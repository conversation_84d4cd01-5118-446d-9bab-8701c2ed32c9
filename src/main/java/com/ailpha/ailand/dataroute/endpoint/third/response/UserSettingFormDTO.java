package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @since 2020-04-06 23:59
 **/
@Data
public class UserSettingFormDTO {

    @ApiModelProperty(value = "操作确认 1普通按钮交互 2离线签名", allowableValues = "1,2")
    private String flowConfirmType;

    @JsonIgnore
    private String verifyOperations;

    @ApiModelProperty(value = "区块链菜单显示配置：1-展示 0-不展示", allowableValues = "0,1")
    private String chainMenuSwitch;

    @ApiModelProperty(value = "是否启用GPU：1-启用 0-不启用", allowableValues = "0,1")
    private String enableGPU;

    @ApiModelProperty("需要验证的操作 contractReview 数据集使用授权 contractProfReview 合约申请授权" +
            "resultReview 结果集审批 resultDownload 结果集下载 datasetDelete 删除结果集")
    private List<String> operationList;

    @ApiModelProperty(value = "在线任务是否支持jar任务开关：1-开启 0-不开启", allowableValues = "0,1")
    private String flinkJarSwitch;

    @ApiModelProperty(value = "logo及产品名称自定义开关：1-开启 0-不开启", allowableValues = "0,1")
    private String customizeSwitch;

    @ApiModelProperty(value = "模型在线推理的部署数量限制")
    private String inferenceDeployNumber;

    @ApiModelProperty(value = "logo 样式一")
    private String logoStyle1;

    @ApiModelProperty(value = "logo 样式二")
    private String logoStyle2;

    @ApiModelProperty(value = "logo 样式三")
    private String logoStyle3;

    @ApiModelProperty(value = "logo 样式四")
    private String logoStyle4;

    @ApiModelProperty(value = "logo 样式五")
    private String logoStyle5;

    @ApiModelProperty(value = "产品名称 样式一")
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]|[a-zA-Z0-9.:\\-_()（）]){0,20}", message = "仅限中英文，数字，下划线，括号，限制长度20")
    private String productName1;

    @ApiModelProperty(value = "产品名称 样式二")
    @Pattern(regexp = "^([\\u4e00-\\u9fa5]|[a-zA-Z0-9.:\\-_()（）]){0,200}", message = "仅限中英文，数字，下划线，括号，限制长度20")
    private String productName2;

    @ApiModelProperty(value = "首页产品介绍")
    private String productIntro;

    public List<String> getOperationList() {
        if (StringUtils.isEmpty(verifyOperations)) {
            return new ArrayList<>();
        }
        return Arrays.asList(verifyOperations.split(","));
    }

    public void setOperationList(List<String> operationList) {
        if (operationList == null || operationList.isEmpty()) {
            verifyOperations = "";
            return;
        }
        verifyOperations = String.join(",", operationList);
    }
}
