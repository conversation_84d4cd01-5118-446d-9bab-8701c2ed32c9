package com.ailpha.ailand.dataroute.endpoint.user.mapstruct;

import com.ailpha.ailand.dataroute.endpoint.user.domain.OpLogRecord;
import com.ailpha.ailand.dataroute.endpoint.user.vo.LogResponse;
import org.mapstruct.*;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface LogMapper {

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(source = "description", target = "desc")
    LogResponse toResponse(OpLogRecord record);

    @AfterMapping
    default void afterMapping(@MappingTarget LogResponse response, OpLogRecord record) {
        response.setResult(record.isSuccess() ? "操作成功" : "操作失败");
    }
}
