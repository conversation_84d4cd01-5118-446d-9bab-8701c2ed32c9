package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDownloadReq {

    @Schema(description = "ID")
    private String sceneId;

    @Schema(description = "资产id")
    private String assertId;

    private String url;

    String apikey;

}