package com.ailpha.ailand.dataroute.endpoint.connector;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.NetworkType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateTimeUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DownloadUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.mapstruct.CompanyMapper;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.configuration.vo.ConfigurationInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy.CheckLicenseHandler;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.*;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RouterHeartBeatRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.UpdateRouterInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.*;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.sun.management.OperatingSystemMXBean;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class RouterService {
    private final LicenseRemoteService licenseRemoteService;
    public final RouterManagerRemoteService routerManagerRemoteService;
    public final DataHubRemoteService dataHubRemoteService;
    private final AiLandProperties aiLandProperties;
    private final CheckLicenseHandler checkLicenseHandler;
    private final FilesStorageServiceImpl filesStorageService;
    public static final String LICENSE_FILENAME = "license.json";
    public static final String ROUTER_FILENAME = "router.json";

    public static final TimedCache<String, RouterServiceStatusInfo> ROUTER_SERVICE_STATUS = CacheUtil.newTimedCache(60 * 1000);
    // 连接器状态
    public static final String ROUTER_SERVICE_STATUS_KEY = "routerServiceStatus:%s";


    @PostConstruct
    public void init() {
        String routeInfo = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME;
        boolean exist = FileUtil.exist(routeInfo);
        if (!exist) {
            File newFile = FileUtil.newFile(routeInfo);
            NodeInfoResponse nodeInfoResponse = new NodeInfoResponse();
            CommonResult<NodeResponse> nodeInfo = licenseRemoteService.initRoute("7");
            Assert.isTrue(nodeInfo.isSuccess(), String.format("获取当前连接器信息异常：%s", nodeInfo.getMessage()));
            nodeInfoResponse.setStatus(RouteStatus.not_activate);
            nodeInfoResponse.setPlatformId(nodeInfo.getData().getRouterId());
            nodeInfoResponse.setPublicKey(nodeInfo.getData().getPublicKey());
            nodeInfoResponse.setCertificateCSR(nodeInfo.getData().getCertificateCSR());
            nodeInfoResponse.setPlatformId(UuidUtils.uuid32());
            FileUtil.writeString(JSONUtil.toJsonStr(nodeInfoResponse), newFile, Charset.defaultCharset());
            log.info("连接器初始化成功，配置文件路径：{}", routeInfo);
        }
    }


    public void export(HttpServletRequest request, HttpServletResponse response) {
        String routeInfo = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME;
        File routeInfoFile = FileUtil.file(routeInfo);
        DownloadUtil downloadUtil;
        try {
            downloadUtil = new DownloadUtil(routeInfoFile);
        } catch (FileNotFoundException e) {
            log.error("连接器基本信息文件不存在 ", e);
            throw new RestfulApiException("导出失败：连接器初始化异常，请联系管理员");
        }
        downloadUtil.downLoad(request, response);
    }

    public NodeInfoResponse currentNode() {
        String nodeInfo = FileUtil.readString(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME, Charset.defaultCharset());
        NodeInfoResponse nodeInfoResponse = JSONUtil.toBean(nodeInfo, NodeInfoResponse.class);
//        nodeInfoResponse.setCompany(currentCompany());
        try {
            ConfigurationInfoVO configurationInfo = new ConfigurationInfoVO();
            Path path = Paths.get(Constants.DATA_PATH, Constants.CONFIG_FILE_NAME);
            if (path.toFile().exists()) {
                String configJson = filesStorageService.readContent(path);
                configurationInfo = JacksonUtils.json2pojo(configJson, ConfigurationInfoVO.class);
            }
            configurationInfo.setIamUrl(aiLandProperties.getIamServer().getExternalUrl());
            nodeInfoResponse.setConfiguration(configurationInfo);
        } catch (Exception e) {
            log.error("读取系统配置异常 error:{}", e.getMessage());
            nodeInfoResponse.setConfiguration(new ConfigurationInfoVO());
        }
        return nodeInfoResponse;
    }

    @Deprecated
    public LicenseDTO currentLicense() {
        boolean exist = FileUtil.exist(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME);
        if (exist) {
            return JSONUtil.toBean(FileUtil.readUtf8String(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME), LicenseDTO.class);

        } else return new LicenseDTO();
    }

    public String uploadLicense(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName) || fileName.contains("../")) {
            throw new RestfulApiException("非法的文件名称");
        }
        if (!aiLandProperties.getFileStorage().getLicenseFileSuffix().contains(FileUtil.getSuffix(fileName))) {
            throw new RestfulApiException("不支持的文件类型，目前支持如下[" + String.join(",", aiLandProperties.getFileStorage().getLicenseFileSuffix()) + "]");
        }
//        NodeInfoResponse nodeInfoResponse = currentNode();
        String fileId = System.currentTimeMillis() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME;
        String filepath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + fileId;
        File newFile = FileUtil.touch(filepath);
        try {
            file.transferTo(newFile);
        } catch (IOException e) {
            log.error("保存license文件异常 ", e);
            throw new RestfulApiException("保存license文件异常");
        }

        // 首次上传证书 更新状态
//        if (nodeInfoResponse.getStatus().equals(RouteStatus.not_activate))
//            updateRouterCache(RouteStatus.import_license);
        OPLogContext.openSwitch();
        return fileId;
    }

    public CheckLicenseResponse checkLicense() {
        String licenseFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME;
        String routeFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME;
        NodeInfoResponse nodeInfoResponse = currentNode();
        if (nodeInfoResponse.getStatus().equals(RouteStatus.activated))
            throw new RestfulApiException("当前连接器已经激活，请勿重复操作");
        boolean exist = FileUtil.exist(licenseFile);
        if (!exist)
            throw new RestfulApiException("激活失败：请先上传license");
        RouteActivateContext activateRequest = new RouteActivateContext();
        activateRequest.setRoute(JSONUtil.toBean(FileUtil.readUtf8String(routeFile), RouteDTO.class));
        activateRequest.setLicense(JSONUtil.toBean(FileUtil.readUtf8String(licenseFile), LicenseDTO.class));
        checkLicenseHandler.handler(activateRequest);
        return null;
    }

    public void networkLink(NetworkLinkRequest request) {
        String licenseFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME;
        String routeFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME;
        if (ObjectUtil.equals(request.getType(), NetworkType.private_net)) {
            RouteActivateContext activateRequest = new RouteActivateContext();
            activateRequest.setRoute(JSONUtil.toBean(FileUtil.readUtf8String(routeFile), RouteDTO.class));
            activateRequest.setLicense(JSONUtil.toBean(FileUtil.readUtf8String(licenseFile), LicenseDTO.class));
            checkLicenseHandler.handler(activateRequest);
        } else if (ObjectUtil.equals(request.getType(), NetworkType.public_net)) {
            String dataHubFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "dataHub.json";
            boolean exist = FileUtil.exist(dataHubFile);
            if (exist) {
                NetworkLinkRequest.HubInfo hubInfo = JSONUtil.toBean(FileUtil.readUtf8String(dataHubFile), NetworkLinkRequest.HubInfo.class);
                FileUtil.writeUtf8String(JSONUtil.toJsonStr(hubInfo), dataHubFile);
            } else {
                FileUtil.writeUtf8String(JSONUtil.toJsonStr(request.getHubInfo()), dataHubFile);
            }
        }

    }

    public ActivateResponse activate() {
        String licenseFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME;
        String routeFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME;
        RouteActivateContext activateRequest = new RouteActivateContext();
        activateRequest.setRoute(JSONUtil.toBean(FileUtil.readUtf8String(routeFile), RouteDTO.class));
        activateRequest.setLicense(JSONUtil.toBean(FileUtil.readUtf8String(licenseFile), LicenseDTO.class));
        checkLicenseHandler.handler(activateRequest);
        return null;
    }

    public Boolean updateRouterCache(RouteStatus status) {
        NodeInfoResponse nodeInfoResponse = currentNode();
        nodeInfoResponse.setStatus(status);
        if (RouteStatus.activated.equals(status) && nodeInfoResponse.getActiveTime() == null) {
            // 首次激活时间
            long nowTime = System.currentTimeMillis();
            log.debug("设置首次激活时间: {}", new Date(nowTime));
            nodeInfoResponse.setActiveTime(nowTime);
        }
        FileUtil.writeUtf8String(JSONUtil.toJsonStr(nodeInfoResponse), aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME);
        return true;
    }

    public NodeInfoResponse updateRouter(UpdateRouterInfoVO request) {
        UpdateRouterInfoRequest updateRouterInfoRequest = BeanUtil.copyProperties(request, UpdateRouterInfoRequest.class);
//        CommonResult<Boolean> updateRouter = routerManagerRemoteService.updateRouter(updateRouterInfoRequest);
//        Assert.isTrue(updateRouter.isSuccess(), updateRouter.getMsg());
        if (StringUtils.isNotEmpty(request.getName())) {
            UpdateRouterRequest updateRouterReq = new UpdateRouterRequest();
            updateRouterReq.setId(request.getRouterId());
            updateRouterReq.setName(request.getName());
            CommonResult<Boolean> update = dataHubRemoteService.update(updateRouterReq);
            Assert.isTrue(update.isSuccess(), "中心服务修改节点名称异常：" + update.getMsg());
        }

        // 更新本地缓存
        NodeInfoResponse nodeInfoResponse = JSONUtil.toBean(FileUtil.readUtf8String(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME), NodeInfoResponse.class);
        nodeInfoResponse.setName(request.getName());
        nodeInfoResponse.setDescription(request.getDesc());
        FileUtil.writeUtf8String(JSONUtil.toJsonStr(nodeInfoResponse), aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME);
        return nodeInfoResponse;
    }

    public DrClientInfoVO getByClientNo(String clientNo) {
        return dataHubRemoteService.getByClientNo(clientNo).getData();
    }

    public String viewCompanyLicense() {
        String licensePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + LICENSE_FILENAME;
        return JSONUtil.getByPath(JSONUtil.parseObj(FileUtil.readUtf8String(licensePath)), "yyzh", "");
    }

    public CompanyDTO currentCompany() {
        try {
            return LoginContextHolder.currentUser().getCompany();
        } catch (Exception e) {
            return CompanyDTO.builder().build();
        }
    }

    /**
     * 当前连接器虚拟IP
     *
     * @return 网卡虚拟IP
     */
    public String currentRouteVirtualIp() {
        OsInfo osInfo = SystemUtil.getOsInfo();
        if (!osInfo.isLinux()) {
            return "**********";
        } else {
            String localIp;
            try {
                localIp = RuntimeUtil.execForStr("bash", "-c", "nmcli device show utun99 | grep 'IP4.ADDRESS' | awk '{print $2}' | cut  -d '/' -f 1");
            } catch (Exception e) {
                log.error("获取当前连接器IP地址异常：" + e);
                List<String> ipList = RuntimeUtil.execForLines("ip addr show | grep -w inet | grep -v 127.0.0.1 | awk '{print $2}' | cut -d '/' -f 1");
                localIp = ipList.isEmpty() ? "" : ipList.getFirst();
            }
            return localIp.replaceAll("\\n", "");
        }
    }

    public void registerToHub(RegisterToHubRequest request) {
        String dataHubFile = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "dataHub.json";
        boolean exist = FileUtil.exist(dataHubFile);
        if (exist) {
            RegisterToHubRequest register = JSONUtil.toBean(FileUtil.readUtf8String(dataHubFile), RegisterToHubRequest.class);
            List<RegisterToHubRequest.HubInfo> hubNodeList = register.getHubNodeList();
            hubNodeList.addAll(register.getHubNodeList());
            register.setHubNodeList(hubNodeList);
            FileUtil.writeUtf8String(JSONUtil.toJsonStr(register), dataHubFile);
        } else {
            FileUtil.writeUtf8String(JSONUtil.toJsonStr(request), dataHubFile);
        }
    }

    private final CompanyMapper companyMapper;

//    @Scheduled(fixedDelay = 30, initialDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void syncRouterServiceStatus() {
        OperatingSystemMXBean osBean = (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();

        // 获取系统CPU负载
        double systemCpuLoad = osBean.getCpuLoad() * 100;

        // 获取进程CPU负载
        double processCpuLoad = osBean.getProcessCpuLoad() * 100;

        BigDecimal cpuUsagePercent = new BigDecimal(systemCpuLoad).setScale(2, RoundingMode.HALF_UP);


        // 获取总的物理内存
        long totalPhysicalMemorySize = osBean.getTotalPhysicalMemorySize();
        BigDecimal totalMemory = new BigDecimal(totalPhysicalMemorySize);
        // 获取空闲的物理内存
        long freePhysicalMemorySize = osBean.getFreePhysicalMemorySize();

        // 计算已使用的物理内存
        BigDecimal usedMemory = totalMemory.subtract(new BigDecimal(freePhysicalMemorySize));
        BigDecimal usedMemoryPercentage = usedMemory.divide(totalMemory, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);

        CompanyRepository companyRepository = SpringUtil.getBean(CompanyRepository.class);
        List<Company> companies = companyRepository.findByStatusAndDeletedFalse(CompanyStatus.REVIEW_PASS);
        companies.forEach(c -> {
            CompanyDTO companyDTO = companyMapper.toDTO(c);
            MDC.put("hubInfo", JSONUtil.toJsonStr(companyDTO.getServiceNode().getHubInfo()));
            RouterServiceStatusInfo routerServiceStatusInfo = new RouterServiceStatusInfo();
            routerServiceStatusInfo.setNetStatus(getNetStatus(c.getNodeId(), companyDTO.getServiceNode().getConnectorInfo().getExposeUrl()));
            routerServiceStatusInfo.setCpuUsage(cpuUsagePercent + "%");
            routerServiceStatusInfo.setMemoryUsage(usedMemoryPercentage + "%");
            routerServiceStatusInfo.setRouterActiveTime(getActiveTime(c.getReviewTime()));
            log.debug("获取连接器服务信息成功：{}", routerServiceStatusInfo);

            ROUTER_SERVICE_STATUS.put(String.format(ROUTER_SERVICE_STATUS_KEY, c.getNodeId()), routerServiceStatusInfo);
        });

    }

    /**
     * 功能描述: 返回自激活时间来 x个月x天
     *
     * @param activeTime activeTime
     * @return: java.lang.String
     * @author: yuwenping
     * @date: 2025/2/28 16:19
     */
    private String getActiveTime(Date activeTime) {
        try {
            Date activeDate;
            if (LoginContextHolder.isLogin()) {
                UserDTO userDTO = LoginContextHolder.currentUser();
                activeDate = userDTO.getCompany().getReviewTime();
            } else {
                activeDate = activeTime;
            }

            if (activeDate == null) {
                return "未激活";
            }
            Date now = new Date();
            int month = DateTimeUtils.monthsBetween(activeDate, now);
            Date date = DateTimeUtils.addMonths(activeDate, month);
            int day = DateTimeUtils.daysBetween(date, now);
            if (month == 0) {
                return day + "天";
            }
            return month + "个月" + day + "天";
        } catch (Exception e) {
            log.error("e:", e);
            return "未激活";
        }
    }

    public RouterServiceStatusInfo getRouterServiceInfo() {
        String key = String.format(ROUTER_SERVICE_STATUS_KEY, LoginContextHolder.currentUser().getCompany().getNodeId());
        RouterServiceStatusInfo routerServiceStatusInfo = ROUTER_SERVICE_STATUS.get(key);
        if (routerServiceStatusInfo != null) {
            return routerServiceStatusInfo;
        }
        syncRouterServiceStatus();
        return ROUTER_SERVICE_STATUS.get(key);
    }

    private String getNetStatus(String nodeId, String ip) {
        try {
            RouterHeartBeatRequest request = new RouterHeartBeatRequest();
            request.setPlatformId(nodeId);
            request.setPlatformIp(ip);
            log.debug("心跳检测: {}", request);
//            CommonResult<Boolean> heartbeat = routerManagerRemoteService.heartbeat(request);
//            if (heartbeat.isShuHanSuccess()) {
//                return RouterServiceStatusInfo.WELL;
//            }
//            log.error("心跳检测异常: {}", heartbeat);
        } catch (Exception e) {
            log.error("心跳检测异常: {}", e.getMessage());
        }
        return RouterServiceStatusInfo.BAD;
    }
}
