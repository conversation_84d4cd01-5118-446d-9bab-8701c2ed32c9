package com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy;

import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.RouteActivateContext;
import com.ailpha.ailand.dataroute.endpoint.connector.RouteStatus;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.DataRouteActivateHandler;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ActivateRouterRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorChangeResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.RouteDTO;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(1003)
public class RouteManagerActivateHandler implements DataRouteActivateHandler {

    private final DataHubHandler dataHubHandler;

    @Override
    public void handler(RouteActivateContext context) {
        if (context.getRoute().getStatus().getOrder() >= RouteStatus.activated.getOrder()) {
            log.info("当前连接器已经激活，进行下一步【枢纽节点注册】");
            dataHubHandler.handler(context);
            return;
        }
        RouterService routerService = SpringUtil.getBean(RouterService.class);
        Boolean flag = routerService.updateRouterCache(RouteStatus.activated);
        RouteDTO route = context.getRoute();
        route.setStatus(RouteStatus.activated);
        context.setRoute(route);
        log.info("router id = {} activated", context.getRoute().getId());
    }


}
