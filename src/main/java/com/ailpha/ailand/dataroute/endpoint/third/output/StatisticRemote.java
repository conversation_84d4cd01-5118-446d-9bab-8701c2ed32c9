package com.ailpha.ailand.dataroute.endpoint.third.output;


import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.base.DataAssetStatisticRsp;
import com.ailpha.ailand.dataroute.endpoint.base.PrivatePlatformRsp;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.statistics.StatisticsResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.GET;
import retrofit2.http.Query;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.TRADE_PLATFORM, tokenUrl = "/third/app/token")
public interface StatisticRemote {

    @GET("/api/dataRoute/statistic")
    CommonResult<StatisticsResponse> statistic();

    @GET("/api/dataRoute/statistic/private-platform")
    CommonResult<PrivatePlatformRsp> privatePlatformStat(@Query(value = "type") String type);

    @GET("/api/dataRoute/statistic/dataAsset/bySourceType")
    CommonResult<DataAssetStatisticRsp> statisticBySourceType();


}
