package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.dbapp.rest.exception.RestfulApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuartzSchedulerManager {

    private SchedulerFactory schedulerFactory;

    private Scheduler scheduler;

//    private final DataAssetPrepareSchedule dataAssetPrepareSchedule;

    @PostConstruct
    private void init() {
        Properties props = new Properties();
        props.put(StdSchedulerFactory.PROP_SCHED_INSTANCE_NAME, "DataRouteAccessDataQuartzScheduler");
        props.put("org.quartz.threadPool.threadCount", "10");
        try {
            schedulerFactory = new StdSchedulerFactory(props);
            scheduler = schedulerFactory.getScheduler();
            scheduler.start();

        } catch (SchedulerException e) {
            log.error("init DataRouteAccessDataQuartzScheduler error", e);
        }

    }

    /**
     * 创建定时任务
     */
    public synchronized void submitCronJob(DataUpdateCronJobParams dataUpdateCronJobParams, boolean firstSubmit) {
        if (StringUtils.isEmpty(dataUpdateCronJobParams.getDataUpdateTask().getCronExpression())) {
            throw new IllegalArgumentException("cronExpression不能为空");
        }

        try {
            String jobKey = dataUpdateCronJobParams.getDataUpdateTask().getId();
            if (scheduler.checkExists(new JobKey(jobKey))) {
                log.warn("DataRouteAccessDataQuartzScheduler can not submit same job ...");
                throw new RestfulApiException("无法提交重复的任务信息");
            }

            JobDetail job = JobBuilder.newJob(DataUpdateCronJob.class)
                    .withIdentity(jobKey)
                    .build();

            job.getJobDataMap().put(DataUpdateCronJob.PARAMS, dataUpdateCronJobParams);

            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(new TriggerKey(jobKey))
                    .withSchedule(CronScheduleBuilder.cronSchedule(dataUpdateCronJobParams.getDataUpdateTask().getCronExpression()))
                    .build();

            scheduler.scheduleJob(job, trigger);

            if (firstSubmit && SchedulerPeriodEnum.HOUR == dataUpdateCronJobParams.getDataUpdateTask().getBaseInfo().getSchedulerPeriod()) {
                // 按小时更新的任务立马执行一次
                log.info("Hour job manual trigger once: jobId {}", job.getKey());
                scheduler.triggerJob(job.getKey());
            }

            log.info("data route cron job add success, task info:{}, cronExpression: {}",
                    JSONUtil.toJsonStr(dataUpdateCronJobParams.getDataUpdateTask()),
                    dataUpdateCronJobParams.getDataUpdateTask().getCronExpression());

        } catch (Exception e) {
            log.error("submit cronJob: {}, cronExpression: {} error: {}",
                    JSONUtil.toJsonStr(dataUpdateCronJobParams.getDataUpdateTask()),
                    dataUpdateCronJobParams.getDataUpdateTask().getCronExpression(),
                    e.getMessage());
            throw new RestfulApiException("提交定时任务异常");
        }
    }

    /**
     * 更新定时任务
     */
    public synchronized void updateJob(String jobId, DataUpdateTask dataUpdateTask) {
        if (StringUtils.isEmpty(dataUpdateTask.getCronExpression())) {
            throw new IllegalArgumentException("cronExpression不能为空");
        }
        try {

            // 获取JobDetail
            JobDetail jobDetail = scheduler.getJobDetail(new JobKey(jobId));
            DataUpdateCronJobParams dataUpdateCronJobParamsOld = (DataUpdateCronJobParams) jobDetail.getJobDataMap().get(DataUpdateCronJob.PARAMS);

            // 判断任务信息是否更新
            if (dataUpdateCronJobParamsOld.getDataUpdateTask().getCronExpression().equals(dataUpdateTask.getCronExpression())) {
                log.info("定时任务cron表达式未更新：{}，跳过此次任务更新操作...", dataUpdateTask.getCronExpression());
                return;
            }

            dataUpdateCronJobParamsOld.setDataUpdateTask(dataUpdateTask);

            // 先删除旧的Job
            if (!scheduler.deleteJob(new JobKey(jobId))) {
                throw new RestfulApiException("删除旧job失败");
            }

            // 创建新的JobDetail
            JobDetail newJobDetail = JobBuilder.newJob(DataUpdateCronJob.class)
                    .withIdentity(jobId)
                    .build();

            // 设置JobDataMap
            newJobDetail.getJobDataMap().put(DataUpdateCronJob.PARAMS, dataUpdateCronJobParamsOld);

            // 创建新的Trigger
            CronTrigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(new TriggerKey(jobId))
                    .withSchedule(CronScheduleBuilder.cronSchedule(dataUpdateTask.getCronExpression()))
                    .build();

            // 同时调度Job和Trigger
            scheduler.scheduleJob(newJobDetail, trigger);

            if (SchedulerPeriodEnum.HOUR == dataUpdateTask.getBaseInfo().getSchedulerPeriod()) {
                // 按小时更新的任务立马执行一次
                log.info("Hour job manual trigger once: jobId {}", jobId);
                scheduler.triggerJob(newJobDetail.getKey());
            }

            log.info("更新定时任务成功, jobId: {}, params: {}", jobId, JSONUtil.toJsonStr(dataUpdateCronJobParamsOld));


//            // 更新JobDetail，为true表示覆盖
//            scheduler.addJob(jobDetail, true);
//
//            log.info("更新定时任务参数成功, jobId: {}, params: {}", jobId, JSONUtil.toJsonStr(dataUpdateCronJobParamsOld));
//
//            TriggerKey triggerKey = new TriggerKey(jobId);
//            Trigger oldTrigger = scheduler.getTrigger(triggerKey);
//            TriggerBuilder triggerBuilder = oldTrigger.getTriggerBuilder();
//
//            Trigger newTrigger = triggerBuilder
//                    .withIdentity(triggerKey)
//                    .withSchedule(CronScheduleBuilder.cronSchedule(dataUpdateTask.getCronExpression()))
//                    .build();
//
//            scheduler.rescheduleJob(triggerKey, newTrigger);
//
//            log.info("更新定时任务成功, jobId: {}", jobId);
        } catch (Exception e) {
            log.error("JobId: {}, cronExpression: {}", jobId, dataUpdateTask.getCronExpression(), e);
            throw new RestfulApiException("更新定时任务异常");
        }
    }

    /**
     * 停止(删除)某个Quartz的定时任务
     *
     * @param taskLogId
     */
    public synchronized void stopCronJob(String taskLogId) {
        try {
            if (scheduler.checkExists(new JobKey(taskLogId))) {
                scheduler.deleteJob(new JobKey(taskLogId));
                log.info("data route cron job delete success, jobId:{}", taskLogId);
                return;
            }

            throw new RestfulApiException("定时任务不存在【" + taskLogId + "】");
        } catch (Exception e) {
            log.error("stop quartz job failed, jobId: {} ", taskLogId, e);
            throw new RestfulApiException("删除定时任务失败【" + taskLogId + "】", e);
        }
    }
//
//    /**
//     * 删除定时任务
//     */
//    public void deleteJob(String taskId) throws SchedulerException {
//        scheduler.deleteJob(new JobKey(taskId));
//        log.info("删除定时任务成功, taskId: {}", taskId);
//    }
//
//    /**
//     * 暂停定时任务
//     */
//    public void pauseJob(String taskId) throws SchedulerException {
//        scheduler.pauseJob(new JobKey(taskId));
//        log.info("暂停定时任务成功, taskId: {}", taskId);
//    }
//
//    /**
//     * 恢复定时任务
//     */
//    public void resumeJob(String taskId) throws SchedulerException {
//        scheduler.resumeJob(new JobKey(taskId));
//        log.info("恢复定时任务成功, taskId: {}", taskId);
//    }
//

    /**
     * 立即执行一次定时任务
     */
    public synchronized void triggerJob(String taskId) {
        try {
            if (!scheduler.checkExists(new JobKey(taskId))) {
                throw new RestfulApiException(String.format("定时任务【%s】不存在", taskId));
            }

            scheduler.triggerJob(new JobKey(taskId));
            log.info("触发定时任务执行成功, taskId: {}", taskId);

        } catch (Exception e) {
            log.error("触发执行定时任务失败: {} ", taskId, e);
            throw new RestfulApiException(e.getMessage());
        }

    }
}