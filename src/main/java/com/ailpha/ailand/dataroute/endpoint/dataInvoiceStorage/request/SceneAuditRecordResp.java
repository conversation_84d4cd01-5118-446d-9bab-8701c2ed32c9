package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneAuditRecordResp {

    @Schema(description = "审核ID")
    private String auditId;

    @Schema(description = "审批状态")
    private ApprovalStatus approvalStatus;

    @Schema(description = "审核人用户ID")
    private String auditorId;

    @Schema(description = "审核意见")
    private String auditOpinion;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description = "审批时间")
    private Date createTime;

}
