package com.ailpha.ailand.dataroute.endpoint.base;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.common.utils.PinYin4JUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.RandomStringUtil;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.openapi.PlatformAppKeySecret;
import com.ailpha.ailand.dataroute.endpoint.openapi.PlatformType;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.ExternalPlatformAppKeyCreateReq;
import com.ailpha.ailand.dataroute.endpoint.third.response.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class BaseCapabilityManager {
    private final AiLandProperties aiLandProperties;

    private final MPCRemote mpcRemote;
    private final TeeRemote teeRemote;

    private final RouterService routerService;

    public static Map<BaseCapabilityType, BaseCapabilityConfig> PUBLIC_CAPABILITY_CONFIG_MAP = new HashMap<>();
    public static Map<String, Map<BaseCapabilityType, BaseCapabilityConfig>> COMPANY_CAPABILITY_CONFIG_MAP = new ConcurrentHashMap<>();
    public static Map<BaseCapabilityType, Map<String, PlatformAppKeySecret>> PLATFORM_APP_KEY_SECRET_MAP = new ConcurrentHashMap<>();
    public static Map<String, String> PLATFORM_APP_KEY_SECRET_UNWRAPPED_MAP = new ConcurrentHashMap<>();
    private static final String PUBLIC_CAPABILITY_CONFIG_FILENAME = "public-capability-config.json";
    private static final String COMPANY_CAPABILITY_CONFIG_FILENAME = "company-capability-config.json";
    /**
     * 目前仅保存tee平台ak，sk，仅注册tee时使用，为了保证该平台所有企业使用的同一个ak，sk，会被使用到data-control与前置机间的鉴权中，方便前置机的鉴权
     */
    private static final String PLATFORM_CAPABILITY_CONFIG_FILENAME = "platform-capability-config.json";
    private static final String PLATFORM_APP_KEY_SECRET_FILENAME = "platform-app-secret.json";

    @PostConstruct
    public void init() {
        String publicCapabilityConfigFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PUBLIC_CAPABILITY_CONFIG_FILENAME;
        if (!FileUtil.exist(publicCapabilityConfigFilePath)) {
            File newFile = FileUtil.newFile(publicCapabilityConfigFilePath);
            FileUtil.writeString(JSONUtil.toJsonStr(PUBLIC_CAPABILITY_CONFIG_MAP), newFile, Charset.defaultCharset());
            log.info("连接器初始化成功，配置文件路径：{}", publicCapabilityConfigFilePath);
        } else {
            String capabilityConfig = FileUtil.readString(publicCapabilityConfigFilePath, Charset.defaultCharset());
            PUBLIC_CAPABILITY_CONFIG_MAP = JSONUtil.toBean(capabilityConfig, new TypeReference<Map<BaseCapabilityType, BaseCapabilityConfig>>() {
            }.getType(), true);
        }

        String companyCapabilityConfigFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + COMPANY_CAPABILITY_CONFIG_FILENAME;
        if (!FileUtil.exist(companyCapabilityConfigFilePath)) {
            File newFile = FileUtil.newFile(companyCapabilityConfigFilePath);
            FileUtil.writeString(JSONUtil.toJsonStr(COMPANY_CAPABILITY_CONFIG_MAP), newFile, Charset.defaultCharset());
            log.info("连接器初始化成功，配置文件路径：{}", companyCapabilityConfigFilePath);
        } else {
            String capabilityConfig = FileUtil.readString(companyCapabilityConfigFilePath, Charset.defaultCharset());
            COMPANY_CAPABILITY_CONFIG_MAP = JSONUtil.toBean(capabilityConfig, new TypeReference<Map<String, Map<BaseCapabilityType, BaseCapabilityConfig>>>() {
            }.getType(), true);
        }

        String platformAppKeySecretFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PLATFORM_APP_KEY_SECRET_FILENAME;
        if (FileUtil.exist(platformAppKeySecretFilePath)) {
            String platformAppKeySecret = FileUtil.readString(platformAppKeySecretFilePath, Charset.defaultCharset());
            PLATFORM_APP_KEY_SECRET_MAP = JSONUtil.toBean(platformAppKeySecret, new TypeReference<Map<BaseCapabilityType, Map<String, PlatformAppKeySecret>>>() {
            }.getType(), true);

            for (Map<String, PlatformAppKeySecret> platformAppKeySecretMap : PLATFORM_APP_KEY_SECRET_MAP.values()) {
                for (PlatformAppKeySecret appKeySecret : platformAppKeySecretMap.values()) {
                    PLATFORM_APP_KEY_SECRET_UNWRAPPED_MAP.put(appKeySecret.getAppKey(), appKeySecret.getAppSecret());
                }
            }
            log.info("连接器初始化成功，配置文件路径：{}", platformAppKeySecretFilePath);
        }
    }

    public Map<BaseCapabilityType, BaseCapabilityConfig> getCapabilityConfig() {
        Map<BaseCapabilityType, BaseCapabilityConfig> capabilityConfigMap = new HashMap<>(PUBLIC_CAPABILITY_CONFIG_MAP);
        String companyId;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            companyId = "SUPER_ADMIN".equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        } else {
            companyId = MDC.get(SystemConstants.COMPANY_ID);
        }
        if (!ObjectUtils.isEmpty(companyId)) {
            if (!ObjectUtils.isEmpty(COMPANY_CAPABILITY_CONFIG_MAP.get(companyId))) {
                capabilityConfigMap.putAll(COMPANY_CAPABILITY_CONFIG_MAP.get(companyId));
            }
            if (DeployMode.share.equals(aiLandProperties.getDeploy().getMode())) { // sass 节点MPC不可用
                capabilityConfigMap.put(BaseCapabilityType.MPC, BaseCapabilityConfig.builder().type(BaseCapabilityType.MPC).enabled(false).build());
            }
        }
        for (BaseCapabilityType capabilityType : BaseCapabilityType.values()) {
            if (!capabilityConfigMap.containsKey(capabilityType)) {
                capabilityConfigMap.put(capabilityType, BaseCapabilityConfig.builder().type(capabilityType).enabled(false).build());
            }
        }
        return capabilityConfigMap;
    }

    public BaseCapabilityConfig getCapabilityConfig(BaseCapabilityType type) {
        BaseCapabilityConfig baseCapabilityConfig;
        String companyId;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            companyId = "SUPER_ADMIN".equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        } else {
            companyId = MDC.get(SystemConstants.COMPANY_ID);
        }
        if (isCompanyCapability(type)) {
            Map<BaseCapabilityType, BaseCapabilityConfig> capabilityConfigMap = COMPANY_CAPABILITY_CONFIG_MAP.get(companyId);
            capabilityConfigMap = ObjectUtils.isEmpty(capabilityConfigMap) ? new HashMap<>() : capabilityConfigMap;
            if (DeployMode.share.equals(aiLandProperties.getDeploy().getMode())) { // sass 节点MPC不可用
                capabilityConfigMap.put(BaseCapabilityType.MPC, BaseCapabilityConfig.builder().type(BaseCapabilityType.MPC).enabled(false).build());
            }
            baseCapabilityConfig = capabilityConfigMap.get(type);
        } else {
            baseCapabilityConfig = PUBLIC_CAPABILITY_CONFIG_MAP.get(type);
        }
        return ObjectUtils.isEmpty(baseCapabilityConfig) ? BaseCapabilityConfig.builder().type(type).enabled(false).build() : baseCapabilityConfig;
    }

    @Value("${server.port}")
    Integer port;

    public synchronized void updateCapabilityConfig(BaseCapabilityType type, BaseCapabilityConfig config) {
        UserDTO currentUser = LoginContextHolder.currentUser();
        String companyId = "SUPER_ADMIN".equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        boolean isCompanyCapability = isCompanyCapability(type);
        updateCapabilityConfig(type, companyId, config, isCompanyCapability);

        BaseCapabilityConfig oldCapabilityConfig = BaseCapabilityConfig.builder().type(type).enabled(false).build();
        try {
            if (config.getEnabled() && (BaseCapabilityType.MPC.equals(type) || BaseCapabilityType.TEE.equals(type))) {
                Map<BaseCapabilityType, BaseCapabilityConfig> capabilityConfigMap = COMPANY_CAPABILITY_CONFIG_MAP.get(companyId);
                capabilityConfigMap = ObjectUtils.isEmpty(capabilityConfigMap) ? new HashMap<>() : capabilityConfigMap;
                oldCapabilityConfig = ObjectUtils.isEmpty(capabilityConfigMap.get(type)) ? BaseCapabilityConfig.builder().type(type).enabled(false).build() : oldCapabilityConfig;

                Assert.isTrue(!ObjectUtils.isEmpty(currentUser.getCompany().getNodeId()), "nodeId不能存在，请检查节点是否激活成功");
                Map<String, PlatformAppKeySecret> platformAppKeySecretMap = PLATFORM_APP_KEY_SECRET_MAP.get(type);
                platformAppKeySecretMap = ObjectUtils.isEmpty(platformAppKeySecretMap) ? new HashMap<>() : platformAppKeySecretMap;
                PlatformAppKeySecret platformAppKeySecret = platformAppKeySecretMap.get(companyId);
                if (ObjectUtils.isEmpty(platformAppKeySecret)) {
                    long timestamp = System.currentTimeMillis();
                    SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmm");
                    String prefix = PinYin4JUtil.getPinYinHeadChar(type.name()).toUpperCase();
                    String appKey = String.format("%s%s%s", prefix.length() > 16 ? prefix.substring(0, 16) : prefix, format.format(timestamp), RandomStringUtil.stringGenerate(4, false, true, false, false));
                    platformAppKeySecret = PlatformAppKeySecret.builder().platformName(type.name() + "_" + companyId).platformType(PlatformType.OTHER).appKey(appKey).appSecret(UUID.randomUUID().toString()).createTime(new Date()).build();
                }
                platformAppKeySecretMap.put(companyId, platformAppKeySecret);
                PLATFORM_APP_KEY_SECRET_MAP.put(type, platformAppKeySecretMap);

                PLATFORM_APP_KEY_SECRET_UNWRAPPED_MAP.put(platformAppKeySecret.getAppKey(), platformAppKeySecret.getAppSecret());

                String platformAppSecretFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PLATFORM_APP_KEY_SECRET_FILENAME;
                FileUtil.writeString(JSONUtil.toJsonStr(PLATFORM_APP_KEY_SECRET_MAP), platformAppSecretFilePath, Charset.defaultCharset());

                if (BaseCapabilityType.MPC.equals(type)) {
                    MDC.put(SystemConstants.COMPANY_ID, companyId);
                    CommonResult<Boolean> registerResult = mpcRemote.appRegister(ExternalPlatformAppKeyCreateReq.builder()
                            .platformDomain(String.format("http://%s:%s", routerService.currentRouteVirtualIp(), port))
                            .platformId(currentUser.getCompany().getNodeId())
                            .platformType(PlatformType.DATA_ROUTE_ENDPOINT)
                            .appKey(platformAppKeySecret.getAppKey())
                            .appSecret(platformAppKeySecret.getAppSecret())
                            .build());
                    Assert.isTrue(registerResult.isSuccess(), registerResult.getMessage());
                }
                if (BaseCapabilityType.TEE.equals(type)) {
                    MDC.put(SystemConstants.COMPANY_ID, companyId);
                    BaseCapabilityConfig teeBaseCapabilityConfig = generateTeePlatformAppKeyIfNotExist();
                    com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult<Boolean> registerResult = teeRemote.appRegister(ExternalPlatformAppKeyCreateReq.builder()
                            .platformDomain(String.format("http://%s:%s", routerService.currentRouteVirtualIp(), port))
                            .platformId(currentUser.getCompany().getNodeId())
                            .platformType(PlatformType.DATA_ROUTE_ENDPOINT)
                            .appKey(platformAppKeySecret.getAppKey())
                            .appSecret(platformAppKeySecret.getAppSecret())
                            .teePlatformAppKey(teeBaseCapabilityConfig.getAppKey())
                            .teePlatformAppSecret(teeBaseCapabilityConfig.getAppSecret())
                            .build());
                    Assert.isTrue(registerResult.isSuccess(), registerResult.getMessage());
                    // save tee platform key
                    teeBaseCapabilityConfig.setBaseUrl(config.getBaseUrl());
                    saveTeePlatformAppKey(teeBaseCapabilityConfig);
                }
            }
        } catch (Exception e) {
            updateCapabilityConfig(type, companyId, oldCapabilityConfig, isCompanyCapability);
            throw new RestfulApiException("向远程基础能力注册失败", e);
        }
        if (isCompanyCapability) {
            String capabilityConfigFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + COMPANY_CAPABILITY_CONFIG_FILENAME;
            FileUtil.writeString(JSONUtil.toJsonStr(COMPANY_CAPABILITY_CONFIG_MAP), capabilityConfigFilePath, Charset.defaultCharset());
        } else {
            String capabilityConfigFilePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PUBLIC_CAPABILITY_CONFIG_FILENAME;
            FileUtil.writeString(JSONUtil.toJsonStr(PUBLIC_CAPABILITY_CONFIG_MAP), capabilityConfigFilePath, Charset.defaultCharset());
        }
    }

    private void saveTeePlatformAppKey(BaseCapabilityConfig baseCapabilityConfig) {
        log.debug("begin saveTeePlatformAppKey :[{}]", baseCapabilityConfig);
        String filePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PLATFORM_CAPABILITY_CONFIG_FILENAME;
        if (!FileUtil.exist(filePath)) {
            log.info("tee platform key is already exists! {}", getTeePlatformAppKey());
            return;
        }
        FileUtil.writeString(JSONUtil.toJsonStr(baseCapabilityConfig), filePath, Charset.defaultCharset());
        log.info("TEE平台密钥 [{}] 配置已保存至：{}", baseCapabilityConfig, filePath);
    }

    public BaseCapabilityConfig getTeePlatformAppKey() {
        String filePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PLATFORM_CAPABILITY_CONFIG_FILENAME;
        if (!FileUtil.exist(filePath)) {
            log.debug("未找到该文件，没有配置tee appKey");
            return null;
        }
        String configJson = FileUtil.readString(filePath, Charset.defaultCharset());
        return JSONUtil.toBean(configJson, BaseCapabilityConfig.class);
    }

    public BaseCapabilityConfig generateTeePlatformAppKeyIfNotExist() {
        String filePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + PLATFORM_CAPABILITY_CONFIG_FILENAME;
        if (!FileUtil.exist(filePath)) {
            long timestamp = System.currentTimeMillis();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmm");
            String prefix = PinYin4JUtil.getPinYinHeadChar(BaseCapabilityType.TEE.name()).toUpperCase();
            String appKey = String.format("%s%s%s",
                    prefix.length() > 16 ? prefix.substring(0, 16) : prefix,
                    format.format(timestamp),
                    RandomStringUtil.stringGenerate(4, false, true, false, false));
            String appSecret = UUID.randomUUID().toString();

            BaseCapabilityConfig config = BaseCapabilityConfig.builder()
                    .type(BaseCapabilityType.TEE)
                    .enabled(true)
                    .appKey(appKey)
                    .appSecret(appSecret)
                    .build();

            FileUtil.writeString(JSONUtil.toJsonStr(config), filePath, Charset.defaultCharset());
            return config;
        }
        return getTeePlatformAppKey();
    }

    private void updateCapabilityConfig(BaseCapabilityType type, String companyId, BaseCapabilityConfig capabilityConfig, boolean isCompanyCapability) {
        if (isCompanyCapability) {
            Map<BaseCapabilityType, BaseCapabilityConfig> capabilityConfigMap = COMPANY_CAPABILITY_CONFIG_MAP.get(companyId);
            capabilityConfigMap = ObjectUtils.isEmpty(capabilityConfigMap) ? new HashMap<>() : capabilityConfigMap;
            capabilityConfigMap.put(type, capabilityConfig);
            COMPANY_CAPABILITY_CONFIG_MAP.put(companyId, capabilityConfigMap);
        } else {
            PUBLIC_CAPABILITY_CONFIG_MAP.put(type, capabilityConfig);
        }
    }

    public boolean isCompanyCapability(BaseCapabilityType type) {
        List<BaseCapabilityType> companyCapabilities = Arrays.asList(BaseCapabilityType.MPC, BaseCapabilityType.TEE, BaseCapabilityType.DATA_INVOICE);
        return companyCapabilities.contains(type);
    }

    public boolean platformEnable(BaseCapabilityType type) {
        return Boolean.TRUE.equals(getCapabilityConfig(type).getEnabled());
    }
}
