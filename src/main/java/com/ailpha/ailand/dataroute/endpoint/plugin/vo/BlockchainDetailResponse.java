package com.ailpha.ailand.dataroute.endpoint.plugin.vo;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.plugin.common.BlockchainStatusConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlockchainDetailResponse {
    private Long id;
    private String name;
    private String moduleType;
    private String description;
    private int status = BlockchainStatusConstant.OFF;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm")
    private LocalDateTime createTime;

    private BlockchainPluginTypeEnum type;
    private List<BlockchainApiDetailResponse> apiDetails;
    private String script;
}
