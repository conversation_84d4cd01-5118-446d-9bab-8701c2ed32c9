package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResolutionResponseDataProduct {
    // 数据产品标识
    String productId;
    // 产品名称
    String productName;
    // 产品类型
    String productType;
    // 覆盖时间范围
    String timeRange;
    // 行业分类
    String industry;
    // 地域分类
    String productRegion;
    // 产品简介
    String description;
    // 交付方式
    String deliveryMethod;
    // 使用限制
    String limitations;
    // 授权使用
    Integer authorize;
    // 是否涉及个人信息
    Integer personalInformation;
    // 数据主体
    String dataSubject;
    // 数据规模
    Double dataSize;
    // 数据规模单位
    String dataSizeUnit;
    // 更新频率
    Integer updateFrequency;
    // 更新频率单位
    String updateFrequencyUnit;
    // 数据资源标识码
    List<String> resourceId;
    // 其他
    String others;
    // 提供方名称
    String providerName;
    // 提供方主体类型
    String providerType;
    // 主体信息
    String entityInformation;
    // 身份标识码
    String identityId;
    // 提供方简介
    String providerDesc;
    // 数据样例
    String dataSample;
    // 数据样例（文件形式）名称
    String dataSampleFileName;
}
