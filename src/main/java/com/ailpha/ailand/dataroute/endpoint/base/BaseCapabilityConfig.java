package com.ailpha.ailand.dataroute.endpoint.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BaseCapabilityConfig {
    BaseCapabilityType type;
    // 默认未启用
    @Builder.Default
    Boolean enabled = false;

    @Schema(description = "页面跳转地址")
    String jumpUrl;
    @Schema(description = "服务接口地址")
    String baseUrl;
    String authType; // appKeySecret publicKey

    String appKey;
    String appSecret;

    String publicKey;

    String desc;
}
