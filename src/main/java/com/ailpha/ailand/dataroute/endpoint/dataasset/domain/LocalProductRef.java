package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "t_local_product_ref")
@AllArgsConstructor
public class LocalProductRef implements Serializable {

    @Id
    @UUID32
    private String id;

    @Column(name = "product_platform_id")
    private String productPlatformId;

    /**
     * 资产id
     */
    @Schema(description = "资产id")
    @Column(name =  "asset_id")
    private String assetId;

    @Schema(description = "company_id")
    @Column(name =  "company_id")
    private String companyId;

    @Schema(description = "extend")
    @Column(name =  "extend")
    private String extend;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

}
