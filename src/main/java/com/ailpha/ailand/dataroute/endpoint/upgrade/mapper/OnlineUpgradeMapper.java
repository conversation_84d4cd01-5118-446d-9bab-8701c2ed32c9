package com.ailpha.ailand.dataroute.endpoint.upgrade.mapper;

import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.UpgradeTask;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.UpgradeTaskVO;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.VersionInfo;
import org.mapstruct.*;

import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class OnlineUpgradeMapper {

    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    public abstract UpgradeTask versionInfoToUpgradeTask(VersionInfo versionInfo);

    @AfterMapping
    void fillOtherInfo(VersionInfo versionInfo, @MappingTarget UpgradeTask.UpgradeTaskBuilder upgradeTaskBuilder) {
        Date date = new Date();
        upgradeTaskBuilder.createTime(date).updateTime(date);
    }

    public abstract UpgradeTaskVO upgradeTaskToUpgradeTaskVO(UpgradeTask upgradeTask);
}
