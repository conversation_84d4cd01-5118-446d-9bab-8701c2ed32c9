package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataProductPublishVO {

    @ApiModelProperty(value = "执行结果状态 0上架成功 1上架失败 2执行失败数据上架已存在")
    private String publishFlag;

    @ApiModelProperty(value = "数据上架id")
    private Long launchId;

    @ApiModelProperty("上架成功时间精确到毫秒-格式为 13位UNIX时间戳")
    private Long registrationTime;

    @ApiModelProperty("登记所属区域/行业功能节点标识")
    private String regionNodeId;

    @ApiModelProperty("数据产品上架业务类型的单据唯一ID ，此ID可用于查询状态和标识")
    private String processId;
}
