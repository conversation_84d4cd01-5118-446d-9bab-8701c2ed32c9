package com.ailpha.ailand.dataroute.endpoint.common.utils.sql;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class VariableUtils {

    public static final String ADD = "+";
    public static final String MINUS = "-";
    public static final String ADD_PATTERN = "\\+";
    public static final String MINUS_PATTERN = "\\-";
    public static String VARIABLE_PATTERN_STRING = "\\$\\{(.)*([+|-][1-9]*)?\\}";

    public static Pattern VARIABLE_PATTERN = Pattern.compile(VARIABLE_PATTERN_STRING,
            Pattern.UNIX_LINES);

    public static final String DATA_FORMAT_YYYYMMDDHH = "yyyyMMddHH";
    public static final String DATA_FORMAT_YYYY_MM_DD_HH = "yyyy-MM-dd HH";

    public static final String DATA_FORMAT_YYYYMMDD = "yyyyMMdd";

    public static final String DATA_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";

    public static final String DATA_FORMAT_YYYYMM = "yyyyMM";

    public static final String DATA_FORMAT_YYYY_MM = "yyyy-MM";

    public static final String DATA_FORMAT_YYYY = "yyyy";

    private static final String DATA_FORMAT_FIST_OF_WEEK = "fist_of_week";

    private static final String DATA_FORMAT_LAST_OF_WEEK = "last_of_week";

    private static final String DATA_FORMAT_FIST_OF_MONTH = "fist_of_month";

    private static final String DATA_FORMAT_LAST_OF_MONTH = "last_of_month";

    public static final int DEFAULT_ADJUST_VALUE = 1;
    public static final int ZERO_ADJUST_VALUE = 0;

    public static final DateTimeFormatter DATETIMEFORMATTER_YYYY_MM_DD_HH = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYY_MM_DD_HH);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYYMMDDHH = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYYMMDDHH);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYY_MM_DD = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYY_MM_DD);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYYMMDD = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYYMMDD);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYY_MM = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYY_MM);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYYMM = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYYMM);
    public static final DateTimeFormatter DATETIMEFORMATTER_YYYY = DateTimeFormatter.ofPattern(DATA_FORMAT_YYYY);

    public static String replaceVariableExpression(Long time, String sql, List<Long> dataDates, int adjustValue) {

        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(sql);

        while (matcher.find()) {

            String matcherItem = matcher.group();
            String variable = matcherItem.substring(2, matcherItem.length() - 1);

            String format = null;

            if (variable.startsWith(DATA_FORMAT_YYYYMMDDHH)) {

                // ${yyyymmdd}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYYMMDDHH, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYY_MM_DD_HH)) {

                // ${yyyy-mm-dd}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYY_MM_DD_HH, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYYMMDD)) {

                // ${yyyymmdd}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYYMMDD, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYY_MM_DD)) {

                // ${yyyy-mm-dd}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYY_MM_DD, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYY_MM)) {

                // ${yyyy-mm}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYY_MM, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYYMM)) {

                // ${yyyymm}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYYMM, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_YYYY)) {
                // ${yyyy}
                format = toRealDate(time, dataDates, variable, DATETIMEFORMATTER_YYYY, adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_FIST_OF_WEEK)) {

                // ${fist_of_week(yyyymmdd)}
                format = toRealDate(time, dataDates, variable, DayOfWeek.MONDAY, adjustValue);
            } else if (variable.startsWith(DATA_FORMAT_LAST_OF_WEEK)) {

                // ${last_of_week(yyyymmdd)}
                format = toRealDate(time, dataDates, variable, DayOfWeek.SUNDAY, adjustValue);
            } else if (variable.startsWith(DATA_FORMAT_FIST_OF_MONTH)) {

                // ${fist_of_month(yyyymmdd)}
                format = toRealDate(time, dataDates, variable, TemporalAdjusters.firstDayOfMonth(), adjustValue);

            } else if (variable.startsWith(DATA_FORMAT_LAST_OF_MONTH)) {

                // ${last_of_month(yyyymmdd)}
                format = toRealDate(time, dataDates, variable, TemporalAdjusters.lastDayOfMonth(), adjustValue);
            }

            sql = sql.replace(matcherItem, format);

        }

        return sql;
    }

    /**
     * 替换新的变量
     *
     * @param sql       原始SQL
     * @param dataDates 替换后的数值
     * @return 替换后的SQL
     */
    public static String replaceVariableExpression(String sql, List<Long> dataDates) {

        return replaceVariableExpression(Clock.systemDefaultZone().millis(), sql, dataDates, DEFAULT_ADJUST_VALUE);
    }

    /**
     * 根据指定日期变量格式 替换新的日期变量
     *
     * @param time
     * @param sql
     * @param dataDates
     * @param adjustValue
     * @param dateFormat
     */
    public static String replaceVariableExpressionByDateFormat(Long time, String sql, List<Long> dataDates,
                                                               int adjustValue, String dateFormat) {

        if (StringUtils.isBlank(sql)) {
            return sql;
        }

        Matcher matcher = VariableUtils.VARIABLE_PATTERN.matcher(sql);
        while (matcher.find()) {

            String matcherItem = matcher.group();
            String variable = matcherItem.substring(2, matcherItem.length() - 1);

            if (variable.contains(dateFormat)) {
                String format = VariableUtils.replaceVariableExpression(time, matcherItem, dataDates, adjustValue);
                sql = sql.replace(matcherItem, format);
            }
        }
        return sql;
    }

    private static String toRealDate(Long time, List<Long> dataDates, String variable,
                                     TemporalAdjuster temporalAdjuster, int adjustValue) {
        boolean addOp = variable.contains(ADD);
        boolean minusOp = variable.contains(MINUS);

        int opValue = 0;

        if (addOp) {
            String[] split = variable.split(ADD_PATTERN);
            opValue = Integer.valueOf(split[1]);
        } else if (minusOp) {
            String[] split = variable.split(MINUS_PATTERN);
            opValue = -Integer.valueOf(split[1]);
        }

        Instant instant = Instant.ofEpochMilli(time);
        LocalDate localDate = LocalDateTime.ofInstant(instant, ZoneId.systemDefault()).toLocalDate();

        LocalDate now = localDate.minusDays(adjustValue).with(temporalAdjuster).plusDays(opValue);

        dataDates.add(Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant()).getTime());
        return DATETIMEFORMATTER_YYYYMMDD.format(now);
    }

    private static String toRealDate(Long time, List<Long> dataDates, String variable, DayOfWeek dayOfWeek,
                                     int adjustValue) {
        boolean addOp = variable.contains(ADD);
        boolean minusOp = variable.contains(MINUS);

        int opValue = 0;

        if (addOp) {
            String[] split = variable.split(ADD_PATTERN);
            opValue = Integer.valueOf(split[1]);
        } else if (minusOp) {
            String[] split = variable.split(MINUS_PATTERN);
            opValue = -Integer.valueOf(split[1]);
        }

        Instant instant = Instant.ofEpochMilli(time);
        LocalDate localDate = LocalDateTime.ofInstant(instant, ZoneId.systemDefault()).toLocalDate();

        LocalDate now = localDate.minusDays(adjustValue).with(dayOfWeek).plusDays(opValue);

        dataDates.add(Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant()).getTime());
        return DATETIMEFORMATTER_YYYYMMDD.format(now);
    }

    private static String toRealDate(Long time, List<Long> dataDates, String variable,
                                     DateTimeFormatter dateTimeFormatter, int adjustValue) {
        SplitVariable splitVariable = new SplitVariable(variable).invoke();
        int opValue = splitVariable.getOpValue();

        DateTime dateTime = new DateTime(time);

        Long dateTimeLong;
        if (dateTimeFormatter == DATETIMEFORMATTER_YYYY_MM_DD_HH || dateTimeFormatter == DATETIMEFORMATTER_YYYYMMDDHH) {
            dateTimeLong = dateTime.minusHours(adjustValue).plusHours(opValue).hourOfDay().roundFloorCopy().toDate().getTime();
        } else {
            dateTimeLong = dateTime.minusDays(adjustValue).plusDays(opValue).dayOfMonth().roundFloorCopy().toDate().getTime();
        }
        dataDates.add(dateTimeLong);

        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(dateTimeLong), ZoneId.systemDefault());
        return dateTimeFormatter.format(localDateTime);
    }

    private static class SplitVariable {

        private String variable;
        private int opValue;

        public SplitVariable(String variable) {
            this.variable = variable;
        }

        public int getOpValue() {
            return opValue;
        }

        public SplitVariable invoke() {
            boolean addOp = variable.contains(ADD);
            boolean minusOp = variable.contains(MINUS);

            opValue = 0;
            if (addOp) {
                String[] split = variable.split(ADD_PATTERN);
                opValue = Integer.valueOf(split[1]);
            } else if (minusOp) {

                String[] split = variable.split(MINUS_PATTERN);
                String lastOp = split[split.length - 1];

                // 因为会有 yyyy-MM-dd-1 这种情况的存在,-符号重复了！
                if (NumberUtils.isDigits(lastOp)) {
                    opValue = -Integer.valueOf(lastOp);
                }
            }

            return this;
        }
    }
}
