package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:48
 * @Description:
 */
public interface PluginDetailRepository extends JpaRepository<PluginDetail, Long>, QuerydslPredicateExecutor<PluginDetail> {
    List<PluginDetail> findAllByStatusAndType(Boolean status, PluginApiTypeEnums type);

    List<PluginDetail> findByIdInAndStatusAndType(List<Long> ids, Boolean status, PluginApiTypeEnums type);

}
