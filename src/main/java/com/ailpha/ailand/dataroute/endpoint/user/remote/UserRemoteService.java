package com.ailpha.ailand.dataroute.endpoint.user.remote;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.DataRouterManagerInterceptor;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.third.request.PageRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.*;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.*;
import com.dbapp.rest.response.SuccessResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = DataRouterManagerInterceptor.class)
public interface UserRemoteService {
    @GET("/gateway/auth/api/user/remote/userInfoByUserName")
    CommonResult<List<UserDetailsResponse>> findBy(@Query("name") String name, @Query("account") String account);

    @GET("/gateway/auth/api/user/remote/userInfoById")
    CommonResult<UserDetailsResponse> userDetail(@Query("id") String id);

    @POST("/gateway/shuhan-business-service/api/drClientUserInfo/remote/baseUserInfo")
    CommonResult<DrClientUserInfoResponse> clientUserDetail(@Query("userId") String userId);

    @POST("/gateway/auth/api/user/remote/bathUserPageInfo")
    PageResult<UserListResponse> userList(@Body PageRequest<QueryUserListRequest> request);

    @POST("/gateway/auth/api/routeUser/remote/saveDataRouteUser")
    CommonResult<AddUserResponse> addUser(@Body AddUserRequest request);
    @POST("/gateway/auth/api/routeManagerUser/remote/saveDataRouteUser")
    CommonResult<AddUserResponse> addCompanyAdmin(@Body AddUserRequest request);

    @POST("/gateway/auth/api/routeUser/remote/updateDataRouteUser")
    CommonResult<Boolean> updateUser(@Body HubUpdateUserRequest request);
    @POST("/gateway/auth/api/routeManagerUser/remote/updateDataRouteUser")
    CommonResult<Boolean> updateCompanyUser(@Body HubUpdateUserRequest request);

    @POST("/gateway/auth/api/routeUser/remote/reset")
    CommonResult<Boolean> resetPwd(@Body ResetPwdRequest request);

    @POST("/gateway/auth/api/routeManagerUser/remote/reset")
    CommonResult<Boolean> companyUserResetPwd(@Body ResetPwdRequest request);
    @POST("/gateway/auth/api/user/remote/delete")
    CommonResult<Boolean> deleteUser(@Body DeleteUserRequest request);

    @POST("/gateway/api/loginBySYQTAMCode")
    CommonResult<CheckTokenResponse> checkToken(@Body CheckTokenRequest request);

    @POST("logout/{userId}")
    SuccessResponse<Boolean> logout(@Path("userId") String userId);

    @DELETE("/gateway/auth/api/routeUser/remote/delUser")
    CommonResult<String> delUser(@Query("id") String id);
}
