package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DescribeAuthzConfigResponse extends GatewayResponse<DescribeAuthzConfigResponse.InvokeResultWrapper> {
    @Getter
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {
        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult {
        List<Allowed> allowed;
        @JsonProperty("create_time")
        Long createTime;
        @JsonProperty("update_time")
        Long updateTime;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Allowed {
        /**
         * <pre>
         * {
         *     "allowed": [
         *         {
         *             "routers": [
         *                 "poG_0qi9Sa-ov95YoZTa4g"
         *             ],
         *             "serviceId": "IT5yN9vLRZua69ENDHxdng",
         *             "type": "router"
         *         },
         *         {
         *             "routers": [
         *                 "Qz8oINl3Qm6-dDjbMSJv8A"
         *             ],
         *             "serviceId": "-cE84LkPR22OpS0a1f7gcw",
         *             "type": "router"
         *         },
         *         {
         *             "routers": [
         *                 "qMNot22_R8u986c8r8VPfA"
         *             ],
         *             "serviceId": "VXP2L2DET2eiSheiGTxspw",
         *             "type": "router"
         *         },
         *         {
         *             "routers": [
         *                 "psWAAJFVRVSiS_CipP6mZQ"
         *             ],
         *             "serviceId": "zyxJIND3Rx6zSmmqXUxxWw",
         *             "type": "router"
         *         },
         *         {
         *             "routers": [
         *                 "2h1T06n7QpWnx8QYmBPCyQ",
         *                 "fkJy-qVOSrylXkanI5hgcg",
         *                 "3v-4xQ3ERmmEFXIA_e5F8Q"
         *             ],
         *             "serviceId": "1N_S9ouXRriivbyNjXwwdA",
         *             "type": "router"
         *         }
         *     ],
         *     "create_time": 1732415813083,
         *     "update_time": 1732416658583
         * }
         * </pre>
         */
        String serviceId;
        String type = "router";
        // API路由ID列表
        List<String> routers;
    }
}
