package com.ailpha.ailand.dataroute.endpoint.demand.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitQuotationResponse {
    Integer demandId;
    String title;
    String price;
    String companyName;
    String userId;
    String routerId;
    String dataAssets;
}
