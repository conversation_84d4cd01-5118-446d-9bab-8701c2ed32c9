package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.IpUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.ProviderExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataResourceRegistRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataResourceRegistUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.GanzhouPublishAttachFile;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.HubGanzhouApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataResourceRegistry;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataResourceRegistryUpdate;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.PublishProduct;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.ResourceItemAddCmd;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataResourceInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.FileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource.COULD_UPDATE_REGISTRATION_STATUS;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataResourceServiceImpl implements DataResourceService {

    private final HubGanzhouApiClient hubGanzhouApiClient;

    private final DataAssetMapper dataAssetMapper;

    private final RouterService routerService;

    private final FilesStorageServiceImpl filesStorageService;

    private final DataResourceRepository dataResourceRepository;

    private final EndpointRemote endpointRemote;
    private final org.ehcache.Cache<String, String> dataAssetCache;
    private final static String DATA_RESOURCE_ROUTER_KEY = "data_resource_router_%s";

    @Override
    public DataResourceVO getDataResource(String dataAssetId) {
        Optional<DataResource> dataResource = dataResourceRepository.findById(dataAssetId);
        Assert.isTrue(dataResource.isPresent(), "未找到 id 为 " + dataAssetId + " 的数据资源");
        return dataAssetMapper.dataResourceToDataResourceVO(dataResource.get());
    }

    @Override
    public Long getApprovedCount() {
        return dataResourceRepository.count(Example.of(DataResource.builder()
                .itemStatus("item_status2")
                .userId(LoginContextHolder.currentUser().getId()).build()));
    }

    @Override
    public DataResourceVO getDataResourceByDataResourcePlatformId(String dataResourcePlatformId) {
        String currentNodeId;
        if (LoginContextHolder.isLogin()) {
            currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        } else {
            currentNodeId = MDC.get("currentNodeId");
        }
        String targetNodeId;
        String companyId;
        if (StringUtils.isEmpty(dataAssetCache.get(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId)))) {
            DataResourceInfo dataResources = hubGanzhouApiClient.getResourceInfo(dataResourcePlatformId);
            JSONObject others = JSONUtil.parseObj(dataResources.getOthers());
            targetNodeId = others.getStr("routerId");
            companyId = others.getStr("companyId");
            dataAssetCache.put(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId), String.format("%s&%s", targetNodeId, companyId));
        } else {
            targetNodeId = StringUtils.substringBefore(dataAssetCache.get(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId)), "&");
            companyId = StringUtils.substringAfterLast(dataAssetCache.get(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId)), "&");
        }
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            TenantContext.setCurrentTenant("tenant_" + companyId);
            DataResource dataResource = dataResourceRepository.findByDataResourcePlatformId(dataResourcePlatformId);
            Assert.notNull(dataResource, "未找到 dataResourcePlatformId 为 " + dataResourcePlatformId + " 的数据资源");
            return dataAssetMapper.dataResourceToDataResourceVO(dataResource);
        } else {
            MDC.put(SystemConstants.ROUTE_ID, targetNodeId);
            MDC.put("X-Tenant-Schema", "tenant_" + companyId);
            SuccessResponse<DataResourceVO> dataAssetSuccessResponse = endpointRemote.dataResourceDetail(dataResourcePlatformId);
            Assert.isTrue(dataAssetSuccessResponse.isSuccess(), "查询目标连接器详情异常");
            return dataAssetSuccessResponse.getData();
        }
    }

    protected NodeInfoResponse currentNodeInfo() {
        return routerService.currentNode();
    }

    @Override
    public SuccessResponse<List<DataResourceListVO>> allDataAssets(int page, int size, Function<Specification<DataResource>, Specification<DataResource>> specificationFunction) {
        Specification<DataResource> dataResourceSpecification = (root, query, cb) -> cb.notEqual(root.get("isDelete"), true);
        if (specificationFunction != null) {
            dataResourceSpecification = specificationFunction.apply(dataResourceSpecification);
        }
        org.springframework.data.domain.Page<DataResource> dataResources;
        PageRequest pageRequest = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createTime"));
        dataResources = dataResourceRepository.findAll(dataResourceSpecification, pageRequest);
        return SuccessResponse.success(
                        dataResources.getContent().stream()
                                .map(dataAssetMapper::dataProductToDataResourceListVO)
                                .toList()
                )
                .total(dataResources.getTotalElements())
                .page(Page.of(page, size))
                .build();
    }

    @Override
    public void updateDataExt(String dataAssetId, Function<DataResource.DataResourceExt, DataResource.DataResourceExt> dataExtUpdate) {
        dataResourceRepository.updateDataExt(dataAssetId, dataExtUpdate.apply(dataResourceRepository.getReferenceById(dataAssetId).getDataExt()));
    }

    @Override
    public void checkNameExists(String dataResourcePlatformId, String dataResourceName) {
        boolean exists = dataResourceRepository.exists(Example.of(DataResource.builder().dataResourceName(dataResourceName).isDelete(false).build()));
        Assert.isTrue(!exists, "数据资源名称重复");
        SuccessResponse<List<DataResourceInfo>> dataResourceByName = hubGanzhouApiClient.allMarketDataResource(
                DataAssetQuery.builder().assetName(dataResourceName).build());
        for (DataResourceInfo mayDuplicateName : dataResourceByName.getData()) {
            if (dataResourceName.equals(mayDuplicateName.getResourceName())) {
                throw new RestfulApiException("数据资源名称重复");
            }
        }
    }

    @Override
    public void temporarySave(DataResourceRegistRequest request) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        DataResource dataResource;
        if (StringUtils.isNotBlank(request.getId())) {
            dataResource = dataResourceRepository.getReferenceById(request.getId());
        } else {
            dataResource = DataResource.builder()
                    .dataResourceName(request.getResourceName())
                    .itemStatus("item_status0")
                    .createTime(new Date())
                    .provider(new ProviderExt())
                    .dataExt(new DataResource.DataResourceExt())
                    .deliveryExt(new DataResource.DataResourceDeliveryExt())
                    .build();
            dataResource.getDataExt().setPublishStatus("0");
            NodeInfoResponse currentNode = currentNodeInfo();
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            String nodeName = currentNode.getName();
            nodeName = StringUtils.isBlank(nodeName) ? "R_" + company.getOrganizationName() : nodeName;
            dataResource.setUserId(userDTO.getId());
            dataResource.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
            dataResource.setUsername(userDTO.getUsername());
            dataResource.setPlatformId(currentNode.getPlatformId());
            dataResource.setPlatformType(0);
            dataResource.getProvider().setRouterId(company.getNodeId());
            dataResource.getProvider().setRouterName(nodeName);
            dataResource.getProvider().setPhone(userDTO.getPhone());
            dataResource.getProvider().setEmail(userDTO.getEmail());
            dataResource.getProvider().setCompany(company);
            dataResource.getProvider().setUserId(userDTO.getId());
            dataResource.getProvider().setUsername(userDTO.getUsername());
            dataResource.setPushStatus(PushStatus.OFFLINE.getCode());
        }
        dataResource.updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateDataSchemaTo(request.getDataSchema())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther());

        dataResource.setUpdateTime(new Date());
        dataResourceRepository.save(dataResource);
    }

    @Override
    public void registration(DataResourceRegistRequest request) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        DataResource dataResource;
        if (StringUtils.isNotBlank(request.getId())) {
            dataResource = dataResourceRepository.getReferenceById(request.getId());
//            Assert.isTrue("item_status0".equals(dataResource.getItemStatus()), "非可登记状态");
        } else {
            dataResource = DataResource.builder()
                    .dataResourceName(request.getResourceName())
                    .createTime(new Date())
                    .provider(new ProviderExt())
                    .dataExt(new DataResource.DataResourceExt())
                    .deliveryExt(new DataResource.DataResourceDeliveryExt())
                    .build();

            NodeInfoResponse currentNode = currentNodeInfo();
            String nodeName = currentNode.getName();
            nodeName = StringUtils.isBlank(nodeName) ? "R_" + company.getOrganizationName() : nodeName;
            dataResource.setUserId(userDTO.getId());
            dataResource.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
            dataResource.setUsername(userDTO.getUsername());
            dataResource.setPlatformId(currentNode.getPlatformId());
            dataResource.setPlatformType(0);
            dataResource.getProvider().setRouterId(company.getNodeId());
            dataResource.getProvider().setRouterName(nodeName);
            dataResource.getProvider().setPhone(userDTO.getPhone());
            dataResource.getProvider().setEmail(userDTO.getEmail());
            dataResource.getProvider().setCompany(company);
            dataResource.getProvider().setUserId(userDTO.getId());
            dataResource.getProvider().setUsername(userDTO.getUsername());
            dataResource.setPushStatus(PushStatus.OFFLINE.getCode());
            dataResource.getDataExt().setPublishStatus("0");
            dataResource.getDataExt().setCreateIp(IpUtils.getClientIp(ServletUtils.getRequest()));
        }
        dataResource.setItemStatus("item_status1");
        dataResource
                .updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateDataSchemaTo(request.getDataSchema())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther())
                .updateResourceTypeTo(request.getResourceType());
        dataResource.getDataExt().setDataVersion(dataResource.getDataExt().getDataVersion() + 1);
        dataResource.getDataExt().setRegistrationTime(System.currentTimeMillis());
        dataResource.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        dataResource.getDataExt().setRegistrationSubmitTime(System.currentTimeMillis());

        dataResource = dataResourceRepository.saveAndFlush(dataResource);

        JSONObject others = new JSONObject();
        others.set("assetId", dataResource.getId());
        others.set("resourceNameCN", dataResource.getDataExt().getAssetNameCN());
        others.set("industry1", dataResource.getDataExt().getIndustry1());
        others.set("region1", dataResource.getDataExt().getRegion1());
        others.set("clientPlatformUniqueNo", dataResource.getProvider().getCompany().getNodeId());
        others.set("userId", dataResource.getUserId());
        others.set("username", dataResource.getUsername());
        others.set("organizationName", dataResource.getProvider().getCompany().getOrganizationName());
        others.set("routerId", dataResource.getProvider().getCompany().getNodeId());
        others.set("platformId", dataResource.getPlatformId());
        others.set("companyId", dataResource.getProvider().getCompany().getId());
        others.set("company", dataResource.getProvider().getCompany());
        try {
            String dataResourceCode = hubGanzhouApiClient.dataResourceRegistry(DataResourceRegistry.builder()
                    .resourceName(dataResource.getDataResourceName())
                    .outerResourceId(dataResource.getId())
                    .dataType(dataResource.getDataExt().getDataSubject() == null ? "01" : dataResource.getDataExt().getDataSubject())
                    .resourceType(dataResource.getDataExt().getResourceType() == null ? "3" : dataResource.getDataExt().getResourceType())
                    .industry(dataResource.getIndustry())
                    .resourceOwner(dataResource.getProvider().getCompany().getOrganizationName())
                    .resourceOwnerId(dataResource.getProvider().getCompany().getEntityId())
                    .resourceOwnerCode(dataResource.getProvider().getCompany().getEntityCode())
                    .capacity(dataResource.getCapacity() == null ? null : String.valueOf(dataResource.getCapacity()))
                    .contacter(dataResource.getUsername())
                    .contactInformation(StringUtils.isBlank(dataResource.getProvider().getPhone()) ? "12345678912" : dataResource.getProvider().getPhone())
                    .resourceAbstract(dataResource.getDescription())
                    .dataSource(dataResource.getDataExt().getSource())
                    .personalInformation(dataResource.getDataExt().getPersonalInformation())
                    .others(JSONUtil.toJsonStr(others))
                    .limitations(dataResource.getDeliveryExt().getLimitations())
                    .authorize(dataResource.getDeliveryExt().getAuthorize())
                    .dpe(dataResource.getProvider().getCompany().getNodeId())
                    .resourceItemList(dataResource.getDataExt().getDataSchema() == null ? "[{\"resourceType\":\"1\",\"code\":\"name\",\"level\":1,\"fieldType\":\"string\",\"filedLength\":15,\"filedDesc\":\"姓名\",\"primaryFlag\":\"0\",\"nullFlag\":\"0\",\"paramType\":\"0\",\"dataType\":\"123123\"}]" : JSONUtil.toJsonStr(dataResource.getDataExt().getDataSchema().stream()
                            .map(dataSchemaBO -> ResourceItemAddCmd.builder()
                                    .code(dataSchemaBO.getFieldName())
                                    .name(dataSchemaBO.getFieldName())
                                    .fieldType(dataSchemaBO.getType())
                                    .primaryFlag(dataSchemaBO.isId() ? "1" : "0")
                                    .fieldDesc(dataSchemaBO.getComment())
//                                .level(dataSchemaBO.getLevel())
                                    .build())
                            .toList()))
                    .build());
            dataResource.setDataResourcePlatformId(dataResourceCode);
            dataResourceRepository.saveAndFlush(dataResource);
        } catch (Exception e) {
            dataResourceRepository.deleteById(dataResource.getId());
            throw e;
        }
    }

    @Override
    public void updateRegistration(DataResourceRegistUpdateRequest request) {
        Optional<DataResource> optionalDataResource = dataResourceRepository.findById(request.getId());
        Assert.isTrue(optionalDataResource.isPresent(), "未找到id为 " + request.getId() + " 的数据资源");
        DataResource dataResource = optionalDataResource.get();
        Assert.isTrue(COULD_UPDATE_REGISTRATION_STATUS.contains(dataResource.getItemStatus()), "非可登记更新状态");
        dataResource.updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateResourceFormat(request.getResourceFormat())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther())
                .updateDataSchemaTo(request.getDataSchema());

        dataResource.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        dataResource.setUpdateTime(new Date());
        if ("item_status3".equals(dataResource.getItemStatus())) {
            // 审批拒绝后更新重置状态待审批
            dataResource.setItemStatus("item_status1");
        }
        dataResource = dataResourceRepository.save(dataResource);

        if ("item_status2".equals(dataResource.getItemStatus())) {
            UserDTO userDTO = LoginContextHolder.currentUser();
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            JSONObject others = new JSONObject();
            others.set("registrationId", dataResource.getDataResourcePlatformId());
            others.set("assetId", dataResource.getId());
            others.set("resourceNameCN", request.getResourceNameCN());
            others.set("industry1", request.getIndustry1());
            others.set("region1", request.getRegion1());
            others.set("clientPlatformUniqueNo", company.getNodeId());
            others.set("userId", dataResource.getUserId());
            others.set("username", dataResource.getUsername());
            others.set("organizationName", company.getOrganizationName());
            others.set("routerId", company.getNodeId());
            others.set("platformId", dataResource.getPlatformId());
            others.set("companyId", company.getId());
            others.set("company", company);
            DataResourceInfo dataAssetUpdateVO = hubGanzhouApiClient.dataResourceRegistryUpdate(DataResourceRegistryUpdate.builder()
                    .outerResourceId(dataResource.getId())
                    .resourceCode(dataResource.getDataResourcePlatformId())
                    .resourceName(dataResource.getDataResourceName())
                    .platformId(company.getNodeId()) // TODO
                    .dataType(dataResource.getDataExt().getDataSubject()) // TODO
                    .resourceType(dataResource.getDataExt().getType())
                    .industry(dataResource.getIndustry())
                    .capacity(dataResource.getCapacity() == null ? null : String.valueOf(dataResource.getCapacity()))
                    .contacter(userDTO.getUsername())
                    .contractInformation(userDTO.getPhone())
                    .resourceAbstract(dataResource.getDescription())
                    .resourceFormat(dataResource.getDataExt().getResourceFormat())
                    .dataSource(dataResource.getDataExt().getSource())
                    .personalInformation(dataResource.getDataExt().getPersonalInformation())
                    .others(others.toString())
                    .limitations(dataResource.getDeliveryExt().getLimitations())
                    .authorize(dataResource.getDeliveryExt().getAuthorize())
                    .resourceItemList(dataResource.getDataExt().getDataSchema() == null ? null :
                                    dataResource.getDataExt().getDataSchema().stream()
                                            .map(dataSchemaBO -> ResourceItemAddCmd.builder()
                                                    .name(dataSchemaBO.getName())
//                                            .level(dataSchemaBO.getLevel())
                                                    .dataType(dataSchemaBO.getType())
                                                    // TODO
                                                    .build()).toList()
                    )
                    .build());
//            Assert.isTrue("0".equals(dataAssetUpdateVO.getUpdateFlag()), "数据资源登记更新失败");
        }
    }

    @Override
    public void revokeRegistration(String productId) {

    }

    @Override
    public void delete(String dataResourceId) {
        DataResource dataResource = dataResourceRepository.getReferenceById(dataResourceId);
        Assert.isTrue("item_status0".equals(dataResource.getItemStatus()), "只可删除暂存状态的数据产品");
        dataResourceRepository.deleteById(dataResourceId);
    }

    @Override
    public void publishResource(PublishProduct publishProduct) {
        String resourceCode = publishProduct.getBizCode();
        DataResource byDataResourceCode = dataResourceRepository.findByDataResourcePlatformId(resourceCode);
        publishProduct.setEntityCode(byDataResourceCode.getProvider().getCompany().getNodeId().substring(1, 19));
        byDataResourceCode.getDataExt().setGanzhouPublishAttachFile(new GanzhouPublishAttachFile());
        if (StringUtils.isNotBlank(publishProduct.getSafetyFile())) {
            Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(byDataResourceCode.getUserId())
                    .resolve(publishProduct.getSafetyFile());
            FileUploadResponse ossFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
            byDataResourceCode.getDataExt().getGanzhouPublishAttachFile().setSafetyFile(ossFile.getUrl());
            publishProduct.setSafetyFile(ossFile.getOssId());
        }
        if (StringUtils.isNotBlank(publishProduct.getProtectFile())) {
            Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(byDataResourceCode.getUserId())
                    .resolve(publishProduct.getProtectFile());
            FileUploadResponse ossFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
            byDataResourceCode.getDataExt().getGanzhouPublishAttachFile().setProtectFile(ossFile.getUrl());
            publishProduct.setProtectFile(ossFile.getOssId());
        }
        hubGanzhouApiClient.publishResource(publishProduct);
        byDataResourceCode.getDataExt().setPublishStatus("1");
        byDataResourceCode.getDataExt().setPublishTime(System.currentTimeMillis());
        byDataResourceCode.getDataExt().setPublishSubmitTime(System.currentTimeMillis());
        byDataResourceCode.getDataExt().setPublishUpdateTime(System.currentTimeMillis());
        dataResourceRepository.saveAndFlush(byDataResourceCode);
    }

}
