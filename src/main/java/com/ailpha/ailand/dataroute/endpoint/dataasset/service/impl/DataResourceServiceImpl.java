package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.IpUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.ProviderExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataResourceRegistRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataResourceRegistUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataResourceUpdateVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetRevokeVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetUpdateVO;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataResourceDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource.COULD_UPDATE_REGISTRATION_STATUS;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType.STRUCTURED;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType.UNSTRUCTURED;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataResourceServiceImpl implements DataResourceService {

    private final HubShuHanApiClient shuHanApiClient;

    private final DataAssetMapper dataAssetMapper;

    @Lazy
    private final TraderService traderService;

    private final DataResourceRepository dataResourceRepository;

    private final EndpointRemote endpointRemote;

    private final org.ehcache.Cache<String, String> dataAssetCache;

    @Override
    public DataResourceVO getDataResource(String dataAssetId) {
        Optional<DataResource> dataResource = dataResourceRepository.findById(dataAssetId);
        Assert.isTrue(dataResource.isPresent(), "未找到 id 为 " + dataAssetId + " 的数据资源");
        return dataAssetMapper.dataResourceToDataResourceVO(dataResource.get());
    }

    @Override
    public DataResourceVO getDataResourceByDataResourcePlatformId(String dataResourcePlatformId, String currentNodeId) {
        String targetNodeId = findDataResourceNodeId(dataResourcePlatformId);
        return getDataResourceByDataResourcePlatformId(dataResourcePlatformId, currentNodeId, targetNodeId);
    }

    @Override
    public DataResourceVO getDataResourceByDataResourcePlatformId(String dataResourcePlatformId, String currentNodeId, String targetNodeId) {
        // 如果目标连接器ID和当前连接器ID相同则本地查询，前置租户过滤器已经完成租户切换 否则调用远程接口
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            return getDataResourceByDataResourcePlatformIdLocal(dataResourcePlatformId);
        } else {
            return getDataResourceByDataResourcePlatformIdFromRemote(dataResourcePlatformId, targetNodeId);
        }
    }

    @Override
    public DataResourceVO getDataResourceByDataResourcePlatformIdFromRemote(String dataResourcePlatformId, String targetNodeId) {
        JSONObject others = detail(dataResourcePlatformId).getFirst().getJSONObject("others");
        DataResourceDetailRequest detailRequest = DataResourceDetailRequest.builder().dataResourcePlatformId(dataResourcePlatformId).build();
        detailRequest.setTargetNodeId(targetNodeId);
        detailRequest.setTargetCompanyId(others.getLong("companyId"));
        CommonResult<DataResourceVO> dataAssetSuccessResponse = endpointRemote.dataResourceDetail(detailRequest);
        Assert.isTrue(dataAssetSuccessResponse.isSuccess(), "查询目标连接器数据产品详情异常：" + dataAssetSuccessResponse.getMessage());
        return dataAssetSuccessResponse.getData();
    }

    public DataResourceVO getDataResourceByDataResourcePlatformIdLocal(String dataResourcePlatformId) {
        DataResource dataResource = dataResourceRepository.findByDataResourcePlatformId(dataResourcePlatformId);
        Assert.notNull(dataResource, "未找到 dataResourcePlatformId 为 " + dataResourcePlatformId + " 的数据产品");
        return dataAssetMapper.dataResourceToDataResourceVO(dataResource);
    }

    private final static String DATA_RESOURCE_ROUTER_KEY = "data_product_router_%s";

    private String findDataResourceNodeId(String dataResourcePlatformId) {
        String targetNodeId;
        final String tmpCache = dataAssetCache.get(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId));
        if (StringUtils.isEmpty(tmpCache)) {
            JSONObject others = detail(dataResourcePlatformId).getFirst().getJSONObject("others");
            targetNodeId = others.getStr("routerId");

            dataAssetCache.put(String.format(DATA_RESOURCE_ROUTER_KEY, dataResourcePlatformId), targetNodeId);
        } else {
            targetNodeId = tmpCache;
        }
        log.info("根据 dataResourcePlatformId:[{}] 查询枢纽 targetNodeId:[{}]", dataResourcePlatformId, targetNodeId);
        return targetNodeId;
    }

    public List<JSONObject> detail(String dataProductPlatformId) {
        SuccessResponse<List<JSONObject>> dataCatalogQuery = shuHanApiClient.dataCatalogQuery(CatalogQueryVM.builder()
                .type(1)
                .filters(List.of(
                        CatalogQueryVM.FilterVM.builder().filterProperty("resourceId").filterOperation("=").filterValue(dataProductPlatformId).build()
                )).orders(List.of())
                .size(100L).page(1L)
                .build());
        Assert.isTrue(dataCatalogQuery.isSuccess() && !CollectionUtils.isEmpty(dataCatalogQuery.getData()), "未查询到id为 " + dataProductPlatformId + " 的数据产品目录数据");
        return dataCatalogQuery.getData();
    }

    @Override
    public SuccessResponse<List<DataResourceListVO>> allDataAssets(int page, int size, Function<Specification<DataResource>, Specification<DataResource>> specificationFunction) {
        Specification<DataResource> dataResourceSpecification = (root, query, cb) -> cb.notEqual(root.get("isDelete"), true);
        if (specificationFunction != null) {
            dataResourceSpecification = specificationFunction.apply(dataResourceSpecification);
        }
        org.springframework.data.domain.Page<DataResource> dataResources;
        PageRequest pageRequest = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createTime"));
        dataResources = dataResourceRepository.findAll(dataResourceSpecification, pageRequest);
        return SuccessResponse.success(
                        dataResources.getContent().stream()
                                .map(dataAssetMapper::dataProductToDataResourceListVO)
                                .toList()
                )
                .total(dataResources.getTotalElements())
                .page(Page.of(page, size))
                .build();
    }

    @Override
    public void updateDataExt(String dataAssetId, Function<DataResource.DataResourceExt, DataResource.DataResourceExt> dataExtUpdate) {
        dataResourceRepository.updateDataExt(dataAssetId, dataExtUpdate.apply(dataResourceRepository.getReferenceById(dataAssetId).getDataExt()));
    }

    @Override
    public void checkNameExists(String dataResourceId, String dataResourceName) {
        Optional<DataResource> optionalDataResource = dataResourceRepository.findOne(Example.of(DataResource.builder().dataResourceName(dataResourceName).isDelete(false).build()));
        optionalDataResource.ifPresent(dataResource -> Assert.isTrue(dataResourceId == null || dataResource.getId().equals(dataResourceId), "数据资源名称重复"));
        SuccessResponse<List<JSONObject>> dataResourceByName = shuHanApiClient.allMarketDataResource(
                DataAssetQuery.builder().assetName(dataResourceName).build());
        for (JSONObject mayDuplicateName : dataResourceByName.getData()) {
            if (dataResourceName.equals(mayDuplicateName.getStr("resourceName"))) {
                throw new RestfulApiException("数据资源名称重复");
            }
        }
    }

    @Override
    public void temporarySave(DataResourceRegistRequest request) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        DataResource dataResource;
        if (StringUtils.isNotBlank(request.getId())) {
            dataResource = dataResourceRepository.getReferenceById(request.getId());
        } else {
            dataResource = DataResource.builder()
                    .dataResourceName(request.getResourceName())
                    .itemStatus("item_status0")
                    .createTime(new Date())
                    .provider(new ProviderExt())
                    .dataExt(new DataResource.DataResourceExt())
                    .deliveryExt(new DataResource.DataResourceDeliveryExt())
                    .build();
            dataResource.getDataExt().setPublishStatus("0");
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            String nodeName = company.getConnectorName();
            nodeName = StringUtils.isBlank(nodeName) ? "R_" + company.getOrganizationName() : nodeName;
            dataResource.setUserId(userDTO.getId());
            dataResource.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
            dataResource.setUsername(userDTO.getUsername());
            dataResource.setPlatformId(company.getNodeId());
            dataResource.setPlatformType(0);
            dataResource.getProvider().setRouterId(company.getNodeId());
            dataResource.getProvider().setRouterName(nodeName);
            dataResource.getProvider().setPhone(userDTO.getPhone());
            dataResource.getProvider().setEmail(userDTO.getEmail());
            dataResource.getProvider().setCompany(company);
            dataResource.getProvider().setUserId(userDTO.getId());
            dataResource.getProvider().setUsername(userDTO.getUsername());
            dataResource.setPushStatus(PushStatus.OFFLINE.getCode());
        }
        dataResource.updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateDataSchemaTo(request.getDataSchema())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther());

        dataResource.setUpdateTime(new Date());
        dataResourceRepository.save(dataResource);
    }

    @Override
    public void registration(DataResourceRegistRequest request) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        DataResource dataResource;
        if (StringUtils.isNotBlank(request.getId())) {
            dataResource = dataResourceRepository.getReferenceById(request.getId());
            Assert.isTrue("item_status0".equals(dataResource.getItemStatus()), "非可登记状态");
        } else {
            dataResource = DataResource.builder()
                    .dataResourceName(request.getResourceName())
                    .createTime(new Date())
                    .provider(new ProviderExt())
                    .dataExt(new DataResource.DataResourceExt())
                    .deliveryExt(new DataResource.DataResourceDeliveryExt())
                    .build();

            String nodeName = company.getConnectorName();
            nodeName = StringUtils.isBlank(nodeName) ? "R_" + company.getOrganizationName() : nodeName;
            dataResource.setUserId(userDTO.getId());
            dataResource.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
            dataResource.setUsername(userDTO.getUsername());
            dataResource.setPlatformId(company.getNodeId());
            dataResource.setPlatformType(0);
            dataResource.getProvider().setRouterId(company.getNodeId());
            dataResource.getProvider().setRouterName(nodeName);
            dataResource.getProvider().setPhone(userDTO.getPhone());
            dataResource.getProvider().setEmail(userDTO.getEmail());
            dataResource.getProvider().setCompany(company);
            dataResource.getProvider().setUserId(userDTO.getId());
            dataResource.getProvider().setUsername(userDTO.getUsername());
            dataResource.setPushStatus(PushStatus.OFFLINE.getCode());
            dataResource.getDataExt().setPublishStatus("0");
            dataResource.getDataExt().setCreateIp(IpUtils.getClientIp(ServletUtils.getRequest()));
        }
        dataResource.setItemStatus("item_status1");
        dataResource
                .updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateDataSchemaTo(request.getDataSchema())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther());
        dataResource.getDataExt().setDataVersion(dataResource.getDataExt().getDataVersion() + 1);
        dataResource.getDataExt().setRegistrationSubmitTime(System.currentTimeMillis());

        dataResourceRepository.saveAndFlush(dataResource);
    }

    @Override
    public void updateRegistration(DataResourceRegistUpdateRequest request) {
        Optional<DataResource> optionalDataResource = dataResourceRepository.findById(request.getId());
        Assert.isTrue(optionalDataResource.isPresent(), "未找到id为 " + request.getId() + " 的数据资源");
        DataResource dataResource = optionalDataResource.get();
        Assert.isTrue(COULD_UPDATE_REGISTRATION_STATUS.contains(dataResource.getItemStatus()), "非可登记更新状态");
        dataResource.updateDataResourceNameCNTo(request.getResourceNameCN())
                .updateResourceFormat(request.getResourceFormat())
                .updateIndustry(request.getIndustry(), request.getIndustry1())
                .updateResourceFormat(request.getResourceFormat())
                .updateRegionInfo(request.getRegion(), request.getRegion1())
                .updatePersonalInformation(request.getPersonalInformation())
                .updatetDescription(request.getDescription())
                .updateSourceTo(request.getSource())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateOtherTo(request.getOther())
                .updateDataSchemaTo(request.getDataSchema());

        dataResource.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        dataResource.setUpdateTime(new Date());
        if ("item_status3".equals(dataResource.getItemStatus()) || "item_status7".equals(dataResource.getItemStatus())) {
            // 审批拒绝后更新重置状态待审批
            dataResource.setItemStatus("item_status1");
        }
        dataResource = dataResourceRepository.save(dataResource);

        if ("item_status2".equals(dataResource.getItemStatus())) {
            UserDTO userDTO = LoginContextHolder.currentUser();
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            JSONObject others = new JSONObject();
            others.set("routerId", company.getNodeId());
            others.set("companyId", company.getId());
            others.set("dataType", dataResource.getDataExt().getDataType());
            DataAssetUpdateVO dataAssetUpdateVO = shuHanApiClient.dataResourceRegistryUpdate(DataResourceUpdateVM.builder()
                    .registrationId(dataResource.getDataResourcePlatformId())
                    .resourceName(dataResource.getDataResourceName())
                    .resourceFormat(dataResource.getDataExt().getResourceFormat())
                    .industry(dataResource.getIndustry().substring(0, 1))
                    .resourceOwner(company.getOrganizationName())
                    .identityId(dataResource.getProvider().getCompany().getCreditCode())
                    .contacter(userDTO.getUsername())
                    .contactInformation(userDTO.getPhone())
                    .resourceAbstract(dataResource.getDescription())
                    .itemList(dataResource.getDataExt().getDataSchema() == null ? Collections.emptyList() :
                            dataResource.getDataExt().getDataSchema().stream().map(schema -> new DataResourceSaveVM.Item(schema.getName(), schema.getType())).toList())
                    .dataSource(dataResource.getDataExt().getSource())
                    .personalInformation("1".equals(dataResource.getDataExt().getPersonalInformation()) ? 1 : 0)
                    .resourceStatus("01")
                    .others(JSONUtil.toJsonStr(others))
                    .dataVersion(String.valueOf(dataResource.getDataExt().getDataVersion()))
                    .build());
        }
    }

    @Override
    public void revokeRegistration(String productId) {
        DataResource dataResource = dataResourceRepository.getReferenceById(productId);
        Assert.notNull(dataResource, "未找到要撤销登记的数据资源");
        UserDTO userDTO = LoginContextHolder.currentUser();
        Assert.isTrue(userDTO.getId().equals(dataResource.getUserId()), "无权操作");
        traderService.deleteFileSource(dataResource.getUserId(), dataResource.getDataExt().getFileSourceMetadata());
        dataResourceRepository.updateItemStatus(dataResource.getId(), "item_status4");
        if (StringUtils.isNotBlank(dataResource.getDataResourcePlatformId())) {
            DataAssetRevokeVO dataAssetRevokeVO = shuHanApiClient.dataResourceRegistryRevoke(dataResource.getDataResourcePlatformId());
            log.debug("撤销登记数据产品 {}({}) , {}", productId, dataResource.getDataResourceName(), dataAssetRevokeVO.getRevokeFlag());
        }
//        try {
//            DescribeRouteResponse describeRouteResponse = gatewayWebApi.describeRoute(DescribeRouteRequest.builder().id(dataResource.getDataExt().getGatewayServiceRouteId()).build());
//            DescribeRouteResponse.InvokeResult routeResponseData = describeRouteResponse.getData().getInvokeResult();
//            gatewayWebApi.deleteRoute(DeleteRouteRequest.builder().ids(routeResponseData.getId()).build());
//            gatewayWebApi.deleteService(DeleteServiceRequest.builder().id(routeResponseData.getServiceId()).build());
//            FileUtil.del(dataResource.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
//        } catch (Exception e) {
//            log.warn("清理数据资源资源出错 {}", e.getMessage());
//        }
    }

    @Override
    public void delete(String dataResourceId) {
        DataResource dataResource = dataResourceRepository.getReferenceById(dataResourceId);
        Assert.isTrue("item_status0".equals(dataResource.getItemStatus()), "只可删除暂存状态的数据产品");
        dataResourceRepository.deleteById(dataResourceId);
    }

    @Override
    public StatisticAssetDTO.StatisticResource statistics() {
        Specification<DataResource> specification = DataResource.itemStatusIs(DataResource.userIdIs(null, LoginContextHolder.currentUser().getId()), "item_status2");
        return StatisticAssetDTO.StatisticResource.builder()
                .count((int) dataResourceRepository.count(DataResource.itemStatusIs(specification, "item_status2")))
                .structuredCount((int) dataResourceRepository.count(DataResource.dataTypeIs(specification, STRUCTURED)))
                .unstructuredCount((int) dataResourceRepository.count(DataResource.dataTypeIs(specification, UNSTRUCTURED)))
                .dataAssetCount((int) dataResourceRepository.count(DataResource.dataType1Is(specification, "数据集")))
                .pictureCount((int) dataResourceRepository.count(DataResource.dataType1Is(specification, "图像")))
                .textCount((int) dataResourceRepository.count(DataResource.dataType1Is(specification, "文件")))
                .build();
    }

}
