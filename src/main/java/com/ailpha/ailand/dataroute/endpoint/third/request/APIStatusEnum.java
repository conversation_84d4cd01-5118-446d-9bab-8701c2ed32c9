package com.ailpha.ailand.dataroute.endpoint.third.request;

public enum APIStatusEnum {
    NORMAL,

    /**
     * 过期状态的修改有两种方式：
     * 1 定时任务 OpenApiExpiredSchedule
     * 2 访问该接口时，如果接口已过期，禁止访问，并将状态改为已过期 OpenApiServiceImpl#queryResult
     */
    EXPIRED,

    DELETED,

    /**
     * 正在导入es数据
     */
    LOADING_ES_DATA,

    UNAVAILABLE;

    /**
     * 判断状态返回是否有效
     *
     * @param apiStatusEnum
     * @return 是否是有效状态
     */
    public static Boolean isValid(APIStatusEnum apiStatusEnum) {
        return NORMAL.equals(apiStatusEnum) || EXPIRED.equals(apiStatusEnum);
    }
}
