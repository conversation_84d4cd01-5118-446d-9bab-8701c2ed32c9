package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetSaveRequest implements Serializable {

    private String id;

    String platformId;

    String dataProductPlatformId;

    /**
     * 连接器ID
     */
    String routerId;

    /**
     * 类型 —— 数据资源 | 数据产品
     */
    AssetType type;

    /**
     * 资产名称
     */
    String assetName;

    /**
     * 资源提供方：默认填入连接器企业认证的企业类型
     */
    String provider;

    /**
     * 数源单位：连接器企业认证的企业名称
     */
    String providerOrg;

    /**
     * 行业分类
     */
    String industry;
    /**
     * 敏感等级
     */
    String sensitiveLevel;

    /**
     * 标签：使用英文逗号分隔
     */
    String tag;

    /**
     * 资源摘要
     */
    String describeMessage;

    /**
     * 数据类型：结构化数据、非结构化数据、模型
     */
    DataType dataType;

    /**
     * 数据接入方式：API、数据库、文件
     */
    SourceType source;
    /**
     * 用户id
     */
    String userId;
    /**
     * 用户名
     */
    String userName;

    String extraData;

}
