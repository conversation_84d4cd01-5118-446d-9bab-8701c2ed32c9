package com.ailpha.ailand.dataroute.endpoint.order.util;

import lombok.Data;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/26 9:32
 */
public class PageUtils {

    @Data
    public static class PageResult<T> {
        private List<T> records;      // 分页数据
        private long total;           // 总记录数
        private long size;            // 每页显示条数
        private long current;         // 当前页
        private long pages;           // 总页数

        public PageResult(List<T> records, long total, long size, long current) {
            this.records = records;
            this.total = total;
            this.size = size;
            this.current = current;
            this.pages = (total + size - 1) / size;
        }
    }

    /**
     * 对List进行分页
     * @param list 待分页的列表
     * @param pageNum 当前页码（从1开始）
     * @param pageSize 每页显示条数
     */
    public static <T> PageResult<T> page(List<T> list, int pageNum, int pageSize) {
        if (list == null || list.isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0, pageSize, pageNum);
        }

        // 总记录数
        int total = list.size();

        // 校验参数
        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize <= 0) {
            pageSize = 10;
        }

        // 计算起始索引和结束索引
        int fromIndex = (pageNum - 1) * pageSize;
        if (fromIndex >= total) {
            return new PageResult<>(Collections.emptyList(), total, pageSize, pageNum);
        }

        // 计算结束索引
        int toIndex = Math.min(fromIndex + pageSize, total);

        // 截取子列表
        List<T> pageList = list.subList(fromIndex, toIndex);

        return new PageResult<>(pageList, total, pageSize, pageNum);
    }
}