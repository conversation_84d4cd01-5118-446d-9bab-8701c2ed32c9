package com.ailpha.ailand.dataroute.endpoint.configuration.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/2/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ConfigurationInfoVO implements Serializable {

    @Schema(description = "自定义logo开关：true（开）false（关）")
    Boolean customLogoEnable;

    @Schema(description = "样式一：导航标签logo")
    Style style1;

    @Schema(description = "样式二：网页标签logo")
    Style style2;

    @Schema(description = "样式三：大屏监控页'产品名称'文本")
    String style3;
    @Schema(description = "零信任页面地址")
    String iamUrl;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Style implements Serializable {
        @Schema(description = "样式地址")
        String url;
        @Schema(description = "样式文件名")
        String fileName;
    }
}
