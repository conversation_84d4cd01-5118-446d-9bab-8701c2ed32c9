package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AttachType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.third.request.GenerateKeyRequest;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import okhttp3.ResponseBody;
import retrofit2.Response;
import retrofit2.http.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/7 10:54
 */
@RetrofitClient(baseUrl = "http://127.0.0.1:8080", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.END_POINT, tokenUrl = "/gateway/auth/api/route/openapi/token")
public interface EndpointRemote {

    @POST("/third/offline/order")
    SuccessResponse<Boolean> offlineOrder(@Query("orderId") String orderId);

    @GET("/third/download/solution/{fileKey}")
    Response<ResponseBody> download(@Path("fileKey") String fileKey);

    @POST("/sse/receiveDataRouteMessage")
    ApiResponse<Void> receiveDataRouteMessage(@Body List<SSEMessageRequest> sseMessageRequests);

    // 数据资产详情
    @GET("/third/data-asset/resource/detail")
    SuccessResponse<DataResourceVO> dataResourceDetail(@Query("dataResourcePlatformId") String dataResourcePlatformId);

    @GET("/third/data-asset/product/detail")
    SuccessResponse<DataProductVO> dataProductDetail(@Query("dataProductPlatformId") String dataProductPlatformId);

    @GET("/third/data-asset/attach-file")
    Response<ResponseBody> dataProductAttachFile(@Query("dataProductPlatformId") String dataProductPlatformId, @Query("attachType") AttachType attachType);

    @POST("/third/generateKey")
    SuccessResponse<String> generateKey(@Body GenerateKeyRequest request);
}
