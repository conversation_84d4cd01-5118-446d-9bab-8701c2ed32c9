package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.GetNonceResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.TokenResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderSuccessDeliveryRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.CompanyInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.PreviewAuthorizationLetterRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.CompanyInfoResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.http.*;

/**
 * <AUTHOR>
 * @date 2025/1/7 10:54
 */
@RetrofitClient(baseUrl = "http://127.0.0.1:8080", sourceOkHttpClient = "customOkHttpClient")
@Sign(
        baseCapabilityType = BaseCapabilityType.END_POINT,
        tokenUrl = "/gateway/auth/api/route/openapi/token",
        getTokenExclude = {"/third/ConnectorIdentityVerify", "/third/ConnectorIdentityVerifyNonce"}
)
public interface EndpointRemote {

//    @POST("/third/offline/order")
//    SuccessResponse<Boolean> offlineOrder(@Query("orderId") String orderId);

    // 获取随机数
    @POST("/third/ConnectorIdentityVerify")
    CommonResult<GetNonceResponse> generateNonce(@Body GenerateUuidRequest request, @Header("metaData") String metaData);

    @POST("/third/ConnectorIdentityVerifyNonce")
    CommonResult<TokenResponse> generateToken(@Body GenerateTokenRequest request, @Header("metaData") String metaData);

    @POST("/third/download/solution")
    Response<ResponseBody> download(@Body DownloadSolutionFileRequest request);

    @POST("/sse/receiveDataRouteMessage")
    CommonResult<Void> receiveDataRouteMessage(@Body SendMessageRequest request);

    // 数据资产详情
    @POST("/third/data-asset/resource/detail")
    CommonResult<DataResourceVO> dataResourceDetail(@Body DataResourceDetailRequest request);

    @POST("/third/data-asset/product/detail")
    CommonResult<DataProductVO> dataProductDetail(@Body DataProductDetailRequest request);

    @POST("/third/data-asset/attach-file")
    Response<ResponseBody> dataProductAttachFile(@Body DownloadFileRequest request);

    @POST("/third/user/preview/authorizationLetter")
    Response<ResponseBody> previewAuthorizationLetter(@Body PreviewAuthorizationLetterRequest request);

    @POST("/third/generateKey")
    CommonResult<String> generateKey(@Body GenerateKeyRequest request);

    @POST("/third/orderDelivery")
    CommonResult<Boolean> orderDelivery(@Body OrderSuccessDeliveryRequest request);

    // 1、获取企业信息 卖方订单使用到
    @POST("/third/companyInfo")
    CommonResult<CompanyInfoResp> companyInfo(@Body CompanyInfoRequest request, @Header("metaData") String metaData);

    @POST("/third/transfer/process")
    CommonResult<String> transferProcess(@Body NegotiateDataTransferDTO request, @Header("metaData") String metaData);

    @POST("/transfer/data/{transferId}")
    String transferApiPull(@Path("transferId") String transferId, @Body ApiImportTestVO request, @Header("metaData") String metaData);

    @Streaming
    @GET("/transfer/data/{transferId}")
    Call<ResponseBody> transferFilePull(@Path("transferId") String transferId, @Query("accessKey") String accessKey, @Query("secretKey") String secretKey, @Header("metaData") String metaData);

}
