package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * 数据产品上下架
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataProductPublishVM extends DataProductSaveVM {

    @ApiModelProperty(value = "数据产品唯一标识（上架或者上架更新的时候需要传）", hidden = true)
    private String registrationId;

    @ApiModelProperty(value = "业务平台唯一标识（上架或者上架更新的时候需要传，上架到哪个业务平台）", hidden = true)
    private String businessPlatformUniqueNo;

    @ApiModelProperty(value = "数据上架id(数据产品上架更新必传")
    private Long launchId;

}
