package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import cn.hutool.json.JSONObject;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据产品上下架
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataProductPublishVM {

    @ApiModelProperty(value = "数据产品标识码", hidden = true)
    private String productId;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @NotBlank(message = "产品名称不能为空")
    @Length(max = 128, message = "产品名称长度不能超过128")
    private String productName;

    /**
     * 产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他
     */
    @ApiModelProperty(value = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他")
    @NotBlank(message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他不能为空")
    @Length(max = 2, message = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他长度不能超过2")
    private String productType;
    /**
     * 覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）
     */
    @ApiModelProperty(value = "覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）")
    @Length(max = 50, message = "覆盖时间范围（YYYY-MM-DD至YYYY-MM-DD）长度不能超过50")
    private String timeRange;

    /**
     * 行业分类（GB/T 4754-2017门类代码）
     */
    @ApiModelProperty(value = "行业分类（GB/T 4754-2017门类代码）")
    @NotBlank(message = "行业分类（GB/T 4754-2017门类代码）不能为空")
    @Length(max = 1, message = "行业分类（GB/T 4754-2017门类代码）长度不能超过1")
    private String industry;

    /**
     * 地域分类（GB/T 2260-2007代码）
     */
    @ApiModelProperty(value = "地域分类（GB/T 2260-2007代码）")
    @Length(max = 12, message = "地域分类（GB/T 2260-2007代码）长度不能超过12")
    private String productRegion;

    /**
     * 是否涉及个人信息：0-否,1-是
     */
    @ApiModelProperty(value = "是否涉及个人信息：0-否,1-是")
    @NotNull(message = "是否涉及个人信息：0-否,1-是不能为空")
    private Integer personalInformation;

    /**
     * 产品简介
     */
    @ApiModelProperty(value = "产品简介")
    @NotBlank(message = "产品简介不能为空")
    @Length(max = 65535, message = "产品简介长度不能超过65535")
    private String description;

    /**
     * 交付方式：01-文件传输,02-数据流传输,03-API传输
     */
    @ApiModelProperty(value = "交付方式：01-文件传输,02-数据流传输,03-API传输")
    @Length(max = 2, message = "交付方式：01-文件传输,02-数据流传输,03-API传输长度不能超过2")
    private String deliveryMethod;

    /**
     * 使用限制
     */
    @ApiModelProperty(value = "使用限制")
    @NotBlank(message = "使用限制不能为空")
    @Length(max = 65535, message = "使用限制长度不能超过65535")
    private String limitations;

    /**
     * 授权使用：0-否,1-是
     */
    @ApiModelProperty(value = "授权使用：0-否,1-是")
    @NotNull(message = "授权使用：0-否,1-是不能为空")
    private Integer authorize;

    /**
     * 数据主体：01-个人信息,02-企业数据,03-公共数据
     */
    @ApiModelProperty(value = "数据主体：01-个人信息,02-企业数据,03-公共数据")
    @NotBlank(message = "数据主体：01-个人信息,02-企业数据,03-公共数据不能为空")
    @Length(max = 2, message = "数据主体：01-个人信息,02-企业数据,03-公共数据长度不能超过2")
    private String dataSubject;

    /**
     * 数据规模（如10GB）
     */
    @ApiModelProperty(value = "数据规模（如10GB）")
    @Length(max = 16, message = "数据规模（如10GB）长度不能超过16")
    private Double dataSize;
    private String dataSizeUnit;

    /**
     * 更新频率（次/天、次/周等）
     */
    @ApiModelProperty(value = "更新频率（次/天、次/周等）")
    @NotBlank(message = "更新频率（次/天、次/周等）不能为空")
    @Length(max = 10, message = "更新频率（次/天、次/周等）长度不能超过10")
    private Integer updateFrequency;
    private String updateFrequencyUnit;

    /**
     * 数据资源标识码
     */
    @ApiModelProperty(value = "数据资源标识码 数组型（一个或多个数据源标识码）")
    @Length(max = 65535, message = "数据资源标识码不能超过65535")
    private List<String> resourceId;

    /**
     * 其他扩展信息 对象
     * industry1
     * region1
     * deliveryModes
     * companyId
     */
    @ApiModelProperty(value = "其他扩展信息对象:{\"registrationId\":\"数据产品唯一标识（上架或者上架更新的时候需要传）\"," +
            "\"clientPlatformUniqueNo\":\"数由器唯一标识（登记的时候需要传，溯源）\"" +
            "\"businessPlatformUniqueNo\":\"业务平台唯一标识（上架或者上架更新的时候需要传，上架到哪个业务平台）\"}")
    @Length(max = 65535, message = "其他扩展信息长度不能超过65535")
    private JSONObject others;

    /**
     * 提供方名称
     */
    @ApiModelProperty(value = "提供方名称")
    @NotBlank(message = "提供方名称不能为空")
    @Length(max = 128, message = "提供方名称长度不能超过128")
    private String providerName;

    /**
     * 提供方主体类型：01-自然人,02-法人,03-非法人组织
     */
    @ApiModelProperty(value = "提供方主体类型：01-自然人,02-法人,03-非法人组织")
    @NotBlank(message = "提供方主体类型：01-自然人,02-法人,03-非法人组织不能为空")
    @Length(max = 2, message = "提供方主体类型：01-自然人,02-法人,03-非法人组织长度不能超过2")
    private String providerType;

    /**
     * 主体信息 对象类型
     */
    @ApiModelProperty(value = "主体信息 对象类型")
    @NotBlank(message = "主体信息不能为空")
    @Length(max = 65535, message = "主体信息长度不能超过65535")
    private String entityInformation;

    /**
     * 身份标识码（NDI-TR-2025-04编码规则）
     */
    @ApiModelProperty(value = "身份标识码（NDI-TR-2025-04编码规则）")
    @NotBlank(message = "身份标识码不能为空")
    @Length(max = 255, message = "身份标识码（NDI-TR-2025-04编码规则）长度不能超过255")
    private String identityId;

    /**
     * 提供方简介
     */
    @ApiModelProperty(value = "提供方简介")
    @NotBlank(message = "提供方简介不能为空")
    @Length(max = 65535, message = "提供方简介长度不能超过65535")
    private String providerDesc;

    /**
     * 法人经办人姓名
     */
    @ApiModelProperty(value = "法人经办人姓名")
    @Length(max = 10, message = "法人经办人姓名长度不能超过10")
    private String operatorName;

    /**
     * 法人经办人电话
     */
    @ApiModelProperty(value = "法人经办人电话")
    @Length(max = 11, message = "法人经办人电话长度不能超过11")
    private String operatorTelephone;

    /**
     * 法人经办人身份证
     */
    @ApiModelProperty(value = "法人经办人身份证")
    @Length(max = 18, message = "法人经办人身份证长度不能超过18")
    private String operatorIdCard;

    /**
     * 授权委托书（二进制文件）
     */
    @ApiModelProperty(value = "授权委托书（二进制文件）")
    @Length(max = 65535, message = "授权委托书（二进制文件）长度不能超过65535")
    private String commission;
    private String commissionFileName;

    /**
     * 数据样例（二进制文件）
     */
    @ApiModelProperty(value = "数据样例（二进制文件）")
    @Length(max = 255, message = "数据样例（二进制文件）长度不能超过255")
    private String dataSample;
    private String dataSampleFileName;

    /**
     * 合法合规声明（二进制文件）
     */
    @ApiModelProperty(value = "合法合规声明（二进制文件）")
    @NotBlank(message = "合法合规声明（二进制文件）不能为空")
    @Length(max = 255, message = "合法合规声明（二进制文件）长度不能超过255")
    private String complianceAndLegalStatement;
    private String complianceAndLegalStatementFileName;

    /**
     * 数据来源声明（二进制文件）
     */
    @ApiModelProperty(value = "数据来源声明（二进制文件）")
    @NotBlank(message = "数据来源声明（二进制文件）不能为空")
    @Length(max = 255, message = "数据来源声明（二进制文件）长度不能超过255")
    private String dataSourceStatement;
    private String dataSourceStatementFileName;

    /**
     * 安全分级分类（二进制文件）
     */
    @ApiModelProperty(value = "安全分级分类（二进制文件）")
    @Length(max = 255, message = "安全分级分类（二进制文件）长度不能超过255")
    private String safeLevel;
    private String safeLevelFileName;

    /**
     * 数据质量/价值评估报告（二进制文件）
     */
    @ApiModelProperty(value = "数据质量/价值评估报告（二进制文件）")
    @Length(max = 255, message = "数据质量/价值评估报告（二进制文件）长度不能超过255")
    private String evaluationReport;
    private String evaluationReportFileName;

    /**
     * 数据版本号，覆盖更新
     */
    @ApiModelProperty(value = "数据版本号，覆盖更新")
    @Length(max = 2, message = "数据版本号，覆盖更新长度不能超过2")
    private String dataVersion;

    @ApiModelProperty(value = "业务平台唯一标识（上架或者上架更新的时候需要传，上架到哪个业务平台）", hidden = true)
    private String businessPlatformUniqueNo;

    @ApiModelProperty(value = "数据上架id(数据产品上架更新必传")
    private Long launchId;

    @ApiModelProperty(value = "合同模板ID")
    private String contractTemplateId;

    @ApiModelProperty(value = "卖方企业编号（数瀚）")
    private String partyACompanyId;

    @ApiModelProperty(value = "卖方用户编号（数瀚）")
    private String partyAUserId;

    @ApiModelProperty(value = "计量方式")
    private String measureMethod;

    @ApiModelProperty(value = "计量单位")
    private String unit;

    @ApiModelProperty(value = "价格 单位 分")
    private Integer price;

    @ApiModelProperty(value = "数据类型: 结构化数据 STRUCTURED, 非结构化数据UNSTRUCTURED")
    private DataType dataType;

    @ApiModelProperty(value = "交付场景：API, FILE_DOWNLOAD, TEE_ONLINE, TEE_MODEL_PREDICT, TEE_MODEL_OPTIMIZE, TEE_OFFLINE, MPC")
    List<DeliveryMode> deliveryModes;

    @ApiModelProperty(value = "交付方式说明 形式为文件 Base64 编码后的字符串")
    private String deliveryInfo;
    private String deliveryInfoFileName;

    @ApiModelProperty(value = "上架业务节点名称")
    private String platformName;
    @ApiModelProperty(value = "上架业务节点ID")
    private String platformId;
    @ApiModelProperty(value = "上架业务节点位置")
    private String platformLocation;
}
