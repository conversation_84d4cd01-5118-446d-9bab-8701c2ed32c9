package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

/**
 * <AUTHOR>
 * 2025/4/3
 */
public interface HubShuHanServiceNodeApi {

    /**
     * 业务节点列表
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/service-node/list")
    ShuhanResponse<IPage<ServiceNodeListVO>> serviceNodeList(@RequestBody PageRequest<ServiceNodeReq> serviceNodeReq);

    /**
     * 业务节点申请
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/service-node/apply")
    ShuhanResponse<Boolean> serviceNodeApply(@RequestBody ServiceNodeApplyReq serviceNodeApplyReq);

    /**
     * 业务节点已申请列表
     */
    @PostExchange("/gateway/shuhan-business-service/api/inner/service-node/apply-list")
    ShuhanResponse<IPage<ServiceNodeApplyListVO>> serviceNodeApplyList(@RequestBody PageRequest<ServiceNodeApplyListReq> pageRequest);
}
