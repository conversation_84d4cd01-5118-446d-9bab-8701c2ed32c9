package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

/**
 * <AUTHOR>
 * 2025/4/3
 */
public interface HubShuHanServiceNodeApi {

    /**
     * 信通院标准
     * <p>
     * 业务节点目录查询接口
     */
    @PostExchange("/regionSyncListQuery")
    ShuhanResponse<RegionSyncListVO> regionSyncListQuery(@RequestBody CatalogQueryVM catalogQueryVM);
}
