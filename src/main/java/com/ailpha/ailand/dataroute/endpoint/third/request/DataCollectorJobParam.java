package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.DatasourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.APISourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PartitionQueryConditionDTO;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataCollectorJobParam {
    String jobId;
    String dataAssetId;
    DatasourceType datasourceType;
    String tableName;
    String jdbcUrl;
    String username;
    String password;
    List<String> columns;
    List<String> columnAlias;
    // 父目录即可，默认文件名会根据表名生成
    String filepath;
    // 文件类型，后缀名类型
    String fileType;
    // >>>>> ODPS
    // 如果是odps 有分区字段
    List<PartitionQueryConditionDTO> conditions;
    String accessId;
    String accessKey;
    String projectName;
    // <<<<< ODPS
    String syncRule;
    String callbackUrl;
    // http
    APISourceMetadata apiParams;
    // 用于前置机连接minio的参数
    String uploadSdkParams;
}
