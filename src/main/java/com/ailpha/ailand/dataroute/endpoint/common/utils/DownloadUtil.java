package com.ailpha.ailand.dataroute.endpoint.common.utils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

@Slf4j
public class DownloadUtil {
    private final InputStream is;
    private final String fileName;
    private String downName;

    public void setDownName(String downName) {
        this.downName = downName;
    }

    public DownloadUtil(File file) throws FileNotFoundException {
        fileName = file.getName();
        this.is = new FileInputStream(file);
    }

    public DownloadUtil(InputStream is, String fileName) {
        this.is = is;
        this.fileName = fileName;
    }


    public void downLoad(HttpServletRequest request, HttpServletResponse response) {
        try {
            OutputStream os = response.getOutputStream();

            String newName;
            String oldName = StringUtils.isNotEmpty(fileName) ? fileName : UUID.randomUUID().toString().replace("-", "");
            if (StringUtils.isBlank(downName)) {
                newName = oldName;
            } else {
                newName = downName;
                if (!newName.contains(".") && oldName.contains(".")) {
                    newName += oldName.substring(oldName.lastIndexOf('.'));
                }
            }
            //去掉空格，不然会杯具
            newName = newName.replace(" ", "");
            log.debug("newName={}", newName);
            log.debug("downName={}", downName);
            //gb2312防止IE下载文件名中文乱码
            byte[] bytes = request.getHeader("User-Agent").contains("MSIE") || request.getHeader("User-Agent").contains("IE") || request.getHeader("User-Agent").contains("like Gecko") ? newName.getBytes("gb2312") : newName.getBytes("UTF-8");
            //输出文件信息
            response.reset();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setContentLengthLong(is.available());
            response.setHeader("Content-disposition", "attachment; filename=" + new String(bytes, StandardCharsets.ISO_8859_1));

            byte[] buf = new byte[1024 * 1024];
            int len;
            while ((len = is.read(buf)) != -1) {
                os.write(buf, 0, len);
            }
            os.flush();
        } catch (Exception e) {
            log.error("下载文件出错", e);
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
