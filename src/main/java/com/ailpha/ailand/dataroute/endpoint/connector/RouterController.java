package com.ailpha.ailand.dataroute.endpoint.connector;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.UploadLicenseResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.util.Base64;

@RestController
@RequestMapping("router")
@RequiredArgsConstructor
@Tag(name = "连接器管理")
public class RouterController {

    private final RouterService routerService;
    private final KeyPair keyPair;

    @GetMapping("publicKey")
    public SuccessResponse<String> publicKey() {
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        return SuccessResponse.success(Base64.getEncoder().encodeToString(rsaPublicKey.getEncoded())).build();
    }

    @GetMapping("export")
    @Operation(summary = "导出节点信息")
//    @OpLog(message = "连接器入网激活-导出文件")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN')")
    public void export(HttpServletRequest request, HttpServletResponse response) {
        OPLogContext.putOpType(InternalOpType.EXPORT_ROUTE_INFO);
        routerService.export(request, response);
        OPLogContext.openSwitch();
    }

    @GetMapping("currentNode")
    @Operation(summary = "展示当前节点信息")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN')")
    @Deprecated
    public SuccessResponse<NodeInfoResponse> currentNode() {
        NodeInfoResponse data = routerService.currentNode();
        return SuccessResponse.success(data).build();
    }

    @PostMapping("upload/license")
    @Operation(summary = "上传证书")
//    @OpLog(message = "连接器入网激活-上传license")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN')")
    public SuccessResponse<UploadLicenseResponse> uploadLicense(@RequestParam("file") MultipartFile file) {
        OPLogContext.putOpType(InternalOpType.UPLOAD_LICENSE);
        return SuccessResponse.success(UploadLicenseResponse.builder().fileId(routerService.uploadLicense(file)).build()).build();
    }

//    @GetMapping("/view/company/license")
//    @Operation(summary = "预览企业营业执照")
//    public SuccessResponse<String> viewCompanyLicense() {
//        return ApiResponse.success(routerService.viewCompanyLicense()).build();
//    }

//    @PostMapping("check/license")
//    @Operation(summary = "校验枢纽可信证书")
//    public SuccessResponse<CheckLicenseResponse> checkLicense() {
//        CheckLicenseResponse response = routerService.checkLicense();
//        return ApiResponse.success(response).build();
//    }

//    @PostMapping("network/link")
//    @Operation(summary = "网络连接")
//    public SuccessResponse<Boolean> networkLink(@RequestBody NetworkLinkRequest request) {
//        routerService.networkLink(request);
//        return ApiResponse.success(true).build();
//    }


//    @PostMapping("activate")
//    @Operation(summary = "连接器激活")
//    public SuccessResponse<ActivateResponse> activate() {
//        ActivateResponse activate = routerService.activate();
//        OPLogContext.openSwitch();
//        return ApiResponse.success(activate).build();
//    }

//    @PostMapping("/update")
//    @Operation(summary = "编辑连接器")
//    @OpLog(message = "连接器信息编辑")
//    public SuccessResponse<NodeInfoResponse> updateRouter(@RequestBody UpdateRouterInfoVO request) {
//        OPLogContext.putOpType(InternalOpType.UPDATE_ROUTE_INFO);
//        SuccessResponse<NodeInfoResponse> successResponse = SuccessResponse.success(routerService.updateRouter(request)).build();
//        OPLogContext.openSwitch();
//        return successResponse;
//    }


//    @PostMapping("hubRegister")
//    public SuccessResponse<Boolean> registerToHub(@RequestBody RegisterToHubRequest request) {
//        routerService.registerToHub(request);
//        return SuccessResponse.success(true).build();
//    }
}
