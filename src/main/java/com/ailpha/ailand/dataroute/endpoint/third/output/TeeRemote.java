package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.TeeContract;
import com.ailpha.ailand.dataroute.endpoint.third.input.TerminalContractRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.ExternalPlatformAppKeyCreateReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.UpdateRouteAssetRequest;
import com.ailpha.ailand.dataroute.endpoint.third.response.SystemConfigureInfo;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import okhttp3.MultipartBody;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.TEE, tokenUrl = "/_ailand/external/openApiKey/token")
public interface TeeRemote {

    @POST("/_ailand/external/dataRouter/createContract")
    CommonResult<Boolean> createContract(@Body TeeContract contract);

    @POST("/_ailand/external/dataRouter/updateContract")
    CommonResult<Boolean> terminalContract(@Body TerminalContractRequest request);

    @GET("/_ailand/external/dataRouter/systemConfig")
    CommonResult<SystemConfigureInfo> systemConfigInfo();

    @POST("/_ailand/external/openApiKey/register")
    CommonResult<Boolean> appRegister(@Body ExternalPlatformAppKeyCreateReq request);

    @POST("/_ailand/external/dataRouter/updateAsset")
    CommonResult<Void> updateAsset(@Body UpdateRouteAssetRequest request);

    /**
     * @param body @see com.ailpha.ailand.dataroute.endpoint.third.request.OpenDataRouterUpdateDatasetRequest
     * @return
     */
    @POST("/_ailand/external/dataRouter/uploadDebugFile")
    CommonResult<Void> uploadDebugFile(@Body MultipartBody body);
}
