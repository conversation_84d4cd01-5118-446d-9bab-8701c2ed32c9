package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ContractOpenApiRequest {

    @Schema(description = "接口id")
    String id;

    @Schema(description = "接口名称")
    String name;

    @Schema(description = "合约id")
    String contractId;

    @Schema(description = "合约名")
    String contractName;

    @Schema(description = "模型id")
    String modelId;

    @Schema(description = "模型名")
    String modelName;

    @Schema(description = "描述")
    String description;

    @Schema(description = "true：上线， false：下线")
    Boolean online;

    @Schema(description = "接口白名单，逗号分隔")
    String ipWhiteList;

    @Schema(description = "访问名单")
    List<OpenApiUserVO> userList;

    @Schema(description = "失效时间，为null表示永久有效")
    Date endTime;

    List<OpenApiResultVO> result;
}
