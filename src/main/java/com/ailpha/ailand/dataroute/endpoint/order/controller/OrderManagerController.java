package com.ailpha.ailand.dataroute.endpoint.order.controller;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListResponse;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderListVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.*;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/16
 */
@RestController
@Tag(name = "订单合同管理")
@RequestMapping("/order-manager")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('TRADER')")
public class OrderManagerController {

    private final OrderManagerService orderManagerService;

    @PostMapping("/create")
    @Operation(summary = "创建订单合同")
    @OpLog(message = "创建订单合同")
    @Deprecated
    public SuccessResponse<Boolean> create(@Valid @RequestBody OrderCreateReq orderCreateReq) throws Exception {
        OPLogContext.putOpType(InternalOpType.CREATE_ORDER);
        orderManagerService.create(orderCreateReq);
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PostMapping("/buyer-list")
    @Operation(summary = "买方-订单合同列表")
    public SuccessResponse<List<OrderListVO>> buyerList(@Valid @RequestBody OrderBuyerListReq orderBuyerListReq) {
        return orderManagerService.buyerList(orderBuyerListReq);
    }

    @GetMapping("/detail")
    @Operation(summary = "订单详情")
    @Parameters({
            @Parameter(name = "orderId", description = "订单id", in = ParameterIn.QUERY)
    })
    public SuccessResponse<OrderVO> detail(@RequestParam(value = "orderId") String orderId) {
        OrderVO orderVO = orderManagerService.detail(orderId);
        return SuccessResponse.success(orderVO).build();
    }

    @PostMapping("/certificate-record")
    @Operation(summary = "数据发票存证记录")
    public SuccessResponse<List<DeliveryListResponse>> certificateRecord(@Valid @RequestBody CertificateRecordListReq certificateRecordListReq) {
        return orderManagerService.certificateRecord(certificateRecordListReq.getOrderId(), certificateRecordListReq.getNum(), certificateRecordListReq.getSize());
    }

    @PostMapping("/seller-list")
    @Operation(summary = "卖方-订单合同列表")
    public SuccessResponse<List<OrderListVO>> sellerList(@Valid @RequestBody OrderSellerListReq orderSellerListReq) {
        return orderManagerService.sellerList(orderSellerListReq);
    }

    @PostMapping("/approve")
    @Operation(summary = "审批通过")
    @OpLog(message = "审批通过，订单ID：{orderId}")
    public SuccessResponse<Boolean> approve(@RequestBody OrderIdReq orderIdReq) {
        OPLogContext.putOpType(InternalOpType.APPROVE_ORDER);
        OPLogContext.put("orderId", orderIdReq.getOrderId());
        orderManagerService.updateStatus(orderIdReq.getOrderId(), OrderStatus.APPROVED);
        return SuccessResponse.success(Boolean.TRUE).build();
    }


//    @PostMapping("/transfer")
//    @Operation(summary = "买方-传输协商")
//    public SuccessResponse<Boolean> transfer(@RequestBody DeliveryTransferRequest request) {
//        orderManagerService.transfer(request);
//        return SuccessResponse.success(Boolean.TRUE).build();
//    }

    @Data
    public static class ApproveBatchRequest {
        List<String> orderIds;
    }

    @PostMapping("/approve-batch")
    @Operation(summary = "批量审批通过")
    @OpLog(message = "批量审批通过，订单ID列表：{orderIds}")
    public SuccessResponse<Boolean> approveBatch(@RequestBody ApproveBatchRequest request) {
        OPLogContext.putOpType(InternalOpType.APPROVE_ORDER);
        OPLogContext.put("orderIds", request.orderIds);
        for (String orderId : request.orderIds) {
            orderManagerService.updateStatus(orderId, OrderStatus.APPROVED);
        }
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PostMapping("/reject")
    @Operation(summary = "审批拒绝")
    @OpLog(message = "审批拒绝，订单ID：{orderId}")
    public SuccessResponse<Boolean> reject(@Valid @RequestBody OrderIdReq orderIdReq) {
        OPLogContext.putOpType(InternalOpType.REJECT_ORDER);
        OPLogContext.put("orderId", orderIdReq.getOrderId());
        orderManagerService.updateStatus(orderIdReq.getOrderId(), OrderStatus.REJECTED);
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @Data
    public static class RejectBatchRequest {
        List<String> orderIds;
    }

    @PostMapping("/reject-batch")
    @Operation(summary = "批量审批拒绝")
    @OpLog(message = "批量审批拒绝，订单ID列表：{orderIds}")
    public SuccessResponse<Boolean> rejectBatch(@Valid @RequestBody RejectBatchRequest request) {
        OPLogContext.putOpType(InternalOpType.REJECT_ORDER);
        OPLogContext.put("orderIds", request.getOrderIds());
        for (String orderId : request.orderIds) {
            orderManagerService.updateStatus(orderId, OrderStatus.REJECTED);
        }
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PostMapping("/terminate")
    @Operation(summary = "终止")
    @OpLog(message = "终止订单，订单ID：{orderId}")
    public SuccessResponse<Boolean> terminate(@Valid @RequestBody OrderIdReq orderIdReq) {
        OPLogContext.putOpType(InternalOpType.TERMINATE_ORDER);
        OPLogContext.put("orderId", orderIdReq.getOrderId());
        orderManagerService.updateStatus(orderIdReq.getOrderId(), OrderStatus.TERMINATED);
        return SuccessResponse.success(Boolean.TRUE).build();
    }
}
