package com.ailpha.ailand.dataroute.endpoint.node.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新节点请求参数
 */
@Data
@Schema(description = "更新节点请求参数")
public class UpdateNodeRequest {

    @Schema(description = "节点名称", example = "华东区域主节点")
    private String nodeName;

    @Schema(description = "节点类型", example = "MASTER")
    private String nodeType;

    // 根据实际需要，您可以添加其他需要更新的字段
    // 例如：
    // @Schema(description = "节点链接类型")
    // private com.ailpha.ailand.dataroute.endpoint.node.LinkType linkType;
    //
    // @Schema(description = "导入文件信息")
    // private NodeRequest.ImportFile importFile;
    //
    // @Schema(description = "手动输入信息")
    // private NodeRequest.ManualInput manualInput;
}