package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface DataResourceRepository extends JpaRepository<DataResource, String>, QuerydslPredicateExecutor<DataResource>, JpaSpecificationExecutor<DataResource> {

    DataResource findByDataResourcePlatformId(String dataResourcePlatformId);

    @Transactional
    @Modifying
    @Query("update DataResource dp set dp.dataResourcePlatformId = :dataResourcePlatformId where dp.id = :id")
    void updateDataResourcePlatformId(@Param(value = "id") String id, @Param(value = "dataResourcePlatformId") String dataResourcePlatformId);

    @Transactional
    @Modifying
    @Query("update DataResource dp set dp.itemStatus = :itemStatus where dp.id = :id")
    void updateItemStatus(@Param(value = "id") String id, @Param(value = "itemStatus") String itemStatus);

    @Transactional
    @Modifying
    @Query("update DataResource dp set dp.pushStatus = :pushStatus where dp.id = :id")
    void updatePushStatus(@Param(value = "id") String id, @Param(value = "pushStatus") String pushStatus);

    @Transactional
    @Modifying
    @Query("update DataResource dp set dp.dataExt = :dataExt where dp.id = :id")
    void updateDataExt(@Param(value = "id") String id, @Param(value = "dataExt") DataResource.DataResourceExt dataExt);

    @Transactional
    @Modifying
    @Query("update DataResource dp set dp.isDelete = true where dp.id = :id")
    void softDelete(@Param(value = "id") String id);
}
