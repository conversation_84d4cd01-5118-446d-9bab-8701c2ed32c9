package com.ailpha.ailand.dataroute.endpoint.common.rest.ailand;


import lombok.Getter;

public enum InternalReturnCode implements IReturnCode {

    SUCCESS(0, "成功"),

    FAIL(-1, "失败");

    @Getter
    private final Integer code;

    private final String message;

    InternalReturnCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getValue() {
        return message;
    }

}
