package com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 场景数据资产关联
 *
 * <AUTHOR>
 * @date 2024/11/18 09:51
 */
@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_scene_asset")
@AllArgsConstructor
public class SceneAsset implements Serializable {

    @Id
    private String id;

    /**
     * 场景id
     */
    @Schema(description = "场景id")
    @Column(name = "delivery_scene_id")
    private String deliverySceneId;

    /**
     * 资产id
     */
    @Schema(description = "资产id")
    @Column(name =  "data_asset_id")
    private String dataAssetId;

    /**
     * api id
     */
    @Schema(description = "api id")
    @Column(name =  "api_id")
    private String apiId;

    /**
     * api key
     */
    @Schema(description = "api key")
    @Column(name =  "api_key")
    private String apiKey;

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    @Column(name =  "order_id")
    private String orderId;

    @Column(name =  "ext")
    private String ext;
}
