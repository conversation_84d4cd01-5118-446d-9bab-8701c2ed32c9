package com.ailpha.ailand.dataroute.endpoint.servicenode.service;

import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.ApprovalStatus;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.QServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.servicenode.mapper.ServiceNodeMapper;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.repository.ServiceNodeRepository;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.ServiceNodeExtend;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ApplyServiceNodeResp;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeAppliedListVO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeProcessStatusVO;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListReq;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/7/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ServiceNodeService {

    private static final QServiceNodeInfo qServiceNodeInfo = QServiceNodeInfo.serviceNodeInfo;
    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final ServiceNodeMapper serviceNodeMapper;
    private final ServiceNodeRepository serviceNodeRepository;
    private final ServiceNodeRemoteService serviceNodeRemote;
    private final CompanyRepository companyRepository;
    private final HubShuHanApiClient hubShuHanApiClient;

    /**
     * 业务节点申请
     */
    public String serviceNodeApply(ServiceNodeListVO serviceNodeListVO) {
        ServiceNodeInfo serviceNodeInfo;
        List<ServiceNodeInfo> applyServiceNodeList = serviceNodeRepository.findAllByServiceNodeId(serviceNodeListVO.getServiceNodeId());
        if (!ObjectUtils.isEmpty(applyServiceNodeList)) {
            serviceNodeInfo = applyServiceNodeList.getFirst();
            BeanUtils.copyProperties(serviceNodeListVO, serviceNodeInfo);
            if (!ApprovalStatus.APPROVED.name().equals(serviceNodeInfo.getProcessStatus())) {
                serviceNodeInfo.setProcessStatus(ApprovalStatus.APPLY.name());
                serviceNodeInfo.setProcessTime(null);
                serviceNodeInfo.setCreateTime(new Date());
            }
        } else {
            long timestamp = System.currentTimeMillis();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            String id = String.format("%s%s%s%s", "SN", format.format(timestamp), timestamp, UUID.randomUUID().toString().replace("-", "").substring(0, 9).toUpperCase());
            serviceNodeInfo = serviceNodeMapper.serviceNodeListVOToServiceNodeInfo(serviceNodeListVO);
            serviceNodeInfo.setId(id);
        }
        if (ApprovalStatus.APPLY.name().equals(serviceNodeInfo.getProcessStatus())) {
            // 调用业务节点
            UserDTO currentUser = LoginContextHolder.currentUser();
            CompanyDTO company = currentUser.getCompany();
            ServiceNodeMetaData metaData = new ServiceNodeMetaData();
            metaData.setNodeId(company.getNodeId());
            metaData.setUrl(serviceNodeInfo.getApiUrl());
            ShuhanResponse<ApplyServiceNodeResp> response = serviceNodeRemote.applyServiceNode(metaData.toBase64());
            if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getData())) {
                String processId = response.getData().getProcessId();
                ServiceNodeExtend extend = ServiceNodeExtend.builder().processId(processId).build();
                serviceNodeInfo.setExtend(JacksonUtils.obj2json(extend));
            } else {
                log.error("业务节点[{}]-[{}]-[{}] 申请异常 ->>> 结果:{}", serviceNodeInfo.getServiceNodeId(), serviceNodeInfo.getEntryName(), serviceNodeInfo.getApiUrl(), response);
                throw new RestfulApiException("业务节点申请异常，结果：" + response);
            }
        }
        serviceNodeRepository.saveAndFlush(serviceNodeInfo);
        if (ApprovalStatus.APPROVED.name().equals(serviceNodeInfo.getProcessStatus())) {
            throw new RestfulApiException(String.format("业务节点[%s]-[%s]已申请通过", serviceNodeInfo.getServiceNodeId(), serviceNodeInfo.getEntryName()));
        }
        return serviceNodeInfo.getId();
    }

    /**
     * 业务节点已申请列表
     */
    public SuccessResponse<List<ServiceNodeAppliedListVO>> serviceNodeApplyList(ServiceNodeApplyListReq serviceNodeApplyListReq) {
        List<ServiceNodeAppliedListVO> serviceNodeAppliedListVOS = new ArrayList<>();
        Predicate predicate = buildServiceNodeInfoPredicate(serviceNodeApplyListReq.getEntryName(), serviceNodeApplyListReq.getServiceNodeId(), serviceNodeApplyListReq.getProcessStatus());
        Sort sort = Sort.by(Sort.Direction.DESC, "createTime");
        Pageable pageable = PageRequest.of((int) serviceNodeApplyListReq.getNum() - 1, (int) serviceNodeApplyListReq.getSize(), sort);
        Page<ServiceNodeInfo> serviceNodeInfoPage = serviceNodeRepository.findAll(predicate, pageable);
        List<ServiceNodeInfo> serviceNodeInfoList = serviceNodeInfoPage.getContent();
        if (!ObjectUtils.isEmpty(serviceNodeInfoList)) {
            serviceNodeAppliedListVOS = serviceNodeInfoList.stream().map(serviceNodeMapper::serviceNodeInfoToServiceNodeAppliedListVO).collect(Collectors.toList());
        }
        return SuccessResponse.success(serviceNodeAppliedListVOS).total(serviceNodeInfoPage.getTotalElements())
                .page(com.dbapp.rest.request.Page.of(serviceNodeApplyListReq.getNum(), serviceNodeApplyListReq.getSize())).build();
    }

    private Predicate buildServiceNodeInfoPredicate(String entryName, String serviceNodeId, String status) {
        BooleanBuilder builder = new BooleanBuilder();
        if (entryName != null && !entryName.isEmpty()) {
            builder.and(qServiceNodeInfo.entryName.like("%" + entryName + "%"));
        }
        if (serviceNodeId != null && !serviceNodeId.isEmpty()) {
            builder.and(qServiceNodeInfo.serviceNodeId.eq(serviceNodeId));
        }
        if (status != null && !status.isEmpty()) {
            builder.and(qServiceNodeInfo.processStatus.eq(status));
        }
        return builder;
    }

    /**
     * 同步业务节点申请状态及信息
     */
    @Scheduled(initialDelay = 30, fixedDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void sync() {
        if (BUSY.get()) {
            return;
        }
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        List<Company> companyList = companyRepository.findAll();
        if (ObjectUtils.isEmpty(companyList)) {
            return;
        }
        for (Company company : companyList) {
            if (ObjectUtils.isEmpty(company) || ObjectUtils.isEmpty(company.getNodeId())) {
                return;
            }
            try {
                BUSY.set(true);
                log.info("service node sync ....");

                AsyncManager.getInstance().executeFuture(() -> {
                    TenantContext.setCurrentTenant("tenant_" + company.getId());
                    syncInfo(company.getNodeId());
                    return true;
                });

            } finally {
                BUSY.set(false);
            }
        }
    }

    private void syncInfo(String nodeId) {
        Date createTime = new Date();
        Pageable pageable = PageRequest.of(0, 50);
        while (true) {
            List<ServiceNodeInfo> serviceNodeInfoList = serviceNodeRepository.findAllByCreateTimeBeforeOrderByCreateTimeDesc(createTime, pageable);
            if (ObjectUtils.isEmpty(serviceNodeInfoList)) {
                return;
            }
            List<ServiceNodeInfo> notApplyServiceNodeList = new ArrayList<>(serviceNodeInfoList.size());
            // 同步业务节点状态及信息
            for (ServiceNodeInfo serviceNodeInfo : serviceNodeInfoList) {
                createTime = serviceNodeInfo.getCreateTime();
                if (ApprovalStatus.APPLY.name().equals(serviceNodeInfo.getProcessStatus())) {
                    try {
                        ServiceNodeMetaData metaData = new ServiceNodeMetaData();
                        metaData.setNodeId(nodeId);
                        metaData.setUrl(serviceNodeInfo.getApiUrl());
                        ShuhanResponse<ServiceNodeProcessStatusVO> response = serviceNodeRemote.serviceNodeProcessStatusQuery(metaData.toBase64());
                        if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getData()) && !ObjectUtils.isEmpty(response.getData().getApiUrl())) {
                            ServiceNodeProcessStatusVO serviceNodeProcessStatusVO = response.getData();
                            BeanUtils.copyProperties(serviceNodeProcessStatusVO, serviceNodeInfo, "processStatus", "processTime");
                            serviceNodeRepository.saveAndFlush(serviceNodeInfo);
                        } else {
                            notApplyServiceNodeList.add(serviceNodeInfo);
                            log.error("业务节点[{}]-[{}]-[{}]同步状态失败 ->>> 结果:{}", serviceNodeInfo.getServiceNodeId(), serviceNodeInfo.getEntryName(), serviceNodeInfo.getApiUrl(), response);
                        }
                    } catch (Exception e) {
                        log.error("业务节点[{}]-[{}]-[{}]同步状态失败：" + e, serviceNodeInfo.getServiceNodeId(), serviceNodeInfo.getEntryName(), serviceNodeInfo.getApiUrl());
                        notApplyServiceNodeList.add(serviceNodeInfo);
                    }
                } else {
                    notApplyServiceNodeList.add(serviceNodeInfo);
                }
            }

            // 同步业务节点信息
            if (!ObjectUtils.isEmpty(notApplyServiceNodeList)) {
                List<String> serviceNodeIds = notApplyServiceNodeList.stream().map(ServiceNodeInfo::getServiceNodeId).toList();
                try {
                    SuccessResponse<List<ServiceNodeListVO>> response = hubShuHanApiClient.serviceNodeList(null, serviceNodeIds, 1L, 9999L);
                    if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getData())) {
                        List<ServiceNodeListVO> serviceNodeListVOList = response.getData();
                        Map<String, ServiceNodeListVO> serviceNodeListVOMap = serviceNodeListVOList.stream().collect(Collectors.toMap(ServiceNodeListVO::getServiceNodeId, serviceNodeListVO -> serviceNodeListVO, (s1, s2) -> s2));
                        for (ServiceNodeInfo serviceNode : notApplyServiceNodeList) {
                            BeanUtils.copyProperties(serviceNodeListVOMap.get(serviceNode.getServiceNodeId()), serviceNode);
                        }
                        serviceNodeRepository.saveAllAndFlush(notApplyServiceNodeList);
                    } else {
                        log.error("业务节点[{}]同步信息失败 ->>> 结果:{}", serviceNodeIds, response);
                    }
                } catch (Exception e) {
                    log.error("业务节点[{}]同步信息失败：" + e, serviceNodeIds);
                }
            }
        }
    }
}
