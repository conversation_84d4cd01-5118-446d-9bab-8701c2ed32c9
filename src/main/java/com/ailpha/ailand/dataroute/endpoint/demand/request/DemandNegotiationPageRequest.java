package com.ailpha.ailand.dataroute.endpoint.demand.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DemandNegotiationPageRequest extends Page {
    @Schema(description = "查询类型", example = "collected：关注 negotiation：磋商 accept：成交 refuse：拒绝")
    String searchType;
    @Schema(description = "标题")
    String title;
    @Schema(description = "数据类型")
    String dataType;
    @Schema(description = "期望交付方式")
    String expectedDeliveryMethod;
    @Schema(hidden = true)
    String userId;
    @Schema(description = "需求ID")
    String demandId;
}
