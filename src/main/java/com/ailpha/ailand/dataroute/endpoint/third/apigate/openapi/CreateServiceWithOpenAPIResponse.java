package com.ailpha.ailand.dataroute.endpoint.third.apigate.openapi;

import com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateServiceResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateServiceWithOpenAPIResponse extends GatewayResponse<CreateServiceResponse.InvokeResult> {

}
