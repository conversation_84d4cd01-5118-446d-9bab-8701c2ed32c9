package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.NetworkLinkRequest;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "${ailand.agent.base-url}", path = "/route-agent")
public interface RouterAgentRemoteService {

    @POST("/activate")
    CommonResult<Void> startNetLink(@Body NetworkLinkRequest request);
}
