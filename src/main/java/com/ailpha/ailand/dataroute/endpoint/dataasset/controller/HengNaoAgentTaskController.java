package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl.HengNaoAgentTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AgentTaskPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.HengNaoAgentTaskDTO;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Tag(name = "数据探查任务管理", description = "数据探查任务的创建和查询接口")
@RestController
@RequestMapping("/api/data-explore-task")
@RequiredArgsConstructor
public class HengNaoAgentTaskController {
    private final HengNaoAgentTaskService agentTaskService;

    @Operation(summary = "创建数据探查任务", description = "上传文件并创建数据探查任务")
    @PostMapping("/createTask")
    public SuccessResponse<String> uploadTask(
            @Parameter(description = "调试数据文件", required = true)
            @RequestParam("debugDataFile") MultipartFile debugDataFile,
            @Parameter(description = "数据来源pdf", required = false)
            @RequestParam(value = "dataSourcePdfFile", required = false) MultipartFile dataSourcePdfFile,
            @Parameter(description = "任务类型 数据资源登记-DATA_RESOURCE_EXPLORE 数据产品登记-DATA_PRODUCT_EXPLORE", required = true)
            @RequestParam("taskType") String taskType) {
        // TODO 消息通知
        return SuccessResponse.success(agentTaskService.createDataExploreTask(debugDataFile, dataSourcePdfFile, taskType)).build();
    }

    @Operation(summary = "分页查询数据探查任务", description = "根据条件分页查询数据探查任务列表")
    @PostMapping("/page")
    public SuccessResponse<List<HengNaoAgentTaskDTO>> listTasks(
            @Parameter(description = "分页查询参数", required = true)
            @RequestBody AgentTaskPageRequest request) {
        return agentTaskService.listTasks(request);
    }

    @Operation(summary = "删除数据探查任务", description = "根据任务ID删除指定的数据探查任务")
    @DeleteMapping("/{taskId}")
    public SuccessResponse<Void> deleteTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {
        agentTaskService.deleteTask(taskId);
        return SuccessResponse.success(null).build();
    }
}
