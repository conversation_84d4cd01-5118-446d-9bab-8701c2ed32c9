package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import lombok.Data;

/**
 * @author: yuwenping
 * @date: 2025/2/28 13:49
 * @Description:
 */
@Data
public class RouterServiceStatusInfo {

    public static final String WELL = "良好";

    public static final String BAD = "异常";

    /**
     * 网络连接状态
     */
    private String netStatus = WELL;

    /**
     * cpu 使用百分比
     */
    private String cpuUsage;

    /**
     * 内存 使用百分比
     */
    private String memoryUsage;

    /**
     * 连接器激活时间
     */
    private String routerActiveTime;
}
