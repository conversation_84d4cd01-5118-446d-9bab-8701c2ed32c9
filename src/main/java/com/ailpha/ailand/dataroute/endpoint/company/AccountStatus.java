package com.ailpha.ailand.dataroute.endpoint.company;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public enum AccountStatus {
    register_pass,
    expired,
    disable,
    ;

    public static AccountStatus getStatus(User user) {
        if (ObjectUtil.isNull(user))
            return null;
        if (user.getDeleted() || !user.getEnabled())
            return disable;
        Date expireDate = user.getExt().getExpireDate();
        if (ObjectUtil.isNotNull(expireDate) && expireDate.before(DateUtil.date())) {
            return expired;
        }
        return register_pass;
    }
}
