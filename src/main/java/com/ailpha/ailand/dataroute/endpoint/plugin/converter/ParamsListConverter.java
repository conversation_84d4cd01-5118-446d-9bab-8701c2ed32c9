package com.ailpha.ailand.dataroute.endpoint.plugin.converter;

import com.ailpha.ailand.biz.api.dataset.BlockchainParamsBO;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

@Converter(autoApply = true)
public class ParamsListConverter implements AttributeConverter<List<BlockchainParamsBO>, String> {
    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public String convertToDatabaseColumn(List<BlockchainParamsBO> attribute) {
        try {
            return mapper.writeValueAsString(attribute);
        } catch (Exception e) {
            throw new IllegalArgumentException("Error converting params list to JSON", e);
        }
    }

    @Override
    public List<BlockchainParamsBO> convertToEntityAttribute(String dbData) {
        try {
            return mapper.readValue(dbData, new TypeReference<List<BlockchainParamsBO>>() {
            });
        } catch (Exception e) {
            throw new IllegalArgumentException("Error converting JSON to params list", e);
        }
    }
}