package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CompanyApplyDTO {
    @Schema(description = "接入主体信息", example = "")
    RegisterConnectorToHubRequest registerConnectorInfo;
    @NotNull(message = "接入主体类型不能为空")
    @Schema(description = "接入主体类型", example = "1")
    private AccessType accessType;

    @Schema(description = "接入主体名称", example = "测试公司")
    private String organizationName;

    @Schema(description = "接入主体代码", example = "********90")
    private String creditCode;

    // 法人相关信息
    @Schema(description = "法人名称", example = "张三")
    private String legalRepresentativeName;
    @Schema(description = "法人联系方式", example = "张三")
    String partyContactInfo;
    @Schema(description = "法人证件类型", example = "1")
    private String legalRepresentativeIdType;
    @Schema(description = "法人证件号码", example = "********90********")
    private String legalRepresentativeIdNumber;
    @Schema(description = "法人证件有效期 开始", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityStartDate;
    @Schema(description = "法人证件有效期 结束", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityEndDate;
    @Schema(description = "法定代表人实名认证等级")
    private String legalRepresentativeAuthLevel;
    @Schema(description = "实名认证方式")
    private String authType;
    @Schema(description = "认证时间")
    String authTime;
    @Schema(description = "法人注册地址")
    private String registeredAddress;
    @Schema(description = "行业类型")
    private String industryType;
    @Schema(description = "经营期限起始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessStartDate;
    @Schema(description = "经营期限截止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessEndDate;
    // 注册日期
    @Schema(description = "注册日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date registrationDate;
    // 注册资本
    @Schema(description = "注册资本", example = "100000000")
    String registeredCapital;
    // 经营范围
    @Schema(description = "经营范围", example = "软件开发")
    String businessScope;

    @Schema(description = "营业执照", example = "xxx/xxxx")
    String businessLicenseLocalUrl;
    String businessLicenseRemoteUrl;
    @Schema(description = "授权书", example = "xxx/xxxx")
    String authorizationLetterLocalUrl;

    String authorizationLetterRemoteUrl;
    // 身份状态
    @Schema(description = "身份状态", example = "0")
    String identityStatus;
    
    @Schema(description = "开户行", example = "中国银行")
    private String bankName;
    
    @Schema(description = "银行账号", example = "6225********9012")
    private String bankAccount;
    
    @Schema(description = "传真", example = "010-********")
    private String fax;
    
    @Schema(description = "邮编", example = "100000")
    private String postalCode;
    
    @Schema(description = "银行地址", example = "北京市朝阳区XX路XX号")
    private String bankAddress;
    
    @Schema(description = "户名", example = "XX有限公司")
    private String accountName;
}