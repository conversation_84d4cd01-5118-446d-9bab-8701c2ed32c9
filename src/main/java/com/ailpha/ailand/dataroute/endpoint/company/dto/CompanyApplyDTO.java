package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CompanyApplyDTO {
    @Schema(description = "接入主体信息", example = "")
    RegisterConnectorToHubRequest registerConnectorInfo;
    @NotNull(message = "接入主体类型不能为空")
    @Schema(description = "接入主体类型", example = "1")
    private AccessType accessType;

    @Schema(description = "接入主体名称", example = "测试公司")
    private String organizationName;

    @Schema(description = "接入主体代码", example = "1234567890")
    private String creditCode;

    // 法人相关信息
    @Schema(description = "法人名称", example = "张三")
    private String legalRepresentativeName;
    @Schema(description = "法人证件类型", example = "1")
    private String legalRepresentativeIdType;
    @Schema(description = "法人证件号码", example = "123456789012345678")
    private String legalRepresentativeIdNumber;
    @Schema(description = "法人证件有效期 开始", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityStartDate;
    @Schema(description = "法人证件有效期 结束", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityEndDate;
    @Schema(description = "法定代表人实名认证等级")
    private String legalRepresentativeAuthLevel;
    @Schema(description = "实名认证方式")
    private String authType;
    @Schema(description = "认证时间")
    String authTime;
    @Schema(description = "法人注册地址")
    private String registeredAddress;
    @Schema(description = "行业类型")
    private String industryType;
    @Schema(description = "经营期限起始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessStartDate;
    @Schema(description = "经营期限截止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessEndDate;
    // 注册日期
    @Schema(description = "注册日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date registrationDate;
    // 注册资本
    @Schema(description = "注册资本", example = "100000000")
    String registeredCapital;
    // 经营范围
    @Schema(description = "经营范围", example = "软件开发")
    String businessScope;

    // 经办人相关信息
    @Schema(description = "经办人姓名", example = "李四")
    private String delegateName;
    @Schema(description = "经办人证件类型", example = "ID_CARD")
    private String delegateIdType;
    @Schema(description = "经办人证件号码", example = "110101199001011234")
    private String delegateIdNumber;
    @Schema(description = "经办人证件有效期-开始日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityStartDate;
    @Schema(description = "经办人证件有效期-结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityEndDate;
    @Schema(description = "经办人联系方式", example = "***********")
    private String delegatePhone;
    @Schema(description = "经办人电子邮箱", example = "<EMAIL>")
    private String delegateEmail;
    @Schema(description = "经办人实名认证等级", example = "LEVEL_2")
    private String delegateAuthLevel;
    @Schema(description = "经办人实名认证方式", example = "FACE")
    private String delegateAuthType;
    @Schema(description = "经办人注册地址", example = "北京市朝阳区XX路XX号")
    private String delegateAddress;
    @Schema(description = "经办人行业类型", example = "软件和信息技术服务业")
    private String delegateIndustryType;
    @Schema(description = "委办任务范围", example = "负责公司业务对接及相关手续办理")
    private String delegateTaskScope;
    @Schema(description = "委办授权期限起始", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthStartDate;
    @Schema(description = "委办授权期限截止", example = "2024-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthEndDate;
    @Schema(description = "经办人备注", example = "特别授权事项说明")
    private String delegateRemark;
    @Schema(description = "营业执照", example = "xxx/xxxx")
    String businessLicenseLocalUrl;
    String businessLicenseRemoteUrl;
    @Schema(description = "授权书", example = "xxx/xxxx")
    String authorizationLetterLocalUrl;

    String authorizationLetterRemoteUrl;
    // 身份状态
    @Schema(description = "身份状态", example = "0")
    String identityStatus;
}