package com.ailpha.ailand.dataroute.endpoint.dataasset.enums;

import lombok.Getter;

@Getter
public enum PushStatus {
    // 0 默认状态 1 同步中 2 待审批 3 通过 4 拒绝
    ONLINE("pushstatus_pushed", "已发布"),
    OFFLINE("pushstatus_not_push", "未发布");

    private final String code;
    private final String desc;

    PushStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PushStatus getByCode(String code) {
        for (PushStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return OFFLINE;
    }

    public static PushStatus fromName(String name) {
        for (PushStatus pushStatus : values()) {
            if (pushStatus.name().equalsIgnoreCase(name)) {
                return pushStatus;
            }
        }
        return null;
    }
}
