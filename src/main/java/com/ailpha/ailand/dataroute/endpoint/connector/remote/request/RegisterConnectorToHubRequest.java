package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RegisterConnectorToHubRequest {
    @Schema(description = "基本信息")
    private BaseInfo baseInfo;
    
    @Schema(description = "身份附加信息")
    private ExtendInfo extendInfo;

    @Data
    public static class BaseInfo {
        String clientNo;
        String certificateNo;
        @Schema(description = "标识ID")
        String identifyID;
        
        @Schema(description = "接入连接器名称", required = true)
        String connectorName;
        
        @Schema(description = "IP 地址列表，以数组方式提交")
        List<String> connectorIpList;
        
        @Schema(description = "域名列表，以数组方式提交")
        List<String> connectorDomainList;
        
        @Schema(description = "连接类型", required = true, example = "1-专线, 2-互联网(固定公网IP), 3-互联网(无固定公网IP), 4-高速数据网, 5-其他")
        String connectorJoinType;
        
        @Schema(description = "所属法人或其他组织名称")
        String enterpriseName;
        
        @Schema(description = "所属法人或其他组织统一社会信用代码", required = true)
        String enterpriseCode;
        
        @Schema(description = "状态", required = true)
        String identityStatus;
        
        @Schema(description = "颁发日期", required = true)
        String authTime;
    }
    
    @Data
    public static class ExtendInfo {
        @Schema(description = "供应商名称")
        private String supplierName;
        
        @Schema(description = "供应商统一社会信用代码")
        private String supplierCode;
        
        @Schema(description = "产品SN号")
        private String connectorSN;
        
        @Schema(description = "产品版本号")
        private String connectorVersion;
        
        @Schema(description = "连接器类型", required = true, example = "1-标准型接入连接器, 2-全功能型接入连接器")
        private String connectorType;
        
        @Schema(description = "物理设备唯一标识符（若有多台，只登记管理服务器mac地址）", required = true)
        private String connectorMac;
    }
}
