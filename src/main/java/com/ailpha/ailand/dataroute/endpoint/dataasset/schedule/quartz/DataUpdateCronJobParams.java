package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import lombok.Data;

@Data
public class DataUpdateCronJobParams {
    private DataUpdateTask dataUpdateTask;

//    private DataProduct dataProduct;

    private DataAssetPrepareSchedule dataAssetPrepareSchedule;

    private Long companyId;
}
