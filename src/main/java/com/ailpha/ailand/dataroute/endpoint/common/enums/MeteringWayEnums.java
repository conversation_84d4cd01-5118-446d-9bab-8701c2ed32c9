package com.ailpha.ailand.dataroute.endpoint.common.enums;

/**
 * @author: sunsas.yu
 * @date: 2024/11/18 14:55
 * @Description:
 */
public enum MeteringWayEnums {
    PER_OCCURRENCE("按次"),

    PER_TIME("按时间");

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private String name;

    MeteringWayEnums(String name){
        this.name = name;
    }



}
