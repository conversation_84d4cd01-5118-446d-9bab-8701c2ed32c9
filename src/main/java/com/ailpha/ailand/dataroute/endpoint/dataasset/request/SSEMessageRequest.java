package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.common.enums.TraderRoleEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 13:44
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SSEMessageRequest {
    /**
     * 消息id
     */
    private Long id;

    private String message;

    private SSEMessageTypeEnum type;

    private TraderRoleEnums traderRoleType;

    private String userId;

    private String dataId;
    String ext;
}
