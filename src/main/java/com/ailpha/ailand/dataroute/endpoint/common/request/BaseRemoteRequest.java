package com.ailpha.ailand.dataroute.endpoint.common.request;

import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import lombok.Data;

/**
 * 远程调用请求基类
 * 包含公共字段 nodeId 和 hubInfo
 */
@Data
public class BaseRemoteRequest {
    /**
     * 目标连接器ID，作用：跨节点查询数据详情 判断是否为当前连接器
     */
    private String targetNodeId;

    /**
     * 目标连接器企业ID
     */
    private Long targetCompanyId;

    /**
     * 枢纽信息
     */
    private NodeDTO.HubInfo hubInfo;

    String localCompanyId;

    public NodeDTO.HubInfo getHubInfo() {
        // 超管没有主体信息，需要提前赋值
        if (hubInfo != null) {
            return hubInfo;
        }
        if (LoginContextHolder.isLogin()) {
            hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo(LoginContextHolder.currentUser().getCompany().getId());
        } else
            hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo();
        return hubInfo;

    }
}