package com.ailpha.ailand.dataroute.endpoint.statistics;

import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderListVO;
import com.ailpha.ailand.dataroute.endpoint.third.request.OrderRecordsReq;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("statistic")
@Tag(name = "大屏监控")
@PreAuthorize("hasAuthority('COMPANY_ADMIN')")
public class StatisticsController {

    private final StatisticsService statisticsService;
    private final OrderManagerService orderManagerService;
    private final RouterService routerService;

    @GetMapping("/order-list")
    @Operation(summary = "实时订单")
    public SuccessResponse<List<OrderListVO>> orderList() {
        OrderRecordsReq orderRecordsReq = OrderRecordsReq.builder().routerId(LoginContextHolder.currentUser().getCompany().getNodeId())
                .notStatus(Arrays.asList(OrderStatus.APPLY.name(), OrderStatus.REJECTED.name())).page(1L).offset(0L).size(20L).build();
        return orderManagerService.orderVOList(orderRecordsReq);
    }

    @GetMapping
    @Operation(summary = "大屏")
    public SuccessResponse<StatisticsResponse> statistic() {
        return SuccessResponse.success(statisticsService.statistics()).build();
    }
}
