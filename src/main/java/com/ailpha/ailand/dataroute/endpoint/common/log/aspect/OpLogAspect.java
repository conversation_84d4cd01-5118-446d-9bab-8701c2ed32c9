package com.ailpha.ailand.dataroute.endpoint.common.log.aspect;

import com.ailpha.ailand.dataroute.endpoint.common.log.OpLogJoinPointProcessor;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Aspect
@Component
@RequiredArgsConstructor
@Order(2)
public class OpLogAspect {
    private final OpLogJoinPointProcessor joinPointProcessor;
    @Pointcut("@annotation(com.ailpha.ailand.dataroute.endpoint.common.log.OpLog)")
    public void controllerAllMethod() {

    }

    @Around("controllerAllMethod()")
    public Object process(ProceedingJoinPoint jp) throws Throwable {
        return joinPointProcessor.process(jp);
    }

}
