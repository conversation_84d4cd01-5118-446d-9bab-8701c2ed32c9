package com.ailpha.ailand.dataroute.endpoint.license;

/**
 * <AUTHOR>
 * @date 2022/8/29 15:24
 * @description
 */
public enum UploadLicenseFlag {
    NEW_LICENSE,
    OLD_LICENSE,
    RENEWAL_LICENSE;

    public static UploadLicenseFlag getFlag(String flag) {
        if ("1".equals(flag)) {
            return NEW_LICENSE;
        } else if ("2".equals(flag)) {
            return OLD_LICENSE;
        }
        return RENEWAL_LICENSE;
    }
}
