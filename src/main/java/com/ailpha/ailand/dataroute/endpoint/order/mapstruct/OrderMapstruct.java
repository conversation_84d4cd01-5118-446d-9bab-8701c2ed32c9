package com.ailpha.ailand.dataroute.endpoint.order.mapstruct;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRecord;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRecordVO;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderRecordDTO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderListVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderCreateReq;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.*;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Slf4j
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class OrderMapstruct {

    @Mapping(target = "orderId", source = "id")
    public abstract OrderRecordDTO OrderApprovalRecordToOrderRecordDTO(OrderApprovalRecord orderApprovalRecord);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "assetId", source = "assetId")
    @Mapping(target = "assetName", ignore = true)
    @Mapping(target = "deliveryMode", ignore = true)
    @Mapping(target = "beneficiaryId", ignore = true)
    @Mapping(target = "beneficiaryUsername", ignore = true)
    @Mapping(target = "beneficiaryRouterId", ignore = true)
    @Mapping(target = "beneficiaryEnterpriseName", ignore = true)
    @Mapping(target = "beneficiaryEnterpriseProperty", ignore = true)
    @Mapping(target = "approverId", ignore = true)
    @Mapping(target = "approverUsername", ignore = true)
    @Mapping(target = "approverRouterId", ignore = true)
    @Mapping(target = "approverEnterpriseName", ignore = true)
    @Mapping(target = "chargingWay", source = "orderCreateReq.chargingWay")
    @Mapping(target = "meteringWay", source = "orderCreateReq.meteringWay")
    @Mapping(target = "allowance", source = "orderCreateReq.allowance")
    @Mapping(target = "successfulUsage", ignore = true)
    @Mapping(target = "unsuccessfulUsage", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "extend", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "expireDate", source = "orderCreateReq.expireDate")
    public abstract OrderApprovalRecord initOrderApprovalRecord(OrderCreateReq orderCreateReq, String id, String assetId, UserDTO currentUser, NodeInfoResponse currentNode, Map<String, DataProductVO> dataAssetMap);

    @AfterMapping
    public void fillOtherInfo(OrderCreateReq orderCreateReq, String id, String assetId, UserDTO currentUser, NodeInfoResponse currentNode, Map<String, DataProductVO> dataAssetMap, @MappingTarget OrderApprovalRecord.OrderApprovalRecordBuilder orderApprovalRecordBuilder) {
        CompanyDTO currentEnterprise = LoginContextHolder.currentUser().getCompany();
        DataProductVO dataAsset = ObjectUtils.isEmpty(dataAssetMap.get(assetId)) ? new DataProductVO() : dataAssetMap.get(assetId);
        orderApprovalRecordBuilder
                .type(orderCreateReq.getType())
                .assetName(dataAsset.getDataProductName())
                .deliveryMode(dataAsset.getDeliveryModes().stream().map(Enum::name).collect(Collectors.joining(",")))
                .beneficiaryId(currentUser.getId())
                .beneficiaryUsername(currentUser.getUsername())
                .beneficiaryRouterId(currentEnterprise.getNodeId())
                .beneficiaryEnterpriseName(currentEnterprise.getOrganizationName())
                .beneficiaryEnterpriseProperty(currentEnterprise.getIndustryType())
                .approverId(dataAsset.getUserId())
                .approverRouterId(dataAsset.getProvider().getRouterId())
                .approverUsername(dataAsset.getProvider().getUsername())
                .approverEnterpriseName(dataAsset.getProvider().getCompany().getOrganizationName())
                .successfulUsage(BigInteger.valueOf(0)).unsuccessfulUsage(BigInteger.valueOf(0))
                .status(OrderStatus.APPLY.toString());
        OderRecordExtend oderRecordExtend = !ObjectUtils.isEmpty(orderCreateReq.getOrderConfig()) ? orderCreateReq.getOrderConfig() : new OderRecordExtend();
        oderRecordExtend.setBeneficiaryCreditCode(currentEnterprise.getCreditCode());
        oderRecordExtend.setApproverCreditCode(dataAsset.getProvider().getCompany().getCreditCode());
        oderRecordExtend.setBeneficiaryCompanyId(String.valueOf(currentEnterprise.getId()));
        oderRecordExtend.setApproverCompanyId(String.valueOf(dataAsset.getProvider().getCompany().getId()));
        orderApprovalRecordBuilder.extend(JacksonUtils.obj2json(oderRecordExtend));
    }

    @Mapping(target = "orderId", source = "orderApprovalRecord.id")
    @Mapping(target = "assetId", source = "orderApprovalRecord.assetId")
    @Mapping(target = "assetName", source = "orderApprovalRecord.assetName")
    @Mapping(target = "deliveryMode", source = "orderApprovalRecord.deliveryMode")
    @Mapping(target = "beneficiaryId", source = "orderApprovalRecord.beneficiaryId")
    @Mapping(target = "beneficiaryUsername", source = "orderApprovalRecord.beneficiaryUsername")
    @Mapping(target = "beneficiaryEnterpriseName", source = "orderApprovalRecord.beneficiaryEnterpriseName")
    @Mapping(target = "beneficiaryEnterpriseProperty", source = "orderApprovalRecord.beneficiaryEnterpriseProperty")
    @Mapping(target = "approverId", source = "orderApprovalRecord.approverId")
    @Mapping(target = "approverUsername", source = "orderApprovalRecord.approverUsername")
    @Mapping(target = "approverRouterId", source = "orderApprovalRecord.approverRouterId")
    @Mapping(target = "approverEnterpriseName", source = "orderApprovalRecord.approverEnterpriseName")
    @Mapping(target = "chargingWay", source = "orderApprovalRecord.chargingWay")
    @Mapping(target = "meteringWay", source = "orderApprovalRecord.meteringWay")
    @Mapping(target = "allowance", source = "orderApprovalRecord.allowance")
    @Mapping(target = "successfulUsage", source = "orderApprovalRecord.successfulUsage")
    @Mapping(target = "unsuccessfulUsage", source = "orderApprovalRecord.unsuccessfulUsage")
    @Mapping(target = "status", source = "orderApprovalRecord.status")
    @Mapping(target = "createTime", source = "orderApprovalRecord.createTime")
    @Mapping(target = "updateTime", source = "orderApprovalRecord.updateTime")
    @Mapping(target = "expireDate", source = "orderApprovalRecord.expireDate")
    @Mapping(target = "approveTime", source = "orderApprovalRecord.approveTime")
    @Mapping(target = "type", source = "orderApprovalRecord.type")
    @Mapping(target = "beneficiaryExtend", ignore = true)
    @Mapping(target = "orderConfig", ignore = true)
    public abstract OrderVO toOrderVO(OrderApprovalRecord orderApprovalRecord, String beneficiaryExtend, DataProductVO assetInfo);

    @AfterMapping
    public void fillOtherInfo(OrderApprovalRecord orderApprovalRecord, String beneficiaryExtend, DataProductVO assetInfo, @MappingTarget OrderVO.OrderVOBuilder orderVOBuilder) {
        String extend = orderApprovalRecord.getExtend();
        orderVOBuilder.beneficiaryExtend(!ObjectUtils.isEmpty(beneficiaryExtend) ? JacksonUtils.json2pojo(beneficiaryExtend, AssetBeneficiaryExtend.class) : new AssetBeneficiaryExtend())
                .orderConfig(!ObjectUtils.isEmpty(extend) ? JacksonUtils.json2pojo(extend, OderRecordExtend.class) : new OderRecordExtend());
    }

    @Mapping(target = "extend", ignore = true)
    public abstract StatisticDeliveryRecordVO toStatisticDeliveryRecordVO(StatisticDeliveryRecord statisticDeliveryRecord);

    @AfterMapping
    public void fillOtherInfo(StatisticDeliveryRecord statisticDeliveryRecord, @MappingTarget StatisticDeliveryRecordVO.StatisticDeliveryRecordVOBuilder statisticDeliveryRecordVOBuilder) {
        String extend = statisticDeliveryRecord.getExtend();
        statisticDeliveryRecordVOBuilder.extend(!ObjectUtils.isEmpty(extend) ? JacksonUtils.json2pojo(extend, StatisticDeliveryRecordExtend.class) : new StatisticDeliveryRecordExtend());
    }

    @Mapping(target = "orderId", source = "orderRecordDTO.orderId")
    @Mapping(target = "assetId", source = "orderRecordDTO.assetId")
    @Mapping(target = "assetName", source = "orderRecordDTO.assetName")
    @Mapping(target = "beneficiaryId", source = "orderRecordDTO.beneficiaryId")
    @Mapping(target = "beneficiaryUsername", source = "orderRecordDTO.beneficiaryUsername")
    @Mapping(target = "beneficiaryEnterpriseName", source = "orderRecordDTO.beneficiaryEnterpriseName")
    @Mapping(target = "beneficiaryEnterpriseProperty", source = "orderRecordDTO.beneficiaryEnterpriseProperty")
    @Mapping(target = "approverId", source = "orderRecordDTO.approverId")
    @Mapping(target = "approverUsername", source = "orderRecordDTO.approverUsername")
    @Mapping(target = "approverRouterId", source = "orderRecordDTO.approverRouterId")
    @Mapping(target = "approverEnterpriseName", source = "orderRecordDTO.approverEnterpriseName")
    @Mapping(target = "chargingWay", source = "orderRecordDTO.chargingWay")
    @Mapping(target = "meteringWay", source = "orderRecordDTO.meteringWay")
    @Mapping(target = "allowance", source = "orderRecordDTO.allowance")
    @Mapping(target = "successfulUsage", source = "orderRecordDTO.successfulUsage")
    @Mapping(target = "unsuccessfulUsage", source = "orderRecordDTO.unsuccessfulUsage")
    @Mapping(target = "status", source = "orderRecordDTO.status")
    @Mapping(target = "orderConfig", ignore = true)
    @Mapping(target = "createTime", source = "orderRecordDTO.createTime")
    @Mapping(target = "type", source = "orderRecordDTO.type")
    public abstract OrderListVO toOrderListVO(OrderRecordDTO orderRecordDTO, DataProductVO dataAssetVO);

    @AfterMapping
    public void fillOtherInfo(OrderRecordDTO orderRecordDTO, DataProductVO dataAssetVO, @MappingTarget OrderListVO.OrderListVOBuilder orderListVOBuilder) {
        String extend = orderRecordDTO.getExtend();
        orderListVOBuilder.orderConfig(!ObjectUtils.isEmpty(extend) ? JacksonUtils.json2pojo(extend, OderRecordExtend.class) : new OderRecordExtend());

        orderListVOBuilder.assetInfo(dataAssetVO);
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "orderId", source = "orderApprovalRecord.id")
    @Mapping(target = "extend", ignore = true)
    public abstract AssetBeneficiaryRel initAssetBeneficiaryRel(OrderApprovalRecord orderApprovalRecord, AssetBeneficiaryExtend assetBeneficiaryExtend);

    @AfterMapping
    public void fillOtherInfo(OrderApprovalRecord orderApprovalRecord, AssetBeneficiaryExtend assetBeneficiaryExtend, @MappingTarget AssetBeneficiaryRel.AssetBeneficiaryRelBuilder assetBeneficiaryRelBuilder) {
        assetBeneficiaryRelBuilder.extend(JacksonUtils.obj2json(assetBeneficiaryExtend));
    }
}
