package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

/**
 * 交易登记
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TransactionRegisterRequest implements Serializable {

    @Schema(description = "第三方平台业务ID  —— 【交付场景ID+资产ID】")
    String thirdBusinessId;
    @Schema(description = "第三方平台扩展信息json")
    String extend;
    @Schema(description = "交易名称")
    String name;
    @Schema(description = "场景证书ID   —— 【数字证书 —— 合规场景id】")
    String sceneId;
    @Schema(description = "交易金额")
    BigDecimal transactionAmount;
    @Schema(description = "第三方平台卖家企业ID  —— 【卖方注册连接器企业ID】")
    String thirdSellerEnterpriseId;
    @Schema(description = "第三方平台买家企业ID  —— 【买方注册连接器企业ID】")
    String thirdBuyerEnterpriseId;
    @Schema(description = "交付方式", allowableValues = "数据文件,API,平台", example = "API")
    String deliveryMode;
    @Schema(description = "获取交易物方式", allowableValues = "交易获得,自主生成", example = "交易获得")
    String acquireBarterMode;
    @Schema(description = "交易权限（多选英文逗号分隔）", allowableValues = "数据资源持有权,数据加工使用权,数据产品经营权", example = "数据加工使用权,数据产品经营权")
    String transactionAuthority;
    @Schema(description = "第三方平台委托交付方企业ID")
    String thirdConsignorEnterpriseId;
    @Schema(description = "交易标的物计量单位（1:年、2:月、3:周、4:天、5:量）", example = "5")
    String measurementUnit;
    @Schema(description = "使用量（计量单位按量时单位为次数）", example = "66")
    BigInteger allowance;
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @Schema(description = "失效时间")
    Date expireDate;
}
