package com.ailpha.ailand.dataroute.endpoint.user.service;

import com.ailpha.ailand.dataroute.endpoint.common.utils.CaptchaUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.RandImageUtils;
import com.ailpha.ailand.dataroute.endpoint.user.contants.CacheCode;
import com.ailpha.ailand.dataroute.endpoint.user.vo.CaptchaVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/2/10
 * @description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaService {

    private final CacheManager cacheManager;

    private String cacheCode(String cacheCodeId) {
        CacheCode cacheCode = cacheManager.getCache("captcha_cache").get(cacheCodeId, CacheCode.class);
        if (cacheCode != null) {
            cacheManager.getCache("captcha_cache").evictIfPresent(cacheCodeId);
            boolean expired = checkExpired(cacheCode);
            if (expired) {
                throw new BadCredentialsException("验证码已失效");
            }
            return cacheCode.getCode();
        }
        return null;
    }

    private boolean checkExpired(CacheCode cacheCode) {
        return (System.currentTimeMillis() - cacheCode.getCreateTime()) / 1000 / 60 > cacheCode.getExpirInMinutes();
    }

    public String getCaptcha(String captchaId) {
        return cacheCode(captchaId);
    }

    public String getMailCode(String mail) {
        return cacheCode(mail);
    }

    @Value("${ailand.mail.captcha.validate-time-minutes:5}")
    Long captchaExpireInMinutes;

    public CaptchaVO generateCaptcha() throws IOException, InterruptedException {
        CacheCode cacheCode = CaptchaUtils.generateCaptcha(TimeUnit.MINUTES.toMillis(captchaExpireInMinutes));
        // 放入缓存
        cacheManager.getCache("captcha_cache").put(cacheCode.getCodeId(), cacheCode);

        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaId(cacheCode.getCodeId());
        captchaVO.setCaptchaBASE64(cacheCode.getCode());
        return captchaVO;
    }

    @Value("${ailand.mail.captcha.validate-time-minutes:5}")
    Long mailCodeExpireInMinutes;

    public String generateMailCode(String mail) throws IOException {
        CacheCode cacheCode = CaptchaUtils.generateMailCode(mail, mailCodeExpireInMinutes);
        // 放入缓存
        cacheManager.getCache("captcha_cache").put(cacheCode.getCodeId(), cacheCode);

        return cacheCode.getCode();
    }
}
