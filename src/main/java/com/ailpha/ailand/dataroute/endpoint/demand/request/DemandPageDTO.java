package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DemandPageDTO {
    @Schema(description = "ID")
    Integer id;

    /**
     * 序列号
     */
    @Schema(description = "序列号")
    String serialNumber;

    /**
     * 标题
     */
    @Schema(description = "标题")
    String title;

    /**
     * 描述
     */
    @Schema(description = "描述")
    String description;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期")
    String expireDate;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    String dataType;

    /**
     * 数据规模
     */
    @Schema(description = "数据规模")
    String dataScale;

    /**
     * 质量要求
     */
    @Schema(description = "质量要求")
    String qualityRequirements;

    /**
     * 预算范围
     */
    @Schema(description = "预算范围")
    String budgetRange;

    /**
     * 预期交付方式
     */
    @Schema(description = "预期交付方式")
    List<String> expectedDeliveryMethod;

    /**
     * 法律条款文件ID
     */
    @Schema(description = "法律条款文件ID")
    String legalTermsFileId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    String status;

    @Schema(description = "标签")
    List<String> tags;
}
