package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ThirdSceneInfoPageReq extends Page {

    @Schema(description = "场景名称")
    private String sceneName;

    @Schema(description = "地域信息")
    private SceneRegionInputReq region;

    @Schema(description = "场景类别：工业、电信、交通、金融、自然资源、卫生健康、教育、科技、其他")
    private String type;

    private int page = 1;

}
