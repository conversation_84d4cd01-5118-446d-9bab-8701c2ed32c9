package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import cn.hutool.core.annotation.Alias;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("数据资源登记分析结果")
public class DataResourceExploreResult {
    @Schema(description = "资源名称")
    @Alias("resourceNameCN")
    String dataResourceName;
    @Schema(description = "资源英文名称")
    @Alias("resourceNameEN")
    String dataResourceNameCN;
    @Schema(description = "资源格式")
    String resourceFormat;
    String industry1;
    @Alias("resourceAbstract")
    String description;
    @Schema(description = "行业分类编码")
    List<String> industryCode;
    @Schema(description = "行业分类中文")
    String industryName;

    @Schema(description = "其他")
    String others;
    @Schema(description = "数据类型")
    String dataType;
    @Schema(description = "数据类型")
    String dataType1;
    @Schema(description = "是否涉及个人信息")
    String personalInformation;
    @Alias("dataSource")
    String source;
    List<DataSchema> dataSchema;

    @Data
    public static class DataSchema {
        @Schema(description = "中文字段名")
        String name;

        /**
         * 字段英文名
         */
        @Schema(description = "英文字段名")
        String fieldName;

        /**
         * 字段类型
         */
        @Schema(description = "字段类型")
        @Alias("dataType")
        String type;
        /**
         * 字段注释
         */
        @Schema(description = "字段注释")
        @Alias("description")
        String comment;
    }
}
