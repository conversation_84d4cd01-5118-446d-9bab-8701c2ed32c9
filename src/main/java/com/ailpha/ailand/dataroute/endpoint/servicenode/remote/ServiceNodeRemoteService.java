package com.ailpha.ailand.dataroute.endpoint.servicenode.remote;

import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.remote.GetEnterpriseInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.EnterpriseInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.GetNonceResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.TokenResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.LoginRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.LoginResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.ContractTemplateDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.IPageDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderConfigDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.TradingStrategyDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.BusinessNodeOrderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderConfigVO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.controller.JumpToOrderRequest;
import com.ailpha.ailand.dataroute.endpoint.servicenode.controller.JumpToOrderResponse;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ApplyServiceNodeResp;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeProcessStatusVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductPublishVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductUnPublishVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetUpdateVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataProductPublishVO;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.AddUserResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = ServiceNodeInterceptor.class)
public interface ServiceNodeRemoteService {

    @POST("/ServiceIdentityVerify")
    CommonResult<GetNonceResponse> getNonce(@Body GenerateUuidRequest request, @Header("metaData") String metaData);

    @POST("/ServiceIdentityVerifyNonce")
    CommonResult<TokenResponse> getToken(@Body GenerateTokenRequest request, @Header("metaData") String metaData);

    @POST("/connector/sso/jumpToOrder")
    CommonResult<JumpToOrderResponse> jumpToOrder(@Body JumpToOrderRequest request);

    @POST("/data-valley-gateway/data-route-business-service/api/openapi/dataProductPublish")
    ShuhanResponse<DataProductPublishVO> dataProductPublish(@Body DataProductPublishVM publishRequest, @Header("metaData") String metaData);

    @POST("/dataProductUnPublish")
    ShuhanResponse<DataAssetUpdateVO> dataProductUnPublish(@Body DataProductUnPublishVM unPublishRequest, @Header("metaData") String metaData);

    @POST("/gateway/data-route-business-service/api/openapi/tradingStrategyGrant")
    ShuhanResponse<IPageDTO<BusinessNodeOrderDTO>> tradingStrategyGrant(@Body CatalogQueryVM catalogQueryVM, @Header("nodeId") String nodeId);

    @POST("/gateway/data-route-business-service/api/openapi/deliveryStatusReport")
    ShuhanResponse<Boolean> deliveryStatusReport(@Body TradingStrategyDelivery strategyDelivery);

    @POST("/gateway/data-route-business-service/api/openapi/deliveryLogReport")
    ShuhanResponse<Boolean> deliveryLogReport(@Body OrderDelivery orderDelivery);

    @POST("/gateway/data-route-business-service/api/openapi/accessConfig")
    ShuhanResponse<List<OrderConfigVO>> accessConfig(@Body OrderConfigDTO orderConfigDTO);

    @GET("/data-valley-gateway/data-route-business-service/api/openapi/contract/template/list")
    ShuhanResponse<List<ContractTemplateDTO>> contractTemplateList(@Header("metaData") String metaData);

    @GET("/data-valley-gateway/data-route-business-service/api/openapi/contract/getTemplate/{templateId}")
    ShuhanResponse<String> contractGetTemplate(@Header("metaData") String metaData, @Path("templateId") String templateId);

    @POST("/gateway/api/ljqLogin")
    CommonResult<LoginResponse> login(@Body LoginRequest request, @Header("nodeId") String nodeId);

    @POST("/gateway/auth/api/routeUser/remote/saveDataRouteUser")
    CommonResult<AddUserResponse> addUser(@Body AddUserRequest request, @Header("nodeId") String nodeId);

    @POST("/gateway/data-route-business-service/api/openapi/GetEnterpriseInfo")
    CommonResult<EnterpriseInfoResponse> getEnterpriseInfo(@Body GetEnterpriseInfoRequest request, @Header("metaData") String metaData);

    @POST("/data-valley-gateway/data-route-business-service/api/openapi/applyServiceNode")
    ShuhanResponse<ApplyServiceNodeResp> applyServiceNode(@Header("metaData") String metaData);

    @POST("/data-valley-gateway/data-route-business-service/api/openapi/serviceNodeProcessStatusQuery")
    ShuhanResponse<ServiceNodeProcessStatusVO> serviceNodeProcessStatusQuery(@Header("metaData") String metaData);
}
