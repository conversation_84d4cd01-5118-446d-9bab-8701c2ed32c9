package com.ailpha.ailand.dataroute.endpoint.user.remote.response;

import lombok.Data;

/**
 * 用户信息DTO
 */
@Data
public class UserInfoDTO {
    /** 用户唯一标识 */
    private String uuid;
    /** 用户姓名 */
    private String userName;
    /** 账号 */
    private String accountNumber;
    /** 工号 */
    private String jobNo;
    /** 手机号 */
    private String mobile;
    /** 邮箱 */
    private String mail;
    /** 昵称 */
    private String nickName;
    /** 身份证号 */
    private String idNum;
    /** 性别（1-男，2-女） */
    private Integer gender;
    /** 职位名称 */
    private String jobTitle;
    /** 工作单位 */
    private String workUnit;
    /** 地址 */
    private String address;
    /** 固定电话 */
    private String tel;
    /** 团队ID列表，多个用逗号分隔 */
    private String teamIds;
    /** 主要工作团队ID列表，多个用逗号分隔 */
    private String mainJobTeamIds;
    /** 来源ID */
    private String sourceId;
    /** 政治面貌代码 */
    private String empPoliticalStatusCode;
    /** 职级代码 */
    private String empJobLevelCode;
    /** 编制岗位代码 */
    private String empBudgetedPostCode;
    /** 政府雇员职位 */
    private String govEmpPosJob;
    /** 状态（1-正常，0-禁用） */
    private Integer status;
}