package com.ailpha.ailand.dataroute.endpoint.servicenode.mapper;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.ApprovalStatus;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.ServiceNodeExtend;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeAppliedListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.RegionSyncListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeListVO;
import org.mapstruct.*;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * 2025/7/30
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class ServiceNodeMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "processStatus", ignore = true)
    @Mapping(target = "processTime", ignore = true)
    @Mapping(target = "extend", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    public abstract ServiceNodeInfo serviceNodeListVOToServiceNodeInfo(ServiceNodeListVO serviceNodeListVO);

    @AfterMapping
    void fillOtherInfo(ServiceNodeListVO serviceNodeListVO, @MappingTarget ServiceNodeInfo.ServiceNodeInfoBuilder serviceNodeInfoBuilder) {
        Date date = new Date();
        serviceNodeInfoBuilder.processStatus(ApprovalStatus.APPLY.name()).processTime(date)
                .createTime(date).updateTime(date);
    }

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "typeDescription", ignore = true)
    @Mapping(target = "ip", ignore = true)
    @Mapping(target = "domainName", ignore = true)
    @Mapping(target = "apiUrl", ignore = true)
    public abstract ServiceNodeListVO regionSyncListVOToServiceNodeListVO(RegionSyncListVO.ServiceNode serviceNode);

    @AfterMapping
    void fillOtherInfo(RegionSyncListVO.ServiceNode serviceNode, @MappingTarget ServiceNodeListVO serviceNodeListVO) {
        if (!ObjectUtils.isEmpty(serviceNode.getAddresses())) {
            RegionSyncListVO.Addresses addresses = serviceNode.getAddresses().getFirst();
            BeanUtils.copyProperties(addresses, serviceNodeListVO);
        }
    }

    @Mapping(target = "extend", ignore = true)
    public abstract ServiceNodeAppliedListVO serviceNodeInfoToServiceNodeAppliedListVO(ServiceNodeInfo serviceNodeInfo);

    @AfterMapping
    void fillOtherInfo(ServiceNodeInfo serviceNodeInfo, @MappingTarget ServiceNodeAppliedListVO.ServiceNodeAppliedListVOBuilder serviceNodeAppliedListVOBuilder) {
        if (!ObjectUtils.isEmpty(serviceNodeInfo.getExtend())) {
            serviceNodeAppliedListVOBuilder.extend(JacksonUtils.json2pojo(serviceNodeInfo.getExtend(), ServiceNodeExtend.class));
        }
    }
}
