package com.ailpha.ailand.dataroute.endpoint.connector.remote.response;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 连接器区域节点解析响应
 *
 * <AUTHOR>
 * @date 2024/11/20
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ConnectorRegionNodeResolutionResponse {
    /**
     * 接入连接器名称
     */
    String connectorName;

    /**
     * 接入连接器ID
     */
    String identityId;

    /**
     * 接入连接器地址
     */
    List<ConnectorNetworkList> connectorNetworkList;

    /**
     * 网络接入类型
     * 5-其他
     * 4-高速数据网
     * 3-互联网（无固定公网IP）
     * 2-互联网（固定公网IP）
     * 1-专线
     */
    String connectorJoinType;

    /**
     * 所属接入主体名称
     */
    String ownerIdentityName;

    /**
     * 所属接入主体标识
     */
    String ownerIdentityId;

    /**
     * 供应商名称
     */
    String supplierName;

    /**
     * 供应商统一社会信用代码
     */
    String supplierCode;

    /**
     * 产品SN号
     */
    String connectorSN;

    /**
     * 2-全功能型接入连接器
     * 1-标准型接入连接器
     * 连接器类型
     */
    String connectorType;

    /**
     * 产品版本号
     */
    String connectorVersion;

    /**
     * 物理设备唯一标识符（若有多台，只登记管理服务器mac地址）
     */
    String connectorMac;

    @Data
    public static class ConnectorNetworkList {
        String ip;
        String domain;
    }
}