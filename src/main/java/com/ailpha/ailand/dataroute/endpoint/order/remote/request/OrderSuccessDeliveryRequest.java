package com.ailpha.ailand.dataroute.endpoint.order.remote.request;


import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import lombok.*;

import java.math.BigInteger;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OrderSuccessDeliveryRequest extends BaseRemoteRequest {

    String orderId;

    /**
     * 是否属于 成功交付 情况
     */
    Boolean isSuccessDelivery;

    BigInteger successfulUsage;

    // ——————————————————————————————————
    /**
     * 订单绑定的场景id
     */
    String deliverySceneId;

    /**
     * 数据名称
     */
    String assetName;

    /**
     * 数据id
     */
    String assetId;

    /**
     * 场景对应交付类型
     */
    DeliveryType deliveryType;
}
