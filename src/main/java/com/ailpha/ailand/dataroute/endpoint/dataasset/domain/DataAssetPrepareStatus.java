package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

/**
 * 数据资产预处理状态
 *
 * <AUTHOR>
 * @Date 2024-11-13
 */
public enum DataAssetPrepareStatus {
    /**
     * 已创建
     */
    CREATED,
    /**
     * 处理成功
     */
    HANDLE_SUCCESS,
    /**
     * 处理失败
     */
    HANDLE_FAILURE,
    /**
     * 数据集同步中
     */
    SYNC,
    /**
     * 数据集同步失败
     */
    SYNC_FAILURE,
    /**
     * 数据集可用
     */
    AVAILABLE;

    public static DataAssetPrepareStatus from(String status) {
        for (DataAssetPrepareStatus dataType : values()) {
            if (dataType.name().equalsIgnoreCase(status)) {
                return dataType;
            }
        }
        return HANDLE_SUCCESS;
    }
}
