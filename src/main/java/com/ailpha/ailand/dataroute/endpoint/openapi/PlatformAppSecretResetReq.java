package com.ailpha.ailand.dataroute.endpoint.openapi;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PlatformAppSecretResetReq implements Serializable {

    @NotNull(message = "ID不能为空")
    @Schema(description = "id", required = true)
    Long id;
}
