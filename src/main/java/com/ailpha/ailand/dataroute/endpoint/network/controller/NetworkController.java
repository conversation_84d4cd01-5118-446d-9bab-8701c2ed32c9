package com.ailpha.ailand.dataroute.endpoint.network.controller;

import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.network.dto.InitializeNetworkRequest;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/network")
@RequiredArgsConstructor
public class NetworkController {

    private final NodeService nodeService;
    private final RouterService routerService;

    @PostMapping("/initialize")
    @Operation(description = "初始化平台虚IP")
    public SuccessResponse<Void> initializeNetwork(@RequestBody InitializeNetworkRequest request) {
        nodeService.initExposeUrl(UuidUtils.uuid32(), request.getCentralIP(), UuidUtils.uuid32(), request.getNetworkAccessType(), request.getIp());
        return SuccessResponse.success(null).build();
    }

    @GetMapping("/virtual-ip")
    @Operation(description = "查询虚IP")
    public SuccessResponse<GetVirtualIpResponse> getVirtualIp() {
        return SuccessResponse.success(routerService.getVirtualIp()).build();
    }
}
