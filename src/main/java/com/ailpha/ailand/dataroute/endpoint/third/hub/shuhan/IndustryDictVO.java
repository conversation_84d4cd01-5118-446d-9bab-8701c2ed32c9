package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IndustryDictVO {
    String id;
    String pid;
    /**
     * 字典代码
     */
    String dicCode;
    /**
     * 字典名称
     */
    String dicName;
    /**
     * 字典备注
     */
    String dicDesc;
    /**
     * 排序
     */
    Integer sort;
    /**
     * 子节点
     */
    List<IndustryDictVO> children;
}
