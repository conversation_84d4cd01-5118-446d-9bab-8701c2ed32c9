package com.ailpha.ailand.dataroute.endpoint.plugin.strategy;

import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import com.ailpha.ailand.invoke.api.CommonException;
import lombok.extern.slf4j.Slf4j;

import javax.script.Bindings;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: yuwenping
 * @date: 2025/5/12 09:33
 * @Description:
 */
@Slf4j
public class GroovyBlockchainStrategy implements BlockchainProcessingStrategy {
    private static final ScriptEngineManager manager = new ScriptEngineManager();
    private static final ScriptEngine engine = manager.getEngineByName("groovy");

    @Override
    public boolean process(String data, BlockchainPluginDetail detail) {
        // 2. 执行脚本
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> context = new HashMap<>();
        params.put("data", data);
        params.put("context", context);
//        params.put("config", detail.getExt());

        execute(detail.getExt(), params);
        return true;
    }

    @Override
    public Long save(BlockchainPluginRequest request) {
        BlockchainPluginDetail detail = baseSave(request);
        return detail.getId();
    }

    @Override
    public Long update(BlockchainPluginUpdateRequest request) {
        return baseUpdate(request).getId();
    }

    public Object execute(String script, Map<String, Object> params) {
        // 绑定参数（message, context, config）
        Bindings bindings = engine.createBindings();
        bindings.putAll(params);
        try {
            return engine.eval(script, bindings);
        } catch (ScriptException e) {
            log.error("e", e);
            throw new CommonException(e.getMessage());
        }
    }
}
