package com.ailpha.ailand.dataroute.endpoint.plugin.strategy;

import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.biz.api.collector.HttpReaderTaskUtil;
import com.ailpha.ailand.biz.api.constants.BlockchainPluginApiTypeEnum;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.BlockchainParamsBO;
import com.ailpha.ailand.biz.api.dataset.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginApiDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import com.ailpha.ailand.dataroute.endpoint.repository.BlockchainPluginApiDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.repository.BlockchainPluginDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.util.JsonPathUtils;
import com.ailpha.ailand.plugin.reader.httpreader.RestTemplateUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: yuwenping
 * @date: 2025/5/12 09:30
 * @Description: API 插件处理
 */
@Slf4j
public class APIBlockchainStrategy implements BlockchainProcessingStrategy {
    @Override
    public boolean process(String data, BlockchainPluginDetail detail) {
        log.debug("api blockchain process data:{}, detail:{}", data, detail);
        List<BlockchainPluginApiDetail> blockchainPluginApiDetails = detail.getBlockchainPluginApiDetails();
        Map<BlockchainPluginApiTypeEnum, BlockchainPluginApiDetail> map = blockchainPluginApiDetails.stream()
                .collect(Collectors.toMap(BlockchainPluginApiDetail::getType, v -> v, (v1, v2) -> v1));
        // 1. pre-post
        BlockchainPluginApiDetail beforePost = map.get(BlockchainPluginApiTypeEnum.BEFORE_POST);
        String tokenResponse = null;
        if (Objects.nonNull(beforePost)) {
            tokenResponse = postForToken(beforePost);
        }
        BlockchainPluginApiDetail upBlockchain = map.get(BlockchainPluginApiTypeEnum.UP_BLOCKCHAIN);
        String result = upBlockchain(upBlockchain, tokenResponse, data);

        log.debug("api blockchain process data success, detail:{}", result);
        // 默认处理逻辑
        return true;
    }

    @Override
    public Long save(BlockchainPluginRequest request) {
        BlockchainPluginDetailRepository blockchainPluginDetailRepository = SpringUtil.getBean(BlockchainPluginDetailRepository.class);
        BlockchainPluginApiDetailRepository blockchainPluginApiDetailRepository = SpringUtil.getBean(BlockchainPluginApiDetailRepository.class);
        BlockchainPluginDetail detail = baseSave(request);

        // 保存明细表
        List<BlockchainPluginApiDetail> apiDetails = request.getApiList().stream()
                .map(api -> BlockchainPluginApiDetail.builder()
                        .url(api.getUrl())
                        .method(api.getMethod())
                        .params(api.getBlockchainParams())
                        .body(api.getBody())
                        .bodyType(api.getBodyType())
                        .headers(api.getBlockchainHeaders())
                        .type(api.getType())
                        .pluginId(detail.getId())
                        .upDataField(api.getUpDataField())
                        .createTime(LocalDateTime.now())
                        .build())
                .collect(Collectors.toList());
        blockchainPluginApiDetailRepository.saveAll(apiDetails);
        return detail.getId();
    }

    public String postForToken(BlockchainPluginApiDetail blockchainPluginApiDetail) {
        String url = blockchainPluginApiDetail.getUrl();
        MethodEnum method = blockchainPluginApiDetail.getMethod();
        List<BlockchainParamsBO> blockchainParams = blockchainPluginApiDetail.getParams();
        String body = blockchainPluginApiDetail.getBody();
        BodyTypeEnum bodyType = blockchainPluginApiDetail.getBodyType();
        List<BlockchainParamsBO> blockchainHeaders = blockchainPluginApiDetail.getHeaders();
        // 组装Params参数
        List<ParamsBO> castParams = blockchainParams.stream().map(a -> (ParamsBO) a).collect(Collectors.toList());
        LinkedHashMap<String, ?> paramsReal = HttpReaderTaskUtil.buildParams(castParams);
        // 组装Body参数
        String bodyReal = HttpReaderTaskUtil.buildBody(bodyType.toString(), body);
        // 发送请求并返回数据
        Map<String, Object> headers = blockchainHeaders.stream().collect(Collectors.toMap(ParamsBO::getKey, ParamsBO::getValue, (s1, s2) -> s2));
        log.debug("调用区块链前置接口 url：{}， params：{}， body：{}，headers：{}", url, paramsReal, bodyReal, headers);
        return RestTemplateUtil.sendHttp(url, method.toString(), paramsReal, bodyReal, bodyType.toString(), headers, false);
    }

    public String upBlockchain(BlockchainPluginApiDetail blockchainPluginApiDetail, String tokenResponse, String data) {
        String url = blockchainPluginApiDetail.getUrl();
        MethodEnum method = blockchainPluginApiDetail.getMethod();
        List<BlockchainParamsBO> blockchainParams = blockchainPluginApiDetail.getParams();
        String body = blockchainPluginApiDetail.getBody();
        BodyTypeEnum bodyType = blockchainPluginApiDetail.getBodyType();
        Map<String, Object> headers = this.buildBlockchainHeaders(blockchainPluginApiDetail.getHeaders(), tokenResponse);
        // 组装Params参数
        LinkedHashMap<String, ?> paramsReal = HttpReaderTaskUtil.buildBlockchainParams(blockchainParams, data);
        // 组装Body参数
        String bodyReal = HttpReaderTaskUtil.buildBlockchainBody(bodyType.toString(), body, data, blockchainPluginApiDetail.getUpDataField());
        // 发送请求并返回数据

        log.debug("调用区块链上链接口 url：{}， params：{}， body：{}，headers：{}", url, paramsReal, bodyReal, headers);
        return RestTemplateUtil.sendHttp(url, method.toString(), paramsReal, bodyReal, bodyType.toString(), headers, false);
    }

    /**
     * 功能描述: 该 headers 中
     *
     * @param headers       headers
     * @param tokenResponse tokenResponse
     * @return: java.util.Map<java.lang.String, java.lang.Object>
     * @author: yuwenping
     * @date: 2025/5/13 11:04
     */
    private Map<String, Object> buildBlockchainHeaders(List<BlockchainParamsBO> headers, String tokenResponse) {
        Map<String, Object> map = new HashMap<>();
        for (BlockchainParamsBO paramsBO : headers) {
            String value = paramsBO.getValue();
            String resolvedValue = resolvePlaceholder(value, tokenResponse);
            map.put(paramsBO.getKey(), resolvedValue);
        }
        return map;
    }

    /**
     * 功能描述: 如果 value 是 ${} 占位符形式，则解析获取 tokenResponse 的值
     *
     * @param value         value
     * @param tokenResponse tokenResponse
     * @return: java.lang.String
     * @author: yuwenping
     * @date: 2025/5/13 11:01
     */
    private String resolvePlaceholder(String value, String tokenResponse) {
        Pattern pattern = Pattern.compile("\\$\\{(.+?)\\}");
        Matcher matcher = pattern.matcher(value);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            String replacement = JsonPathUtils.getValue(tokenResponse, key, String.class);
            matcher.appendReplacement(sb, replacement != null ? replacement : "");
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    @Override
    public Long update(BlockchainPluginUpdateRequest request) {
        // 更新主表
        BlockchainPluginDetail detail = baseUpdate(request);

        BlockchainPluginApiDetailRepository blockchainPluginApiDetailRepository = SpringUtil.getBean(BlockchainPluginApiDetailRepository.class);
        // 删除旧明细
        blockchainPluginApiDetailRepository.deleteByPluginId(detail.getId());

        // 保存新明细
        List<BlockchainPluginApiDetail> apiDetails = request.getApiList().stream()
                .map(api -> BlockchainPluginApiDetail.builder()
                        .url(api.getUrl())
                        .method(api.getMethod())
                        .params(api.getBlockchainParams())
                        .body(api.getBody())
                        .bodyType(api.getBodyType())
                        .headers(api.getBlockchainHeaders())
                        .type(api.getType())
                        .pluginId(detail.getId())
                        .createTime(LocalDateTime.now())
                        .upDataField(api.getUpDataField())
                        .build())
                .collect(Collectors.toList());
        blockchainPluginApiDetailRepository.saveAll(apiDetails);

        return detail.getId();
    }
}
