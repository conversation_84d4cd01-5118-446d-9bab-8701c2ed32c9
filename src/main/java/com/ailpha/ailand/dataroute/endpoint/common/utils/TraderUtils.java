package com.ailpha.ailand.dataroute.endpoint.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiMarkTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginCredentials;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/11/17 11:44
 * 交易所请求工具类
 */
@Slf4j
public class TraderUtils {

    /**
     * 数交所获取token值
     *
     * @param timestamp    时间戳毫秒值
     * @param platformCode 颁发的平台码
     * @param privateKey   私钥
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String getToken(String timestamp, String platformCode, String privateKey) throws NoSuchAlgorithmException {
        String originSign = StrUtil.join("", timestamp, platformCode, privateKey);
        return Md5Utils.encrypt(originSign);
    }


    public static String encryptBodyParam(String privateKey, String bodyStr) {
        log.info("加密前body参数信息：{}", bodyStr);
        RSA rsa = new RSA(privateKey, null);
        String encryptBase64 = rsa.encryptBase64(bodyStr, KeyType.PrivateKey);
        log.info("加密后body参数：{}", encryptBase64);

        return encryptBase64;
    }

    public static String decryptBodyParam(String publicKey, String bodyStr) {
        log.info("解密前body参数信息：{}", bodyStr);
        RSA rsa = new RSA(null, publicKey);
        String encryptBase64 = rsa.decryptStr(bodyStr, KeyType.PublicKey);
        log.info("解密后body参数：{}", encryptBase64);

        return encryptBase64;
    }

    /**
     * 交易所登记
     *
     * @param bodyObject   body 参数
     * @param domainUrl    域名前缀
     * @param platformCode code
     * @param privateKey   私钥
     * @return
     */
    public static String traderReport(Object bodyObject, String domainUrl, String businessUrl, String platformCode, String privateKey) throws NoSuchAlgorithmException {
        long timestamp = System.currentTimeMillis();
        String token = TraderUtils.getToken(String.valueOf(timestamp), platformCode, privateKey);

        String jsonStr = JSONUtil.toJsonStr(bodyObject);
        String encryptBodyParam = TraderUtils.encryptBodyParam(privateKey, jsonStr);

        String url = domainUrl + businessUrl;
        Map<String, String> header = new LinkedHashMap<>();
        header.put("platformCode", platformCode);
        header.put("token", token);
        header.put("timestamp", String.valueOf(timestamp));
        header.put("Content-Type", "application/json");

        HttpRequest httpRequest = HttpUtil.createRequest(Method.POST, url).addHeaders(header).body(encryptBodyParam);
        HttpResponse execute = httpRequest.execute();
        String body = execute.body();
        log.debug("上报交易所接口返回：{}", body);
        return body;
    }

    public static String traderReport(Object bodyObject, PluginDetail pluginDetail, PluginApiMarkTypeEnums type, String platformCode) throws NoSuchAlgorithmException {
        PluginApiDetail pluginApiDetail = pluginDetail.getPluginApiDetails().stream().filter(item -> type.equals(item.getApiMark())).findFirst().orElse(null);
        if (pluginApiDetail != null && pluginApiDetail.getEnabled()) {
            PluginCredentials bean = JSONUtil.toBean(pluginDetail.getPlugCredentials(), PluginCredentials.class);
            return traderReport(bodyObject, pluginDetail.getDomain(), pluginApiDetail.getApiUrl(), platformCode, bean.getPrivateKey());
        }
        log.info("该交易所[{}]未配置该类型接口[{}]或者该接口未启用，忽略本次上报", pluginDetail.getName(), type);
        return null;
    }

    public static String encryptWithPrivateKey(String data, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] encryptedData = cipher.doFinal(data.getBytes());
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    public static String decryptWithPublicKey(String encryptedData, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] decryptedData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedData);
    }

    public static PublicKey loadPublicKey(String key) throws Exception {

        byte[] keyBytes = Base64.getDecoder().decode(key);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        return keyFactory.generatePublic(keySpec);
    }

    public static PrivateKey loadPrivateKey(String key) throws Exception {

        byte[] keyBytes = Base64.getDecoder().decode(key);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        return keyFactory.generatePrivate(keySpec);
    }

    public static Integer getDataCompanyType(String provider) {
//        switch (provider) {
//            case "普通":
//                return 1;
//            case "卖家":
//                return 2;
//            case "第三方服务机构":
//                return 3;
//            default:
//                return 0;
//        }
        return 1;
    }

    public static void main(String[] args) throws Exception {
        String privateKey = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOlcUKgV3Swx4wq425dSeCm0Fq23ALbac7jLLwWbOoeW3pel/uaAdej0pulNbMWaohbCcLX5fpf/QKIonO5Hzc3quUA+9QZt+NvRC2hzvUjuhQpoyaOkp0BWoaeYdHExP1vixE5RtR8NzGSgzdbVZRXDctNXDyLHMhGVm41xI7BBAgMBAAECgYAN+r/CMf0b5qkpPUW6XPBh3y30nh/m1FNWYKWielgZQ0p2XzVT7aIioHVXSJIZjFflcSR7YkTZKitfC3vLMnX7oKH0bRQQVripQdvQ6OJA2XhXUGLPKvJk4db5w20DseVON60QOu0CMzu3QfO8n4L/tmaVT4ley0W0FuDsmpQiAQJBAP99JzqFlOuMTBZlTfkSJ9K4EUzU3MuqCW6M6hjfRN4sjxdeUpWiDkG9PkUOVNIUmk5e+10xOdS6HaWZ5pe09jECQQDp09Q49a0OjW1EKgfqOdFxy54qCF4kZWzh1JpkJTS98lHhvu5NiME6yPSxXUUHYb9PoTrSLU9HdxTk2vP0pAcRAkEAwOJ16WLMaYDEOOxGj9I7KoU42m6iO0imUkYkvp5hWfL+HMBjTQDKDquXBX+qXeicDqr6zwLJDGdu1oV4Z/SBIQJBALV7tfHnrMPovO/092OyMH90HHRfPkJdfl7hFlu4DPGIjEj8WhVPmTo5EsI+s/8AvO3fI7pRdIDxSgQ+dK5iSSECQCIjvV2qnzmVLXJD3LknsK/V5TsYk4tAiZ2xncjLxHU/JebsHJAvBDifL0ujsMAIKt1K2clBc6Rn27C9zugShkU=";

        String platformCode = "1858337670212923394";

        String token = TraderUtils.getToken(String.valueOf(12345), platformCode, privateKey);
        System.out.println(token);

//        BusinessReportDTO businessReportDTO = new BusinessReportDTO();
//        businessReportDTO.setCompanyName("杭州xxx信息技术有限公司");
//        businessReportDTO.setCompanySucc("9133123456L");
//        businessReportDTO.setPlatformCompanyId("00000001");
//        businessReportDTO.setSourcePlatformId(platformCode);
//
//        ArrayList<BusinessReportDTO.CompanyLink> companyLinks = new ArrayList<>();
//        BusinessReportDTO.CompanyLink companyLink = new BusinessReportDTO.CompanyLink();
//        companyLink.setCompanyLinkName("联系人1");
//        companyLink.setCompanyLinkMobile("***********");
//        companyLinks.add(companyLink);
//        businessReportDTO.setCompanyLinkParams(companyLinks);
//
//        String domainUrl = "https://ditmtest.zjdex.com:8089/tsslapi";
//
//        String body = traderReport(businessReportDTO, domainUrl, TraderConstant.BUSINESS_REPORTING, platformCode, privateKey);
//        log.info("数商登记接口返回：{}", body);

    }


}
