package com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy;

import com.ailpha.ailand.dataroute.endpoint.connector.RouteActivateContext;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.DataRouteActivateHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DataHubHandler implements DataRouteActivateHandler {

//    private final DataHubRemoteService dataHubRemoteService;
//    private final SyncAdmin2IamHandler syncAdmin2IamHandler;

    @Override
    public void handler(RouteActivateContext context) {
        // 注册终端信息到枢纽
//        NodeInfoResponse nodeInfoResponse = SpringUtil.getBean(RouterService.class).currentNode();
//        HubUpsertRouteRequest hubUpsertRouteRequest = new HubUpsertRouteRequest();
//        hubUpsertRouteRequest.setPublicKey(nodeInfoResponse.getPublicKey());
//        hubUpsertRouteRequest.setNodeId(nodeInfoResponse.getRouterId());
//        hubUpsertRouteRequest.setNodeType("CONNECTOR");
//        CommonResult<Void> upsert = dataHubRemoteService.upsert(hubUpsertRouteRequest);
//        log.info("终端注册枢纽节点返回信息 = {}", upsert);

//        RouterService routerService = SpringUtil.getBean(RouterService.class);
//        ActivateRouterRequest activateRouterRequest = new ActivateRouterRequest();
//        activateRouterRequest.setClientNo(context.getRoute().getId());
//        activateRouterRequest.setStatus(RouteStatus.activated);
//        activateRouterRequest.setClientIp(routerService.currentRouteVirtualIp() + ":" + SpringUtil.getProperty("server.port"));
//        dataHubRemoteService.activate(activateRouterRequest);
//        log.info("终端注册基础服务平台 = {}", activateRouterRequest);

//        syncAdmin2IamHandler.handler(context);
    }
}
