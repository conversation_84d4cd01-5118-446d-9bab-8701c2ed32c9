package com.ailpha.ailand.dataroute.endpoint.dataprope.service;

import com.ailpha.ailand.dataroute.endpoint.dataprope.vo.DataProbeReportVO;
import com.ailpha.ailand.dataroute.endpoint.dataprope.vo.request.DataProbeListReq;
import com.ailpha.ailand.dataroute.endpoint.third.output.AiSortRemote;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortColumnResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortDataSourceResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortResponse;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortTablesResp;
import com.ailpha.ailand.dataroute.endpoint.third.util.AiSortAESUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/2/20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataProbeService {

    private final AiSortRemote aiSortRemote;

    /**
     * 数据目录–获取表分页
     */
    public SuccessResponse<List<AiSortTablesResp.Record>> tables(DataProbeListReq dataProbeListReq) {
        List<AiSortTablesResp.Record> records = new ArrayList<>();

        UserDTO currentUser = LoginContextHolder.currentUser();
        AiSortResponse<AiSortTablesResp> aiSortResponse = aiSortRemote.tables(dataProbeListReq.getDbName(), dataProbeListReq.getDbType(),
                String.valueOf(dataProbeListReq.getNum()), String.valueOf(dataProbeListReq.getSize()), dataProbeListReq.getSourceName(),
                dataProbeListReq.getTableNameDesc(), null, currentUser.getUsername(), Boolean.TRUE, "update_time", "desc");
        long total = 0;
        if (!ObjectUtils.isEmpty(aiSortResponse.getData())) {
            AiSortTablesResp data = aiSortResponse.getData();
            records = data.getRecords();
            total = data.getTotal();
        }
        return SuccessResponse.success(records).page(new Page(dataProbeListReq.getNum(), dataProbeListReq.getSize(), dataProbeListReq.getOffset())).total(total).build();
    }

    /**
     * 数据源管理-根据ID获取数据源详情
     */
    public AiSortDataSourceResp getById(Long id) {
        AiSortDataSourceResp aiSortDataSource = AiSortDataSourceResp.builder().build();
        AiSortResponse<AiSortDataSourceResp> aiSortResponse = aiSortRemote.getById(id);
        if (!ObjectUtils.isEmpty(aiSortResponse.getData())) {
            aiSortDataSource = aiSortResponse.getData();
            if (!ObjectUtils.isEmpty(aiSortDataSource.getPassword())) {
                String decrypt = AiSortAESUtil.decrypt(aiSortDataSource.getPassword());
                aiSortDataSource.setPassword(decrypt);
            }
        }
        return aiSortDataSource;
    }

    /**
     * 数据目录–根据表id获取字段列表
     */
    public List<AiSortColumnResp> columns(String comment, Long id, String name) {
        List<AiSortColumnResp> columns = new ArrayList<>();
        AiSortResponse<List<AiSortColumnResp>> aiSortResponse = aiSortRemote.columns(comment, id, name);
        if (!ObjectUtils.isEmpty(aiSortResponse.getData())) {
            columns = aiSortResponse.getData();
        }
        return columns;
    }

    /**
     * 数据探查报告
     */
    public DataProbeReportVO report() {
        return null;
    }
}
