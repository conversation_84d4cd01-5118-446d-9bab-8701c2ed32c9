package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiMarkTypeEnums;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 16:39
 * @Description:
 */
@Data
@Schema(description = "插件新增/修改请求")
public class PluginApiDetailRequest {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "插件id")
    private Long pluginId;
    @Schema(description = "标识")
    private PluginApiMarkTypeEnums mark;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "url")
    private String url;
    @Schema(description = "插件状态")
    private Boolean enabled;

}
