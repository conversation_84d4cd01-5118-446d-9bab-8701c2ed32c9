package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageReadStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderCreateReq;
import com.ailpha.ailand.dataroute.endpoint.sse.entity.SSEMessageRecord;
import com.dbapp.rest.response.ApiResponse;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @author: yuwenping
 * @date: 2024/11/16 14:01
 * @Description:
 */
public interface SSEMessageService {

    /**
     * 功能描述:  建立 SSE 连接
     *
     * @return reactor.core.publisher.Flux<java.lang.String>
     * <AUTHOR>
     * @date 2024/11/17 11:38
     */
    Flux<String> streamNotifications(String uid);

    void logSinkInfo();

    /**
     * 功能描述: 产生消息体发送到对应的连接器
     *
     * @param sseMessageRequests sseMessageRequests 消息列表
     * @param clientNo           clientNo 路由器编号
     * @return void
     * <AUTHOR>
     * @date 2024/11/17 11:37
     */
    void notifyDataRouteMessage(List<SSEMessageRequest> sseMessageRequests, String clientNo);

    /**
     * 功能描述: 对应连接器收到消息落库，并推送消息到前端（客户端）
     *
     * @param sseMessageRequests sseMessageRequests
     * @return void
     * <AUTHOR>
     * @date 2024/11/17 13:55
     */
    void receiveDataRouteMessage(List<SSEMessageRequest> sseMessageRequests);

    /**
     * 功能描述: 发送消息到前端
     *
     * @param userId  userId
     * @param message message
     * @return void
     * <AUTHOR>
     * @date 2024/11/17 11:39
     */
    void sendNotification(String userId, String message);

    void sendNotification(SSEMessageRecord sseMessageRecord);

    /**
     * 功能描述: 标记消息
     *
     * @param id     id 可不传，不传处理全部未读消息
     * @param status status 默认为  SSEMessageReadStatus.READ
     * @return void
     * <AUTHOR>
     * @date 2024/11/17 14:37
     */
    void markNotice(Long id, SSEMessageReadStatus status);

    void mockData(String message);

    ApiResponse<List<SSEMessageRecord>> messageList(SSEMessageListRequest sseMessageListRequest);

    void totalCountMessage();

    void notifyOrderApplyMessage(List<OrderApprovalRecord> orderApprovalRecords, OrderCreateReq orderCreateReq);

    /**
     * 功能描述: 审批通过/拒绝，发送消息
     *
     * @param orderApprovalRecord orderApprovalRecord 订单记录
     * @param updateStatus        updateStatus 审批状态
     * @return void
     * <AUTHOR>
     * @date 2024/11/23 16:51
     */
    void notifyOrderAuditMessage(OrderApprovalRecord orderApprovalRecord, OrderStatus updateStatus);

    Object unReadCount();
}
