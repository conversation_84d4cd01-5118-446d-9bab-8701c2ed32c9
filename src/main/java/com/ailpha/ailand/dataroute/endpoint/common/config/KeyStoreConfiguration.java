package com.ailpha.ailand.dataroute.endpoint.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Paths;
import java.security.KeyPair;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(AiLandProperties.class)
@ConditionalOnClass({KeyStoreFactory.class, KeyPair.class})
@ConditionalOnProperty(prefix = "ailand.secure.key-pair", value = "enable", matchIfMissing = true)
public class KeyStoreConfiguration {

    @Bean
    public KeyStoreFactory keyStoreKeyFactory(AiLandProperties aiLandProperties) {
        return new KeyStoreFactory(aiLandProperties.keyPair.getKeyStorePassword().toCharArray(), Paths.get(aiLandProperties.keyPair.storePath, "data-route.p12"), aiLandProperties.keyPair.storeType);
    }

    @Bean
    @ConditionalOnMissingBean(KeyPair.class)
    public KeyPair keyPair(AiLandProperties aiLandProperties, KeyStoreFactory keyStoreKeyFactory) {
        return keyStoreKeyFactory.getKeyPair(aiLandProperties.keyPair.getAlias(), aiLandProperties.keyPair.getKeyPassword().toCharArray(), aiLandProperties.keyPair.getStoreType());
    }
}
