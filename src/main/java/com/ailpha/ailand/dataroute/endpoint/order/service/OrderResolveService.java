package com.ailpha.ailand.dataroute.endpoint.order.service;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.BusinessNodeOrderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderResolveDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderResolveService {

    private final AssetBeneficiaryRepository assetBeneficiaryRepository;

    private final OrderRecordRepository orderRecordRepository;


    public List<BusinessNodeOrderDTO> filterExistOrder(List<BusinessNodeOrderDTO> businessNodeOrderDTOS) {
        List<String> orderIdList = businessNodeOrderDTOS.stream().map(BusinessNodeOrderDTO::getCtrlInstructionId).toList();

        List<OrderApprovalRecord> recordList = orderRecordRepository.findAllById(orderIdList);
        List<String> existOrderIdList = recordList.stream().map(OrderApprovalRecord::getId).toList();
        
        // 过滤不存在的订单
        return businessNodeOrderDTOS.stream()
                .filter(businessNodeOrderDTO -> {
                    boolean isNew = !existOrderIdList.contains(businessNodeOrderDTO.getCtrlInstructionId());
                    if (isNew) {
                        log.info("获取新订单信息:{}", JSONUtil.toJsonStr(businessNodeOrderDTO));
                    }
                    return isNew;
                })
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public void save(OrderResolveDTO resolveDTO) {
        try {
            log.info("order resolve save request: {}", JSONUtil.toJsonStr(resolveDTO));

            OrderApprovalRecord record = resolveDTO.getRecord();
            orderRecordRepository.save(record);

            AssetBeneficiaryRel rel = resolveDTO.getRel();
            assetBeneficiaryRepository.save(rel);

            log.info("order resolve save request done");
        } catch (Exception e) {
            log.error("order resolve error: ", e);
        }
    }


}
