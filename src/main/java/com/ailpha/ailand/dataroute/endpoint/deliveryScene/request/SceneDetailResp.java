package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneDetailResp {

    @Schema(description = "ID")
    String id;

    @Schema(description = "交付方式")
    String deliveryType;

    @Schema(description = "状态 RUNNING（进行中）TERMINATED（已终止）COMPLETED（已完成）")
    String sceneStatus;

    @Schema(description = "数字证书 —— 合规场景名称")
    String digitalSceneName;

    @Schema(description = "关联数据资产id")
    List<DataAssetResp> dataAssetSceneRefList;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    Date createTime;
    @Schema(description = "合约配置信息")
    String contract;

}
