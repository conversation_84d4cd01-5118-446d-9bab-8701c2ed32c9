package com.ailpha.ailand.dataroute.endpoint.user.controller;

import com.ailpha.ailand.dataroute.endpoint.common.utils.CaptchaUtils;
import com.ailpha.ailand.dataroute.endpoint.user.contants.CacheCode;
import com.ailpha.ailand.dataroute.endpoint.user.service.CaptchaService;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.ailpha.ailand.dataroute.endpoint.user.vo.CaptchaVO;

/**
 * <AUTHOR>
 * @date 2022/1/24
 * @description
 */
@RestController
@RequestMapping
@Tag(name = "获取随机验证码")
@RequiredArgsConstructor
public class CaptchaController {

    private final CaptchaService captchaService;

    /**
     * 验证码
     */
    @GetMapping("/captcha")
    @ApiOperation("获取验证码")
    public SuccessResponse<CaptchaVO> captcha() throws IOException, InterruptedException {
        return SuccessResponse.success(captchaService.generateCaptcha()).build();
    }

    @Value("${ailand.testing.enabled:false}")
    Boolean isTestingEnv;


    @Value("${ailand.mail.captcha.validate-time-minutes:5}")
    Long captchaExpireInMinutes;
    private final CacheManager cacheManager;

    /**
     * 验证码获取，方便自动化测试使用
     * 生产环境确保 ailand.testing.enabled=false
     */
    @GetMapping("/captcha1")
    @ApiOperation(value = "获取验证码(方便自动化测试使用)")
    public SuccessResponse<Map<String, String>> captcha1() throws IOException, InterruptedException {
        Assert.isTrue(isTestingEnv, "无访问权限");

        CacheCode cacheCode = CaptchaUtils.generateCaptcha(TimeUnit.MINUTES.toMillis(captchaExpireInMinutes));
        // 放入缓存
        cacheManager.getCache("captcha_cache").put(cacheCode.getCodeId(), cacheCode);

        Map<String, String> result = new HashMap<>(2);
        if (cacheCode != null) {
            result.put("captchaId", cacheCode.getCodeId());
            result.put("captchaCode", cacheCode.getCode());
        }
        return SuccessResponse.success(result).build();
    }
}
