package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LicenseDTO {
    String encryptedBusinessInfo;
    String centreLocation;
    String iamIp;
    String routeCertSerialNumber;

    String routeCertificate;
    String caCertificate;
    @JsonUnwrapped
    CompanyDTO company;

    String secret;
    AdminUserDTO admin;
    ShuHanServer server;

    UserInfo userInfo;
    @Data
    public static class UserInfo {
        String phone;
        String email;
        String account;
        String realName;
        Long companyId;
    }

    @Data
    public static class ShuHanServer {
        String appKey;
        String appSecret;
        String clientNo;
    }
}
