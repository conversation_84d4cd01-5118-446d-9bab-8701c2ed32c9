package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OfflineAPIRequest {
    String id;

    InvokeParam invokeParam;

    public String getInvokeParam() {
        return JacksonUtils.obj2json(invokeParam);
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeParam {
        Integer status = 0;
    }
}
