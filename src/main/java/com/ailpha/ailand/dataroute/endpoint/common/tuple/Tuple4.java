package com.ailpha.ailand.dataroute.endpoint.common.tuple;

import java.util.Objects;

public final class Tuple4<F, S, T, FOUR> {
    public F first;
    public S second;
    public T third;

    public FOUR four;

    public Tuple4() {
    }

    public Tuple4(F first, S second, T third, FOUR four) {
        this.first = first;
        this.second = second;
        this.third = third;
        this.four = four;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Tuple4<?, ?, ?, ?> tuple3 = (Tuple4<?, ?, ?, ?>) o;
        if (!Objects.equals(first, tuple3.first)) return false;
        if (!Objects.equals(second, tuple3.second)) return false;
        if (!Objects.equals(third, tuple3.third)) return false;
        return Objects.equals(four, tuple3.four);
    }

    @Override
    public int hashCode() {
        int result = first != null ? first.hashCode() : 0;
        result = 31 * result + (second != null ? second.hashCode() : 0);
        result = 31 * result + (third != null ? third.hashCode() : 0);
        result = 31 * result + (four != null ? four.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Tuple3{" +
                "first=" + first +
                ", second=" + second +
                ", third=" + third +
                ", four=" + four +
                '}';
    }

}
