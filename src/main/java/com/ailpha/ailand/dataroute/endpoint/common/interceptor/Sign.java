package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.InterceptMark;

import java.lang.annotation.*;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@InterceptMark
public @interface Sign {

    BaseCapabilityType baseCapabilityType();

    String tokenUrl() default "/openapi/token";

    String[] include() default {"/**"};

    String[] exclude() default {};

    String[] getTokenExclude() default {};

    Class<? extends BasePathMatchInterceptor> handler() default SignInterceptor.class;
}
