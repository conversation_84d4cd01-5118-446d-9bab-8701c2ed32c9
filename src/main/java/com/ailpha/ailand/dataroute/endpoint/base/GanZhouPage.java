package com.ailpha.ailand.dataroute.endpoint.base;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.IPageResult;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:57
 */
@Setter
public class GanZhouPage<T> implements IPageResult<T> {

    private long total = 0L;

    private List<T> rows;

    private int code;

    private String msg = "查询成功";

    @Override
    public String getMessage() {
        return msg;
    }

    @Override
    public long getTotal() {
        return total;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public List<T> getData() {
        return rows;
    }

    @Override
    public boolean isSuccess() {
        return code == 200;
    }

}
