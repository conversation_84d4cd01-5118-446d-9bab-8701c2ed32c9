package com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain;

import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "t_negotiate_transfer")
@AllArgsConstructor
public class NegotiateTransfer implements Serializable {

    @Id
    private String id;

    @Schema(description = "业务节点ID")
    @Column(name = "node_id")
    private String serviceNodeId;

    @Schema(description = "交易合约标识")
    @Column(name = "trading_strategy_code")
    private String tradingStrategyCode;

    @Schema(description = "交易合约名称")
    @Column(name = "trading_strategy_name")
    private String tradingStrategyName;

    @Schema(description = "控制指令编号")
    @Column(name = "ctrl_instruction_id")
    private String ctrlInstructionId;

    @Embedded
    @Schema(description = "交易合约控制指令")
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "transaction_execution_strategy")
    private NegotiateDataTransferDTO.ExecutionStrategy transactionExecutionStrategy;

    @Schema(description = "传输模式：当前仅支持【主动拉取】推送-1 拉取-2")
    @Column(name = "transfer_mode")
    private String transferMode;

    /**
     * 扩展
     */
    @Schema(description = "扩展")
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "extend")
    private String extend;


    @Column(name = "create_time")
    private Date createTime;
}
