package com.ailpha.ailand.dataroute.endpoint.common.log;

public enum InternalOpType implements OpType {
    OTHER("其他操作", InternalOpModule.OTHER),

    LOGOUT("退出", InternalOpModule.USER_CENTER),
    ROUTE_REGISTER("连接器入网", InternalOpModule.ROUTE_REGISTER),
    UPLOAD_LICENSE("上传证书", InternalOpModule.ROUTE_REGISTER),
    EXPORT_ROUTE_INFO("导出凭证", InternalOpModule.ROUTE_REGISTER),
    UPDATE_ROUTE_INFO("更新连接器信息", InternalOpModule.ROUTE_REGISTER),
    ADD_USER("新增用户", InternalOpModule.USER_MANAGER),
    DELETE_USER("删除用户", InternalOpModule.USER_MANAGER),
    RESET_USER_PWD("重置密码", InternalOpModule.USER_MANAGER),
    UPDATE_USER_INFO("编辑用户", InternalOpModule.USER_MANAGER),

    DATAASSET_TEMPORARY_SAVE("数据资产登记（暂存）", InternalOpModule.DATA_ASSET),
    DATAASSET_REGIST("数据资产登记", InternalOpModule.DATA_ASSET),
    DATAASSET_REGIST_UPDATE("数据资产登记更新", InternalOpModule.DATA_ASSET),
    DATAASSET_REGIST_REVOKE("数据资产登记撤销", InternalOpModule.DATA_ASSET),
    DATAASSET_CREATE("创建数据资产", InternalOpModule.DATA_ASSET),
    DATAASSET_UPDATE("更新数据资产", InternalOpModule.DATA_ASSET),
    DATAASSET_ONLINE("数据资产上架", InternalOpModule.DATA_ASSET),
    DATAASSET_ONLINE_UPDATE("数据资产上架更新", InternalOpModule.DATA_ASSET),
    DATAASSET_OFFLINE("下架数据资产", InternalOpModule.DATA_ASSET),
    DATAASSET_DELETE("删除数据资产", InternalOpModule.DATA_ASSET),

    CREATE_ORDER("创建订单", InternalOpModule.ORDER_MANAGER),
    APPROVE_ORDER("审批通过", InternalOpModule.ORDER_MANAGER),
    REJECT_ORDER("审批拒绝", InternalOpModule.ORDER_MANAGER),
    TERMINATE_ORDER("终止订单", InternalOpModule.ORDER_MANAGER),

    CREATE_SCENE("创建交付场景", InternalOpModule.SCENE_MANAGER),
    API_INVOKE("API接口使用", InternalOpModule.SCENE_MANAGER),
    FILE_DOWNLOAD_INVOKE("数据集使用", InternalOpModule.SCENE_MANAGER),

    ASSET_AUDIT_REGISTRATION("资产登记审批", InternalOpModule.DATA_ASSET_APPROVAL),
    ASSET_AUDIT_PUBLISH("资产上架审批", InternalOpModule.DATA_ASSET_APPROVAL),

    EXPORT_LICENSE_REQ_FILE("导出许可证申请文件", InternalOpModule.LICENSE_MANGER),
    UPLOAD_LIC("导入许可证", InternalOpModule.LICENSE_MANGER),

    ADD_PLUGIN("新增插件", InternalOpModule.PLUGIN_MANAGER),
    ENABLE_PLUGIN("启用插件", InternalOpModule.PLUGIN_MANAGER),
    FORBIDDEN_PLUGIN("禁用插件", InternalOpModule.PLUGIN_MANAGER),

    SAVE_CONFIGURATION("保存配置", InternalOpModule.CONFIGURATION),
    CUSTOM_LOGO_CONFIGURATION("更新企业自定义logo", InternalOpModule.CONFIGURATION),
    ;
    private final String bizName;

    private final OpModule opModule;

    InternalOpType(String bizName, OpModule opModule) {
        this.bizName = bizName;
        this.opModule = opModule;
    }

    @Override
    public OpModule module() {
        return opModule;
    }

    @Override
    public String bizName() {
        return bizName;
    }

    public static InternalOpType getByName(String bizName) {
        for (InternalOpType type : InternalOpType.values()) {
            if (type.bizName.equals(bizName)) {
                return type;
            }
        }
        return null;
    }
}
