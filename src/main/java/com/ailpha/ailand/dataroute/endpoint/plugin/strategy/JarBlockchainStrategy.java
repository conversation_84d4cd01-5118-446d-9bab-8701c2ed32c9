package com.ailpha.ailand.dataroute.endpoint.plugin.strategy;

import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import com.ailpha.ailand.dataroute.endpoint.plugin.BlockchainJarPlugin;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;

/**
 * @author: yuwenping
 * @date: 2025/5/12 09:32
 * @Description:
 */
@Slf4j
public class JarBlockchainStrategy implements BlockchainProcessingStrategy {

    private final Map<String, BlockchainJarPlugin> plugins = new HashMap<>();
    private final Map<String, URLClassLoader> loaders = new HashMap<>();

    @Override
    public boolean process(String data, BlockchainPluginDetail detail) {
        return true;
    }

    @Override
    public Long save(BlockchainPluginRequest request) {
        baseSave(request);

        try {
            loadPlugin(request.getPluginContent());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public Long update(BlockchainPluginUpdateRequest request) {
        return baseUpdate(request).getId();
    }


    public void loadPlugin(String jarPath) throws Exception {
        File jarFile = new File(jarPath);
        URLClassLoader classLoader = new URLClassLoader(
                new URL[]{jarFile.toURI().toURL()},
                this.getClass().getClassLoader()
        );

        ServiceLoader<BlockchainJarPlugin> serviceLoader = ServiceLoader.load(BlockchainJarPlugin.class, classLoader);
        for (BlockchainJarPlugin plugin : serviceLoader) {
            String pluginId = jarFile.getName() + "@" + plugin.getClass().getName();
            plugins.put(pluginId, plugin);
            loaders.put(pluginId, classLoader);
            log.info("插件加载成功: {}", pluginId);
        }
    }

    public void unloadPlugin(String pluginId) {
        try {
            plugins.remove(pluginId);
            URLClassLoader classLoader = loaders.remove(pluginId);
            if (classLoader != null) {
                classLoader.close();
            }
        } catch (Exception e) {
            log.error("插件卸载失败: {}", pluginId, e);
        }
    }

    public void executeAll(String logData) {
        plugins.forEach((id, plugin) -> {
            try {
                plugin.execute(logData, new HashMap<>());
            } catch (Exception e) {
                log.error("插件执行失败: {}", id, e);
            }
        });
    }
}
