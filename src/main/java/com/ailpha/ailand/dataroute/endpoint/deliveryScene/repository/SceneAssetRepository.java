package com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository;

import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface SceneAssetRepository extends JpaRepository<SceneAsset, String>, JpaSpecificationExecutor<SceneAsset> {

    List<SceneAsset> findAllByDeliverySceneIdIn(List<String> sceneIds);

    List<SceneAsset> findAllByOrderId(String orderId);

    SceneAsset findFirstByOrderIdAndDeliverySceneId(String orderId, String deliverySceneId);

}
