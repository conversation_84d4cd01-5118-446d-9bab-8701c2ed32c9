package com.ailpha.ailand.dataroute.endpoint.common.filters;

import cn.hutool.core.text.AntPathMatcher;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.IOpenApiCheck;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

import static com.dbapp.rest.constant.OpenApiConstant.*;
import static com.dbapp.rest.constant.OpenApiConstant.SIGN;

//@Component
@RequiredArgsConstructor
public class ConnectorAuthFilter extends OncePerRequestFilter implements IOpenApiCheck {

    private final RouterManagerRemoteService routerManagerRemoteService;
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public void checkToken(String token) {
//        HttpServletRequest request = ServletUtils.getRequest();
//        MDC.put("hubInfo", new String(Base64.getDecoder().decode(request.getHeader("hubInfo")), StandardCharsets.UTF_8));
//        CommonResult<Boolean> checkToken = routerManagerRemoteService.checkToken(token);
//        Assert.isTrue(checkToken.isSuccess() && checkToken.getData(), "非法的token");

    }

    @Override
    public void checkNonce(String token, String nonce) {

    }

    @Override
    public void checkTimestamp(String timestamp) {
        if (!StringUtils.hasLength(timestamp)) {
            throw new RestfulApiException("缺少timestamp");
        }
        if (13 != timestamp.length()) {
            throw new RestfulApiException("timestamp 格式不正确，请使用13位毫秒时间戳！");
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        if (antPathMatcher.match("/third/baseCapabilityConfig", uri)
                || antPathMatcher.match("/third/download/solution/**", uri)
                || antPathMatcher.match("/sse/receiveDataRouteMessage", uri)
        ) {
//            String routeId = request.getHeader("routerId");
            String timestamp = request.getHeader(TIMESTAMP);
            Assert.isTrue(StringUtils.hasLength(timestamp), "缺少" + TIMESTAMP);
            String token = request.getHeader(TOKEN);
            String nonce = request.getHeader(NONCE);
            Assert.isTrue(StringUtils.hasLength(nonce), "缺少" + NONCE);
            String sign = request.getHeader(SIGN);
            Assert.isTrue(StringUtils.hasLength(sign), "缺少" + SIGN);
            checkOpenApi(token, nonce, timestamp, sign, request.getQueryString());
        }
        filterChain.doFilter(request, response);
    }
}
