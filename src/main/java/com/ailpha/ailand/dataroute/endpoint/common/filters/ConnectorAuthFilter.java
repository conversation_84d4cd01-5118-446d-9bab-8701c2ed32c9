package com.ailpha.ailand.dataroute.endpoint.common.filters;

import cn.hutool.core.text.AntPathMatcher;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.InternalReturnCode;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.IOpenApiCheck;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Slf4j
@Component
@RequiredArgsConstructor
public class ConnectorAuthFilter extends OncePerRequestFilter implements IOpenApiCheck {
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
    private final ThirdService thirdService;
    private final MappingJackson2HttpMessageConverter messageConverter;

    @Override
    public void checkToken(String token) {
        Boolean checkToken = thirdService.checkToken(token);
        Assert.isTrue(checkToken, "token 已过期");
    }

    @Override
    public void checkNonce(String token, String nonce) {

    }

    @Override
    public void checkTimestamp(String timestamp) {
        if (!StringUtils.hasLength(timestamp)) {
            throw new RestfulApiException("缺少timestamp");
        }
        if (13 != timestamp.length()) {
            throw new RestfulApiException("timestamp 格式不正确，请使用13位毫秒时间戳！");
        }
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String uri = request.getRequestURI();
        if (antPathMatcher.match("/third/baseCapabilityConfig", uri)
                || antPathMatcher.match("/third/download/solution/**", uri)
                || antPathMatcher.match("/sse/receiveDataRouteMessage", uri)
                || antPathMatcher.match("/third/data-asset/**", uri)
                || antPathMatcher.match("/third/generateKey", uri)
                || antPathMatcher.match("/third/orderDelivery", uri)
                || antPathMatcher.match("/third/transfer/process", uri)
                || antPathMatcher.match("/third/companyInfo", uri)
        ) {
//            String routeId = request.getHeader("routerId");
//            String timestamp = request.getHeader(TIMESTAMP);
//            Assert.isTrue(StringUtils.hasLength(timestamp), "缺少" + TIMESTAMP);
//            String token = request.getHeader(TOKEN);
//            String nonce = request.getHeader(NONCE);
//            Assert.isTrue(StringUtils.hasLength(nonce), "缺少" + NONCE);
//            String sign = request.getHeader(SIGN);
//            Assert.isTrue(StringUtils.hasLength(sign), "缺少" + SIGN);
//            checkOpenApi(token, nonce, timestamp, sign, request.getQueryString());
            String header = request.getHeader("authorization");
            Assert.isTrue(StringUtils.hasLength(header), "缺少 authorization");
            Assert.isTrue(header.startsWith("Bearer "), "token 格式错误");
            header = header.substring(7);
            try {
                checkToken(header);
            } catch (Exception e) {
                log.error("校验token异常：", e);
                messageConverter.write(CommonResult.FAIL("token 已过期", InternalReturnCode.UNAUTHORIZED), MediaType.APPLICATION_JSON, new ServletServerHttpResponse(response));
                return;
            }
        }
        filterChain.doFilter(request, response);
    }
}
