package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DemandMapDetailDTO {
    @Schema(description = "需求标题")
    private String title;

    @Schema(description = "数据描述")
    private String description;

    @Schema(description = "预算范围")
    private String budgetRange;

    @Schema(description = "过期时间")
    private String expireDate;
    @Schema(description = "数据类型")
    String dataType;
    @Schema(description = "标签")
    List<String> tags;
    @Schema(description = "数据量级")
    String dataScale;

    @Schema(description = "期望交付方式")
    private String expectedDeliveryMethod;
    String qualityRequirements;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "需求主体名称")
    DemandSideDTO demandSide;
    @Schema(description = "是否关注")
    private Boolean collected;
    @Schema(description = "是否提交过报价")
    private Boolean committed;

}
