package com.ailpha.ailand.dataroute.endpoint.tenant.interceptor;

import cn.hutool.core.text.AntPathMatcher;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.tenant.service.TenantService;
import com.dbapp.rest.response.ErrorCode;
import com.dbapp.rest.response.ErrorResponse;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.GenericFilterBean;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TenantFilter extends GenericFilterBean {

    private static final String TENANT_HEADER = "X-Tenant-Schema";
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
    // 不需要设置租户schema的接口
    private final List<String> skipList = List.of("/callback/executor/**", "/data-asset/api/**");

    // 主库开放接口
    private final List<String> publicSchemaWhiteList = List.of("/node/simple/page", "/user/companyAdmin/register", "/third/userinfo/**");

    private final TenantService tenantService;
    private final MappingJackson2HttpMessageConverter messageConverter;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws ServletException, IOException {
        doFilter((HttpServletRequest) servletRequest, (HttpServletResponse) servletResponse, filterChain);
    }

    private void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        String tenantSchema = request.getHeader(TENANT_HEADER);
        if (log.isDebugEnabled())
            log.debug("当前请求URL：{} 指定请求头：{}", requestURI, tenantSchema);

        if (skip(requestURI)) {
            chain.doFilter(request, response);
            return;
        }
        if (StringUtils.isNotEmpty(tenantSchema)) {
            Boolean schemaExist = tenantService.schemaExist(tenantSchema);
            if (!schemaExist) {
                messageConverter.write(ErrorResponse.error(ErrorCode.InternalError).message("非法请求").build(), MediaType.APPLICATION_JSON, new ServletServerHttpResponse(response));
                return;
            }
        }

        if (antPathMatcher.match("/login", requestURI)) {
            // 特殊处理登录session存储问题 admin存储在public 其他用户存储在对应租户
            try {
                String username = request.getParameter("username");
                String schema = request.getParameter("schema");
                // 根据用户名判断租户
                if ("admin".equals(username)) {
                    TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
                } else {
                    // 可以根据用户名查询对应的租户，或者使用默认租户
                    TenantContext.setCurrentTenant(schema);
                }
                if (log.isTraceEnabled())
                    log.trace("登录用户[{}]设置租户上下文成功", schema);

            } catch (Exception e) {
                log.error("解析登录请求参数异常：", e);
                // 不要返回false，避免阻止请求继续处理
                // 让请求继续，后续的认证逻辑会处理登录失败
            }
        } else if (isPublicSchemaWhiteListUrl(requestURI)) {
            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            if (log.isTraceEnabled())
                log.trace("白名单接口[{}]，schema直接设置为主库", requestURI);
        } else {
            // 登录成功后，前端会把指定schema方到请求头中
            if (StringUtils.isNotEmpty(tenantSchema))
                TenantContext.setCurrentTenant(tenantSchema);
            else {
                // 这里按道理是只有超管是没有请求头 或者可以让前端默认把schema加上，不应该没有接口是没有schema的
                TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            }
            if (log.isTraceEnabled())
                log.trace("读取请求头schema并设置租户[{}]上下文成功", tenantSchema);
        }
        // 继续过滤器链
        chain.doFilter(request, response);
    }

    private boolean isPublicSchemaWhiteListUrl(String url) {
        return publicSchemaWhiteList.stream().anyMatch(u -> antPathMatcher.match(u, url));
    }

    private boolean skip(String url) {
        return skipList.stream().anyMatch(u -> antPathMatcher.match(u, url));
    }

}