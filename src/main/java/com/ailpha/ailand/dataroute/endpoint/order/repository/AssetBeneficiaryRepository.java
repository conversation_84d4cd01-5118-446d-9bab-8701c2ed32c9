package com.ailpha.ailand.dataroute.endpoint.order.repository;

import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface AssetBeneficiaryRepository extends JpaRepository<AssetBeneficiaryRel, String>, JpaSpecificationExecutor<AssetBeneficiaryRel> {

    AssetBeneficiaryRel findFirstByOrderId(String orderId);

}
