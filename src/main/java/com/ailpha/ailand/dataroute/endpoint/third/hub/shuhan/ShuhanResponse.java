package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShuhanResponse<T> {
    /**
     * {
     * "code": 0,
     * "data": true,
     * "msg": "ok",
     * "path": null,
     * "extra": null,
     * "timestamp": "1731936728004",
     * "success": true,
     * "isSuccess": true,
     * "isFail": false
     * }
     */
    int code;
    T data;
    String msg;
    String timestamp;
    boolean success;
}
