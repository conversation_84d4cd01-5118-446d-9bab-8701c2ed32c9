package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.InternalReturnCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShuhanResponse<T> {
    /**
     * {
     * "code": 0,
     * "data": true,
     * "msg": "ok",
     * "path": null,
     * "extra": null,
     * "timestamp": "1731936728004",
     * "success": true,
     * "isSuccess": true,
     * "isFail": false
     * }
     */
    int statusCode;
    T data;
    String message;
    String timestamp;
    boolean success;

    public Boolean isSuccess() {
        return InternalReturnCode.SUCCESS.getCode().equals(this.statusCode);
    }

    public Boolean unauthorized() {
        return InternalReturnCode.UNAUTHORIZED.getCode().equals(this.statusCode);
    }
}
