package com.ailpha.ailand.dataroute.endpoint.third.constants;

import org.apache.commons.lang3.StringUtils;

import java.io.File;

public class DatasourceConstants {

    public static String WEB_HOME = System.getProperty("user.dir");

    public static String DATASOURCE_PATH = StringUtils.join(new String[]{
            WEB_HOME, "conf", "datasource.json"}, File.separator);

    public static String DATASOURCE_FILE_TYPE = StringUtils.join(new String[]{
            WEB_HOME, "conf", "fileType.json"}, File.separator);

    public static String DATASOURCE_DEBUG_DATA_FILE = StringUtils.join(new String[]{
            WEB_HOME, "datasource", "debugData"}, File.separator);

    public static String DATASOURCE_KERBEROS_KEYTAB_FILE = StringUtils.join(new String[]{
            WEB_HOME, "kerberos", "keytabFile"}, File.separator);

    public static String DATASOURCE_KERBEROS_CONF_FILE = StringUtils.join(new String[]{
            WEB_HOME, "kerberos", "conf"}, File.separator);
}
