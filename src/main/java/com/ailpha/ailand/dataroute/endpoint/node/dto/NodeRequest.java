package com.ailpha.ailand.dataroute.endpoint.node.dto;

import com.ailpha.ailand.dataroute.endpoint.common.enums.NetworkType;
import com.ailpha.ailand.dataroute.endpoint.node.LinkType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "节点请求")
public class NodeRequest {
    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "节点类型")
    private String nodeType;

    LinkType linkType;

    ImportFile importFile;

    ManualInput manualInput;
    @Data
    public static class ImportFile {
        String fileId;
    }

    @Data
    public static class ManualInput {
        String url;
        String publicKey;
        String nodeId;
        NetworkType networkType;
        String ip;
    }
}