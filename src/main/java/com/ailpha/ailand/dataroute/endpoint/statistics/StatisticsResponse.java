package com.ailpha.ailand.dataroute.endpoint.statistics;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsResponse {
    @Schema(description = "总览")
    Overview overview;

    @Schema(description = "数据资源统计")
    StatisticResource resourceStatistics;

    @Schema(description = "数据产品统计")
    StatisticProduct productStatistics;

    @Schema(description = "买方订单")
    List<UserOrderCount> buyerOrderTop10;
    @Schema(description = "卖方订单")
    List<UserOrderCount> sellerOrderTop10;
    List<DataAssetGroupByAccount> dataAsset;
    @Schema(description = "使用场景")
    List<UseScene> useScene;

    @Data
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticResource {
        @Schema(description = "图片资源数量")
        Integer pictureCount;
        @Schema(description = "数据集资源数量")
        Integer dataAssetCount;
        @Schema(description = "文本资源数量")
        Integer textCount;
        @Schema(description = "用户资源数统计")
        Map<String, Long> userResourceCount;
    }

    @Data
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticProduct {
        @Schema(description = "图片产品数量")
        Integer pictureCount;
        @Schema(description = "数据集产品数量")
        Integer dataAssetCount;
        @Schema(description = "文本产品数量")
        Integer textCount;
        @Schema(description = "模型产品数量")
        Integer modelCount;
        @Schema(description = "交付方式数量")
        Map<String, Integer> deliveryMethodCount = new HashMap<>();
        @Schema(description = "交付场景数量")
        Map<DeliveryMode, Integer> deliveryModeCount = new HashMap<>();
        @Schema(description = "用户登记发布数量")
        Map<String, Map<String, Long>> userRegistAndPublishCount = new HashMap<>();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Overview {
        @Schema(description = "用户")
        Integer userCount;
        @Schema(description = "数据资源登记数量")
        Integer dataResourceRegistCount;
        @Schema(description = "数据产品登记数量")
        Integer dataProductRegistCount;
        @Schema(description = "数据产品上架数量")
        Integer dataProductPublishCount;

        @Schema(description = "订单数量")
        Long orderCount;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class UseScene implements Serializable {
        @Schema(description = "创建用户名")
        String createUsername;
        @Schema(description = "使用场景统计")
        List<UseSceneStatistic> useSceneStatistic;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class UseSceneStatistic implements Serializable {
        @Schema(description = "使用场景")
        DeliveryType deliveryType;
        @Schema(description = "数量")
        Long count;
    }
}
