package com.ailpha.ailand.dataroute.endpoint.statistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StatisticsResponse {
    @Schema(description = "总览")
    Overview overview;
    @Schema(description = "买方订单")
    List<UserOrderCount> buyerOrderTop10;
    @Schema(description = "卖方订单")
    List<UserOrderCount> sellerOrderTop10;
    @Schema(description = "场景交付统计")
    DeliveryStatistic delivery;
    List<DataAssetGroupByAccount> dataAsset;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Overview {
        @Schema(description = "用户")
        Integer userCount;
        @Schema(description = "数据资产")
        Integer dataAssetCount;
        @Schema(description = "场景交付")
        Integer dataAssetUseCount;
        @Schema(description = "订单")
        Integer orderCount;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeliveryStatistic {
        @Schema(description = "场景交付-根据交付类型分组")
        List<GroupByDeliveryModeVO> groupByDeliveryMode;
        @Schema(description = "场景交付-根据数据资产分组")
        List<GroupByDataAssetVO> groupByDataAsset;
    }
}
