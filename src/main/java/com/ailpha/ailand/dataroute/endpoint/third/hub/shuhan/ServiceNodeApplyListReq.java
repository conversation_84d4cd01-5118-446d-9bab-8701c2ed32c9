package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/4/3
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeApplyListReq extends Page implements Serializable {

    @Schema(description = "业务节点登记名称")
    String entryName;

    @NotEmpty(message = "业务节点标识编码不能为空")
    @Schema(description = "业务节点标识编码")
    String serviceNodeId;

    @Schema(description = "状态：APPLY（待审批）APPROVED（已通过）REJECTED（已拒绝）")
    String processStatus;
}
