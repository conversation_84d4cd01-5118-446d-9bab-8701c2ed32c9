package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据探查任务响应对象")
public class HengNaoAgentTaskDTO {
    @Schema(description = "任务ID")
    String taskId;

    @Schema(description = "文件名")
    String filename;

    String pdfName;

    @Schema(description = "状态 1-执行中，2-成功，3-取消，4-失败")
    int status;
    @Schema(description = "任务执行结果")
    Object result;
}
