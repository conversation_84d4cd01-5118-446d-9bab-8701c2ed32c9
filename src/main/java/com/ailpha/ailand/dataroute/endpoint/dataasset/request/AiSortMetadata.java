package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiSortMetadata implements Serializable {
    @Schema(description = "数据源id")
    Long sourceId;
    @Schema(description = "表id")
    Long id;
    @Schema(description = "表名称")
    String tableName;
    @Schema(description = "表描述")
    String tableDesc;
    @Schema(description = "schemaName")
    String schemaName;
}
