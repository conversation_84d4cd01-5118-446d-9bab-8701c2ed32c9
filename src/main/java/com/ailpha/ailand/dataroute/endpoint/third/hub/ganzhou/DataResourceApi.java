package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou;

import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataResourceInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataProductPublishVO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

public interface DataResourceApi {

    /**
     * 资源目录登记接口
     *
     * @param dataResourceRegistry 数据资源基本信息
     * @return
     */
    @PostExchange("/open/resource/dataResourceRegistry")
    ResponseWrapper<String> dataResourceRegistry(@RequestBody DataResourceRegistry dataResourceRegistry);

    /**
     * 资源目录更新接口
     *
     * @param dataResourceRegistryUpdate 数据产品基本信息
     * @return
     */
    @PostExchange("/open/resource/dataResourceRegistryUpdate")
    ResponseWrapper<DataResourceInfo> dataResourceRegistryUpdate(@RequestBody DataResourceRegistryUpdate dataResourceRegistryUpdate);

    /**
     * 资源目录撤销接口
     *
     * @param dataResourceRegistryRevoke 数据标识
     * @return 执行结果状态：0 数据产品登记撤销成功 1 执行失败
     */
    @PostExchange("/open/resource/dataResourceRegistryRevoke")
    ResponseWrapper<Void> dataResourceRegistryRevoke(@RequestBody DataResourceRegistryRevoke dataResourceRegistryRevoke);

    /**
     * 资源发布接口
     *
     * @param publishProduct 数据资源发布信息
     * @return
     */
    @PostExchange("/open/resource/publishProduct")
    ResponseWrapper<DataProductPublishVO> publishResource(@RequestBody PublishProduct publishProduct);

    /**
     * 资源目录列表接口
     *
     * @param catalogQuery 数据目录查询参数
     */
    @PostExchange("/open/resource/dataCatalogQuery")
    ResponseListWrapper<DataResourceInfo> dataCatalogQuery(@RequestBody DataResourceCatalogQuery catalogQuery);

    /**
     * 数据目录详情接口
     *
     * @param getResourceInfo 数据目录详情请求参数
     */
    @PostExchange("/open/resource/getByResourceInfo")
    ResponseWrapper<DataResourceInfo> getByResourceInfo(@RequestBody GetByResourceInfo getResourceInfo);
}
