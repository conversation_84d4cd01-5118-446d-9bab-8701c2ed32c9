package com.ailpha.ailand.dataroute.endpoint.upgrade.repository;

import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.UpgradeTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * <AUTHOR>
 * 2025/6/9
 */
public interface UpgradeTaskRepository extends JpaRepository<UpgradeTask, String>, QuerydslPredicateExecutor<UpgradeTask> {

    UpgradeTask findTopByOrderByCreateTimeDesc();
}
