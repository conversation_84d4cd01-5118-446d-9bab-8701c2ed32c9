package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetResp {

    String orderId;

    @Schema(description = "资产id", requiredMode = Schema.RequiredMode.REQUIRED)
    String assetId;
    @Schema(description = "资产对应连接器ID")
    String routerId;

    @Schema(description = "资产类型")
    AssetType type;

    @Schema(description = "过期时间")
    String expireDate;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "资源提供方")
    String provider;

    @Schema(description = "数源单位")
    String providerOrg;

    @Schema(description = "行业分类")
    String industry;

    @Schema(description = "敏感等级")
    String sensitiveLevel;

    @Schema(description = "标签")
    String tags;

    @Schema(description = "资源摘要")
    String summary;

    @Schema(description = "数据类型")
    String dataType;

    @Schema(description = "交付场景")
    String deliveryType;

    @Schema(description = "交付方式 国标 01、02、03")
    String deliveryMethod;

    @Schema(description = "计量方式")
    private String meteringWay;

    @Schema(description = "计费方式")
    private String chargingWay;

    @Schema(description = "使用次数上限")
    BigInteger allowance;
    @Schema(description = "数源方")
    String userAccount;

    @Schema(description = "订单extend")
    String extend;

    String dataProductPlatformId;

    String price;

    String source;

    String productionType;

    Boolean isLLM;

    String dataType1;
}
