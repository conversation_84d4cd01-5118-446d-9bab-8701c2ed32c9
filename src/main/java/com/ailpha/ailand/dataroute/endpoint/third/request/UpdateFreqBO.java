package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class UpdateFreqBO {

    @Schema(description = "更新频率：DAY（每日更新）WEEK（每周更新）MONTH（每月更新）SINGLE（单次更新）HOUR（按小时更新）MULTI（多次更新）", example = "DAY")
    private SchedulerPeriodEnum updateFreq = SchedulerPeriodEnum.DAY;

    @Schema(description = "选中的日期，星期一到星期日为1-7，月份日期1-31", example = "1")
    private String selectDate = "1";

    @Schema(description = "几点执行，默认0", example = "0")
    private String selectHour = "0";

    public UpdateFreqBO() {
    }

    public boolean supportUpdateBeforeRunTask() {
        return updateFreq != null && updateFreq == SchedulerPeriodEnum.MULTI;
    }

    public String getSelectDate() {
        return StringUtils.isBlank(selectDate) || "null".equals(selectDate) ? "1" : selectDate;
    }

    public String getSelectHour() {
        return StringUtils.isBlank(selectHour) || "null".equals(selectDate) ? "0" : selectHour;
    }

    public UpdateFreqBO(SchedulerPeriodEnum updateFreq, String selectDate, String selectHour) {
        this.updateFreq = updateFreq;
        this.selectDate = selectDate;
        this.selectHour = selectHour;
    }

    @JsonIgnore
    public String getCronExpr() {
        if (updateFreq == null || SchedulerPeriodEnum.SINGLE.equals(updateFreq) || StringUtils.isBlank(selectDate)) {
            return null;
        }
        String dateCron;
        String weekCron;
        switch (updateFreq) {
            case MONTH:
                dateCron = selectDate;
                weekCron = "?";
                break;
            case WEEK:
                dateCron = "?";
                weekCron = (Integer.parseInt(selectDate) + 1) % 8 + "";
                break;
            case DAY:
                dateCron = "?";
                weekCron = "*";
                break;
            default:
                return null;
        }
        return String.format("00 00 %2d %s * %s", Integer.valueOf(selectHour), dateCron, weekCron);
    }


}
