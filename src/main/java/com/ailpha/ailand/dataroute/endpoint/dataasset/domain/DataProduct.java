package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;


import com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Expression;
import lombok.*;
import lombok.experimental.FieldDefaults;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode.API;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode.FILE_DOWNLOAD;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_data_product")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProduct {
    @Id
    @UUID32
    String id;
    /**
     * 数据资源全局（连接器空间）唯一标识
     */
    @Column(name = "data_product_platform_id")
    String dataProductPlatformId;
    /**
     * 连接器ID
     */
    @Column(name = "platform_id")
    String platformId;
    /**
     * 连接器类型：0 标准型 1 全功能型
     */
    @Column(name = "platform_type")
    int platformType;
    /**
     * 数据资源名称
     */
    @Column(name = "data_product_name")
    String dataProductName;
    /**
     * 数据资源描述
     */
    String description;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 数据接入方式：API,DATABASE,FILE
     */
    @Column(name = "source_type")
    SourceType sourceType;
    /**
     * 数据资源大小
     */
    Long capacity;
    /**
     * 敏感等级
     */
    @Column(name = "sensitive_level")
    String sensitiveLevel;
    /**
     * 更新频率：数源的更新迭代不代表接入平台的数据是否更新
     */
    @Column(name = "update_frequency")
    String updateFrequency;
    /**
     * 登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销
     */
    @Column(name = "item_status")
    String itemStatus;
    /**
     * @see PushStatus
     */
    @Deprecated
    @Column(name = "push_status")
    String pushStatus;
    /**
     * 是否已删除
     */
    @Builder.Default
    @Column(name = "is_delete")
    Boolean isDelete = false;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    String userId;
    /**
     * 用户名
     */
    String username;

    @Column(name = "create_time")
    Date createTime;

    @Column(name = "update_time")
    Date updateTime;
    /**
     * 资源提供方：默认填入连接器企业认证的企业类型
     * 数源单位：连接器企业认证的企业名称
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "provider", columnDefinition = "json")
    ProviderExt provider;

    /**
     * 数据资源扩展信息:
     * dataCoverage 数据覆盖范围
     * dataCoverageTimeStart 数据覆盖周期开始时间
     * dataCoverageTimeEnd 数据覆盖周期结束时间
     * apiQueryWay (接入方式为API)API查询方式,可用值:REALTIME,OFFLINE
     * dataType 数据类型(接入方式为FILE),可用值:STRUCTURED,UNSTRUCTURED,MODEL
     * debugDataSource 调试数据来源: {@link DebugDataSourceEnum}
     * mpcResultId 产品来源: 平台生成(MPC) 外部生成的结果集id
     * 等信息
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_ext", columnDefinition = "json")
    DataProductExt dataExt;

    public static class DataProductExt extends DataAssetExt {

    }

    /**
     * 交付信息扩展字段：
     * deliveryModes 交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC
     * mpcPurpose MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "delivery_ext", columnDefinition = "json")
    DataProductDeliveryExt deliveryExt;

    @Data
    public static class DataProductDeliveryExt extends DataAssetDeliveryExt {

        // 交易中心需要字段
        String companyId;

        // 产品类型 01 数据集", "02 API产品", "03 数据应用", "04 数据报告", "05 其他
        String type;

        String mpcOpenAPIId;

    }

    /**
     * 更新产品中文名称
     *
     * @param productNameCN 新的产品中文名称
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateProductNameCNTo(String productNameCN) {
        if (!StringUtils.equals(this.getDataExt().getAssetNameCN(), productNameCN)) {
            this.getDataExt().setAssetNameCN(productNameCN);
        }
        return this;
    }

    /**
     * 更新数据产品类型
     *
     * @param type 新的数据产品类型
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateTypeTo(String type) {
        if (StringUtils.isNotBlank(type) && !StringUtils.equals(this.getDataExt().getType(), type)) {
            this.getDataExt().setType(type);
        }
        if (StringUtils.isNotBlank(type) && this.getDeliveryExt() != null && !StringUtils.equals(this.getDeliveryExt().getType(), type)) {
            this.getDeliveryExt().setType(type);
        }
        return this;
    }

    /**
     * 更新数据覆盖周期的开始和结束时间
     *
     * @param dataCoverageTimeStart 数据覆盖周期开始时间
     * @param dataCoverageTimeEnd   数据覆盖周期结束时间
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataCoverageTime(String dataCoverageTimeStart, String dataCoverageTimeEnd) {
        if (dataExt == null) {
            dataExt = new DataProductExt();
        }
        if (!StringUtils.equals(dataCoverageTimeStart, dataExt.getDataCoverageTimeStart())) {
            dataExt.setDataCoverageTimeStart(dataCoverageTimeStart);
        }
        if (!StringUtils.equals(dataCoverageTimeEnd, dataExt.getDataCoverageTimeEnd())) {
            dataExt.setDataCoverageTimeEnd(dataCoverageTimeEnd);
        }
        return this;
    }

    /**
     * 更新行业分类
     *
     * @param industry  新的行业分类
     * @param industry1 新的行业分类(前端回显用)
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateIndustryTo(String industry, String industry1) {
        if (StringUtils.isNotBlank(industry) && !StringUtils.equals(this.getIndustry(), industry)) {
            this.setIndustry(industry);
        }
        if (StringUtils.isNotBlank(industry1) && !StringUtils.equals(this.getDataExt().getIndustry1(), industry1)) {
            this.getDataExt().setIndustry1(industry1);
        }
        return this;
    }

    /**
     * 更新地域分类
     *
     * @param region  新的地域分类
     * @param region1 新的地域分类(前端回显用)
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateRegionTo(String region, String region1) {
        if (!StringUtils.equals(this.getDataExt().getRegion(), region)) {
            this.getDataExt().setRegion(region);
        }
        if (!StringUtils.equals(this.getDataExt().getRegion1(), region1)) {
            this.getDataExt().setRegion1(region1);
        }
        return this;
    }

    /**
     * 更新是否涉及个人信息
     *
     * @param personalInformation 新的是否涉及个人信息标识
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updatePersonalInformationTo(String personalInformation) {
        if (StringUtils.isNotBlank(personalInformation) && !StringUtils.equals(this.getDataExt().getPersonalInformation(), personalInformation)) {
            this.getDataExt().setPersonalInformation(personalInformation);
        }
        return this;
    }

    /**
     * 更新产品简介
     *
     * @param description 新的产品简介
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDescriptionTo(String description) {
        if (StringUtils.isNotBlank(description) && !StringUtils.equals(this.getDescription(), description)) {
            this.setDescription(description);
        }
        return this;
    }

    /**
     * 更新产品来源
     *
     * @param source 新的产品来源
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateSourceTo(String source) {
        if (source != null && !StringUtils.equals(this.getDataExt().getSource(), source)) {
            this.getDataExt().setSource(source);
        }
        return this;
    }

    /**
     * 更新数据规模
     *
     * @param scale 新的数据规模
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateScaleTo(String scale) {
        if (scale != null) {
            try {
                scale = StringUtils.isEmpty(scale) ? null : scale.trim();
                this.setCapacity(scale == null ? null : Long.parseLong(scale));
            } catch (Exception ignore) {
            }
        } else {
            this.setCapacity(null);
        }
        return this;
    }

    /**
     * 更新更新频率
     *
     * @param updateFrequency 新的更新频率
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateFrequencyTo(String updateFrequency) {
        if (StringUtils.isNotBlank(updateFrequency) && !StringUtils.equals(this.getUpdateFrequency(), updateFrequency)) {
            this.setUpdateFrequency(updateFrequency);
        }
        return this;
    }

    /**
     * 更新敏感等级
     *
     * @param sensitiveLevel 新的敏感等级
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateSensitiveLevelTo(String sensitiveLevel) {
        if (StringUtils.isNotBlank(sensitiveLevel) && !StringUtils.equals(this.getSensitiveLevel(), sensitiveLevel)) {
            this.setSensitiveLevel(sensitiveLevel);
        }
        return this;
    }

    /**
     * 更新使用限制
     *
     * @param limitations 新的使用限制
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateLimitationsTo(String limitations) {
        if (StringUtils.isNotBlank(limitations) && !StringUtils.equals(this.getDeliveryExt().getLimitations(), limitations)) {
            this.getDeliveryExt().setLimitations(limitations);
        }
        return this;
    }

    /**
     * 更新授权使用标识
     *
     * @param authorize 新的授权使用标识
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateAuthorizeTo(String authorize) {
        if (StringUtils.isNotBlank(authorize) && !StringUtils.equals(this.getDeliveryExt().getAuthorize(), authorize)) {
            this.getDeliveryExt().setAuthorize(authorize);
        }
        return this;
    }

    /**
     * 更新是否允许二次加工标识
     *
     * @param isSecondaryProcessed 新的是否允许二次加工标识
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateIsSecondaryProcessedTo(String isSecondaryProcessed) {
        if (StringUtils.isNotBlank(isSecondaryProcessed) && !StringUtils.equals(this.getDeliveryExt().getIsSecondaryProcessed(), isSecondaryProcessed)) {
            this.getDeliveryExt().setIsSecondaryProcessed(isSecondaryProcessed);
        }
        return this;
    }

    /**
     * 更新产品关联数据资源统一标识列表
     *
     * @param platformResourceId 新的产品关联数据资源统一标识列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateResourceIdTo(String platformResourceId) {
        if (StringUtils.isNotBlank(platformResourceId) && !StringUtils.equals(this.getDataExt().getPlatformResourceId(), platformResourceId)) {
            this.getDataExt().setPlatformResourceId(platformResourceId);
        }
        return this;
    }

    /**
     * 更新产品血缘
     *
     * @param lineage 新的产品血缘
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateLineageTo(String lineage) {
        if (StringUtils.isNotBlank(lineage) && !StringUtils.equals(this.getDataExt().getLineage(), lineage)) {
            this.getDataExt().setLineage(lineage);
        }
        return this;
    }

    public DataProduct updateOtherTo(String other) {
        if (other != null && !other.equals(this.getDataExt().getOther())) {
            this.getDataExt().setOther(other);
        }
        return this;
    }

    /**
     * 更新数据样例
     *
     * @param qualificationDoc dataSampleAttach 新的数据样例
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataSampleAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (qualificationDoc.getDataSampleAttach() != null && !StringUtils.equals(this.getDataExt().getQualificationDoc().getDataSampleAttach(), qualificationDoc.getDataSampleAttach())) {
            this.getDataExt().getQualificationDoc().setDataSampleAttach(qualificationDoc.getDataSampleAttach());
        }
        return this;
    }

    /**
     * 更新合法合规声明
     *
     * @param qualificationDoc complianceAndLegalStatementAttach 新的合法合规声明
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateComplianceAndLegalStatementAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (StringUtils.isNotBlank(qualificationDoc.getComplianceAndLegalStatementAttach()) && !StringUtils.equals(this.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach(), qualificationDoc.getComplianceAndLegalStatementAttach())) {
            this.getDataExt().getQualificationDoc().setComplianceAndLegalStatementAttach(qualificationDoc.getComplianceAndLegalStatementAttach());
        }
        return this;
    }

    /**
     * 更新数据来源声明
     *
     * @param qualificationDoc dataSourceStatementAttach 新的数据来源声明
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataSourceStatementAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (StringUtils.isNotBlank(qualificationDoc.getDataSourceStatementAttach()) && !StringUtils.equals(this.getDataExt().getQualificationDoc().getDataSourceStatementAttach(), qualificationDoc.getDataSourceStatementAttach())) {
            this.getDataExt().getQualificationDoc().setDataSourceStatementAttach(qualificationDoc.getDataSourceStatementAttach());
        }
        return this;
    }

    /**
     * 更新安全分类分级
     *
     * @param qualificationDoc safeLevelAttach 新的安全分类分级
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateSafeLevelAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (qualificationDoc.getSafeLevelAttach() != null && !StringUtils.equals(this.getDataExt().getQualificationDoc().getSafeLevelAttach(), qualificationDoc.getSafeLevelAttach())) {
            this.getDataExt().getQualificationDoc().setSafeLevelAttach(qualificationDoc.getSafeLevelAttach());
        }
        return this;
    }

    /**
     * 更新数据质量产品价值评估报告
     *
     * @param qualificationDoc evaluationReportAttach 新的数据质量产品价值评估报告
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateEvaluationReportAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (qualificationDoc.getEvaluationReportAttach() != null && !StringUtils.equals(this.getDataExt().getQualificationDoc().getEvaluationReportAttach(), qualificationDoc.getEvaluationReportAttach())) {
            this.getDataExt().getQualificationDoc().setEvaluationReportAttach(qualificationDoc.getEvaluationReportAttach());
        }
        return this;
    }

    /**
     * 更新合规自查手册
     *
     * @param qualificationDoc complianceSelfCheckManualAttach 新的合规自查手册
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateComplianceSelfCheckManualAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (qualificationDoc.getComplianceSelfCheckManualAttach() != null && !StringUtils.equals(this.getDataExt().getQualificationDoc().getComplianceSelfCheckManualAttach(), qualificationDoc.getComplianceSelfCheckManualAttach())) {
            this.getDataExt().getQualificationDoc().setComplianceSelfCheckManualAttach(qualificationDoc.getComplianceSelfCheckManualAttach());
        }
        return this;
    }

    /**
     * 更新其他
     *
     * @param qualificationDoc otherAttach 新的其他
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateOtherAttach(QualificationDoc qualificationDoc) {
        if (this.getDataExt().getQualificationDoc() == null) {
            this.getDataExt().setQualificationDoc(new QualificationDoc());
        }
        if (qualificationDoc.getOtherAttach() != null && !StringUtils.equals(this.getDataExt().getQualificationDoc().getOtherAttach(), qualificationDoc.getOtherAttach())) {
            this.getDataExt().getQualificationDoc().setOtherAttach(qualificationDoc.getOtherAttach());
        }
        return this;
    }

    /**
     * 更新发布业务节点
     *
     * @param serviceNodes 新的发布业务节点列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateServiceNodesTo(List<ServiceNodeApplyListVO> serviceNodes) {
        boolean hasSameElements = false;
        if (serviceNodes != null && this.getDeliveryExt().getServiceNodes() != null) {
            for (ServiceNodeApplyListVO node : serviceNodes) {
                Assert.hasText(node.getServiceNodeUrl(), node.getServiceNodeName() + " url 未填写");
                if (this.getDeliveryExt().getServiceNodes().contains(node)) {
                    hasSameElements = true;
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(serviceNodes) && !hasSameElements) {
            this.getDeliveryExt().setServiceNodes(serviceNodes);
        }
        return this;
    }

    /**
     * 更新计费方式
     *
     * @param billingMethod 新的计费方式
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateBillingMethodTo(String billingMethod) {
        if (!StringUtils.equals(this.getDeliveryExt().getBillingMethod(), billingMethod)) {
            this.getDeliveryExt().setBillingMethod(billingMethod);
        }
        return this;
    }

    /**
     * 更新购买单位
     *
     * @param purchaseUnit 新的购买单位
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updatePurchaseUnitTo(String purchaseUnit) {
        if (!StringUtils.equals(this.getDeliveryExt().getPurchaseUnit(), purchaseUnit)) {
            this.getDeliveryExt().setPurchaseUnit(purchaseUnit);
        }
        return this;
    }

    /**
     * 更新单价
     *
     * @param price 新的单价
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updatePriceTo(String price) {
        if (!StringUtils.equals(this.getDeliveryExt().getPrice(), price)) {
            this.getDeliveryExt().setPrice(price);
        }
        return this;
    }

    /**
     * 更新交付方式
     *
     * @param deliveryMode 新的交付方式
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
//    public CatalogQueryDataProduct updateDeliveryMethodTo(String deliveryMethod) {
//        if (!StringUtils.equals(this.getDeliveryExt().getDeliveryMethod(), deliveryMethod)) {
//            this.getDeliveryExt().setDeliveryMethod(deliveryMethod);
//        }
//        return this;
//    }
    public DataProduct updateDeliveryModeTo(DeliveryMode deliveryMode) {
        if (deliveryMode != null) {
            this.getDeliveryExt().setDeliveryMode(deliveryMode);
        }
        return this;
    }

    public DataProduct updateDeliveryModesTo(List<DeliveryMode> deliveryModes) {
        boolean hasSameElements = false;
        if (deliveryModes != null && this.getDeliveryExt().getDeliveryModes() != null) {
            for (DeliveryMode mode : deliveryModes) {
                if (this.getDeliveryExt().getDeliveryModes().contains(mode)) {
                    hasSameElements = true;
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(deliveryModes) && !hasSameElements) {
            this.getDeliveryExt().setDeliveryModes(deliveryModes);
        }
        return this;
    }

    /**
     * 更新数据类型
     *
     * @param dataType 新的数据类型
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataTypeTo(DataType dataType) {
        if (dataType != null && !Objects.equals(this.getDataExt().getDataType(), dataType)) {
            this.getDataExt().setDataType(dataType);
        }
        return this;
    }

    /**
     * 更新数据类型1
     *
     * @param dataType1 新的数据类型1
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataType1To(String dataType1) {
        if (StringUtils.isNotBlank(dataType1) && !StringUtils.equals(this.getDataExt().getDataType1(), dataType1)) {
            this.getDataExt().setDataType1(dataType1);
        }
        return this;
    }

    /**
     * 更新数据接入方式
     *
     * @param accessWay 新的数据接入方式
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateAccessWayTo(SourceType accessWay) {
        if (accessWay != null && !Objects.equals(this.getSourceType(), accessWay)) {
            this.setSourceType(accessWay);
        }
        return this;
    }

    /**
     * 更新API查询方式
     *
     * @param apiQueryWay 新的API查询方式
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateAPIQueryWayTo(APIQueryWay apiQueryWay) {
        if (apiQueryWay != null && !Objects.equals(this.getDataExt().getApiQueryWay(), apiQueryWay)) {
            this.getDataExt().setApiQueryWay(apiQueryWay);
        }
        return this;
    }

    /**
     * 更新MPC用途
     *
     * @param mpcPurpose 新的MPC用途列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateMPCPurposeTo(List<MPCPurpose> mpcPurpose) {
        boolean hasSameElements = false;
        if (mpcPurpose != null && this.getDeliveryExt().getMpcPurpose() != null) {
            for (MPCPurpose purpose : mpcPurpose) {
                if (this.getDeliveryExt().getMpcPurpose().contains(purpose)) {
                    hasSameElements = true;
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(mpcPurpose) && !hasSameElements) {
            this.getDeliveryExt().setMpcPurpose(mpcPurpose);
        }
        return this;
    }

    /**
     * 更新调试数据来源
     *
     * @param debugDataSource 新的调试数据来源
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDebugDataSourceTo(DebugDataSourceEnum debugDataSource) {
        if (debugDataSource != null && !Objects.equals(this.getDataExt().getDebugDataSource(), debugDataSource)) {
            this.getDataExt().setDebugDataSource(debugDataSource);
        }
        return this;
    }

    /**
     * 更新数据结构
     *
     * @param dataSchema 新的数据结构列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDataSchemaTo(List<DataSchemaBO> dataSchema) {
        if (dataSchema == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(dataSchema).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getDataSchema()))) {
            this.getDataExt().setDataSchema(dataSchema);
        }
        return this;
    }

    /**
     * 更新调试数据分隔符
     *
     * @param separator 新的调试数据分隔符
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateSeparatorTo(String separator) {
        if (StringUtils.isNotBlank(separator) && !StringUtils.equals(this.getDataExt().getSeparator(), separator)) {
            this.getDataExt().setSeparator(separator);
        }
        return this;
    }

    public DataProduct updateCompanyIdTo(String companyId) {
        if (StringUtils.isNotBlank(companyId) && !StringUtils.equals(this.getDeliveryExt().getCompanyId(), companyId)) {
            this.getDeliveryExt().setCompanyId(companyId);
        }
        return this;
    }

    public DataProduct updateMpcOpenAPIIdIdTo(String mpcOpenAPIId) {
        if (StringUtils.isNotBlank(mpcOpenAPIId) && !StringUtils.equals(this.getDeliveryExt().getMpcOpenAPIId(), mpcOpenAPIId)) {
            this.getDeliveryExt().setMpcOpenAPIId(mpcOpenAPIId);
        }
        return this;
    }

    /**
     * 更新调试数据是否包含表头
     *
     * @param hasHeader 新的调试数据是否包含表头标识
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateHasHeaderTo(Integer hasHeader) {
        if (hasHeader != null && !Objects.equals(this.getDataExt().getHasHeader(), hasHeader)) {
            this.getDataExt().setHasHeader(hasHeader);
        }
        return this;
    }

    /**
     * 更新API数据资产元数据
     *
     * @param apiSourceMetadata 新的API数据资产元数据
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateAPISourceMetadataTo(APISourceMetadata apiSourceMetadata) {
        if (apiSourceMetadata == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(apiSourceMetadata).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getApiSourceMetadata()))) {
            this.getDataExt().setApiSourceMetadata(apiSourceMetadata);
        }
        return this;
    }

    /**
     * 更新文件数据资产元数据
     *
     * @param fileSourceMetadata 新的文件数据资产元数据
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateFileSourceMetadataTo(FileSourceMetadata fileSourceMetadata) {
        if (fileSourceMetadata == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(fileSourceMetadata).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getFileSourceMetadata()))) {
            this.getDataExt().setFileSourceMetadata(fileSourceMetadata);
        }
        return this;
    }

    /**
     * 更新数据库数据资产元数据
     *
     * @param databaseSourceMetadata 新的数据库数据资产元数据
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateDatabaseSourceMetadataTo(DatabaseSourceMetadata databaseSourceMetadata) {
        if (databaseSourceMetadata == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(databaseSourceMetadata).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getDatabaseSourceMetadata()))) {
            this.getDataExt().setDatabaseSourceMetadata(databaseSourceMetadata);
        }
        return this;
    }

    /**
     * 更新AiSort元数据
     *
     * @param aiSortMetadata 新的AiSort元数据
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateAiSortMetadataTo(AiSortMetadata aiSortMetadata) {
        if (aiSortMetadata == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(aiSortMetadata).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getAiSortMetadata()))) {
            this.getDataExt().setAiSortMetadata(aiSortMetadata);
        }
        return this;
    }

    /**
     * 更新绑定交易所插件
     *
     * @param exchangePluginIds 新的绑定交易所插件列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateExchangePluginIdsTo(List<Long> exchangePluginIds) {
        if (!Objects.equals(this.getDataExt().getExchangePluginIds(), exchangePluginIds)) {
            this.getDataExt().setExchangePluginIds(exchangePluginIds);
        }
        return this;
    }

    /**
     * 更新绑定数字证书插件
     *
     * @param certificatePluginIds 新的绑定数字证书插件列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateCertificatePluginIdsTo(List<Long> certificatePluginIds) {
        if (!Objects.equals(this.getDataExt().getCertificatePluginIds(), certificatePluginIds)) {
            this.getDataExt().setCertificatePluginIds(certificatePluginIds);
        }
        return this;
    }

    /**
     * 更新平台生成(MPC) 结果集(openapiId)
     *
     * @param mpcOpenAPIId 新的平台生成(MPC) 结果集(openapiId)
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateMPCOpenAPIIdTo(String mpcOpenAPIId) {
        if (StringUtils.isNotBlank(mpcOpenAPIId) && !StringUtils.equals(this.getDataExt().getMpcOpenAPIId(), mpcOpenAPIId)) {
            this.getDataExt().setMpcOpenAPIId(mpcOpenAPIId);
        }
        return this;
    }

    /**
     * 更新是否改写响应体
     *
     * @param extractResponse 新的是否改写响应体标识
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataProduct updateExtractResponseTo(Boolean extractResponse) {
        if (extractResponse != null && !Objects.equals(this.getDataExt().getExtractResponse(), extractResponse)) {
            this.getDataExt().setExtractResponse(extractResponse);
        }
        return this;
    }

    public static final Function<DeliveryMode, String> deliveryModeMapping = (deliveryMode) -> {
        switch (deliveryMode) {
            case FILE_DOWNLOAD -> {
                return "01";
            }
            case API -> {
                return "03";
            }
            case null, default -> {
                return "02";
            }
        }
    };

    public static final Function<String, DeliveryMode> deliveryModeMapping1 = (deliveryMode) -> {
        switch (deliveryMode) {
            case "01" -> {
                return FILE_DOWNLOAD;
            }
            case "03" -> {
                return API;
            }
            case null, default -> {
                return FILE_DOWNLOAD;
            }
        }
    };

    public static final Set<String> COULD_UPDATE_REGISTRATION_STATUS = Set.of("item_status1", "item_status2", "item_status3");


    public static Specification<DataProduct> dataProductNameLike(Specification<DataProduct> specification, String dataProductName) {
        if (StringUtils.isNotBlank(dataProductName)) {
            Specification<DataProduct> dataProductNameLike = (root, query, cb) -> cb.like(root.get("dataProductName"), "%" + dataProductName + "%");
            specification = specification.and(dataProductNameLike);
        }
        return specification;
    }

    public static Specification<DataProduct> itemStatusIs(Specification<DataProduct> specification, String itemStatus) {
        if (StringUtils.isNotBlank(itemStatus)) {
            Specification<DataProduct> itemStatusIs = (root, query, cb) -> cb.equal(root.get("itemStatus"), itemStatus);
            specification = specification.and(itemStatusIs);
        }
        return specification;
    }

    public static Specification<DataProduct> itemStatusNot(Specification<DataProduct> specification, String itemStatus) {
        if (StringUtils.isNotBlank(itemStatus)) {
            Specification<DataProduct> itemStatusNot = (root, query, cb) -> cb.notEqual(root.get("itemStatus"), itemStatus);
            specification = specification.and(itemStatusNot);
        }
        return specification;
    }

    public static Specification<DataProduct> publishStatusIs(Specification<DataProduct> specification, String publishStatus) {
        if (StringUtils.isNotBlank(publishStatus)) {
            Specification<DataProduct> publishStatusIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("dataExt"), cb.literal("publishStatus"));
                return cb.equal(jsonExtract, cb.literal(publishStatus));
            };
            specification = specification.and(publishStatusIs);
        }
        return specification;
    }

    public static Specification<DataProduct> publishStatusNot(Specification<DataProduct> specification, String publishStatus) {
        if (StringUtils.isNotBlank(publishStatus)) {
            Specification<DataProduct> publishStatusIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("dataExt"), cb.literal("publishStatus"));
                return cb.notEqual(jsonExtract, cb.literal(publishStatus));
            };
            specification = specification.and(publishStatusIs);
        }
        return specification;
    }

    public static Specification<DataProduct> dataTypeIs(Specification<DataProduct> specification, DataType dataType) {
        if (dataType != null) {
            Specification<DataProduct> dataTypeIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("dataExt"), cb.literal("dataType"));
                return cb.equal(jsonExtract, cb.literal(dataType.name()));
            };
            specification = specification.and(dataTypeIs);
        }
        return specification;
    }

    public static Specification<DataProduct> prepareStatusIs(Specification<DataProduct> specification, DataAssetPrepareStatus prepareStatus) {
        if (prepareStatus != null) {
            Specification<DataProduct> prepareStatusIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("dataExt"), cb.literal("dataAssetPrepareStatus"));
                return cb.equal(jsonExtract, cb.literal(prepareStatus.name()));
            };
            specification = specification.and(prepareStatusIs);
        }
        return specification;
    }

    public static Specification<DataProduct> accessWayIs(Specification<DataProduct> specification, SourceType accessWay) {
        if (accessWay != null) {
            Specification<DataProduct> sourceIs = (root, query, cb) -> cb.equal(root.get("sourceType").as(String.class), accessWay.ordinal() + "");
            specification = specification.and(sourceIs);
        }
        return specification;
    }

    public static Specification<DataProduct> userIdIs(Specification<DataProduct> specification, String userId) {
        if (StringUtils.isNotBlank(userId)) {
            specification = specification.and((root, query, cb) -> cb.equal(root.get("userId"), userId));
        }
        return specification;
    }

    public static Specification<DataProduct> publishSubmitTimeBefore(Specification<DataProduct> specification, Date publishSubmitTime) {
        if (publishSubmitTime != null) {
            Specification<DataProduct> publishSubmitTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("publishSubmitTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(publishSubmitTime.getTime()));
            };
            specification = specification.and(publishSubmitTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> publishSubmitTimeAfter(Specification<DataProduct> specification, Date publishSubmitTime) {
        if (publishSubmitTime != null) {
            Specification<DataProduct> publishSubmitTimeAfter = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("publishSubmitTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(publishSubmitTime.getTime()));
            };
            specification = specification.and(publishSubmitTimeAfter);
        }
        return specification;
    }

    public static Specification<DataProduct> publishTimeBefore(Specification<DataProduct> specification, Date publishTime) {
        if (publishTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("publishTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(publishTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> publishTimeAfter(Specification<DataProduct> specification, Date publishTime) {
        if (publishTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("publishTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(publishTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> registrationSubmitTimeBefore(Specification<DataProduct> specification, Date registrationSubmitTime) {
        if (registrationSubmitTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationSubmitTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(registrationSubmitTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> registrationSubmitTimeAfter(Specification<DataProduct> specification, Date registrationSubmitTime) {
        if (registrationSubmitTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationSubmitTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(registrationSubmitTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> registrationTimeBefore(Specification<DataProduct> specification, Date registrationTime) {
        if (registrationTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(registrationTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> registrationTimeAfter(Specification<DataProduct> specification, Date registrationTime) {
        if (registrationTime != null) {
            Specification<DataProduct> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(registrationTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataProduct> deliveryModeIs(Specification<DataProduct> specification, DeliveryMode deliveryMode) {
        if (deliveryMode != null) {
            Specification<DataProduct> prepareStatusIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("deliveryExt"), cb.literal("deliveryMode"));
                return cb.equal(jsonExtract, cb.literal(deliveryMode.name()));
            };
            specification = specification.and(prepareStatusIs);
        }
        return specification;
    }
}
