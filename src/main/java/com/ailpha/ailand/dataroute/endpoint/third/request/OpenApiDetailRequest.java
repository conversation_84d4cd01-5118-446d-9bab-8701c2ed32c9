package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @Date 2022/1/6
 * @Description
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiDetailRequest {

    @Schema(description = "用户id")
    String userId;

    @Schema(description = "api id")
    String apiId;

    @Schema(description = "asset id")
    String assetId;
}
