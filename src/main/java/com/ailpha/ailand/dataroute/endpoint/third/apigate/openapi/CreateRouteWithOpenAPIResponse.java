package com.ailpha.ailand.dataroute.endpoint.third.apigate.openapi;

import com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayResponse;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateRouteWithOpenAPIResponse extends GatewayResponse<CreateRouteResponse.InvokeResult> {

}
