package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
//@JsonIgnoreProperties(ignoreUnknown = true)
public class DataProductInfo {
    /**
     * 基础支撑平台产品 ID
     */
    String productId;
    /**
     * 平台 ID
     */
    String platformId;
    /**
     * 外部产品 ID
     */
    String outerProductId;
    /**
     * 产品类型
     */
    String productType;
    /**
     * 产品名称
     */
    String productName;
    /**
     * 有效期开始 2024-05-30 14:30:00
     */
    String validStartTime;
    /**
     * 有效期结束 2024-05-30 14:30:00
     */
    String validEndTime;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 地域分类
     */
    String productRegion;
    /**
     * 是否涉及个人信息
     */
    String personalInformation;
    /**
     * 产品简介
     */
    String description;
    /**
     * 交付方式
     */
    String deliveryMethod;
    /**
     * 使用限制
     */
    String limitations;
    /**
     * 授权使用
     */
    String authorize;
    /**
     * 数据主体
     */
    String dataSubject;
    /**
     * 数据规模 单位为 MB、GB、TB 等
     */
    String dataSize;
    /**
     * 更新频率
     */
    String updateFrequency;
    /**
     * 其他
     */
    String others;
    /**
     * 提供方名称
     */
    String providerName;
    /**
     * 提供方主体类型 01：自然人 02：法人 03：非法人组织
     */
    String providerType;
    /**
     * 主体信息
     */
    String entityInformation;
    /**
     * 身份标识码
     */
    String identityId;
    /**
     * 提供方简介
     */
    String providerDesc;
    /**
     * 法人经办人姓名
     */
    String operatorName;
    /**
     * 法人经办人电话
     */
    String operatorTelephone;
    /**
     * 法人经办人身份证
     */
    String operatorIdCard;
    /**
     * 授权委托书
     */
    String commission;
    /**
     * 数据样例 文件地址，通过文件上传接口获取。
     */
    String dataSample;
    /**
     * 合法合规声明 文件地址，通过文件上传接口获取。
     */
    String complianceAndLegalStatement;
    /**
     * 数据来源声明 文件地址，通过文件上传接口获取。
     */
    String dataSourceStatement;
    /**
     * 安全分级分类 文件地址，通过文件上传接口获取。
     */
    String safeLevel;
    /**
     * 数据质量、产品价值评估报告 文件地址，通过文件上传接口获取。
     */
    String evaluationReport;
    /**
     * 主体 ID
     */
    String entityId;
    /**
     * 主体编号
     */
    String entityCode;
    /**
     * 产品编码
     */
    String productCode;
    /**
     * 产品状态 01 待登记，02 登记，03 撤销
     */
    String productStatus;
    /**
     * 创建时间
     */
    String coverBeginDate;
    /**
     * 更新时间
     */
    String coverEndDate;
    String deliveryType;
    /**
     * 连接器
     */
    String dpe;
    /**
     * 历史版本ID
     */
    List<String> historyIds;
}
