package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/6/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ContractTemplateDTO implements Serializable {

    @Schema(description = "模板ID")
    private String id;

    @Schema(description = "模板名称")
    private String name;
}
