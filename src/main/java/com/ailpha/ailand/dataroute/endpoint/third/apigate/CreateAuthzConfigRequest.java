package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateAuthzConfigRequest {
    String username;
    InvokeParam invokeParam;

    public String getInvokeParam() {
        return JacksonUtils.obj2json(invokeParam);
    }

    public InvokeParam getInvokeParam1() {
        return invokeParam;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeParam {
        List<Allowed> allowed = new ArrayList<>();

        InvokeParam addAllowed(Allowed allowed) {
            this.allowed.add(allowed);
            return this;
        }
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Allowed {
        /**
         * {
         * "serviceId": "VQfb-PdKR12JqFSLoteXmA",
         * "type": "router",
         * "routers": [
         * "xjLRfsUUTOa7EKAvk3NZvg"
         * ],
         * "dataSource": [
         * {
         * "key": "xjLRfsUUTOa7EKAvk3NZvg",
         * "value": "api name",
         * "serviceId": "VQfb-PdKR12JqFSLoteXmA"
         * }
         * ]
         * }
         */
        String serviceId;
        @Builder.Default
        String type = "router";
        // API路由ID列表
        List<String> routers;
        List<DataSource> dataSource;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DataSource {
        /**
         * API路由ID
         */
        String key;
        /**
         * API路由名称
         */
        String value;
        /**
         * API路由服务ID
         */
        String serviceId;
    }
}
