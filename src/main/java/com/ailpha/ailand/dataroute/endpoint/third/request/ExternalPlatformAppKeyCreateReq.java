package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.openapi.PlatformType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExternalPlatformAppKeyCreateReq implements Serializable {

    @NotBlank(message = "平台ID不能为空")
    @Schema(description = "平台ID", required = true)
    String platformId;

    @Schema(description = "平台服务地址（IP端口）**********:8080")
    String platformDomain;

    @NotNull(message = "平台类型不能为空")
    @Schema(description = "平台类型：DATA_ROUTE_HUB（连接器枢纽）DATA_ROUTE_ENDPOINT（连接器终端）OTHER（其他）", required = true)
    PlatformType platformType;

    @NotBlank(message = "appKey不能为空")
    @Schema(description = "appKey", required = true)
    String appKey;

    @NotBlank(message = "secret不能为空")
    @Schema(description = "secret", required = true)
    String appSecret;

    @Schema(description = "teePlatformAppKey，仅注册tee时使用，为了保证该平台所有企业使用的同一个ak，sk方便后续前置机的鉴权")
    String teePlatformAppKey;

    @Schema(description = "teePlatformAppSecret")
    String teePlatformAppSecret;
}
