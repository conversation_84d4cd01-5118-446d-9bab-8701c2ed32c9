package com.ailpha.ailand.dataroute.endpoint.third.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiSortResponse<T> implements Serializable {

    private Long code;
    private T data;
    private String message;
    private Boolean success;
}
