package com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple3;
import com.ailpha.ailand.dataroute.endpoint.connector.*;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.DataRouteActivateHandler;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterAgentRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.NetworkLinkRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.LicenseDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.RouteDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(1002)
@RequiredArgsConstructor
@Slf4j
public class NetworkLinkHandler implements DataRouteActivateHandler {
    private final RouteManagerActivateHandler nextHandler;
    private final RouterAgentRemoteService routerAgentRemoteService;

    @Override
    public void handler(RouteActivateContext context) {
        RouteDTO route = context.getRoute();
        RouterService routerService = SpringUtil.getBean(RouterService.class);
        if (context.getRoute().getStatus().getOrder() >= RouteStatus.network_link_pass.getOrder()) {
            log.info("当前连接器已经组网成功，进行下一步【中心激活】");
            nextHandler.handler(context);
            return;
        }
        NetworkLinkRequest networkLinkRequest = new NetworkLinkRequest();
        NodeInfoResponse nodeInfoResponse = routerService.currentNode();
//        LicenseDTO routeLicenseDTO = routerService.currentLicense();
//         todo 这里不合理
//        networkLinkRequest.setCompanyId(0L);
//        networkLinkRequest.setRouterId(nodeInfoResponse.getPlatformId());
//        networkLinkRequest.setCentralIP(routerService.currentLicense().getIamIp());
//        networkLinkRequest.setCertId(routeLicenseDTO.getRouteCertificate());
//        CommonResult<Void> startNetLink = routerAgentRemoteService.startNetLink(networkLinkRequest);
//        Assert.isTrue(startNetLink.isSuccess(), "组网失败：" + startNetLink.getMsg());
        String localIp = routerService.currentRouteVirtualIp();
        if (StringUtils.isEmpty(localIp) || localIp.contains("错误") || localIp.contains("Error") || localIp.contains("error")) {
            // 如果这里拿不到 可以重试一下
            int retryTimes = 3;
            int retryWaitTime = 2;
            while (retryTimes > 0) {
                localIp = RuntimeUtil.execForStr("bash", "-c", "nmcli device show utun99 | grep 'IP4.ADDRESS' | awk '{print $2}' | cut  -d '/' -f 1");
                if (StringUtils.isEmpty(localIp) || localIp.contains("错误")) {
                    retryTimes--;
                    ThreadUtil.safeSleep(retryWaitTime * 1000);
                } else {
                    break;
                }
            }
            org.springframework.util.Assert.isTrue(StringUtils.isNotEmpty(localIp) && !localIp.contains("错误"), "获取零信任组网虚拟IP异常，请联系管理员");
        }
        log.info("当前连接器已经组网成功，进行下一步【中心激活】");
        route.setStatus(RouteStatus.network_link_pass);
        context.setRoute(route);
        routerService.updateRouterCache(RouteStatus.network_link_pass);
//        nextHandler.handler(context);
    }

    private Tuple3<String, String, Integer> parseUrl(String url) {
        if (StringUtils.isEmpty(url))
            return new Tuple3<>("", "", 0);
        String schema;
        if (StringUtils.startsWith(url, "https"))
            schema = "https://";
        else
            schema = "http";
        String ip = url.substring(7).split(":")[0];
//        String ip = "************";
        Integer port = Integer.parseInt(url.substring(7).split(":")[1]);
//        Integer port = 8032;
        return new Tuple3<>(schema, ip, port);

    }
}
