package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * 数据资源信息项
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResourceItemAddCmd {
    String parentId;
    List<ResourceItemAddCmd> children;
    /**
     * 类型 0 出参 1 入参 default 0
     */
    String resourceType;
    /**
     * 中文名称
     */
    String name;
    /**
     * 编码
     */
    String code;
    /**
     * 开放等级
     */
    Integer level;
    /**
     * 字段类型
     */
    String fieldType;
    /**
     * 字段长度
     */
    Integer fieldLength;
    /**
     * 字段描述
     */
    String fieldDesc;
    /**
     * 是否为主键 1 是 0 否
     */
    String primaryFlag;
    /**
     * 是否为空 1 是 0 否
     */
    String nullFlag;
    /**
     * *参数类型 0-(入)body参数 1-(出)出参 2-(入)path 参数 3-(入)query 参数 4-(入)header 参数
     */
    String paramType;
    /**
     * 原始参数？张三？
     */
    String dataType;
}
