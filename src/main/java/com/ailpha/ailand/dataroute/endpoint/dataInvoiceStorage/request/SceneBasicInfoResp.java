package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneBasicInfoResp {

    @Schema(description = "场景ID")
    private String sceneId;

    @Schema(description = "场景名称")
    private String sceneName;

    @Schema(description = "场景类别")
    private String type;

    @Schema(description = "买方行业")
    private String buyerIndustry;

    @Schema(description = "卖方行业")
    private String sellerIndustry;

    @Schema(description = "发起人")
    private String initiatorName;

    @JsonFormat(pattern = "yyyy/MM/dd",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @Schema(description = "失效时间")
    private Date expireDate;


}
