package com.ailpha.ailand.dataroute.endpoint.dataprope.vo.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/2/20
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProbeListReq extends Page implements Serializable {

    @Schema(description = "数据库名")
    String dbName;

    @Schema(description = "数据库类型")
    String dbType;

    @Schema(description = "数据源名称–除数据源分类外用")
    String sourceName;

    @Schema(description = "表名/表注释–新版接口入参")
    String tableNameDesc;
}
