package com.ailpha.ailand.dataroute.endpoint.servicenode.controller;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.*;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/4/7
 */
@RestController
@Tag(name = "业务节点申请")
@RequestMapping("/service-node")
@RequiredArgsConstructor
public class ServiceNodeController {

    private final HubShuHanApiClient shuHanApiClient;

    @PostMapping("/list")
    @Operation(summary = "业务节点列表")
    public SuccessResponse<List<ServiceNodeListVO>> serviceNodeList(@Valid @RequestBody Page page) {
        return shuHanApiClient.serviceNodeList((int) page.getNum(), (int) page.getSize());
    }

    @PostMapping("/apply")
    @Operation(summary = "业务节点申请")
    public SuccessResponse<Boolean> serviceNodeApply(@Valid @RequestBody ServiceNodeApplyReq serviceNodeApplyReq) {
        shuHanApiClient.serviceNodeApply(serviceNodeApplyReq);
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @PostMapping("/apply-list")
    @Operation(summary = "业务节点已申请列表")
    public SuccessResponse<List<ServiceNodeApplyListVO>> serviceNodeApplyList(@Valid @RequestBody ServiceNodeApplyListReq serviceNodeApplyListReq) {
        return shuHanApiClient.serviceNodeApplyList(serviceNodeApplyListReq, (int) serviceNodeApplyListReq.getNum(), (int) serviceNodeApplyListReq.getSize());
    }
}
