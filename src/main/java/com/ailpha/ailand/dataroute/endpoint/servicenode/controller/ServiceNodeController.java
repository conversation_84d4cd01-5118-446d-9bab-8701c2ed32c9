package com.ailpha.ailand.dataroute.endpoint.servicenode.controller;

import com.ailpha.ailand.dataroute.endpoint.servicenode.service.ServiceNodeService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.request.ServiceNodeListReq;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeAppliedListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListReq;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeListVO;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/4/7
 */
@RestController
@Tag(name = "业务节点")
@RequestMapping("/service-node")
@RequiredArgsConstructor
public class ServiceNodeController {

    private final HubShuHanApiClient shuHanApiClient;
    private final ServiceNodeService serviceNodeService;

    @PostMapping("/list")
    @Operation(summary = "业务节点列表")
    public SuccessResponse<List<ServiceNodeListVO>> serviceNodeList(@Valid @RequestBody ServiceNodeListReq serviceNodeListReq) {
        List<String> serviceNodeIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(serviceNodeListReq.getServiceNodeId())) {
            serviceNodeIds.add(serviceNodeListReq.getServiceNodeId());
        }
        return shuHanApiClient.serviceNodeList(serviceNodeListReq.getEntryName(), serviceNodeIds, serviceNodeListReq.getNum(), serviceNodeListReq.getSize());
    }

    @PostMapping("/apply")
    @Operation(summary = "业务节点申请")
    public SuccessResponse<String> serviceNodeApply(@Valid @RequestBody ServiceNodeListVO serviceNodeListVO) {
        String id = serviceNodeService.serviceNodeApply(serviceNodeListVO);
        return SuccessResponse.success(id).build();
    }

    @PostMapping("/apply-list")
    @Operation(summary = "业务节点已申请列表")
    public SuccessResponse<List<ServiceNodeAppliedListVO>> serviceNodeApplyList(@Valid @RequestBody ServiceNodeApplyListReq serviceNodeApplyListReq) {
        return serviceNodeService.serviceNodeApplyList(serviceNodeApplyListReq);
    }
}
