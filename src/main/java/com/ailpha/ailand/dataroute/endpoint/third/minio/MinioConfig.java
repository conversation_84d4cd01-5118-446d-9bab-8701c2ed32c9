package com.ailpha.ailand.dataroute.endpoint.third.minio;

import com.ailpha.ailand.dataroute.endpoint.third.config.MinioProperties;
import io.minio.MinioClient;
import io.minio.admin.MinioAdminClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

@Slf4j
@Configuration
public class MinioConfig {
    @Bean
    public MinioClient minioClient(MinioProperties properties) {
        return MinioClient.builder()
                .endpoint(properties.getEndpoint())
                .credentials(properties.getRootUser(), properties.getRootPassword())
                .httpClient(customHttpClient())
                .build();
    }

    @Bean
    public MinioAdminClient minioAdminClient(MinioProperties properties) {
        return MinioAdminClient.builder()
                .endpoint(properties.getEndpoint())
                .credentials(properties.getRootUser(), properties.getRootPassword())
                .httpClient(customHttpClient())
                .build();
    }

    public static OkHttpClient customHttpClient() {
        // Create a trust manager that does not validate certificate chains
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                        // Do nothing (trust any client certificate)
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                        // Do nothing (trust any server certificate)
                    }
                }
        };

        // Install the all-trusting trust manager
        SSLContext sslContext = null;
        try {
            sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new SecureRandom());
        } catch (Exception e) {
            log.error("Install the all-trusting trust manager error:{}", e.getMessage());
        }

        // Create a custom OkHttpClient that trusts all certificates
        return new OkHttpClient.Builder()
                .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                .hostnameVerifier((hostname, session) -> true)
                .build();
    }
}
