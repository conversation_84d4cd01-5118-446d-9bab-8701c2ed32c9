package com.ailpha.ailand.dataroute.endpoint.user.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.LoginResponse;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.interceptor.TenantFilter;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.schedule.LoginMonitorSchedule;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.CaptchaService;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.login.LoginRequest;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.session.CompositeSessionAuthenticationStrategy;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor
@Tag(name = "登录")
public class LoginController {

    private final AuthenticationManager authenticationManager;
    private final CompositeSessionAuthenticationStrategy sessionAuthenticationStrategy;
    private final ApplicationEventPublisher eventPublisher;
    private final SecurityContextRepository securityContextRepository;
    private final UserService userService;
    private final CompanyService companyService;

    @Value("${ailand.useCaptcha:true}")
    private boolean useCaptcha;
    private final CaptchaService captchaService;
    private final RouterManagerRemoteService routerManagerRemoteService;

    private final ServiceNodeRemoteService serviceNodeRemoteService;

    @PostMapping("/login")
    public SuccessResponse<Void> login(LoginRequest loginRequest, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.equals(loginRequest.getUsername(), "admin") && StringUtils.isNotEmpty(loginRequest.getSchema()))
            Assert.isTrue(StringUtils.equals(loginRequest.getSchema(), TenantIdentifierResolver.DEFAULT_TENANT), "请使用正确的登录地址");
        String username = loginRequest.getUsername();
        // 检查账号是否被锁定
        userService.checkAccountIsLock(username);
        String password = userService.decryptPwd(loginRequest.getPassword());
        if (useCaptcha) {
            if (StringUtils.isAllBlank(loginRequest.getCaptchaId()) || StringUtils.isAllBlank(loginRequest.getCaptchaValue())) {
                throw new BadCredentialsException("验证码 id 或者 验证码为空");
            }
            String captcha = captchaService.getCaptcha(loginRequest.getCaptchaId());
            if (!StringUtils.equalsIgnoreCase(captcha, loginRequest.getCaptchaValue())) {
                throw new BadCredentialsException("验证码错误");
            }
        }
        if (!StringUtils.equals("admin", username)) {
            UserDTO userDetails = userService.loadUserByUsername(username);
            Assert.isTrue(StringUtils.equals(loginRequest.getSchema(), userDetails.getCompany().getSchema()), "登录地址与当前账号不匹配，请检查登录地址是否正确或请联系接入主体管理员.");
//         调用枢纽登录认证接口
            com.ailpha.ailand.dataroute.endpoint.connector.remote.request.LoginRequest loginRequest1 = com.ailpha.ailand.dataroute.endpoint.connector.remote.request.LoginRequest.builder()
                    .account(username).password(password)
                    .build();
            Long companyId = Long.valueOf(StringUtils.split(loginRequest.getSchema(), "_")[1]);
//            loginRequest1.setHubInfo(companyService.getHubInfo(companyId));
//            CommonResult<LoginResponse> loginResult = routerManagerRemoteService.login(loginRequest1);
//            if (loginResult.unauthorized()) {
//                userService.afterLoginFailed(username);
//            } else
//                Assert.isTrue(loginResult.isSuccess() && StringUtils.isNotEmpty(loginResult.getData().getToken()), "登录失败：" + loginResult.getMessage());
//            if (StringUtils.equals(loginResult.getData().getRole(), "法人"))
//                userService.addEntityUser(loginResult.getData().getUserId(), username, password, companyId);
            // todo 临时方案

            NodeDTO.HubInfo hubInfo = companyService.getHubInfo(companyId);
            hubInfo.setUrl(hubInfo.getServiceNodeUrl());
            loginRequest1.setHubInfo(hubInfo);
            CommonResult<LoginResponse> loginResult1 = serviceNodeRemoteService.login(loginRequest1, userDetails.getCompany().getNodeId());
            if (loginResult1.unauthorized()) {
                userService.afterLoginFailed(username);
            } else
                Assert.isTrue(loginResult1.isSuccess() && StringUtils.isNotEmpty(loginResult1.getData().getToken()), "登录失败：" + loginResult1.getMessage());
        }

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
        // authenticationToken.setDetails(authenticationDetailsSource.buildDetails(request));
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        this.sessionAuthenticationStrategy.onAuthentication(authenticate, request, response);
        return successfulAuthentication(authenticate, request, response);
    }

    private SuccessResponse<Void> successfulAuthentication(Authentication authentication, HttpServletRequest request,
                                                           HttpServletResponse response) {
        SecurityContext emptyContext = SecurityContextHolder.createEmptyContext();
        emptyContext.setAuthentication(authentication);
        SecurityContextHolder.setContext(emptyContext);
        if (ObjectUtil.isNotNull(eventPublisher))
            eventPublisher.publishEvent(new InteractiveAuthenticationSuccessEvent(authentication, this.getClass()));
        securityContextRepository.saveContext(emptyContext, request, response);
        if (StringUtils.equals("admin", ((UserDTO) authentication.getPrincipal()).getUsername())) {
            response.setHeader(TenantFilter.TENANT_HEADER, TenantIdentifierResolver.DEFAULT_TENANT);
        } else
            response.setHeader(TenantFilter.TENANT_HEADER, ((UserDTO) authentication.getPrincipal()).getCompany().getSchema());
        userService.updateLoginTime((((UserDTO) authentication.getPrincipal()).getUsername()));
        userService.resetLoginFailedTimes(((UserDTO) authentication.getPrincipal()).getUsername());

        if (!StringUtils.equals("admin", ((UserDTO) authentication.getPrincipal()).getUsername())) {
            LoginMonitorSchedule.addLoginRecord(String.format("%s_%s_%s", ((UserDTO) authentication.getPrincipal()).getCompany().getId(), ((UserDTO) authentication.getPrincipal()).getCompany().getThirdBusinessId(), ((UserDTO) authentication.getPrincipal()).getCompany().getNodeId()));
        }
        return SuccessResponse.success(null).build();

    }

    @PostMapping("/logout")
    @Operation(summary = "登出")
//     @OpLog(message = "登出")
    public SuccessResponse<Boolean> logout() {
        // OPLogContext.putOpType(InternalOpType.LOGOUT);
        log.info("用户【{}】登出成功", LoginContextHolder.currentUser().getUsername());
        return SuccessResponse.success(true).build();
    }
}
