package com.ailpha.ailand.dataroute.endpoint.user.controller;

import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SmUtil;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.LoginResponse;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.user.contants.UserConstant;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.CaptchaService;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.login.LoginRequest;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.event.InteractiveAuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.session.CompositeSessionAuthenticationStrategy;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping
@RequiredArgsConstructor
@Tag(name = "登录")
public class LoginController {

    private final AuthenticationManager authenticationManager;
    private final CompositeSessionAuthenticationStrategy sessionAuthenticationStrategy;
    private final Cache<String, String> userCache;
    private final ApplicationEventPublisher eventPublisher;
    private final SecurityContextRepository securityContextRepository;
    private final UserService userService;
    private final NodeService nodeService;
    @Lazy
    private final CompanyService companyService;

    @Value("${spring.profiles.active}")
    private String profiles;

    @Value("${ailand.useCaptcha:true}")
    private boolean useCaptcha;
    private final CaptchaService captchaService;
    private final RouterManagerRemoteService routerManagerRemoteService;
    public static final String HUB_LOGIN_TOKEN = "token_%s";

    @PostMapping("/login")
    public SuccessResponse<Void> login(LoginRequest loginRequest, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.equals(loginRequest.getUsername(), "admin") && StringUtils.isNotEmpty(loginRequest.getSchema()))
            Assert.isTrue(StringUtils.equals(loginRequest.getSchema(), TenantIdentifierResolver.DEFAULT_TENANT), "请使用正确的登录地址");
        String username = loginRequest.getUsername();
        String password = userService.decryptPwd(loginRequest.getPassword());
        if (useCaptcha) {
            if (StringUtils.isAllBlank(loginRequest.getCaptchaId()) || StringUtils.isAllBlank(loginRequest.getCaptchaValue())) {
                throw new BadCredentialsException("验证码 id 或者 验证码为空");
            }
            String captcha = captchaService.getCaptcha(loginRequest.getCaptchaId());
            if (!StringUtils.equalsIgnoreCase(captcha, loginRequest.getCaptchaValue())) {
                throw new BadCredentialsException("验证码错误");
            }
        }
        if (!StringUtils.equals("admin", username)) {
//         调用枢纽登录认证接口
            CommonResult<LoginResponse> loginResult = routerManagerRemoteService.login(com.ailpha.ailand.dataroute.endpoint.connector.remote.request.LoginRequest.builder()
                    .accountName(username).password(SmUtil.encryptBySm4(password, nodeService.getFirstNode().getHubInfo().getSk()))
                    .build());
            Assert.isTrue(loginResult.isSuccess() && StringUtils.isNotEmpty(loginResult.getData().getAccessToken()), "登录失败：" + loginResult.getMsg());
            userCache.put(String.format(HUB_LOGIN_TOKEN, username), loginResult.getData().getAccessToken());
            userService.initUser(loginRequest.getUsername(), loginRequest.getPassword(), loginResult.getData().getAccessToken());
        }

        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
        // authenticationToken.setDetails(authenticationDetailsSource.buildDetails(request));
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        this.sessionAuthenticationStrategy.onAuthentication(authenticate, request, response);
        return successfulAuthentication(authenticate, request, response);
    }

    private SuccessResponse<Void> successfulAuthentication(Authentication authentication, HttpServletRequest request,
                                                           HttpServletResponse response) {
        SecurityContext emptyContext = SecurityContextHolder.createEmptyContext();
        emptyContext.setAuthentication(authentication);
        SecurityContextHolder.setContext(emptyContext);
        if (ObjectUtil.isNotNull(eventPublisher))
            eventPublisher.publishEvent(new InteractiveAuthenticationSuccessEvent(authentication, this.getClass()));
        securityContextRepository.saveContext(emptyContext, request, response);
        userCache.put(String.format(UserConstant.USER_LOGIN_SET_KEY, ((UserDTO) authentication.getPrincipal()).getId()), "1");
        if (StringUtils.equals("admin", ((UserDTO) authentication.getPrincipal()).getUsername())) {
            response.setHeader("X-Tenant-Schema", TenantIdentifierResolver.DEFAULT_TENANT);
        } else
            response.setHeader("X-Tenant-Schema", ((UserDTO) authentication.getPrincipal()).getCompany().getSchema());
        return SuccessResponse.success(null).build();

    }

    @PostMapping("/logout")
    @Operation(summary = "登出")
//     @OpLog(message = "登出")
    public SuccessResponse<Boolean> logout() {
        // OPLogContext.putOpType(InternalOpType.LOGOUT);
        log.info("用户【{}】登出成功", LoginContextHolder.currentUser().getUsername());
        return SuccessResponse.success(true).build();
    }
}
