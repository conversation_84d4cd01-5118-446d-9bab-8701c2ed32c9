package com.ailpha.ailand.dataroute.endpoint.plugin.strategy;

import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import com.ailpha.ailand.dataroute.endpoint.plugin.common.BlockchainStatusConstant;
import com.ailpha.ailand.dataroute.endpoint.repository.BlockchainPluginDetailRepository;
import com.ailpha.ailand.invoke.api.CommonException;

import java.time.LocalDateTime;

/**
 * @author: yuwenping
 * @date: 2025/5/9 13:44
 * @Description:
 */
public interface BlockchainProcessingStrategy {
    boolean process(String data, BlockchainPluginDetail detail);

    Long save(BlockchainPluginRequest request);

    Long update(BlockchainPluginUpdateRequest request);

    default BlockchainPluginDetail baseSave(BlockchainPluginRequest request) {
        BlockchainPluginDetailRepository blockchainPluginDetailRepository = SpringUtil.getBean(BlockchainPluginDetailRepository.class);
        // 保存主表
        BlockchainPluginDetail detail = BlockchainPluginDetail.builder()
                .name(request.getName())
                .description(request.getDescription())
                .moduleType(request.getModuleType())
                .ext(request.getPluginContent())
                .type(request.getType())
                .status(BlockchainStatusConstant.ON)
                .createTime(LocalDateTime.now())
                .build();
        return blockchainPluginDetailRepository.save(detail);
    }

    default BlockchainPluginDetail baseUpdate(BlockchainPluginUpdateRequest request) {
        BlockchainPluginDetailRepository blockchainPluginDetailRepository = SpringUtil.getBean(BlockchainPluginDetailRepository.class);
        BlockchainPluginDetail detail = blockchainPluginDetailRepository.findById(request.getId())
                .orElseThrow(() -> new CommonException("插件不存在"));
        detail.setName(request.getName());
        detail.setModuleType(request.getModuleType());
        detail.setDescription(request.getDescription());
        detail.setExt(request.getPluginContent());
        detail.setStatus(request.getStatus());

        return blockchainPluginDetailRepository.save(detail);
    }
}
