package com.ailpha.ailand.dataroute.endpoint.common.exception;

import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.dbapp.rest.response.ErrorCode;
import com.dbapp.rest.response.ErrorResponse;
import com.github.lianjiatech.retrofit.spring.boot.exception.RetrofitException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 请求执行顺序 filter->interceptor->controllerAdvice->aspect->controller
 * 异常处理顺序则刚刚相反
 * 注意  某些异常的抛出点可能不在controller 比如validator的参数校验异常在advice层直接接收到
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {


    @Value("${spring.profiles.active}")
    private String profile;


    @ExceptionHandler
    @ResponseBody
    public ErrorResponse handle(Exception exception, HttpServletRequest request) {
        //开发环境下打印出所有异常信息堆栈
        boolean flag = profile.startsWith("dev") || profile.startsWith("sit");
        if (flag) {
            log.error("GlobalExceptionHandler捕获异常：", exception);
        }

        if (exception instanceof RetrofitException) {
            Tuple2<String, String> parsedRetrofitException;
            try {
                parsedRetrofitException = parseRetrofitException((RetrofitException) exception);
                return ErrorCode.InternalError.message(StringUtils.isNotEmpty(parsedRetrofitException.first) ? parsedRetrofitException.first : "调用远程服务异常，请联系管理员").target(parsedRetrofitException.second).build();
            } catch (Exception e) {
                return ErrorCode.InternalError.message(e.getMessage()).build();
            }
        }

        if (exception instanceof IllegalArgumentException) {
            log.error("request fail method:{} uri:{}", request.getMethod(), request.getRequestURI(), exception);
            return ErrorCode.BadArgument.message(exception.getMessage()).target(exception.getLocalizedMessage()).build();
        }

        if (exception instanceof MissingServletRequestParameterException) {
            return ErrorCode.BadUserArgument.message(exception.getMessage()).build();
        }

        // mvc参数校验抛出的异常不会经过aspect切面，直接来到这里
        if (exception instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException e = (MethodArgumentNotValidException) exception;
            List<String> errorMessages = e.getBindingResult().getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.toList());
            log.error("MethodArgumentNotValidException {}", errorMessages);
            return ErrorCode.BadArgument.message(exception.getMessage()).build();
        }

        if (exception instanceof ConstraintViolationException) {
            log.error("主键异常：", exception);
            return ErrorResponse.error(ErrorCode.InvalidOperation).build();
        }

        if (exception instanceof HttpMessageNotReadableException) {
            log.warn("接口缺少参数或参数解析异常 ", exception);
            return ErrorResponse.error(ErrorCode.BadArgument).build();
        }
        if (exception instanceof SQLIntegrityConstraintViolationException) {
            log.warn("数据库主键冲突 ", exception);
            return ErrorResponse.error(ErrorCode.InvalidOperation).build();
        }
        if (exception instanceof IOException &&
                (exception.getMessage().equals("你的主机中的软件中止了一个已建立的连接。") || exception.getMessage().contains("断开的管道"))) {
            // 忽略该异常的打印及返回
            log.warn("连接断开: {} ", exception.getMessage());
            return null;
        }

        //防止重复打印
        if (!flag) {
            log.error("GlobalExceptionHandler捕获未定义异常：", exception);
        }

        return ErrorCode.InternalError.message(exception.getMessage()).build();
    }

    private Tuple2<String, String> parseRetrofitException(RetrofitException e) {
        String detailMessage = StringUtils.substringBetween(e.getMessage(), "", ", request");
        String request = StringUtils.substringBetween(e.getMessage(), "request=Request", ", response=");
        if (StringUtils.isEmpty(request)) {
            request = StringUtils.substringBetween(e.getMessage(), "request=Request", "}");
        }
        String body = StringUtils.substringAfter(e.getMessage(), "body=");
        return new Tuple2<>(StringUtils.isEmpty(detailMessage) ? body : detailMessage, StringUtils.join(List.of(StringUtils.substringBetween(request, "method=", ","),
                StringUtils.substringBetween(request, "url=", ","), body), " "));
    }

    public static void main(String[] args) {
        String s = "com.github.lianjiatech.retrofit.spring.boot.exception.RetrofitException: invalid Response! request=Request{method=GET, url=http://127.0.0.1:8081/_ailand/external/dataRouter/systemConfig, tags={class retrofit2.Invocation=com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote.systemConfigInfo() []}}, response=Response{protocol=h2, code=500, message=基础能力 TEE 未启用, url=http://127.0.0.1:8081/_ailand/external/dataRouter/systemConfig}, body={\"message\":\"基础能力TEE 未启用\"}";
        String detailMessage = StringUtils.substringBetween(s, "", ", request");
        String request = StringUtils.substringBetween(s, "request=Request", ", response=");
        if (StringUtils.isEmpty(request)) {
            request = StringUtils.substringBetween(s, "request=Request", "}");
        }
        String body = StringUtils.substringAfter(s, "body=");
        Tuple2<String, String> stringStringTuple2 = new Tuple2<>(detailMessage, StringUtils.join(List.of(StringUtils.substringBetween(request, "method=", ","),
                StringUtils.substringBetween(request, "url=", ","), body), " "));
        System.out.println(stringStringTuple2);
    }
}
