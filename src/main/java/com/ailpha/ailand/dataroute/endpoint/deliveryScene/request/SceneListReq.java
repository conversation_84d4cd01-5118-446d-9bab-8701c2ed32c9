package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneListReq extends Page {

    private String createUser;
    @Schema(description = "ID")
    private String sceneId;

    @Schema(description = "资产类型 RESOURCE、PRODUCT")
    private AssetType type;

    @Schema(description = "交付方式 API:API接口交付 FILE_DOWNLOAD:数据集交付 TEE:TEE交付 MPC:MPC交付")
    private String deliveryType;

    @Schema(description = "数字证书 —— 合规场景名称")
    private String digitalSceneName;

    @Schema(description = "状态")
    String sceneStatus;

    public SceneListReq(String createUser, String deliveryType, long num, long size) {
        super(num, size);
        this.createUser = createUser;
        this.deliveryType = deliveryType;
    }
}