package com.ailpha.ailand.dataroute.endpoint.common.rest.ailand;

import jakarta.validation.constraints.Min;
import lombok.Data;


@Data
public class IPage {

    /*查询页码*/
    @Min(1)
    private int pageNo = Integer.parseInt(DEFAULT_PAGE_NO);

    /*每页记录数*/
    @Min(0)
    private int pageSize = Integer.parseInt(DEFAULT_PAGE_SIZE);

    private static final String DEFAULT_PAGE_NO = "1";

    private static final String DEFAULT_PAGE_SIZE = "20";

    public int offset() {
        return pageSize == 0 ? 0 : (pageNo - 1) * pageSize;
    }


}
