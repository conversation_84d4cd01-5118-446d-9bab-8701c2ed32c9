package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.biz.api.collector.HttpReaderTaskUtil;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.ApiImportExtendBO;
import com.ailpha.ailand.biz.api.dataset.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.common.ConnectorMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.*;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.FileEncryptKeyHeader;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.SeparatorEnum;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneAssetResp;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.config.MinioProperties;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DatasourceConstants;
import com.ailpha.ailand.dataroute.endpoint.third.constants.OperatorConstants;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.util.DockerNetworkUtils;
import com.ailpha.ailand.invoke.api.CommonException;
import com.ailpha.ailand.jni.SecIslandTEE;
import com.ailpha.ailand.plugin.reader.httpreader.RestTemplateUtil;
import com.ailpha.ailand.utils.Bean2HeaderUtils;
import com.ailpha.ailand.utils.safe.RSAUtil;
import com.dbapp.rest.exception.RestfulApiException;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Call;
import retrofit2.Response;
import sun.security.krb5.internal.ktab.KeyTab;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateKey;
import java.util.*;
import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants.*;
import static com.ailpha.ailand.dataroute.endpoint.third.minio.MinioConfig.customHttpClient;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataAssetServiceImpl implements DataAssetService {

    private final FilesStorageServiceImpl filesStorageService;

    private final RouterService routerService;

    private final KeyPair keyPair;

    private final DataResourceService dataResourceService;
    private final CompanyService companyService;

    private final DataProductService dataProductService;

    private final DataResourceRepository dataResourceRepository;
    private final DataProductRepository dataProductRepository;
    private final DataAssetMapper dataAssetMapper;
    private final CompanyRepository companyRepository;

    private final DeliveryService deliveryService;
    private final OrderManagerService orderManagerService;
    private final AiLandProperties aiLandProperties;
    private final HubShuHanApiClient hubShuHanApiClient;

    @Value("${ailand.support.file.encoding:UTF-8,GB2312}")
    private String supportFileEncoding;
    private final Cache<String, String> dataAssetCache;

    private final MinioProperties minioProperties;
    private final EndpointRemote endpointRemote;

    @Value("${ailand.internal.ip}")
    String routerInternalIp;

    @Value("${gateway.docker.name:apig-engine}")
    private String API_GATEWAY_DOCKER_NAME;


    static {
        try {
            trustAllHttpsCertificates();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // 在方法前加上如下代码（只需设置一次即可，全局生效）
    private static void trustAllHttpsCertificates() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }

                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
        };
        SSLContext sc = SSLContext.getInstance("TLS");
        sc.init(null, trustAllCerts, new SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }

    @Override
    public DataAsset getLocalDataAssetById(String assetId) {
        Optional<DataProduct> optionalDataProduct = dataProductRepository.findById(assetId);
        if (optionalDataProduct.isPresent()) {
            return dataAssetMapper.dataProductToDataAsset(optionalDataProduct.get());
        }
        Optional<DataResource> optionalDataResource = dataResourceRepository.findById(assetId);
        if (optionalDataResource.isPresent()) {
            return dataAssetMapper.dataResourceToDataAsset(optionalDataResource.get());
        }
        throw new CommonException("未找到 id 为 {}" + assetId + " 的本地资源或产品");
    }

    @Override
    public DataAssetFileUploadResponse uploadFile(MultipartFile file) throws IOException {
        Assert.isTrue(DataAsset.validateFilenameUsingRegex(Objects.requireNonNull(file.getOriginalFilename()).toLowerCase(), DataAsset.SUPPORT_SUFFIX), "支持的文件格式为：" + DataAsset.SUPPORT_SUFFIX);
        UserDTO userDTO = LoginContextHolder.currentUser();
        String uuid32 = UuidUtils.uuid32();
        Path path = filesStorageService.save(file,
                filesStorageService.getRootPath()
                        .resolve("dataAsset")
                        .resolve(userDTO.getId())
                        .resolve(uuid32 + "." + FileUtil.extName(file.getOriginalFilename())));
        dataAssetCache.put(uuid32, path.toFile().getAbsolutePath());
        DataAssetFileUploadResponse.DataAssetFileUploadResponseBuilder dataAssetFileUploadResponseBuilder = DataAssetFileUploadResponse.builder()
                .dataAssetFileId(uuid32);
        if (file.getOriginalFilename().endsWith(".csv")) {
            // 非结构化数据不需要此操作
            String lineValue = FileUtils.readFirstLine(path.toFile());
            List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
            dataAssetFileUploadResponseBuilder.dataSchema(dataSchema);
        }
        return dataAssetFileUploadResponseBuilder.build();
    }

    @Override
    public DataAssetFileUploadResponse uploadModelFile(MultipartFile file) {
        Assert.isTrue(DataAsset.validateFilenameUsingRegex(Objects.requireNonNull(file.getOriginalFilename()).toLowerCase(), aiLandProperties.getModel().getSupportedFileSuffix()),
                "支持的模型文件后缀名: " + aiLandProperties.getModel().getSupportedFileSuffix());
        String uuid32 = UuidUtils.uuid32();
        String safeName = FilenameUtils.normalize(file.getOriginalFilename());
        if (safeName == null || safeName.contains("../")) {
            throw new IllegalArgumentException("非法文件名");
        }
        // 中文文件名改为拼音展示，中文名上传存在bug
        String pingYinFileName = PinYin4JUtil.getPingYin(file.getOriginalFilename());
        Path path = filesStorageService.save(file,
                filesStorageService.getRootPath()
                        .resolve("model")
                        .resolve(uuid32 + "_" + pingYinFileName));
        dataAssetCache.put(uuid32, path.toFile().getAbsolutePath());
        DataAssetFileUploadResponse.DataAssetFileUploadResponseBuilder dataAssetFileUploadResponseBuilder = DataAssetFileUploadResponse.builder()
                .dataAssetFileId(uuid32);
        return dataAssetFileUploadResponseBuilder.build();
    }

    public DataAssetFileUploadResponse exploreFile(String filepath) {
        // 检查路径是否非法
        Assert.isTrue(StringUtils.isNotEmpty(filepath), "文件路径不能为空");
        Assert.isTrue(!filepath.contains(".."), "文件路径包含非法字符 '..'");
        // 2. 检查特定操作系统的非法字符 (这是一个简化版本，可以根据需要扩展)
        // 对于更全面的检查，可以考虑使用白名单字符集或更专业的库
        String normalizedFilepath = FilenameUtils.normalize(filepath);
        Assert.isTrue(ObjectUtil.isNotNull(normalizedFilepath), "文件路径格式非法或包含向上遍历的 '..'");
        // 3. 将用户提供的路径与允许的基础路径结合，并规范化
        Path combinedPath;
        Path basePath = filesStorageService.getRootPath().resolve("files").toAbsolutePath().normalize();

        try {
            // 如果 filepath 是相对路径，则基于 basePath 解析
            // 如果 filepath 已经是绝对路径，需要判断它是否在 basePath 之下
            Path userPath = Paths.get(filepath);
            if (userPath.isAbsolute()) {
                combinedPath = userPath.toAbsolutePath().normalize();
            } else {
                combinedPath = basePath.resolve(filepath).toAbsolutePath().normalize();
            }
        } catch (InvalidPathException e) {
            throw new RestfulApiException("提供的文件路径格式无效: " + e.getMessage());
        }
        Assert.isTrue(combinedPath.startsWith(basePath), "非法的文件路径访问，超出允许范围,请将服务器文件移动至[" + basePath + "]目录下");
        Assert.isTrue(FileUtil.exist(filepath), "服务器文件路径不存在");
        String uuid32 = UuidUtils.uuid32();
        dataAssetCache.put(uuid32, combinedPath.toFile().getAbsolutePath());
        return DataAssetFileUploadResponse.builder()
                .dataAssetFileId(uuid32).build();
    }

    @Override
    public DebugFileUploadResponse uploadDebugDataReturnSchemaAndExampleData(String datasetId, MultipartFile file, String separator, Integer hasHeader, String dataStructureType) throws Exception {
        if (DataType.UNSTRUCTURED.name().equals(dataStructureType)) {
            return justUploadDebugData(datasetId, file);
        }
        Charset charset = FileUtils.getFileEncode(file.getInputStream());
        if (charset == null) {
            throw new RestfulApiException("未知的调试数据编码");
        }
        if (!supportFileEncoding.contains(charset.name())) {
            throw new RestfulApiException("调试数据文件编码格式异常，请上传如下编码格式的文件：" + supportFileEncoding);
        }
        File tempFile = filesStorageService.save2TempFile(file).toFile();
        FilesStorageServiceImpl.checkIfIsShellAndThrowExceptions(tempFile);
        FilesStorageServiceImpl.csvRequired(tempFile);
        tempFile = FilesStorageServiceImpl.convert2UTF8(tempFile);

        DebugFileUploadResponse result = new DebugFileUploadResponse();
        String lineValue = FileUtils.readFirstLine(tempFile);

        List<DataSchemaBO> dataSchema = parseDataSchema(separator, lineValue);
        result.setDataSchema(dataSchema);
        String tempDebugFileId = UUID.randomUUID().toString();
        dataAssetCache.put(tempDebugFileId, tempFile.getAbsolutePath());
        result.setTempDebugFileId(tempDebugFileId);

        List<List<String>> lists = new ArrayList<>();
        try (RandomAccessFile raf = new RandomAccessFile(tempFile, "r")) {
            for (int i = 0; i < 10; i++) {
                //解决中文乱码
                String rawStr = raf.readLine();
                if (StringUtils.isBlank(rawStr) || (hasHeader == 1 && i == 0)) {
                    continue;
                }
                String line = new String(rawStr.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                lists.add(Arrays.asList(line.split(separator)));
            }
        }
        result.setDataList(lists);
        if (lists.isEmpty()) {
            throw new RestfulApiException("第一行不能为空");
        }
        if (!ObjectUtils.isEmpty(datasetId)) {
            DataAsset localDataAssetById = getLocalDataAssetById(datasetId);
            if (lists.getFirst().size() != localDataAssetById.getExtraData().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery).count()) {
                throw new CommonException("调试数据结构与数据集描述不一致，请检查是否正确配置");
            }
            updateDebugData(datasetId, tempFile.getAbsolutePath());
        }
        return result;
    }

    @Override
    public boolean download(AssetType assetType, String deliverId, String companyId, String dataProductPlatformId, String accessKey, String secretKey, boolean dispatch, HttpServletResponse response) {
        SceneAssetResp sceneAssetResp;
        if (!dispatch) {
            sceneAssetResp = checkOrderLegal(companyId, deliverId, dataProductPlatformId);
        } else {
            sceneAssetResp = null;
        }

        String routerId;

        // 获取资产详情信息
        DataProductVO dataProduct;
        CompanyDTO companyDTO = companyService.localCompany(Long.valueOf(companyId));
        try {
            dataProduct = AsyncManager.getInstance().executeFuture(() -> {
                hubShuHanApiClient.setCompany(companyDTO);
                return dataProductService.getDataProductByDataProductPlatformIdFromRemoteNoLoginSpecial(dataProductPlatformId, Long.valueOf(companyId));
            });
            routerId = dataProduct.getProvider().getRouterId();
        } catch (Exception e) {
            log.error("获取资产信息为空", e);
            throw new RestfulApiException("未找到数据资产信息");
        }

        String extension = "csv";
        try {
            extension = FileUtil.extName(dataProduct.getFileSourceMetadata().getDataAssetFilePath());
        } catch (Exception ignore) {
        }
        String fileName = dataProduct.getDataProductName() + "." + extension;

        response.reset();
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName + ".tar.gz", StandardCharsets.UTF_8));

        if (!companyService.localExistCompanyByRouteId(routerId)) {
            try {
                NodeDTO.HubInfo hubInfo = companyService.getHubInfo(companyDTO.getId());
                String headerHubInfo = Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(hubInfo).getBytes(StandardCharsets.UTF_8));
                DrClientInfoVO router = routerService.getByClientNo(routerId, headerHubInfo);
                Assert.notNull(router, "未找到数据资产所在的连接器节点信息");
                final String routeVirtualIp = router.getClientIp().split(":")[0];

                String transferId = sceneAssetResp.getExt().getTransferResp();
                String downloadUrl = String.format("https://%s:4443/_data-route/transfer/data/%s?accessKey=%s&secretKey=%s&dispatch=%s",
                        routeVirtualIp, transferId, accessKey, secretKey, true);
                log.info("文件交付磋商请求 ————> {}", downloadUrl.substring(0, downloadUrl.lastIndexOf("accessKey") - 1));

                try {
                    ConnectorMetaData connectorMetaData = new ConnectorMetaData();
                    connectorMetaData.setTargetNodeId(routerId);
                    Call<ResponseBody> bodyCall = endpointRemote.transferFilePull(transferId, accessKey, secretKey, connectorMetaData.toBase64());
                    Response<ResponseBody> execute = bodyCall.execute();
                    if (execute.isSuccessful() && execute.body() != null) {
                        try (InputStream inputStream = execute.body().byteStream();
                             OutputStream outputStream = response.getOutputStream()) {

                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            while ((bytesRead = inputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                            outputStream.flush();
                        } catch (Exception e) {
                            log.error("文件下载异常:", e);
                            throw new RestfulApiException("文件下载异常");
                        }
                    }
                } catch (Exception e) {
                    log.error("文件下载异常:", e);
                }
                //safeForwardDownload(downloadUrl, response.getOutputStream());
                // 转发了  这里是买方操作
                AsyncManager.getInstance().execute(() -> orderManagerService.buyerOrderDeliverySuccess(sceneAssetResp, null, DeliveryType.FILE_DOWNLOAD));

            } catch (Exception e) {
                throw new RestfulApiException("数据资产文件下载出错", e);
            }
            return true;
        }

        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(minioProperties.getEndpoint())
                .credentials(accessKey, secretKey)
                .httpClient(customHttpClient())
                .build();
             InputStream inputStream = minioClient.getObject(GetObjectArgs.builder()
                     .bucket(dataProduct.getUserId())
                     .object(Paths.get(dataProduct.getId(), fileName).toString())
                     .build())) {
            transferFile(inputStream, response.getOutputStream());
        } catch (Exception e) {
            throw new RestfulApiException("数据资产文件下载出错", e);
        }

        // todo： 异步日志上报
        // thirdService.logReport(List.of(LogReportReq.builder().apiId(sceneAssetResp.getApiId()).status(1).assetRouteId(extData.getRouteId()).build()));
        if (!dispatch) {
            AsyncManager.getInstance().execute(() -> orderManagerService.buyerOrderDeliverySuccess(sceneAssetResp, null, DeliveryType.FILE_DOWNLOAD));
        }

        return true;
    }

    private void safeForwardDownload(String url, OutputStream outputStream) throws Exception {
        try {
            // 使用HttpURLConnection来获取响应状态和内容
            URL downloadUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) downloadUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(300000); // 5分钟超时

            // 检查HTTP状态码
            int responseCode = connection.getResponseCode();
            if (responseCode != 200) {
                // 读取错误响应
                try (InputStream errorStream = connection.getErrorStream()) {
                    if (errorStream != null) {
                        String errorContent = IOUtils.toString(errorStream, StandardCharsets.UTF_8);
                        log.error("转发下载失败，HTTP状态码: {}, 错误内容: {}", responseCode, errorContent);
                        throw new RestfulApiException("数据资产文件下载失败: HTTP " + responseCode);
                    }
                }
                throw new RestfulApiException("数据资产文件下载失败: HTTP " + responseCode);
            }

            // 检查Content-Type，如果是JSON格式则可能是异常响应
            String contentType = connection.getContentType();
            if (contentType != null && contentType.contains("application/json")) {
                // 读取响应内容检查是否为异常信息
                try (InputStream inputStream = connection.getInputStream()) {
                    String responseContent = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                    // 尝试解析为JSON，如果成功且包含错误信息，则抛出异常
                    try {
                        JSONObject jsonResponse = JSONUtil.parseObj(responseContent);
                        if (jsonResponse.containsKey("success") && !jsonResponse.getBool("success", true)) {
                            String errorMsg = jsonResponse.getStr("message", "未知错误");
                            log.error("转发下载返回异常信息: {}", responseContent);
                            throw new RestfulApiException("数据资产文件下载失败: " + errorMsg);
                        }
                        // 如果是JSON但不是错误信息，可能是正常的JSON响应，也抛出异常
                        log.error("转发下载返回意外的JSON响应: {}", responseContent);
                        throw new RestfulApiException("数据资产文件下载失败: 服务器返回了意外的响应格式");
                    } catch (Exception jsonParseException) {
                        // JSON解析失败，说明不是JSON格式，可能是正常的文件内容
                        // 重新获取输入流并写入输出流
                        try (InputStream retryStream = new URL(url).openStream()) {
                            IOUtils.copy(retryStream, outputStream);
                        }
                    }
                }
            } else {
                // 正常的文件下载，直接复制流
                try (InputStream inputStream = connection.getInputStream()) {
                    IOUtils.copy(inputStream, outputStream);
                }
            }
        } catch (RestfulApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("转发下载过程中发生异常", e);
            throw new RestfulApiException("数据资产文件下载出错", e);
        }
    }

    private void transferFile(InputStream inputStream, OutputStream outputStream) throws Exception {
        FileEncryptKeyHeader fileEncryptKeyHeader = Bean2HeaderUtils.readAndRemoveHeader(inputStream, FileEncryptKeyHeader.class);
        String encryptKey = RSAUtil.decryptByPrivateKey(fileEncryptKeyHeader.getEncryptKey(), (RSAPrivateKey) keyPair.getPrivate());
        int targetSize = 1024;
        byte[] bytes = new byte[targetSize];
        int len;
        int totalRead = 0;
        while ((len = inputStream.read(bytes, totalRead, targetSize - totalRead)) != -1) {
            if (totalRead == targetSize) {
                byte[] bytesDecrypted = SecIslandTEE.tee_SM4_Decrypt_ecb(encryptKey.getBytes(StandardCharsets.UTF_8), bytes);
                outputStream.write(bytesDecrypted);
                totalRead = 0;
            } else {
                totalRead += len;
            }
        }
        if (totalRead > 0) {
            byte[] bytesDecrypted = SecIslandTEE.tee_SM4_Decrypt_ecb(encryptKey.getBytes(StandardCharsets.UTF_8), Arrays.copyOf(bytes, totalRead));
            outputStream.write(bytesDecrypted);
        }
        outputStream.close();
    }

    @Override
    public String datasourceProperties() throws IOException {
        try (InputStream inputStream = Files.newInputStream(Paths.get(DatasourceConstants.DATASOURCE_PATH))) {
            String datasourceForm = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
            JSONObject tree = JSONUtil.parseObj(datasourceForm);
            // TODO 带后期对接TEE
//            if (tree.has("serverfile")) {
//                Iterator<JsonNode> serverFileNode = tree.get("serverfile").elements();
//                while (serverFileNode.hasNext()) {
//                    ObjectNode node = (ObjectNode) serverFileNode.next();
//                    if (node.has("code") && "executorNo".equals(node.get("code").asText())) {
//                        // routeId 作为组织编号
//                        String userOrg = routerService.currentNode().getRouterId();
//                        List<ExecutorInfo> executorInfos = executorCertificateService.getExecutorService(userOrg);
//                        List<Map<String, String>> executors = executorInfos.stream()
//                                .filter(executorInfo -> "1".equals(executorInfo.getStatus()))
//                                .map(executorInfo -> {
//                                            Map<String, String> option = new HashMap<>(2);
//                                            option.put("label", String.format("%s:%s", executorInfo.getIp(), executorInfo.getPort()));
//                                            option.put("value", executorInfo.getExecutorNo());
//                                            return option;
//                                        }
//                                ).collect(Collectors.toList());
//                        node.set("options", JacksonUtils.readTree(JacksonUtils.obj2json(executors)));
//                    }
//                }
//            }
            return tree.toString();
        }
    }

    @Override
    public ApiImportFileVO apiImportUploadBatchParamsFile(MultipartFile file, String separator, Integer hasHeader) throws Exception {
        String fileName = file.getOriginalFilename();
        if (ObjectUtils.isEmpty(fileName) || !fileName.endsWith(".csv")) {
            throw new RestfulApiException("请上传.csv格式文件");
        }
        Charset charset = FileUtils.getFileEncode(file.getInputStream());
        if (charset == null) {
            throw new RestfulApiException("未知的文件编码");
        }
        if (!supportFileEncoding.contains(charset.name())) {
            throw new RestfulApiException("调试数据文件编码格式异常，请上传如下编码格式的文件：" + supportFileEncoding);
        }

        File tempFile = filesStorageService.save2TempFile(file).toFile();
        FilesStorageServiceImpl.checkIfIsShellAndThrowExceptions(tempFile);
        FilesStorageServiceImpl.csvRequired(tempFile);
        tempFile = FilesStorageServiceImpl.convert2UTF8(tempFile);

        String batchParamsFileId = UUID.randomUUID().toString();
        dataAssetCache.put(OperatorConstants.API_IMPORT_BATCH_PARAMS_FILE_ID,
                tempFile.getAbsolutePath().substring(filesStorageService.getRootPath().toAbsolutePath().toString().length() + 1));
        ApiImportFileVO apiImportFileVO = ApiImportFileVO.builder().fileId(batchParamsFileId).fileName(tempFile.getName()).build();

        List<List<String>> lists = new ArrayList<>();
        try (RandomAccessFile raf = new RandomAccessFile(tempFile, "r")) {
            for (int i = 0; i < 10; i++) {
                //解决中文乱码
                String rawStr = raf.readLine();
                if (StringUtils.isBlank(rawStr) || (hasHeader == 1 && i == 0)) {
                    continue;
                }
                String line = new String(rawStr.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                lists.add(Arrays.asList(line.split(separator)));
            }
        }
        apiImportFileVO.setDataList(lists);
        if (lists.isEmpty()) {
            throw new RuntimeException("第一行不能为空");
        }
        return apiImportFileVO;
    }

    @Override
    public String datasourceFileType() throws Exception {
        return IOUtils.toString(new FileInputStream(DatasourceConstants.DATASOURCE_FILE_TYPE), StandardCharsets.UTF_8);
    }

    @Value("${ailand.endpoint.ip}")
    private String routeEndpoint;

    /**
     * API接口导入url限制
     */
    @Override
    public void checkApiImportUrl(String url) {
        // 分割出 ip 信息
        String ip = IpUtils.getIP(url);
        Assert.isTrue(!IpUtils.isLoopBackAddress(ip), String.format("API接口导入禁止访问 %s", ip));
        Assert.isTrue(!"0.0.0.0".equals(ip), "API接口导入禁止访问 0.0.0.0");
        if (!url.startsWith(routeEndpoint + "/_ailand-mpc/mpc/openapi")) { // NOTE: MPC 开放接口放行
            Assert.isTrue(!StringUtils.equals(IpUtils.getIP(routeEndpoint), ip), String.format("API接口导入禁止访问 %s", IpUtils.getIP(routeEndpoint)));
        }
    }

    @Override
    public void downloadBatchParamsFile(AssetType assetType, String dataAssetId, String routerId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        ApiImportExtendBO extendBO = getApiImportExtendBO(assetType, dataAssetId);
        File file = filesStorageService.getRootPath().resolve(extendBO.getBatchParamsPath()).toFile();
        if (!file.exists()) {
            throw new RestfulApiException("文件不存在");
        }
        try (FileInputStream fis = new FileInputStream(file);
             BufferedReader br = new BufferedReader(new InputStreamReader(fis))) {
            OutputStream os = response.getOutputStream();
            String fileName = file.getName();
            String newName = StringUtils.isNotEmpty(fileName) ? fileName : UuidUtils.uuid32();
            //去掉空格，不然会杯具
            newName = newName.replace(" ", "");
            //gb2312防止IE下载文件名中文乱码
            byte[] bytes = request.getHeader("User-Agent").contains("MSIE") || request.getHeader("User-Agent").contains("IE") || request.getHeader("User-Agent").contains("like Gecko") ? newName.getBytes("gb2312") : newName.getBytes(StandardCharsets.UTF_8);
            //输出文件信息
            response.reset();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" + new String(bytes, StandardCharsets.ISO_8859_1));

            long contentLength = fis.available();
            response.setContentLengthLong(contentLength);
            String line;
            while ((line = br.readLine()) != null) {
                os.write(line.getBytes(StandardCharsets.UTF_8));
                os.write(System.lineSeparator().getBytes(StandardCharsets.UTF_8));
            }
            os.flush();
        }
    }

    private ApiImportExtendBO getApiImportExtendBO(AssetType assetType, String dataAssetId) {
        if (AssetType.PRODUCT.equals(assetType)) {
            return dataProductRepository.getReferenceById(dataAssetId).getDataExt().getApiSourceMetadata().getExtend();
        } else {
            return dataResourceRepository.getReferenceById(dataAssetId).getDataExt().getApiSourceMetadata().getExtend();
        }
    }

    @Override
    public List<List<String>> previewBatchParams(AssetType assetType, String dataAssetId, String routerId) {
        ApiImportExtendBO extendBO = getApiImportExtendBO(assetType, dataAssetId);
        if (!extendBO.getIsBatchParams() || ObjectUtils.isEmpty(extendBO.getBatchParamsPath())) {
            return new ArrayList<>();
        }
        return filesStorageService.getPreviewDataFromFile(extendBO.getBatchParamsPath(), ",");
    }

    @Override
    public SimpleLocalFileVO uploadKerberosKeytabFile(MultipartFile file) {
        Path tempPath = filesStorageService.save2KerberosFile(file, LoginContextHolder.currentUser().getId());
        KeyTab keyTab = KeyTab.getInstance(tempPath.toFile());
        if (!keyTab.isValid()) {
            throw new CommonException("非法的 Keytab 文件");
        }

        return new SimpleLocalFileVO(tempPath.toFile().getAbsolutePath(), file.getOriginalFilename());
    }

    @Override
    public SimpleLocalFileVO uploadKerberosConfFile(MultipartFile file) {
        Path tempPath = filesStorageService.save2KerberosFile(file, LoginContextHolder.currentUser().getId());
        FilesStorageServiceImpl.checkIfIsShellAndThrowExceptions(tempPath.toFile());
        return new SimpleLocalFileVO(tempPath.toFile().getAbsolutePath(), file.getOriginalFilename());
    }

    @Override
    public void downloadDownloadDebugFile(AssetType assetType, String assetId, HttpServletResponse response) {
        DataAsset dataAsset = getLocalDataAssetById(assetId);
        Path path = Paths.get(dataAsset.getExtraData().getDebugDataPath());

        if (!path.toFile().exists()) {
            throw new RestfulApiException("下载失败，文件不存在");
        }
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        try (FileInputStream fis = new FileInputStream(path.toFile());
             BufferedReader br = new BufferedReader(new InputStreamReader(fis))) {
            OutputStream os = response.getOutputStream();
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(path.getFileName().toString(), StandardCharsets.UTF_8));

            String header = dataAsset.getExtraData().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery).map(DataSchemaBO::getFieldName).collect(Collectors.joining(","));
            ByteArrayInputStream headerStream = new ByteArrayInputStream((header + System.lineSeparator()).getBytes(StandardCharsets.UTF_8));

            byte[] buf = new byte[1024 * 1024];
            int len;
            while ((len = headerStream.read(buf)) != -1) {
                os.write(buf, 0, len);
            }

            String line;
            boolean headerSkiped = false;
            while ((line = br.readLine()) != null) {
                if (headerSkiped) {
                    os.write(line.getBytes(StandardCharsets.UTF_8));
                    os.write(System.lineSeparator().getBytes(StandardCharsets.UTF_8));
                } else {
                    headerSkiped = true;
                }
            }
            os.flush();
        } catch (Exception e) {
            throw new RestfulApiException("调试数据文件下载出错", e);
        }
    }

    private DebugFileUploadResponse justUploadDebugData(String datasetId, MultipartFile file) {
        File tempFile = filesStorageService.save2TempFile(file).toFile();
        DebugFileUploadResponse result = new DebugFileUploadResponse();
        String tempDebugFileId = UUID.randomUUID().toString();
        String filePath = tempFile.getAbsolutePath();
        dataAssetCache.put(tempDebugFileId, filePath);
        result.setTempDebugFileId(tempDebugFileId);

        if (!ObjectUtils.isEmpty(datasetId)) {
            updateDebugData(datasetId, filePath);
        }
        return result;
    }

    private void updateDebugData(String datasetId, String filePath) {
        try {
            dataProductService.updateDataExt(datasetId, dataProductExt -> {
                dataProductExt.setDebugDataPath(filePath);
                return dataProductExt;
            });
        } catch (Exception ignore) {
            dataResourceService.updateDataExt(datasetId, dataProductExt -> {
                dataProductExt.setDebugDataPath(filePath);
                return dataProductExt;
            });
        }
    }

    public List<DataSchemaBO> parseDataSchema(String separator, String firstLine) {
        String bom = String.valueOf('\ufeff');
        if (firstLine.startsWith(bom)) {
            firstLine = firstLine.substring(bom.length());
        }
        return Arrays.stream(firstLine.split(separator, -1)).map(
                a -> new DataSchemaBO(a.toLowerCase(), "STRING", null, "safeLevel1", true, false, false, false, a, "STRING")
        ).collect(Collectors.toList());
    }

    @Override
    public String uploadAttachFile(MultipartFile file) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        String fileName = UuidUtils.uuid32() + "." + FileUtil.extName(file.getOriginalFilename());
        filesStorageService.save(file,
                filesStorageService.getRootPath()
                        .resolve("attach")
                        .resolve(userDTO.getId())
                        .resolve(fileName));
        return fileName;
    }


    @Override
    public String callDataApiProcess(AssetType assetType, String deliverId, String companyId, String dataAssetId, ApiImportTestVO apiImportTestVO, boolean dispatch) {
        SceneAssetResp sceneAssetResp;
        if (!dispatch) {
            sceneAssetResp = checkOrderLegal(companyId, deliverId, dataAssetId);
        } else {
            sceneAssetResp = null;
        }

        String routerId;
        // 获取资产详情信息
        DataProductVO dataProduct;
        CompanyDTO companyDTO = companyService.localCompany(Long.valueOf(companyId));
        String response;
        try {
            dataProduct = AsyncManager.getInstance().executeFuture(() -> {
                hubShuHanApiClient.setCompany(companyDTO);
                return dataProductService.getDataProductByDataProductPlatformIdFromRemoteNoLoginSpecial(dataAssetId, Long.valueOf(companyId));
            });
            routerId = dataProduct.getProvider().getRouterId();
        } catch (Exception e) {
            log.error("获取资产信息为空", e);
            throw new RestfulApiException("未找到数据资产信息");
        }

        // 不是当前节点 —— 转发到资产节点
        Map<String, String> param = new HashMap<>();
        if (!companyService.localExistCompanyByRouteId(routerId)) {
            try {
                NodeDTO.HubInfo hubInfo = companyService.getHubInfo(Long.valueOf(companyId));
                String headerHubInfo = Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(hubInfo).getBytes(StandardCharsets.UTF_8));
                DrClientInfoVO router = routerService.getByClientNo(routerId, headerHubInfo);

                final String routeVirtualIp = router.getClientIp().split(":")[0];

                String transferId = sceneAssetResp.getExt().getTransferResp();
                String url = String.format("https://%s:4443/_data-route/transfer/data/%s",
                        routeVirtualIp, transferId);
                log.info("API交付磋商请求获取数据 ————> {}", url);

                ConnectorMetaData connectorMetaData = new ConnectorMetaData();
                connectorMetaData.setTargetNodeId(routerId);
                response = endpointRemote.transferApiPull(transferId, apiImportTestVO, connectorMetaData.toBase64());

                // 发送请求并返回数据
                // response = RestTemplateUtil.sendHttp(url, "POST", param, body, BodyTypeEnum.JSON.name(), null, false);

                // 异步日志上报 异步日志上报: TEE 在线接收回调存证
                if (!StringUtils.equalsIgnoreCase(DeliveryType.TEE_ONLINE.name(), sceneAssetResp.getExt().getDeliveryType())) {
                    AsyncManager.getInstance().execute(() -> orderManagerService.buyerOrderDeliverySuccess(sceneAssetResp, String.valueOf(response.hashCode()), DeliveryType.API));
                }
                return response;
            } catch (Exception e) {
                log.error("本次调用API异常流水号: {}  产品id: {} 控制指令编号: {} 目标连接器: {} 发起连接器: {} 内容摘要: {}", param.get(sequenceNum), dataAssetId, deliverId, param.get(targetConnectorId), param.get(issuerConnectorId), param.get(bodyHash));
                throw new RestfulApiException("数据资产调用失败", e);
            }
        }

        // API 地址变更
        String ip = DockerNetworkUtils.dockerVirtualIp(API_GATEWAY_DOCKER_NAME);
        String url = String.format("https://%s:443/data_%s", ip, dataProduct.getId());
        apiImportTestVO.setUrl(url);
        response = callApi(apiImportTestVO, false);

        // 日志上报  1、订单交付累计+1  2、交付登记
        if (!dispatch) {
            // 异步日志上报 异步日志上报: TEE 在线接收回调存证
            if (!StringUtils.equalsIgnoreCase(DeliveryType.TEE_ONLINE.name(), sceneAssetResp.getExt().getDeliveryType())) {
                AsyncManager.getInstance().execute(() -> orderManagerService.buyerOrderDeliverySuccess(sceneAssetResp, String.valueOf(response.hashCode()), DeliveryType.API));
            }
        }

        return response;
    }

    /**
     * 检查订单、交付场景状态
     */
    private SceneAssetResp checkOrderLegal(String companyId, String deliverId, String dataAssetId) {
        Long localCompanyId = Long.valueOf(companyId);
        Company company = AsyncManager.getInstance().executeFuture(() -> {
            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            return companyRepository.findById(localCompanyId).orElse(null);
        });

        SceneAssetResp sceneAssetResp = null;
        if (company != null) {
            sceneAssetResp = AsyncManager.getInstance().executeFuture(() -> {
                TenantContext.setCurrentTenant("tenant_" + company.getId());
                return deliveryService.sceneAssetResp(deliverId, dataAssetId);
            });
        }

        if (sceneAssetResp == null) {
            throw new RestfulApiException("非法请求");
        }

        // step 1、授权有没有过期
        Assert.isTrue(StringUtils.equalsIgnoreCase(sceneAssetResp.getSceneStatus(), "RUNNING"), String.format("交付状态%s", sceneAssetResp.getSceneStatus().equalsIgnoreCase("TERMINATED") ? "已终止" : "已完成"));
        if (StringUtils.equalsIgnoreCase(sceneAssetResp.getOrderStatus(), "COMPLETED")) {
            throw new RestfulApiException("订单已完成，无法调用");
        }
        Assert.isTrue(StringUtils.equalsIgnoreCase(sceneAssetResp.getOrderStatus(), "APPROVED"), "无效订单");
        return sceneAssetResp;
    }

    /**
     * 添加标准参数
     */
    private Map<String, String> standardParam(String deliverId, String datasetId, String body, String target, String issuer) {
        Map<String, String> param = new HashMap<>();
        // 请求唯一标识
        param.put(sequenceNum, IdUtil.fastSimpleUUID());
        // 数据产品唯一标识
        param.put(productId, datasetId);
        // 控制指令编号
        param.put(ctrlInstructionId, deliverId);
        // 目标连接器
        param.put(targetConnectorId, target);
        // 发起连接器
        param.put(issuerConnectorId, issuer);
        // 鉴权信息暂时用不到
        param.put(connectorProof, UUID.randomUUID().toString());
        // body hash
        param.put(bodyHash, Md5Utils.sha256Hash(body));
        // 转发
        param.put(dispatch, Boolean.TRUE.toString());
        return param;
    }

    @Override
    public String callApi(ApiImportTestVO apiImportTestVO, boolean dispatch) {
        // API接口导入url限制
        checkApiImportUrl(apiImportTestVO.getUrl());
        // NOTE 解决交付页面 连接测试 内外网 问题
        URI originalURI = URI.create(apiImportTestVO.getUrl());
        URI endpointURI = URI.create(routeEndpoint);
        if (originalURI.getHost().equals(endpointURI.getHost())) {
            apiImportTestVO.setUrl(apiImportTestVO.getUrl().replace(originalURI.getHost(), routerInternalIp));
        }
        if (dispatch) {
            // 转发
            String dispatchLocalUrl = apiImportTestVO.getUrl();
            String body = JacksonUtils.obj2json(apiImportTestVO);

            // 发送请求并返回数据
            return RestTemplateUtil.sendHttp(dispatchLocalUrl, "POST", null, body, BodyTypeEnum.JSON.name(), null, false);
        }

        String url = apiImportTestVO.getUrl();
        MethodEnum method = apiImportTestVO.getMethod();
        List<ParamsBO> params = apiImportTestVO.getParams();
        String body = apiImportTestVO.getBody();
        BodyTypeEnum bodyType = apiImportTestVO.getBodyType();
        List<ParamsBO> headersBOS = apiImportTestVO.getHeaders();
        // 组装Params参数
        LinkedHashMap<String, ?> paramsReal = HttpReaderTaskUtil.buildParams(params);
        // 组装Body参数
        String bodyReal = HttpReaderTaskUtil.buildBody(bodyType.toString(), body);
        // 发送请求并返回数据
        Map<String, Object> headers = headersBOS.stream().collect(Collectors.toMap(ParamsBO::getKey, ParamsBO::getValue, (s1, s2) -> s2));
        return RestTemplateUtil.sendHttp(url, method.toString(), paramsReal, bodyReal, bodyType.toString(), headers, false);
    }


}
