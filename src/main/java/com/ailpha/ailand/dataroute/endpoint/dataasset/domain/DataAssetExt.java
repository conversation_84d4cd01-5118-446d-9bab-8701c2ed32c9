package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PROTECTED)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataAssetExt implements Serializable {
    /**
     * 数据资产中文名称
     */
    String assetNameCN;
    /**
     * 数据产品类型: "01 数据集", "02 API产品", "03 数据应用", "04 数据报告", "05 其他"
     */
    String type;
    Double dataSize;
    /**
     * 数据规模单位（单位为MB、GB、TB）
     */
    String dataSizeUnit;
    Integer updateFrequency;
    String updateFrequencyUnit;
    /**
     * 地域分类
     */
    String region;
    /**
     * 地域分类(前端回显用)
     */
    String region1;
    /**
     * 是否涉及个人信息：0:否，1:是
     */
    String personalInformation;
    /**
     * 产品来源
     */
    String source;
    /**
     * 产品关联数据资源统一标识，基于那些数据资源形成的数据产品。
     */
    String platformResourceId;
    /**
     * 数据资源登记、数据产品登记、数据产品上架业务类型的单据唯一ID ，此ID可用于查询状态和标识
     */
    String processId;
    /**
     * 其他相关说明
     */
    String note;
    /**
     * 数据资产预处理状态：已创建、已处理
     */
    @Builder.Default
    DataAssetPrepareStatus dataAssetPrepareStatus = DataAssetPrepareStatus.CREATED;
    /**
     * 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销
     */
    String publishStatus;
    /**
     * 是否还有未同步业务状态的上架状态
     */
    String hasNonSyncedPublishStatus = "0";
    /**
     * 数据预处理失败原因
     */
    String processResultMessage;
    /**
     * 数据库数据资产元数据
     */
    @Builder.Default
    Map<DeliveryMode, ProcessResult> processResult = new HashMap<>();
    /**
     * 标签
     */
    List<String> tag;
    /**
     * 数据覆盖周期开始时间（精确到2024.1 ）
     */
    String dataCoverageTimeStart;
    /**
     * 数据覆盖周期结束时间（精确到2024.12）
     */
    String dataCoverageTimeEnd;
    /**
     * 数据类型：结构化数据、非结构化数据
     */
    DataType dataType;
    /**
     * 数据类型 结构化对应数据集；非结构化对应文本、图像、模型
     */
    String dataType1;

    public static final List<String> SUPPORTED_DATATYPE1 = List.of("数据集", "文件", "图像", "模型");

    /**
     * 文件后缀名 .tar.gz, .zip .csv
     */
    String datasetFileType;
    /**
     * API查询方式
     */
    APIQueryWay apiQueryWay;
    /**
     * 调试数据来源
     */
    DebugDataSourceEnum debugDataSource;
    /**
     * 调试数据路径
     */
    String debugDataPath;
    /**
     * 调试数据分隔符
     */
    String separator;
    /**
     * 调试数据是否包含表头
     */
    Integer hasHeader;
    /**
     * 数据结构
     */
    List<DataSchemaBO> dataSchema;
    /**
     * 网关服务路由ID
     */
    String gatewayServiceRouteId;
    /**
     * API数据资产元数据
     */
    @Builder.Default
    APISourceMetadata apiSourceMetadata = new APISourceMetadata();
    /**
     * 文件数据资产元数据
     */
    @Builder.Default
    FileSourceMetadata fileSourceMetadata = new FileSourceMetadata();
    /**
     * 数据库数据资产元数据
     */
    @Builder.Default
    DatabaseSourceMetadata databaseSourceMetadata = new DatabaseSourceMetadata();
    /**
     * ApiSort元数据
     */
    @Builder.Default
    AiSortMetadata aiSortMetadata = new AiSortMetadata();
    /**
     * 绑定交易所插件
     */
    List<Long> exchangePluginIds;

    List<Long> certificatePluginIds;
    /**
     * 产品血缘
     */
    String lineage;
    /**
     * 平台生成(MPC) 结果集(openapiId)
     */
    String mpcOpenAPIId;
    /**
     * 行业分类（前端回显用）
     */
    String industry1;
    /**
     * 是否改写响应体
     */
    Boolean extractResponse;

    String createIp;

    // 声明信息
    QualificationDoc qualificationDoc;

    @Builder.Default
    int dataVersion = 0;

    /**
     * 数据主体：01-个人信息,02-企业数据,03-公共数据
     */
    String dataSubject;
    /**
     * 数据资源标识码 数组型（一个或多个数据源标识码）
     */
    String resourceId;
    /**
     * 资源格式
     */
    String resourceFormat;
    /**
     * 登记提交时间
     */
    Long registrationSubmitTime;
    /**
     * 登记时间
     */
    Long registrationTime;
    /**
     * 最近登记更新时间
     */
    Long registrationUpdateTime;
    /**
     * 上架提交时间
     */
    Long publishSubmitTime;
    /**
     * 上架时间
     */
    Long publishTime;
    /**
     * 最近上架更新时间
     */
    Long publishUpdateTime;

    String platformId;

    String other;


    /**
     * 接入数据的更新方式：ONCE -> 单次 SCHEDULE -> 定时 MANUAL -> 手动
     */
    UpdateWay updateWay;
    /**
     * 更新类型：0 -> 按天执行, 1 -> 按周执行, 2 -> 按月执行, 3 -> 单次执行 4 -> 按小时执行, 5 -> 多次执行
     */
    SchedulerPeriodEnum updateFreq;
    /**
     * 选中的日期，星期一到星期日为1-7，月份日期1-31
     */
    Integer selectDate;
    /**
     * 几点执行，默认0, 范围 0-23
     */
    Integer selectHour;

    /**
     * 是否是大模型
     */
    Boolean isLLM;
    @Builder.Default
    ModelMetadata modelMetadata = new ModelMetadata();
//    String modelFilePath; -> fileSourceMetadata.dataAssetFilePath

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ProcessResult implements Serializable {
        /**
         * 数据预处理状态
         */
        @Builder.Default
        DataAssetPrepareStatus dataAssetPrepareStatus = DataAssetPrepareStatus.CREATED;
        /**
         * 数据预处理失败原因
         */
        String processResultMessage;
    }

    List<ProcessLog> processLogs;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ProcessLog implements Serializable {
        /**
         * 阶段
         */
        String stage;
        /**
         * 时间戳
         */
        Long timestamp;
        /**
         * 处理意见/备注
         */
        String message;
        /**
         * 处理人
         */
        String operator;
    }

    public DataAssetExt addProcessLog(String stage, Long timestamp, String message, String operator) {
        if (processLogs == null) {
            processLogs = new ArrayList<>();
        }
        processLogs.add(new ProcessLog(stage, timestamp, message, operator));
        return this;
    }

    public static String fileContentBase64(Path filePath) {
        if (filePath == null || !filePath.toFile().exists() || !filePath.toFile().isFile()) {
            return null;
        }
        try {
            return Base64.getEncoder().encodeToString(Files.readAllBytes(filePath));
        } catch (IOException e) {
            log.warn("获取文件 {} 内容Base64失败", filePath, e);
            return null;
        }
    }
}
