package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.dataasset.request.APISourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AiSortMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DatabaseSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.FileSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.GanzhouPublishAttachFile;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataAssetExt {
    /**
     * 数据资产中文名称
     */
    String assetNameCN;
    /**
     * 数据产品类型: "01 数据集", "02 API产品", "03 数据应用", "04 数据报告", "05 其他"
     */
    String type;
    /**
     * 地域分类
     */
    String region;
    /**
     * 地域分类(前端回显用)
     */
    String region1;
    /**
     * 是否涉及个人信息：0:否，1:是
     */
    String personalInformation;
    /**
     * 产品来源
     */
    String source;
    /**
     * 产品关联数据资源统一标识，基于那些数据资源形成的数据产品。
     */
    String platformResourceId;
    /**
     * 其他相关说明
     */
    String note;
    /**
     * 数据资产预处理状态：已创建、已处理
     */
    @Builder.Default
    DataAssetPrepareStatus dataAssetPrepareStatus = DataAssetPrepareStatus.CREATED;
    /**
     * 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销
     * 0 未发布 1 已发布 ==> 赣州
     */
    String publishStatus;
    /**
     * 数据预处理失败原因
     */
    String processResultMessage;
    /**
     * 数据库数据资产元数据
     */
    @Builder.Default
    Map<DeliveryMode, ProcessResult> processResult = new HashMap<>();
    /**
     * 标签
     */
    List<String> tag;
    /**
     * 数据覆盖周期开始时间（精确到2024.1 ）
     */
    String dataCoverageTimeStart;
    /**
     * 数据覆盖周期结束时间（精确到2024.12）
     */
    String dataCoverageTimeEnd;
    /**
     * 数据类型：结构化数据、非结构化数据、模型
     */
    DataType dataType;
    /**
     * 数据类型 结构化对应数据集；非结构化对应文本、图像
     */
    String dataType1;
    /**
     * API查询方式
     */
    APIQueryWay apiQueryWay;
    /**
     * 调试数据来源
     */
    DebugDataSourceEnum debugDataSource;
    /**
     * 调试数据路径
     */
    String debugDataPath;
    /**
     * 调试数据分隔符
     */
    String separator;
    /**
     * 调试数据是否包含表头
     */
    Integer hasHeader;
    /**
     * 数据结构
     */
    List<DataSchemaBO> dataSchema;
    /**
     * 网关服务路由ID
     */
    String gatewayServiceRouteId;
    /**
     * API数据资产元数据
     */
    @Builder.Default
    APISourceMetadata apiSourceMetadata = new APISourceMetadata();
    /**
     * 文件数据资产元数据
     */
    @Builder.Default
    FileSourceMetadata fileSourceMetadata = new FileSourceMetadata();
    /**
     * 数据库数据资产元数据
     */
    @Builder.Default
    DatabaseSourceMetadata databaseSourceMetadata = new DatabaseSourceMetadata();
    /**
     * ApiSort元数据
     */
    @Builder.Default
    AiSortMetadata aiSortMetadata = new AiSortMetadata();
    /**
     * 绑定交易所插件
     */
    List<Long> exchangePluginIds;
    /**
     * 绑定数字证书插件
     */
    List<Long> certificatePluginIds;
    /**
     * 产品血缘
     */
    String lineage;
    /**
     * 平台生成(MPC) 结果集(openapiId)
     */
    String mpcOpenAPIId;
    /**
     * 行业分类（前端回显用）
     */
    String industry1;
    /**
     * 是否改写响应体
     */
    Boolean extractResponse;

    String createIp;

    // 声明信息
    QualificationDoc qualificationDoc;
    // 声明信息（文件地址数瀚）
    QualificationDoc qualificationDocShuhan;
    // 赣州平台发布附件
    GanzhouPublishAttachFile ganzhouPublishAttachFile;

    // 业务节点 -> launchId
    Map<String, Long> serviceNodeLaunchIds;

    int dataVersion = 0;

    /**
     * 数据主体：01-个人信息,02-企业数据,03-公共数据
     */
    String dataSubject;
    /**
     * 数据资源标识码 数组型（一个或多个数据源标识码）
     */
    String resourceId;
    /**
     * 资源格式
     */
    String resourceFormat;
    /**
     * 登记提交时间
     */
    Long registrationSubmitTime;
    /**
     * 登记时间
     */
    Long registrationTime;
    /**
     * 最近登记更新时间
     */
    Long registrationUpdateTime;
    /**
     * 上架提交时间
     */
    Long publishSubmitTime;
    /**
     * 上架时间
     */
    Long publishTime;
    /**
     * 最近上架更新时间
     */
    Long publishUpdateTime;

    String platformId;

    String other;

    /**
     * 资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据
     */
    String resourceType;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ProcessResult {
        /**
         * 数据预处理状态
         */
        @Builder.Default
        DataAssetPrepareStatus dataAssetPrepareStatus = DataAssetPrepareStatus.CREATED;
        /**
         * 数据预处理失败原因
         */
        String processResultMessage;
    }
}
