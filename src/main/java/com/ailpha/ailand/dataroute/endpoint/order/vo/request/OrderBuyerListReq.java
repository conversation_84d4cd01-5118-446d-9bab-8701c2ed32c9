package com.ailpha.ailand.dataroute.endpoint.order.vo.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderBuyerListReq extends Page implements Serializable {

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "订单交付方式")
    DeliveryMode deliveryMode;

    @Schema(description = "交付方式——国标筛选 01、02、03")
    String deliveryMethod;

    @Schema(description = "交付使用场景")
    DeliveryMode deliverySceneMode;

    @Schema(description = "状态：APPLY（待审批）APPROVED（通过）REJECTED（拒绝）TERMINATED（已终止）COMPLETED（已完成）")
    String status;
}
