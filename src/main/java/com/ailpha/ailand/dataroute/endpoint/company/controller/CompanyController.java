package com.ailpha.ailand.dataroute.endpoint.company.controller;

import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.dto.*;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.invoke.api.tuple.Tuple3;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Tag(name = "接入主体管理")
@RestController
@RequestMapping("/company")
@RequiredArgsConstructor
public class CompanyController {
    private final CompanyService companyService;
    private final UserService userService;

    @PostMapping("/apply")
    @Operation(summary = "提交企业认证申请")
    @PreAuthorize("hasAuthority('COMPANY_ADMIN')")
    public SuccessResponse<Boolean> apply(@RequestBody CompanyApplyDTO dto) {
        Company company = companyService.apply(dto);
        companyService.updatePublicSchema(company);
        userService.reloadUserDetails();
        return SuccessResponse.success(true).build();
    }

    // 企业详情
    @GetMapping("/detail/{id}")
    @Operation(summary = "企业详情")
    @PreAuthorize("hasAnyAuthority('COMPANY_ADMIN','SUPER_ADMIN')")
    public SuccessResponse<CompanyDetailResponse> detail(@PathVariable Long id) {
        Tuple3<Company, User, Boolean> companyDetail = companyService.getCompanyDetail(id);
        if (companyDetail.third)
            companyService.updatePublicSchema(companyDetail.first);
        return SuccessResponse.success(CompanyDetailResponse.of(companyDetail.first, companyDetail.second)).build();
    }

    @PostMapping("page")
    @Operation(summary = "查询接入主体列表")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<List<CompanyListResponse>> list(@RequestBody CompanyPageRequest request) {
        return companyService.list(request);
    }

    // 重置企业用户密码
    @PostMapping("/resetPassword/{id}")
    @Operation(summary = "重置企业用户密码")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<String> resetPassword(@PathVariable Long id) {
        return SuccessResponse.success(companyService.resetPassword(id)).build();
    }

    // 禁用企业
    @PostMapping("/disable/{id}")
    @Operation(summary = "禁用企业")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<String> disable(@PathVariable Long id) {
        Company company = companyService.disable(id);
        companyService.updateTenantSchema(company);
        return SuccessResponse.success("禁用成功").build();
    }

    @PostMapping("/enable/{id}")
    @Operation(summary = "启用企业")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<String> enable(@PathVariable Long id) {
        Company company = companyService.enable(id);
        companyService.updateTenantSchema(company);
        return SuccessResponse.success("启用成功").build();
    }

    // 新增企业
    @PostMapping("/add")
    @Operation(summary = "新增企业")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<String> add(@RequestBody AddCompanyRequest request) {
        companyService.add(request);
        return SuccessResponse.success("新增成功").build();
    }

    // 编辑企业
    @PostMapping("/edit")
    @Operation(summary = "编辑企业")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<String> edit(@RequestBody UpdateCompanyRequest request) {
        companyService.edit(request);
        return SuccessResponse.success("编辑成功").build();
    }


    @PostMapping("/upload")
    @Operation(summary = "上传附件")
    @PreAuthorize("hasAnyAuthority('COMPANY_ADMIN','TRADER')")
    public SuccessResponse<UploadResponse> upload(MultipartFile file) {
        Tuple2<String, String> data = companyService.uploadFile(file);
        return SuccessResponse.success(UploadResponse.builder().local(data.first).remote(data.second).build()).build();
    }

}