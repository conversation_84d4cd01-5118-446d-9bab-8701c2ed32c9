package com.ailpha.ailand.dataroute.endpoint.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ApiGateResponse {
    @Schema(description = "api总览")
    BaseInfo resApiSummary;

    @Schema(description = "api请求趋势")
    List<ApiHourRequest> requestCount;
    @Schema(description = "api请求top10")
    List<ApiRequestTop10> requestCountWithAPITop10;

    @Schema(description = "安全规则分布")
    List<SecurityRule> resRiskLevelSecurityDistribution;
    @Schema(description = "访问控制")
    List<SecurityRule> resRiskLevelAccessDistribution;


    @Data
    public static class BaseInfo {
        @Schema(description = "api总数")
        Long totalApiNum;
        @Schema(description = "活跃API数")
        Long activeApiNum;
        @Schema(description = "敏感API数")
        Long sensitiveApiNum;
    }


    @Data
    private static class ApiHourRequest {
        @Schema(description = "时间")
        String trendTime;
        @Schema(description = "坐标值")
        Long trendValue;
    }

    @Data
    public static class ApiRequestTop10 {
        @Schema(description = "id")
        String topId;
        @Schema(description = "名称")
        String topName;
        @Schema(description = "值")
        Long topValue;
    }

    @Data
    private static class SecurityRule {
        @Schema(description = "分类")
        String alarmLevel1;
        @Schema(description = "名称")
        String alarmLevelCN;
        @Schema(description = "值")
        Long alarmCount;
    }


}
