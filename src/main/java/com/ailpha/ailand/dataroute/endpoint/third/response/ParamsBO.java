package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * 2023/3/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ParamsBO {
    /**
     * 键
     */
    String key;

    /**
     * 值
     */
    String value;

    /**
     * 数据类型
     */
    DataTypeEnum dataType;

    /**
     * 描述
     */
    String desc;
}
