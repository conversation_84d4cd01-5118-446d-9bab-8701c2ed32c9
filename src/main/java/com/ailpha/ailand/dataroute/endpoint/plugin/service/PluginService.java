package com.ailpha.ailand.dataroute.endpoint.plugin.service;

import com.ailpha.ailand.biz.api.collector.BlockchainPluginPageRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainDetailResponse;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainListResponse;
import com.dbapp.rest.response.SuccessResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: yuwenping
 * @date: 2025/5/8 10:26
 * @Description:
 */
public interface PluginService {
    String storePlugin(MultipartFile file);

    void saveBlockchainPlugin(BlockchainPluginRequest request);

    void upBlockchain(String moduleName, String data);

    SuccessResponse<List<BlockchainListResponse>> listBlockchainPlugin(BlockchainPluginPageRequest request);

    SuccessResponse<BlockchainDetailResponse> blockchainPluginDetail(Long id);

    void updateBlockchainPlugin(BlockchainPluginUpdateRequest request);

    void updateStatusBlockchainPlugin(BlockchainPluginUpdateRequest request);
}
