package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetBeneficiaryRelDTOListReq implements Serializable {

    @Schema(description = "资产ID列表")
    List<String> assetIds;

    @Schema(description = "获益人用户ID")
    String beneficiaryId;
}
