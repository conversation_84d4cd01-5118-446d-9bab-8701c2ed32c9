package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "上下架请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OnlineOfflineRequest {
    @Schema(description = "资产id", requiredMode = Schema.RequiredMode.REQUIRED)
    String assetId;
}
