package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.ApiGateResponse;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.restclient.BufferingClientHttpResponseWrapper;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.ApiGateCommonResult;
import com.ailpha.ailand.dataroute.endpoint.third.response.PathTypeBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.ResponseBO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.ehcache.Cache;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GatewayWebApi {
    private final Cache<String, String> dataAssetCache;
    private final BaseCapabilityManager baseCapabilityManager;

    private final static String DATA_ASSET_FIELD_PATH_KEY = "data_asset_field_path_%s";


    public GatewayWebApi(BaseCapabilityManager baseCapabilityManager, Cache<String, String> dataAssetCache) {
        this.baseCapabilityManager = baseCapabilityManager;
        this.dataAssetCache = dataAssetCache;
    }


    private RestClient restClient() {
        URL url = URLUtil.url(baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.API_GATE).getBaseUrl());
        return RestClient.builder()
                .baseUrl(String.format("%s://%s", url.getProtocol(), url.getAuthority()))
                .requestInterceptor((request, body, execution) -> {
                    if (log.isDebugEnabled()) {
                        log.debug("API网关接口 {} 请求体 {}", request.getURI(), new String(body, Charset.defaultCharset()));
                    }
                    ClientHttpResponse response = execution.execute(request, body);
                    BufferingClientHttpResponseWrapper httpResponseWrapper = new BufferingClientHttpResponseWrapper(response);
                    String responseBody = IOUtils.toString(httpResponseWrapper.getBody(), Charset.defaultCharset());
                    JsonNode jsonNode = JacksonUtils.readTree(responseBody);
                    if (log.isDebugEnabled()) {
                        log.debug("API网关接口响应 {}", jsonNode);
                    }
                    return httpResponseWrapper;
                })
                .build();
    }

    /**
     * 根据服务名称查询API服务
     *
     * @param serviceName 服务名称
     * @return API服务详情
     */
    public DescribeServicesResponse describeServices(String serviceName, int page, int size) {
        DescribeServicesResponse describeServicesResponse = restClient().get()
                .uri(
                        uriBuilder -> uriBuilder.path("/webapi/apig/5.0/DescribeServices.json")
                                .queryParam("name", serviceName) // 模糊查询
                                .queryParam("currentPage", page)
                                .queryParam("pageSize", size)
                                .build()
                ).retrieve()
                .body(DescribeServicesResponse.class);
        if (describeServicesResponse == null || !describeServicesResponse.isSuccess()) {
            log.error("根据服务名称 {} 查询API服务失败: {}", serviceName, JacksonUtils.obj2json(describeServicesResponse));
        }
        return describeServicesResponse;
    }

    /**
     * 根据服务名称查询API服务
     *
     * @param serviceName 服务名称
     * @return API服务详情
     */
    public DescribeServicesResponse describeServices(String serviceName) {
        return describeServices(serviceName, 1, 1);
    }

    /**
     * 创建API服务
     * 用户名 -> API服务名称
     * 数据资产 -> API
     * 一个用户对应一个API网关服务，同一个用户的API数据资产挂载一个网关服务下
     */
    public CreateServiceResponse createService(CreateServiceRequest createServiceRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(createServiceRequest));
        CreateServiceResponse createServiceResponse = restClient().post()
                .uri("/webapi/apig/5.0/CreateService.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(CreateServiceResponse.class);
        if (createServiceResponse == null || !createServiceResponse.isSuccess()) {
            log.error("创建API服务失败: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(createServiceResponse));
        }
        Assert.isTrue(createServiceResponse != null && createServiceResponse.isSuccess(), createServiceResponse != null ?
                "创建API服务失败: " + createServiceResponse.getMessage() : "创建API服务失败");
        return createServiceResponse;
    }

    /**
     * 创建API调用者
     */
    public CreateConsumerResponse createConsumer(CreateConsumerRequest createConsumerRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(createConsumerRequest));
        CreateConsumerResponse createConsumerResponse = restClient().post()
                .uri("/webapi/apig/5.0/CreateConsumer.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(CreateConsumerResponse.class);
        if (createConsumerResponse == null || !createConsumerResponse.isSuccess()) {
            log.error("创建API调用者: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(createConsumerResponse));
        }
        Assert.isTrue(createConsumerResponse != null && createConsumerResponse.isSuccess(), createConsumerResponse != null ?
                "创建API调用者失败: " + createConsumerResponse.getMessage() : "创建API调用者失败");
        return createConsumerResponse;
    }

    /**
     * 查询API调用者
     *
     * @param describeConsumerRequest 查询API调用者请求参数
     * @return API调用者详情
     */
    public DescribeConsumerResponse describeConsumer(DescribeConsumerRequest describeConsumerRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(describeConsumerRequest));
        DescribeConsumerResponse describeConsumerResponse = restClient().post()
                .uri("/webapi/apig/5.0/DescribeConsumer.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map).retrieve()
                .body(DescribeConsumerResponse.class);
        if (describeConsumerResponse == null || !describeConsumerResponse.isSuccess()) {
            log.error("查询API调用者: {}, {}", JacksonUtils.obj2json(describeConsumerRequest), JacksonUtils.obj2json(describeConsumerResponse));
        }
        return describeConsumerResponse;
    }

    public DescribeAuthzConfigResponse describeAuthzConfig(DescribeAuthzConfigRequest describeAuthzConfigRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(describeAuthzConfigRequest));
        DescribeAuthzConfigResponse describeAuthzConfigResponse = restClient().post()
                .uri("/webapi/apig/5.0/DescribeAuthzConfig.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(DescribeAuthzConfigResponse.class);
        if (describeAuthzConfigResponse == null || !describeAuthzConfigResponse.isSuccess()) {
            log.error("查询API授权配置: {}, {}", JacksonUtils.obj2json(describeAuthzConfigRequest), JacksonUtils.obj2json(describeAuthzConfigResponse));
        }
        return describeAuthzConfigResponse;
    }


    /**
     * 创建API授权配置
     *
     * @param createAuthzConfigRequest 创建API授权配置请求
     * @return 创建API授权配置响应
     */
    public CreateAuthzConfigResponse createAuthzConfig(CreateAuthzConfigRequest createAuthzConfigRequest) {
        DescribeAuthzConfigResponse describeAuthzConfigResponse = describeAuthzConfig(DescribeAuthzConfigRequest.builder().username(createAuthzConfigRequest.getUsername()).build());
        if (describeAuthzConfigResponse.isSuccess()) {
            // NOTE：获取路由列表用于补充createAuthzConfigRequest中datasource中缺失的value(API路由名称)
            DescribeRoutesResponse describeRoutesResponse = this.describeRoutes(null, 1, 1000);
            Map<String, DescribeRouteResponse.InvokeResult> routerMap = describeRoutesResponse.getData().getInvokeResult().getRows().stream()
                    .collect(Collectors.toMap(DescribeRouteResponse.InvokeResult::getId, Function.identity()));

            List<CreateAuthzConfigRequest.Allowed> oldAllowedList = describeAuthzConfigResponse.getData().getInvokeResult().getAllowed().stream()
                    .map(describeAuthzConfigResponseAllowd -> CreateAuthzConfigRequest.Allowed.builder()
                            .serviceId(describeAuthzConfigResponseAllowd.getServiceId())
                            .routers(describeAuthzConfigResponseAllowd.getRouters())
                            .dataSource(describeAuthzConfigResponseAllowd.getRouters().stream().map(
                                            router -> {
                                                DescribeRouteResponse.InvokeResult routerInfo = routerMap.get(router);
                                                if (routerInfo == null) {
                                                    return null;
                                                }
                                                return CreateAuthzConfigRequest.DataSource.builder()
                                                        .serviceId(routerInfo.getServiceId())
                                                        .key(routerInfo.getId())
                                                        .value(routerInfo.getName())
                                                        .build();
                                            }
                                    ).filter(Objects::nonNull)
                                    .collect(Collectors.toList()))
                            .build()
                    ).toList();
            createAuthzConfigRequest.getInvokeParam1().getAllowed().addAll(oldAllowedList);
            HashMap<String, ArrayList<CreateAuthzConfigRequest.Allowed>> allowedMap = createAuthzConfigRequest.getInvokeParam1().getAllowed().stream()
                    .collect(Collectors.groupingBy(CreateAuthzConfigRequest.Allowed::getServiceId, HashMap::new, Collectors.toCollection(ArrayList::new)));
            List<CreateAuthzConfigRequest.Allowed> allowedList = allowedMap.values().stream().map(allowedUnderSameService -> {
                Set<CreateAuthzConfigRequest.DataSource> mergedSource = allowedUnderSameService.stream()
                        .map(CreateAuthzConfigRequest.Allowed::getDataSource)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .collect(Collectors.toSet());
                Optional<CreateAuthzConfigRequest.Allowed> notEmptySourceAllowed = allowedUnderSameService.stream()
                        .filter(allowed -> allowed.getDataSource() != null && !allowed.getDataSource().isEmpty())
                        .findFirst();
                if (notEmptySourceAllowed.isPresent()) {
                    CreateAuthzConfigRequest.Allowed allowed = notEmptySourceAllowed.get();
                    allowed.setDataSource(List.copyOf(mergedSource));
                    allowed.setRouters(mergedSource.stream().map(CreateAuthzConfigRequest.DataSource::getKey).toList());
                    return allowed;
                }
                return allowedUnderSameService.getFirst();
            }).toList();
            createAuthzConfigRequest.getInvokeParam1().setAllowed(allowedList);
        }
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(createAuthzConfigRequest));
        CreateAuthzConfigResponse createAuthzConfigResponse = restClient().post()
                .uri("/webapi/apig/5.0/CreateAuthzConfig.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(CreateAuthzConfigResponse.class);
        if (createAuthzConfigResponse == null || !createAuthzConfigResponse.isSuccess()) {
            log.error("创建API授权配置: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(createAuthzConfigResponse));
        }
        Assert.isTrue(createAuthzConfigResponse != null && createAuthzConfigResponse.isSuccess(), createAuthzConfigResponse != null ?
                "创建API授权配置失败: " + createAuthzConfigResponse.getMessage() : "创建API授权配置失败");
        return createAuthzConfigResponse;
    }

    /**
     * 创建API路由
     */
    public CreateRouteResponse createRoute(CreateRouteRequest createRouteRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(createRouteRequest));
        CreateRouteResponse createRouteResponse = restClient().post()
                .uri("/webapi/apig/5.0/CreateRoute.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(CreateRouteResponse.class);
        if (createRouteResponse == null || !createRouteResponse.isSuccess()) {
            log.error("创建API路由: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(createRouteResponse));
        }
        Assert.isTrue(createRouteResponse != null && createRouteResponse.isSuccess(), createRouteResponse != null ?
                "创建API路由失败: " + createRouteResponse.getMessage() : "创建API路由失败");
        return createRouteResponse;
    }

    /**
     * 获取网关路由信息
     *
     * @param describeRouteRequest 获取网关路由信息请求
     * @return 网关路由信息
     */
    public DescribeRouteResponse describeRoute(DescribeRouteRequest describeRouteRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(describeRouteRequest));
        DescribeRouteResponse describeRouteResponse = restClient().post()
                .uri("/webapi/apig/5.0/DescribeRoute.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(DescribeRouteResponse.class);
        if (describeRouteResponse == null || !describeRouteResponse.isSuccess()) {
            log.error("获取网关路由信息: {}, {}", JacksonUtils.obj2json(describeRouteRequest), JacksonUtils.obj2json(describeRouteResponse));
        }
        return describeRouteResponse;
    }

    public DescribeRoutesResponse describeRoutes(String routeName) {
        return this.describeRoutes(routeName, 1, 1);
    }

    /**
     * 获取网关路由信息
     *
     * @param routeName 网关路由名称
     * @return 网关路由信息
     */
    public DescribeRoutesResponse describeRoutes(String routeName, int currentPage, int pageSize) {
        DescribeRoutesResponse describeRoutesResponse = restClient().get()
                .uri(
                        uriBuilder -> uriBuilder.path("/webapi/apig/5.0/DescribeRouters.json")
                                .queryParam("routeName", routeName)
                                .queryParam("currentPage", currentPage)
                                .queryParam("pageSize", pageSize)
                                .build()
                ).retrieve()
                .body(DescribeRoutesResponse.class);
        if (describeRoutesResponse == null || !describeRoutesResponse.isSuccess()) {
            log.error("根据路由名称 {} 获取网关路由信息: {}", routeName, JacksonUtils.obj2json(describeRoutesResponse));
        }
        Assert.isTrue(describeRoutesResponse != null && describeRoutesResponse.isSuccess(), describeRoutesResponse != null ?
                "获取网关路由信息失败: " + describeRoutesResponse.getMessage() : "获取网关路由信息失败");
        return describeRoutesResponse;
    }

    public DeleteRouteResponse deleteRoute(DeleteRouteRequest deleteRouteRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(deleteRouteRequest));
        DeleteRouteResponse deleteRouteResponse = restClient().post()
                .uri("/webapi/apig/5.0/DeleteRoute.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(DeleteRouteResponse.class);
        if (deleteRouteResponse == null || !deleteRouteResponse.isSuccess()) {
            log.error("删除网关路由信息: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(deleteRouteResponse));
        }
        return deleteRouteResponse;
    }

    public DeleteServiceResponse deleteService(DeleteServiceRequest deleteServiceRequest) {
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("data", JacksonUtils.obj2json(deleteServiceRequest));
        DeleteServiceResponse deleteServiceResponse = restClient().post()
                .uri("/webapi/apig/5.0/DeleteServices.json")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .body(map)
                .retrieve()
                .body(DeleteServiceResponse.class);
        if (deleteServiceResponse == null || !deleteServiceResponse.isSuccess()) {
            log.error("删除网关服务信息: {}, {}", JacksonUtils.obj2json(map), JacksonUtils.obj2json(deleteServiceResponse));
        }
        return deleteServiceResponse;
    }

    private CreateRouteRequest.InvokeParam.InvokeParamBuilder generateCreateRouteRequestInvokeParamBuilder(String serviceName, String routeName, String targetUri, boolean buyer) throws URISyntaxException {
        CreateRouteRequest.InvokeParam.InvokeParamBuilder invokeParamBuilder = CreateRouteRequest.InvokeParam.builder()
                .name(serviceName)
                .name(routeName);
        DescribeServicesResponse describeServicesResponse = this.describeServices(serviceName);
        URI uri = new URI(targetUri);
        if (describeServicesResponse.isSuccess() && describeServicesResponse.getData().getInvokeResult().getTotalCount() > 0) {
            DescribeServicesResponse.ServiceDescription serviceDescription = describeServicesResponse.getData().getInvokeResult().getRows().getFirst();
            invokeParamBuilder.serviceId(serviceDescription.getId());
            invokeParamBuilder.serviceName(serviceDescription.getName());
        } else {
            // 调用网关接口创建服务
            CreateServiceRequest.InvokeParam.UpStream upStream = CreateServiceRequest.InvokeParam.UpStream.builder().build();
            upStream.addNode(uri.getAuthority(), 1);
            upStream.setScheme(uri.getScheme());
            CreateServiceResponse createServiceResponse = this.createService(CreateServiceRequest.builder()
                    .invokeParam(CreateServiceRequest.InvokeParam.builder()
                            .name(serviceName)
                            .desc("")
                            .plugins(buyer ? CreateServiceRequest.InvokeParam.EMPTY_PLUGINS : CreateServiceRequest.InvokeParam.DEFAULT_PLUGINS)
                            .upstream(upStream)
                            .build())
                    .build());
            if (createServiceResponse.isSuccess()) {
                CreateServiceResponse.InvokeResult gatewayService = createServiceResponse.getData().getInvokeResult();
                invokeParamBuilder.serviceId(gatewayService.getId());
                invokeParamBuilder.serviceName(gatewayService.getName());
            }
        }
        return invokeParamBuilder;
    }

    /**
     * 为外部API创建API网关服务并创建API路由
     * 1数据资产1网关服务1路由
     * 买方
     *
     * @param dataAssetId    数据资产ID
     * @param dataPath       接口响应体中需要获取的字段的json路径
     * @param responseFields 接口响应体中需要获取的字段
     * @param serviceName    服务名称 id_name (userId_userName、assetId_assetName)
     * @param routeName      路由名称
     * @param targetUri      目标URI 用户页面所填
     * @return API网关路由 https://当前连接器地址:当前连接器网关服务端口/[dataAssetId]
     */
    public CreateRouteResponse createServiceAndRouteForExternalAPI(String dataAssetId, List<PathTypeBO> dataPath, List<ResponseBO> responseFields, String responseEcho, String serviceName, String routeName, String targetUri, boolean extractResponse) throws URISyntaxException {
        CreateRouteRequest.InvokeParam.InvokeParamBuilder invokeParamBuilder = generateCreateRouteRequestInvokeParamBuilder(serviceName, routeName, targetUri, false);
        URI uri = new URI(targetUri);
        CreateRouteRequest.Headers headers = new CreateRouteRequest.Headers();
        headers.setHeader("ROUTE_ID", getRouterId());
        headers.setHeader("API_TYPE", "SELLER");
        CreateRouteRequest.PluginWrapper.PluginWrapperBuilder pluginWrapper = CreateRouteRequest.PluginWrapper.builder()
                .proxyReWrite(CreateRouteRequest.ProxyReWrite.builder()
                        .uri(StringUtils.hasLength(uri.getPath()) ? uri.getPath() : "/*")
                        .headers(headers)
                        .build());

        List<CreateRouteRequest.FieldPath> fieldPaths = new ArrayList<>();
        if (extractResponse) {
            StringBuilder path = new StringBuilder();
            for (PathTypeBO pathTypeBO : dataPath) {
                if ("根节点".equals(pathTypeBO.getDataPath())) {
                    if (DataTypeEnum.ARRAY.equals(pathTypeBO.getDataType())) {
                        path.append("[*].");
                    }
                    continue;
                }
                path.append(pathTypeBO.getDataPath()).append(".");
                if (DataTypeEnum.ARRAY.equals(pathTypeBO.getDataType())) {
                    path.append("[*].");
                }
            }
            for (ResponseBO responseField : responseFields) {
                fieldPaths.add(CreateRouteRequest.FieldPath.builder().path(path + responseField.getFieldName()).build());
            }
            pluginWrapper
                    .responseExtract(CreateRouteRequest.ResponseExtract.builder()
                            .metadata(CreateRouteRequest.PluginMetadata.builder()
                                    .disabled(false)
                                    .build())
                            .sourceFields(fieldPaths)
                            .build());
        } else {
            JsonNode response = JacksonUtils.readTree(responseEcho);
            generateFieldPath(response.get("responseData"), fieldPaths);
        }
        // /[dataAssetId] -> targetUri
        CreateRouteResponse createRouteResponse = this.createRoute(CreateRouteRequest.builder()
                .invokeParam(invokeParamBuilder
                        // NOTE: 路径中添加前缀是因为网关API不支持纯数字的路径
                        .uri("/data_" + dataAssetId)
                        // .methods(List.of("GET", "POST"))
                        .labels(Map.of(
                                "API_VERSION", "V1",
                                "API_TYPE", "SELLER" // API_TYPE: SELLER BUYER 买方(数据资产) 卖方(数据资产交付)
                        ))
                        .plugins(pluginWrapper.build())
                        .build())
                .build());
        dataAssetCache.put(String.format(DATA_ASSET_FIELD_PATH_KEY, dataAssetId), JacksonUtils.obj2json(fieldPaths));
        createRouteRes(createRouteResponse.getData().getInvokeResult().getId(), fieldPaths);
        return createRouteResponse;
    }

    private void generateFieldPath(JsonNode jsonNode, List<CreateRouteRequest.FieldPath> fieldPaths) {
        if (jsonNode instanceof ArrayNode arrayNode) {
            for (JsonNode _jsonNode : arrayNode) {
                generateFieldPath(_jsonNode, fieldPaths);
            }
        } else {
            JsonNode children = jsonNode.get("children");
            if (children != null && !children.isEmpty()) {
                generateFieldPath(children, fieldPaths);
            } else if (jsonNode.has("path") && jsonNode.get("path").isArray()) {
                JsonNode pathArr = jsonNode.get("path");
                StringBuilder path = new StringBuilder();
                for (JsonNode pathItem : pathArr) {
                    if ("ITEMS".equals(pathItem.get("dataPath").asText()) && pathItem.get("isItems").asBoolean()) {
                        continue;
                    }
                    if ("根节点".equals(pathItem.get("dataPath").asText())) {
                        if (DataTypeEnum.ARRAY.name().equals(pathItem.get("dataType").asText())) {
                            path.append("[*].");
                        }
                        continue;
                    }
                    path.append(pathItem.get("dataPath")).append(".");
                    if (DataTypeEnum.ARRAY.name().equals(pathItem.get("dataType").asText())) {
                        path.append("[*].");
                    }
                }
                String s = path.toString().replaceAll("\"", "");
                fieldPaths.add(CreateRouteRequest.FieldPath.builder().path(s.substring(0, s.length() - 1)).build());
            }
        }
    }

    private void createRouteRes(String routeId1, List<CreateRouteRequest.FieldPath> fieldPaths) {
        Map<String, Object> map = new HashMap<>();
        map.put("routeId1", routeId1);
        map.put("routeRes", fieldPaths);
        CreateRouteResResponse createServiceResponse = restClient().post().uri("/webapi/apig/5.0/CreateRouteRes.json")
                .contentType(MediaType.APPLICATION_JSON)
                .body(map)
                .retrieve()
                .body(CreateRouteResResponse.class);
        if (createServiceResponse == null) {
            log.error("创建API响应信息接口异常:接口响应失败");
            return;
        }
        if (!createServiceResponse.isSuccess())
            log.error("创建API响应信息接口异常：" + createServiceResponse.getMessage());
    }

    protected String getRouterId() {
        return LoginContextHolder.currentUser().getCompany().getNodeId();
    }

    /**
     * 为内部API创建服务与路由
     * 1用户1个服务 1数据资产1个路由
     * 买方
     *
     * @param dataAssetId   数据资产ID
     * @param dataAssetName -> routeName   路由名称
     * @param serviceName   -> serviceName      userId_userName
     * @param targetUri     目标URI：连接器文件下载接口、连接器文件查询接口、连接器数据库查询接口
     *                      https://当前连接器地址:当前连接器网关服务端口/[internalAPIType.getPath().replace("{dataAssetId}", dataAssetId)]
     * @return 网关API路由信息 https://当前连接器地址:当前连接器网关服务端口/[dataAssetId]
     */
    public CreateRouteResponse createServiceAndRouteForInternalAPI(String dataAssetId, String dataAssetName, String serviceName, String targetUri) throws URISyntaxException {
        CreateRouteRequest.InvokeParam.InvokeParamBuilder invokeParamBuilder = generateCreateRouteRequestInvokeParamBuilder(serviceName, dataAssetId, targetUri, false);
        CreateRouteRequest.Headers headers = new CreateRouteRequest.Headers();
        headers.setHeader("ROUTE_ID", getRouterId());
        headers.setHeader("API_TYPE", "SELLER");
        // /[dataAssetId] -> targetUri
        URI uri = new URI(targetUri);
        return this.createRoute(CreateRouteRequest.builder()
                .invokeParam(invokeParamBuilder
                        // NOTE: 路径中添加前缀是因为网关API不支持纯数字的路径
                        .uri("/data_" + dataAssetId)
                        // .methods(List.of("GET", "POST"))
                        .labels(Map.of(
                                "API_VERSION", "V1",
                                "API_TYPE", "SELLER" // API_TYPE: SELLER BUYER 买方(数据资产) 卖方(数据资产交付)
                        ))
                        .plugins(CreateRouteRequest.PluginWrapper.builder()
                                .proxyReWrite(CreateRouteRequest.ProxyReWrite.builder()
                                        .uri(uri.getPath())
                                        .headers(headers)
                                        .build())
                                .build())
                        .build())
                .build());
    }

    /**
     * 为数据资产交付创建服务与纯转发路由
     * 1连接器1个服务 1交付1个路由
     * 卖方
     *
     * @param apiServiceName  连接器ID
     * @param deliverySceneId 数据交付场景ID
     * @param targetUri       https://数据资产连接器地址:数据资产连接器网关服务端口/[internalAPIType.getPath().replace("{dataAssetId}", dataAssetId)]
     * @return 网关API路由信息 https://当前连接器地址:当前连接器网关服务端口/[deliverySceneId]
     */
    public CreateRouteResponse createServiceAndRouteForDeliver(String assetId, String apiServiceName, String routeName, String proxyPath, String targetUri, boolean needBuyerHeader) throws URISyntaxException {
        CreateRouteRequest.InvokeParam.InvokeParamBuilder invokeParamBuilder = generateCreateRouteRequestInvokeParamBuilder(apiServiceName, routeName, targetUri, true);
        URI uri = new URI(targetUri);
        CreateRouteRequest.Headers headers = new CreateRouteRequest.Headers();
        headers.setHeader("ROUTE_ID", getRouterId());
        Map<String, String> labels = new HashMap<>(Map.of("API_VERSION", "V1"));
        if (needBuyerHeader) {
            labels.put("API_TYPE", "BUYER"); // API_TYPE: SELLER BUYER 买方(数据资产) 卖方(数据资产交付)
            headers.setHeader("API_TYPE", "BUYER");
        }

        // /[dataAssetId] -> targetUri
        CreateRouteResponse routeResponse = this.createRoute(CreateRouteRequest.builder()
                .invokeParam(invokeParamBuilder
                        // NOTE: 路径中添加前缀是因为网关API不支持纯数字的路径
                        .uri(proxyPath)
                        // .methods(List.of("GET", "POST"))
                        .labels(labels)
                        .plugins(CreateRouteRequest.PluginWrapper.builder()
                                .proxyReWrite(CreateRouteRequest.ProxyReWrite.builder()
                                        .uri(uri.getPath())
                                        .headers(headers)
                                        .build())
                                .build())
                        .build())
                .build());

        List<CreateRouteRequest.FieldPath> fieldPaths = StringUtils.hasLength(dataAssetCache.get(String.format(DATA_ASSET_FIELD_PATH_KEY, assetId))) ?
                JacksonUtils.json2pojo(dataAssetCache.get(String.format(DATA_ASSET_FIELD_PATH_KEY, assetId)), List.class) : List.of();
        createRouteRes(routeResponse.getData().getInvokeResult().getId(), fieldPaths);
        return routeResponse;
    }

    /**
     * 为授权通过的资产订单生成接口调用apiKey
     *
     * @return apiKey
     */
    public String generateAPIKeyForOrder(String username, String apiRouterId) {
        DescribeConsumerResponse describeConsumerResponse = this.describeConsumer(DescribeConsumerRequest.builder()
                .username(username)
                .build());
        String apiKey;
        if (!describeConsumerResponse.isSuccess()) {
            apiKey = UUID.randomUUID().toString();
            this.createConsumer(CreateConsumerRequest.builder()
                    .invokeParam(CreateConsumerRequest.InvokeParam.builder()
                            .username(username)
                            .plugins(CreateConsumerRequest.PluginWrapper.builder()
                                    .keyAuth(CreateConsumerRequest.KeyAuth.builder()
                                            .key(apiKey)
                                            .build())
                                    .build())
                            .build())
                    .build());
        } else {
            DescribeConsumerResponse.InvokeResultWrapper describeConsumerResponseData = describeConsumerResponse.getData();
            apiKey = describeConsumerResponseData.getInvokeResult().getPlugins().getKeyAuth().getKey();
        }
        DescribeRouteResponse describeRouteResponse = this.describeRoute(DescribeRouteRequest.builder().id(apiRouterId).build());
        DescribeRouteResponse.InvokeResult routeInfo = describeRouteResponse.getData().getInvokeResult();
        CreateAuthzConfigRequest.InvokeParam createAuthzConfigParam = new CreateAuthzConfigRequest.InvokeParam();
        createAuthzConfigParam.addAllowed(CreateAuthzConfigRequest.Allowed.builder()
                .serviceId(routeInfo.getServiceId())
                .type("router")
                .routers(List.of(routeInfo.getId()))
                .dataSource(List.of(CreateAuthzConfigRequest.DataSource.builder()
                        .key(routeInfo.getId())
                        .value(routeInfo.getName())
                        .serviceId(routeInfo.getServiceId())
                        .build()))
                .build());
        this.createAuthzConfig(CreateAuthzConfigRequest.builder()
                .username(username)
                .invokeParam(createAuthzConfigParam)
                .build());
        return apiKey;
    }

    public String consumerAuthKey(String username) {
        DescribeConsumerResponse describeConsumerResponse = this.describeConsumer(DescribeConsumerRequest.builder()
                .username(username)
                .build());
        DescribeConsumerResponse.InvokeResultWrapper describeConsumerResponseData = describeConsumerResponse.getData();
        return describeConsumerResponseData.getInvokeResult().getPlugins().getKeyAuth().getKey();
    }


    public ApiGateResponse getReportData() {
        GetReportDataRequest request = GetReportDataRequest.builder()
                .startTime(DateUtil.beginOfDay(DateUtil.date()).getTime())
                .endTime(DateUtil.endOfDay(DateUtil.date()).getTime())
                .build();
        String reportDataCode = "resApiSummary,requestCount,requestCountWithAPITop10,resRiskLevelSecurityDistribution,resRiskLevelAccessDistribution";
        ApiGateCommonResult<ApiGateResponse> apiGateResponse = restClient().get()
                .uri(String.format("/openapi/apig/5.0/DescribereportTransactionData.json?reportDataCode=%s&startTime=%s&endTime=%s",
                        reportDataCode, DateUtil.beginOfDay(DateUtil.date()).getTime(),
                        DateUtil.endOfDay(DateUtil.date()).getTime())
                )
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        if (apiGateResponse == null || !apiGateResponse.isSuccess()) {
            log.error("获取买卖方报表数据异常: {}, {}", JSONUtil.toJsonStr(request), JacksonUtils.obj2json(apiGateResponse));
            throw new RestfulApiException(apiGateResponse != null ? "获取买卖方报表数据异常:" + apiGateResponse.getMessage() : "获取买卖方报表数据异常:未收到响应");
        }
        return apiGateResponse.getData();
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GetReportDataRequest {
        Long startTime;
        Long endTime;
        String reportDataCode;
        String routeId1;
        String username;
        String transaction;

    }


}
