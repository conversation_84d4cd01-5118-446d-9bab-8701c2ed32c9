package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetDeliveryExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface DataProductRepository extends JpaRepository<DataProduct, String>, QuerydslPredicateExecutor<DataProduct>, JpaSpecificationExecutor<DataProduct> {

    @Query("SELECT dp.username as username, COUNT(dp) as count FROM DataProduct dp where dp.itemStatus = 'item_status2' GROUP BY dp.username ORDER BY count DESC limit 10")
    List<Object[]> countDataProductByUsernameTop10();

    DataProduct findByDataProductPlatformId(String dataProductPlatformId);

    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.dataProductPlatformId = :dataProductPlatformId where dp.id = :id")
    void updateDataProductPlatformId(@Param(value = "id") String id, @Param(value = "dataProductPlatformId") String dataProductPlatformId);

    /**
     * @param id
     * @param itemStatus 登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销 item_status5 功能节点待审批 item_status6 功能节点通过 item_status7 功能节点拒绝
     */
    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.itemStatus = :itemStatus where dp.id = :id")
    void updateItemStatus(@Param(value = "id") String id, @Param(value = "itemStatus") String itemStatus);

    /**
     * 更新发布状态
     *
     * @param id
     * @param pushStatus @see PushStatus
     */
    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.pushStatus = :pushStatus where dp.id = :id")
    void updatePushStatus(@Param(value = "id") String id, @Param(value = "pushStatus") String pushStatus);

    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.dataExt = :dataExt where dp.id = :id")
    void updateDataExt(@Param(value = "id") String id, @Param(value = "dataExt") DataAssetExt dataExt);

    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.deliveryExt = :deliveryExt where dp.id = :id")
    void updateDeliveryExt(@Param(value = "id") String id, @Param(value = "deliveryExt") DataAssetDeliveryExt deliveryExt);

    @Transactional
    @Modifying
    @Query("update DataProduct dp set dp.isDelete = true where dp.id = :id")
    void softDelete(@Param(value = "id") String id);
}
