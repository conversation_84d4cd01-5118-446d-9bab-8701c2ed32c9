package com.ailpha.ailand.dataroute.endpoint.common.pk;

import java.util.UUID;

/**
 * @author: yuwenping
 * @date: 2025/3/11 20:22
 * @Description:
 */
public class UUID64Generator {


    public static Long generate() {
        UUID uuid = UUID.randomUUID();
        long mostSigBits = uuid.getMostSignificantBits();
        long leastSigBits = uuid.getLeastSignificantBits();

        // 取高低位的部分组合成64位
        return (mostSigBits & 0xFFFFFFFF00000000L)
                | ((leastSigBits >> 32) & 0xFFFFFFFFL);
    }
}
