package com.ailpha.ailand.dataroute.endpoint.user.remote.request;

import cn.hutool.core.annotation.Alias;
import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HubUpdateUserRequest extends BaseRemoteRequest {
    Long id;
    String name;
    String mobile;
    String email;
    @Schema(description = "旧密码")
    @Alias("opassword")
    String oldPassword;
    @Schema(description = "新密码")
    @Alias("npassword")
    String newPassword;
    @Schema(description = "重复新密码")
    @Alias("cpassword")
    String verifyPassword;
}
