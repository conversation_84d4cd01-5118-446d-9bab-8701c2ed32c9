package com.ailpha.ailand.dataroute.endpoint.user.remote.request;

import cn.hutool.core.annotation.Alias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class HubUpdateUserRequest {
    Long id;
    String name;
    String mobile;
    String email;
    @Schema(description = "旧密码")
    @Alias("opassword")
    String oldPassword;
    @Schema(description = "新密码")
    @Alias("npassword")
    String newPassword;
    @Schema(description = "重复新密码")
    @Alias("cpassword")
    String verifyPassword;
}
