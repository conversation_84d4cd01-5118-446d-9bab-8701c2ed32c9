package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.mapper.PluginDetailMapper;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.PluginApiDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.PluginDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginApiDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.PlugDetailService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailPageVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailVO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.QPluginDetail;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:46
 * @Description:
 */
@Service
@AllArgsConstructor
public class PlugDetailServiceImpl implements PlugDetailService {

    private final PluginDetailRepository pluginDetailRepository;

    private final PluginApiDetailRepository pluginApiDetailRepository;

    private final PluginDetailMapper pluginDetailMapper;

    private final JPAQueryFactory queryFactory;

    private final DataAssetService dataAssetService;

    @Override
    public PluginDetailVO getById(Long id) {
        PluginDetail pluginDetail = pluginDetailRepository.findById(id).orElseThrow(() -> new RestfulApiException("未找到该插件信息"));
        return pluginDetailMapper.toPluginDetailVO(pluginDetail);
    }

    @Override
    public ApiResponse<List<PluginDetailPageVO>> pageByParam(PluginDetailPageRequest request) {
        UserDTO iUserDTO = LoginContextHolder.currentUser();
        String currentUserId = iUserDTO.getId();
        Page page = Page.of(request.getNum(), request.getSize());

        QPluginDetail qPluginDetail = QPluginDetail.pluginDetail;
        JPAQuery<PluginDetail> jpaQuery = queryFactory.selectFrom(qPluginDetail);
        BooleanBuilder booleanBuilder = new BooleanBuilder(qPluginDetail.createUser.eq(currentUserId));
        if (ObjectUtil.isNotNull(request.getStatus())) {
            booleanBuilder.and(qPluginDetail.status.eq(request.getStatus()));
        }
        if (StringUtils.isNotBlank(request.getName())) {
            booleanBuilder.and(qPluginDetail.name.contains(request.getName()));
        }
        Long count = jpaQuery.select(qPluginDetail.count()).from(qPluginDetail).where(booleanBuilder).fetchOne();

        List<PluginDetail> resList = jpaQuery.select(qPluginDetail).where(booleanBuilder)
                .orderBy(qPluginDetail.createTime.desc())
                .offset(page.getOffset()).limit(request.getSize()).fetch();
        List<PluginDetailPageVO> res = resList.stream().map(pluginDetailMapper::toPluginDetailPageVO).collect(Collectors.toList());
        SuccessResponse<List<PluginDetailPageVO>> response = SuccessResponse.success(res).build();
        response.setTotal(count);
        response.setPage(request);
        return response;
    }

    @Override
    public List<PluginDetail> listByParam(Boolean status, PluginApiTypeEnums type) {

        return pluginDetailRepository.findAllByStatusAndType(status, type);
    }

    @Override
    @Transactional
    public boolean updateStatus(PluginDetailRequest request) {
        // only update status
        PluginDetail pluginDetail = pluginDetailRepository.findById(request.getId()).orElseThrow(() -> new RestfulApiException("未找到该插件信息"));
        pluginDetail.setStatus(request.getStatus());
        pluginDetail.setUpdateTime(new Date());
        pluginDetailRepository.saveAndFlush(pluginDetail);
        return true;
    }

    @Override
    @Transactional
    public boolean savePlug(PluginDetailRequest request) {
        PluginDetail pluginDetail = pluginDetailMapper.requestToPluginDetail(request);
        pluginDetail.setCreateTime(new Date());
        pluginDetail.setStatus(true);
        pluginDetail.setCreateUser(LoginContextHolder.currentUser().getId());
        pluginDetailRepository.saveAndFlush(pluginDetail);
        List<PluginApiDetail> pluginApiDetails = pluginDetail.getPluginApiDetails();
        pluginApiDetails.forEach(item -> {
            item.setPluginId(pluginDetail.getId());
            item.setCreateUser(LoginContextHolder.currentUser().getId());
            item.setCreateTime(new Date());
        });
        pluginApiDetailRepository.saveAll(pluginApiDetails);
        // 交易所新增则上报该交易所
        if (PluginApiTypeEnums.EXCHANGE.equals(request.getType())) {
            TraderService traderService = SpringUtil.getBean(TraderService.class);
            traderService.businessReportingTrader(pluginDetail);
        }
        return true;
    }

    @Override
    @Transactional
    public boolean updatePlug(PluginDetailRequest request) {
        PluginDetail pluginDetail = pluginDetailRepository.findById(request.getId()).orElseThrow(() -> new RestfulApiException("未找到该插件信息"));
        pluginDetail.setUpdateTime(new Date());
        pluginDetail.setName(request.getName());
        pluginDetail.setPlugCredentials(JSONUtil.toJsonStr(request.getPlugCredentials()));
        pluginDetail.setDomain(request.getDomain());
        pluginDetail.setEncryptType(request.getEncryptType());
        pluginDetail.setStatus(request.getStatus());
        pluginDetail.setType(request.getType());
        pluginDetailRepository.saveAndFlush(pluginDetail);

        List<PluginApiDetailRequest> pluginApiDetails = request.getPluginApiDetails();
        List<PluginApiDetail> pluginApiDetailList = pluginApiDetails.stream().map(item -> {
            PluginApiDetail pluginApiDetail = pluginApiDetailRepository.findById(item.getId()).get();
            pluginApiDetail.setUpdateTime(new Date());
            pluginApiDetail.setApiUrl(item.getUrl());
            pluginApiDetail.setApiName(item.getName());
            pluginApiDetail.setApiMark(item.getMark());
            pluginApiDetail.setEnabled(item.getEnabled());
            return pluginApiDetail;
        }).toList();
        pluginApiDetailRepository.saveAll(pluginApiDetailList);
        return true;
    }

    @Override
    public List<PluginDetail> findAllByIdIn(List<Long> id) {
        return pluginDetailRepository.findAllById(id);
    }

    @Override
    public List<PluginDetail> getByDataAssertIds(List<String> dataAssertIds) {
        List<PluginDetail> res = new ArrayList<>();
        for (String dataAssetId : dataAssertIds) {
            DataAsset dataAsset = dataAssetService.getLocalDataAssetById(dataAssetId);
            if (dataAsset == null) {
                continue;
            }
            List<Long> exchangePluginIds = dataAsset.getExtraData().getExchangePluginIds();
            if (CollectionUtils.isEmpty(exchangePluginIds)) {
                continue;
            }
            res.addAll(findAllByIdIn(exchangePluginIds));
        }
        return res;
    }
}
