package com.ailpha.ailand.dataroute.endpoint.dataasset.mapper;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginApiDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginCredentials;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailPageVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailVO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 14:11
 * @Description:
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class PluginDetailMapper {
    @Mapping(target = "plugCredentials", qualifiedByName = "toPlugCredentials")
    @Mapping(target = "pluginApiDetails", qualifiedByName = "toPlugApiDetailsRequest")
    public abstract PluginDetailVO toPluginDetailVO(PluginDetail pluginDetail);

    public abstract PluginDetailPageVO toPluginDetailPageVO(PluginDetail pluginDetail);

    @Mapping(target = "plugCredentials", qualifiedByName = "toPlugCredentialsStr")
    @Mapping(target = "pluginApiDetails", qualifiedByName = "toPlugApiDetails")
    public abstract PluginDetail requestToPluginDetail(PluginDetailRequest pluginDetailRequest);

    @Named("toPlugCredentials")
    public PluginCredentials toPlugCredentials(String plugCredentials) {
        return JSONUtil.toBean(plugCredentials, PluginCredentials.class);
    }

    @Named("toPlugCredentialsStr")
    public String toPlugCredentialsStr(PluginCredentials plugCredentials) {
        return JSONUtil.toJsonStr(plugCredentials);
    }

    @Named("toPlugApiDetails")
    public List<PluginApiDetail> toPlugApiDetails(List<PluginApiDetailRequest> apiDetailRequestList) {
        return apiDetailRequestList.stream().map(item -> {
            PluginApiDetail pluginApiDetail = new PluginApiDetail();
            pluginApiDetail.setId(item.getId());
            pluginApiDetail.setPluginId(item.getPluginId());
            pluginApiDetail.setApiMark(item.getMark());
            pluginApiDetail.setApiUrl(item.getUrl());
            pluginApiDetail.setApiName(item.getName());
            pluginApiDetail.setEnabled(item.getEnabled());
            return pluginApiDetail;
        }).toList();
    }

    @Named("toPlugApiDetailsRequest")
    public List<PluginApiDetailRequest> toPlugApiDetailsRequest(List<PluginApiDetail> pluginApiDetails) {
        return pluginApiDetails.stream().map(item -> {
            PluginApiDetailRequest pluginApiDetailRequest = new PluginApiDetailRequest();
            pluginApiDetailRequest.setId(item.getId());
            pluginApiDetailRequest.setPluginId(item.getPluginId());
            pluginApiDetailRequest.setMark(item.getApiMark());
            pluginApiDetailRequest.setUrl(item.getApiUrl());
            pluginApiDetailRequest.setName(item.getApiName());
            pluginApiDetailRequest.setEnabled(item.getEnabled());
            return pluginApiDetailRequest;
        }).toList();
    }
}
