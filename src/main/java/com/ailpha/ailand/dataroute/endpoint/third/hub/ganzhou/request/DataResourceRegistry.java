package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceRegistry {
    /**
     * 资源名称
     */
    String resourceName;
    /**
     * 来源平台内部标识
     */
    String outerResourceId;
    /**
     * 数据类型：1 个人 2 企业 3 公共
     */
    String dataType;
    /**
     * 资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据
     */
    String resourceType;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 资源持有方
     */
    String resourceOwner;
    /**
     * 资源持有方ID
     */
    String resourceOwnerId;
    /**
     * 资源持有方编号
     */
    String resourceOwnerCode;
    /**
     * 存储容量
     */
    String capacity;
    /**
     * 联系人
     */
    String contacter;
    /**
     * 联系方式
     */
    String contactInformation;
    /**
     * 资源摘要
     */
    String resourceAbstract;
    /**
     * 资源格式
     */
    String resourceFormat;
    /**
     * 数据来源 01 收集取得 02 原始取得 03 交易取得 04 其他
     */
    String dataSource;
    /**
     * 是否涉及个人信息： 0 否 1 是
     */
    String personalInformation;
    /**
     * 其他
     */
    String others;
    /**
     * 使用限制
     */
    String limitations;
    /**
     * 授权使用：0 否 1 是
     */
    String authorize;
    /**
     * 提供方接入连接器标识
     */
    String dpe;
    /**
     * 查询资源列表 List<ResourceItemAddCmd>
     */
    String resourceItemList;
}
