package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTaskLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 数据接入任务历史记录数据访问层
 */
public interface DataUpdateTaskLogRepository extends JpaRepository<DataUpdateTaskLog, String>, JpaSpecificationExecutor<DataUpdateTaskLog> {
    /**
     * 根据任务ID分页查询历史记录
     *
     * @param taskId   任务ID
     * @param pageable 分页参数
     * @return 历史记录分页数据
     */
    Page<DataUpdateTaskLog> findByTaskIdOrderByCreateTimeDesc(String taskId, Pageable pageable);

    /**
     * 根据状态查询历史任务
     * @param taskStatus
     * @return
     */
    List<DataUpdateTaskLog> findByTaskStatus(DataUpdateStatus taskStatus);

    /**
     * 根据任务id查询历史
     * @param taskId
     * @return
     */
    List<DataUpdateTaskLog> findByTaskId(String taskId);

    /**
     * 更新任务信息
     */
    @Transactional
    @Modifying
    @Query("update DataUpdateTaskLog dutl set dutl.startTime = :startTime where dutl.id = :id")
    void updateStartTime(@Param(value = "id") String id, @Param(value = "startTime") Date startTime);

    @Transactional
    @Modifying
    @Query("update DataUpdateTaskLog dutl set dutl.endTime = :endTime where dutl.id = :id")
    void updateEndTime(@Param(value = "id") String id, @Param(value = "endTime") Date endTime);

    @Transactional
    @Modifying
    @Query("update DataUpdateTaskLog dutl set dutl.taskStatus = :taskStatus where dutl.id = :id")
    void updateTaskStatus(@Param(value = "id") String id, @Param(value = "taskStatus") DataUpdateStatus taskStatus);

    @Transactional
    @Modifying
    @Query("update DataUpdateTaskLog dutl set dutl.log = :log where dutl.id = :id")
    void updateLog(@Param(value = "id") String id, @Param(value = "log") String log);

    @Transactional
    @Modifying
    @Query("update DataUpdateTaskLog dutl set dutl.ext = :ext where dutl.id = :id")
    void updateExt(@Param(value = "id") String id, @Param(value = "ext") DataUpdateTaskLog.DataUpdateTaskLogExt ext);
}