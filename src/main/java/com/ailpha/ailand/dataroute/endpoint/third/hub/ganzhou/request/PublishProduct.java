package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PublishProduct {
    /**
     * 产品编号
     */
    @Schema(description = "资源目录编码/产品编码")
    String bizCode;
    /**
     * 发布类型 0 全平台 1 部分平台
     */
    @Schema(description = "发布类型 0 全平台 1 部分平台")
    String publishType;
    /**
     * 发布说明
     */
    @Schema(description = "发布说明")
    String publishExplanation;
    /**
     * 发布类型为部分平台，该字段不为空。
     * Array<platformId>转 String
     */
    @Schema(description = "发布类型为部分平台时传入的platformId列表")
    String platformIds;
    /**
     * 主体唯一编码
     */
    @Schema(description = "主体唯一编码")
    String entityCode;
    /**
     * 登记确认单 文件上传后的OssId
     */
    @Schema(description = "登记确认单 文件上传后的OssId")
    String safetyFile;
    /**
     * 其他说明 文件上传后的OssId
     */
    @Schema(description = "其他说明 文件上传后的OssId")
    String protectFile;
}
