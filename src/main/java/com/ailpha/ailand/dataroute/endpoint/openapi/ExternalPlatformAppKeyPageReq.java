package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExternalPlatformAppKeyPageReq extends Page implements Serializable {

    @Schema(description = "平台ID")
    String platformId;
}
