package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResItemPageVm {
    /**
     * {
     * "assetIds": [],
     * "clientId": "",
     * "clientIdList": [],
     * "clientPlatformUniqueNo": "",
     * "companyName": "",
     * "dataAssetPrepareStatus": "",
     * "dataResPlatformUniqueNo": "",
     * "dataSource": "",
     * "deliveryModes": "",
     * "enterType": "",
     * "industryClassify": "",
     * "isAllowedEmptyItemNum": true,
     * "isDelete": true,
     * "itemCode": "",
     * "itemNum": "",
     * "itemStatus": "",
     * "itemType": "",
     * "level": "",
     * "name": "",
     * "nickName": "",
     * "orgId": 0,
     * "providerOrgName": "",
     * "pushStatus": "",
     * "safeLevel": "",
     * "sourceOrg": "",
     * "targetObject": "",
     * "userId": "",
     * "userName": ""
     * }
     */
    /**
     * 资产ID列表
     */
    List<String> assetIds;
    /**
     * 客户端ID
     */
    String clientId;
    /**
     * 客户端ID集合
     */
    List<String> clientIdList;
    /**
     * 连接器平台唯一编号
     */
    String clientPlatformUniqueNo;
    /**
     * 企业名称
     */
    String companyName;
    /**
     * 数据资源平台唯一编号
     */
    String dataResPlatformUniqueNo;
    /**
     * 数据来源
     */
    String dataSource;
    /**
     * 交付方式
     */
    String deliveryModes;
    /**
     * 行业分类
     */
    String industryClassify;
    List<String> industryClassifyList;
    /**
     * 是否允许资源代码为空
     */
    @Builder.Default
    Boolean isAllowedEmptyItemNum = true;
    /**
     * 是否删除
     */
    @Builder.Default
    Boolean isDelete = false;
    /**
     * 资源代码
     */
    String itemCode;
    /**
     * 资源代码
     */
    String itemNum;
    /**
     * 资源状态
     */
    String itemStatus;
    /**
     * 是否公有
     */
    String itemType;
    /**
     * 级别
     */
    String level;
    /**
     * 名称
     */
    String name;
    /**
     * 数据项名称
     */
    String nickName;
    /**
     * 当前用户部门Id
     */
    Integer orgId;
    /**
     * 提供方单位名称
     */
    String providerOrgName;
    /**
     * 发布状态
     */
    String pushStatus;
    /**
     * 敏感等级
     */
    String safeLevel;
    /**
     * 数据来源
     */
    String sourceOrg;
    /**
     * 适用对象
     */
    String targetObject;
    /**
     * 用户id
     */
    String userId;
    /**
     * 用户名
     */
    String userName;
}
