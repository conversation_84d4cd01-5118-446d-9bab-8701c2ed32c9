package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * @author: yuwenping
 * @date: 2025/6/17 13:31
 * @Description:
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StandardBaseRequest<T> extends BaseRemoteRequest {
    @ApiModelProperty(value = "每个请求的标识符")
    String sqNo;

    @ApiModelProperty(value = "数据")
    T data;

    @ApiModelProperty(value = "指标类型")
    String type;

    @ApiModelProperty(value = "时间戳")
    Long timestamp;

    @ApiModelProperty(value = "指标值")
    String value;

    @ApiModelProperty(value = "指标备注")
    String remark;
}
