package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * 2023/3/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PathTypeBO {

    /**
     * 元数据路径
     */
    String dataPath;

    /**
     * 数据类型
     */
    DataTypeEnum dataType;

    @JsonIgnore
    PathTypeBO child;
}
