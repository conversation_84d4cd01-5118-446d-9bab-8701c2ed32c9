package com.ailpha.ailand.dataroute.endpoint.third.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SqlUtils;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.DatasourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.FileType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.SeparatorEnum;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.APISourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PartitionQueryConditionDTO;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataCollectorJobParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

public class DataCollectorUtils {

    public static String generateJobParam(DataCollectorJobParam params) {
        String version = IdUtil.simpleUUID();
        URL resource = ResourceUtil.getResource(String.format("datasource-template/%s-template.json", params.getDatasourceType().name()));
        JSONObject datasourceTemplate = JSONUtil.parseObj(FileUtil.readString(resource, StandardCharsets.UTF_8));
        datasourceTemplate.putByPath("job.callbackUrl", params.getCallbackUrl());
        if (params.getDatasourceType() == DatasourceType.odps) {
            datasourceTemplate.putByPath("job.content[0].reader.parameter.access_id", params.getAccessId());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.access_key", params.getAccessKey());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.project_name", params.getProjectName());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.table", params.getTableName());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.partition", StringUtils.isEmpty(generateSqlConditionByReader(params.getConditions())) ?
                    Collections.emptyList() : Collections.singletonList(generateSqlConditionByReader(params.getConditions())));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.column", params.getColumns());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.packageAuthorizedProject", "packageAuthorizedProject");
            datasourceTemplate.putByPath("job.content[0].reader.parameter.splitMode", "splitMode");
            datasourceTemplate.putByPath("job.content[0].reader.parameter.odpsServer", params.getJdbcUrl().substring(10));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.where", SqlUtils.replaceDateExpression(System.currentTimeMillis(), generateSqlConditionByQuery(params.getSyncRule(), params.getConditions()), new ArrayList<>()));
        } else if (params.getDatasourceType() == DatasourceType.hdfs) {
            datasourceTemplate.putByPath("job.content[0].reader.parameter.path", params.getTableName());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.defaultFS", params.getJdbcUrl());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.column", Collections.singletonList("*"));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.fileType", FileType.CSV.name());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.encoding", "UTF-8");
            datasourceTemplate.putByPath("job.content[0].reader.parameter.fieldDelimiter", SeparatorEnum.comma.getFieldDelimiter());
//            Boolean hasKerberos = datasource.getExt().getBool("hasKerberos", false);
            Boolean hasKerberos = false;
            datasourceTemplate.putByPath("job.content[0].reader.parameter.haveKerberos", hasKerberos);
            if (hasKerberos) {
                datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosConfPath", "kerberosConfPath");
                datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosPrincipal", "kerberosPrincipal");
                datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosKeytabFilePath", "keytabFilePath");
            }
        } else if (params.getDatasourceType() == DatasourceType.http) {
            APISourceMetadata apiParams = params.getApiParams();
            datasourceTemplate.putByPath("job.content[0].reader.parameter.url", apiParams.getUrl());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.method", apiParams.getMethod());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.params", JacksonUtils.obj2json(apiParams.getParams()));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.body", apiParams.getBody());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.bodyType", apiParams.getBodyType());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.headers", JacksonUtils.obj2json(apiParams.getHeaders()));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.response", JacksonUtils.obj2json(apiParams.getResponse()));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.dataPath", JacksonUtils.obj2json(apiParams.getDataPath()));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.extend", JacksonUtils.obj2json(apiParams.getExtend()));
        } else if (params.getDatasourceType() == DatasourceType.serverfile) {
            datasourceTemplate.putByPath("job.content[0].reader.parameter.path", params.getFilepath());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.column", "[]");
            datasourceTemplate.putByPath("job.content[0].reader.parameter.fileType", params.getFileType());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.encoding", "UTF-8");
            datasourceTemplate.putByPath("job.content[0].reader.parameter.fieldDelimiter", ",");

            datasourceTemplate.putByPath("job.content[0].writer.parameter.sdk-params", params.getUploadSdkParams());
            // syncWay 全量更新
            datasourceTemplate.putByPath("job.content[0].writer.parameter.syncWay", "2");
        } else {
            datasourceTemplate.putByPath("job.content[0].reader.parameter.password", params.getPassword());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.column", params.getColumns());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.username", params.getUsername());
            datasourceTemplate.putByPath("job.content[0].reader.parameter.connection[0].jdbcUrl", List.of(params.getJdbcUrl()));
            if (params.getDatasourceType() == DatasourceType.oracle)
                datasourceTemplate.putByPath("job.content[0].reader.parameter.connection[0].table", Collections.singletonList(params.getTableName().toUpperCase(Locale.ROOT)));
            else if (params.getDatasourceType() == DatasourceType.hive) {
                Boolean hasKerberos = false;
                datasourceTemplate.putByPath("job.content[0].reader.parameter.connection[0].table", Collections.singletonList(params.getTableName()));
                datasourceTemplate.putByPath("job.content[0].reader.parameter.haveKerberos", hasKerberos);
                if (hasKerberos) {
                    datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosConfPath", "kerberosConfPath");
                    datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosPrincipal", "kerberosPrincipal");
                    datasourceTemplate.putByPath("job.content[0].reader.parameter.kerberosKeytabFilePath", "keytabFilePath");
                }
            } else
                datasourceTemplate.putByPath("job.content[0].reader.parameter.connection[0].table", Collections.singletonList(params.getTableName()));
            datasourceTemplate.putByPath("job.content[0].reader.parameter.where",
                    SqlUtils.replaceDateExpression(System.currentTimeMillis(), generateSqlConditionByQuery(params.getSyncRule(), params.getConditions()), new ArrayList<>()));
        }
        datasourceTemplate.putByPath("core.container.job.id", params.getJobId());
        datasourceTemplate.putByPath("job.content[0].writer.jobId", params.getJobId());
        datasourceTemplate.putByPath("job.content[0].writer.jobVersion", version);
        datasourceTemplate.putByPath("job.content[0].writer.parameter.version", version);
        datasourceTemplate.putByPath("job.content[0].writer.parameter.fullFileName", StringUtils.substringAfterLast(params.getFilepath(), FileUtil.FILE_SEPARATOR));
        datasourceTemplate.putByPath("job.content[0].writer.parameter.fullFilePath", params.getFilepath());
        datasourceTemplate.putByPath("job.content[0].writer.parameter.fileName", FileUtil.getPrefix(StringUtils.substringAfterLast(params.getFilepath(), FileUtil.FILE_SEPARATOR)));
        datasourceTemplate.putByPath("job.content[0].writer.parameter.path", StringUtils.substringBeforeLast(params.getFilepath(), FileUtil.FILE_SEPARATOR));
        datasourceTemplate.putByPath("job.content[0].writer.parameter.writeMode", "truncate");
//        if (!params.getDatasourceType().equals(DatasourceType.hdfs) && !params.getDatasourceType().equals(DatasourceType.http)) {
//            datasourceTemplate.putByPath("job.content[0].writer.parameter.header", params.getColumnAlias());
//        }
        return JSONUtil.toJsonStr(datasourceTemplate);
    }

    private static String generateSqlConditionByQuery(String syncRule, List<PartitionQueryConditionDTO> partitionQueryConditions) {
        String whereSql = StringUtils.isEmpty(syncRule) ? "" : syncRule;
        if (CollectionUtils.isEmpty(partitionQueryConditions)) {
            return whereSql;
        }
        List<String> conditions = new ArrayList<>();
        partitionQueryConditions.forEach(x -> {
            if (!StringUtils.isEmpty(x.getValue())) {
                conditions.add(String.format("%s='%s'", x.getColName(), x.getValue()));
            }
        });
        if (StringUtils.isNotBlank(whereSql)) {
            return whereSql + " and " + StringUtils.join(conditions, " and ");
        }
        return StringUtils.join(conditions, " and ");
    }

    private static String generateSqlConditionByReader(List<PartitionQueryConditionDTO> partitionQueryConditions) {
        List<String> conditions = new ArrayList<>();
        partitionQueryConditions.forEach(x -> {
            if (StringUtils.isEmpty(x.getValue())) {
                conditions.add(String.format("%s=%s", x.getColName(), '*'));
            } else {
                conditions.add(String.format("%s=%s", x.getColName(), x.getValue()));
            }

        });
        return StringUtils.join(conditions, ",");
    }
}
