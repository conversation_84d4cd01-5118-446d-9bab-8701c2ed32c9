package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateRouteResponse extends GatewayResponse<CreateRouteResponse.InvokeResultWrapper> {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {
        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult extends CreateServiceRequest.InvokeParam {
        String id;
    }
}
