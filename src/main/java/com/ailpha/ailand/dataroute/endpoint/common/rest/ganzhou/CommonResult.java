package com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.IResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.IReturnCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonResult<T> implements IResult<T> {

    @Serial
    private static final long serialVersionUID = 1581651691719242300L;

    @Setter
    private int code = InternalReturnCode.SUCCESS.getCode();

    @Setter
    @Getter
    private String message = "成功";

    /**
     * 数瀚接口专用
     */
    @Getter
    @Setter
    private String msg;

    @Setter
    @Getter
    private T data;

    private boolean success;

    public CommonResult() {
    }

    protected CommonResult(int code, String message, T data, boolean success) {
        this.code = code;
        this.msg = message;
        this.data = data;
        this.success = success;
    }

    public CommonResult(T data) {
        this.data = data;
        if (data == null) {
            code = com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.FAIL.getCode();
            msg = "失败!";
        }
    }

    public CommonResult(String message) {
        this.code = com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.FAIL.getCode();
        this.msg = message;
    }

    public CommonResult(T data, String message) {
        this.data = data;
        this.msg = message;
    }

    public CommonResult(T data, IReturnCode returnCode) {
        this.data = data;
        this.code = returnCode.getCode();
    }

    public CommonResult(IReturnCode returnCode, String message) {
        this.msg = message;
        this.code = returnCode.getCode();
    }

    public static CommonResult<Void> SUCCESS() {
        CommonResult<Void> response = new CommonResult<>();
        response.setCode(com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.SUCCESS.getCode());
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data) {
        CommonResult<T> response = new CommonResult<>();
        response.setCode(com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.SUCCESS.getCode());
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data, String message) {
        CommonResult<T> response = SUCCESS(data);
        response.setMsg(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(String message, IReturnCode code) {
        CommonResult<T> response = new CommonResult<T>();
        response.setCode(code.getCode());
        response.setMsg(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(IReturnCode errorCode, String message, T data) {
        return new CommonResult<T>(errorCode.getCode(), message, data, false);
    }

    public static <T> CommonResult<T> FAIL() {
        return FAIL("", com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(String message) {
        return FAIL(message, com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(T data) {
        CommonResult<T> response = FAIL("失败");
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(T data, String message) {
        CommonResult<T> response = FAIL(message);
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(Exception e) {
        return FAIL(e.getMessage());
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public boolean isSuccess() {
        return InternalReturnCode.SUCCESS.getCode().equals(code);
    }


}
