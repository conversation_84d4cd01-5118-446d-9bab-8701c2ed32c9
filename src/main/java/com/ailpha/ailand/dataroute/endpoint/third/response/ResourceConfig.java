package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@ApiModel("资源配置")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResourceConfig {

    @ApiModelProperty(value = "离线-Python模型训练（调试环境）")
    ContainerBasedConfig modelTrainingDebug = new ContainerBasedConfig();
    @ApiModelProperty(value = "离线-Python数据处理（调试环境）")
    ContainerBasedConfig dataProcessingDebug = new ContainerBasedConfig();
    @ApiModelProperty(value = "离线-PySpark（调试环境）")
    PySparkConfig pySparkConfigDebug = new PySparkConfig();
    @ApiModelProperty(value = "离线-R语言（调试环境）")
    RConfig rConfigDebug = new RConfig();
    @ApiModelProperty(value = "模型预测（调试环境）")
    ContainerBasedConfig modelPredictDebug = new ContainerBasedConfig();
    @ApiModelProperty(value = "模型精调（调试环境）")
    ContainerBasedConfig modelFineTuningDebug = new ContainerBasedConfig();

    @ApiModelProperty(value = "离线-Python模型训练")
    ContainerBasedConfig modelTraining = new ContainerBasedConfig();
    @ApiModelProperty(value = "离线-Python数据处理")
    ContainerBasedConfig dataProcessing = new ContainerBasedConfig();
    @ApiModelProperty(value = "离线-PySpark")
    PySparkConfig pySparkConfig = new PySparkConfig();
    @ApiModelProperty(value = "离线-R语言")
    RConfig rConfig = new RConfig();
    @ApiModelProperty(value = "在线-SQL流任务")
    FlinkConfig flinkSQL = new FlinkConfig();
    @ApiModelProperty(value = "在线-JAR任务")
    FlinkConfig flinkJar = new FlinkConfig();
    @ApiModelProperty(value = "模型预测")
    ContainerBasedConfig modelPredict = new ContainerBasedConfig();
    @ApiModelProperty(value = "模型精调")
    ContainerBasedConfig modelFineTuning = new ContainerBasedConfig();
    @ApiModelProperty(value = "模型在线推理")
    ContainerBasedConfig modelOnlinePredict = new ContainerBasedConfig();

    @Data
    public static class ContainerBasedConfig {
        @ApiModelProperty(value = "内存大小", example = "2G")
        Integer memory = 20;
        @ApiModelProperty(value = "CPU核数系数", example = "0.8")
        float cpuCoreCoefficient = 0.8F;
    }

    @Data
    public static class PySparkConfig {
        @ApiModelProperty(value = "driver内存大小", example = "1G")
        Integer driverMem = 20;
        @ApiModelProperty(value = "executor内存大小", example = "1")
        Integer driverCores = 16;
    }

    @Data
    public static class RConfig {
        @ApiModelProperty(value = "内存大小", example = "1024G")
        Integer memory = 20;
    }

    @Data
    public static class FlinkConfig {
        @ApiModelProperty(value = "slots", example = "1")
        Integer slots = 4;
        @ApiModelProperty(value = "jobManager", example = "1024MB")
        Integer jobManagers = 2048;
        @ApiModelProperty(value = "taskManager", example = "2048MB")
        Integer taskManagers = 2048;
    }

}
