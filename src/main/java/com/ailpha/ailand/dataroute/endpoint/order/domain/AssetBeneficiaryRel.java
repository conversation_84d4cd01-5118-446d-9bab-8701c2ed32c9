package com.ailpha.ailand.dataroute.endpoint.order.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/11/16
 */
@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_asset_beneficiary_rel")
@AllArgsConstructor
public class AssetBeneficiaryRel implements Serializable {

    /**
     * ID
     */
    @Id
    @Schema(description = "ID")
    @Column(name = "id")
    String id;
    /**
     * 资产ID
     */
    @Schema(description = "资产ID")
    @Column(name = "asset_id")
    String assetId;
    /**
     * 获益人用户ID
     */
    @Schema(description = "获益人用户ID")
    @Column(name = "beneficiary_id")
    String beneficiaryId;
    /**
     * 订单ID
     */
    @Schema(description = "订单编号")
    @Column(name = "order_id")
    String orderId;
    /**
     * 扩展字段json（api鉴权key等信息）
     */
    @Schema(description = "扩展字段json")
    @Column(name = "extend")
    String extend;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Column(name = "create_time")
    Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @Column(name = "update_time")
    Date updateTime;
}
