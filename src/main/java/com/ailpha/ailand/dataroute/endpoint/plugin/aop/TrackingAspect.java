package com.ailpha.ailand.dataroute.endpoint.plugin.aop;

import com.ailpha.ailand.dataroute.endpoint.plugin.service.TrackingModuleService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yuwenping
 * @date: 2025/5/28 16:44
 * @Description:
 */
@Aspect
@Component
@Slf4j
public class TrackingAspect {

    @Autowired
    private TrackingModuleService trackingModuleService;

    @Around("@annotation(trackingPoint)")
    public Object track(ProceedingJoinPoint joinPoint, TrackingPoint trackingPoint) throws Throwable {
        Object result = null;
        try {

            log.debug("TrackingAspect detected,begin track");
            // 前置处理
            String moduleName = trackingPoint.moduleType();

            // 执行原方法
            result = joinPoint.proceed();

            // 触发埋点
            Map<String, Object> params = new HashMap<>();
            params.put("module", moduleName);
            params.put("result", result);

            // 添加上下文参数
            Map<String, Object> contextParams = TrackingContext.getContext();
            params.putAll(contextParams);

            trackingModuleService.track(moduleName, params);

            log.debug("TrackingAspect successfully execute,end track");
        } catch (Exception e) {
            log.error("e:", e);
        }
        return result;
    }
}
