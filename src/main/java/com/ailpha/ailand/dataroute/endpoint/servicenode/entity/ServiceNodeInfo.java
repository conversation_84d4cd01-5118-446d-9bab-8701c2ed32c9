package com.ailpha.ailand.dataroute.endpoint.servicenode.entity;

import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.ApprovalStatus;
import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response.ServiceNodeProcessStatusVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/7/30
 */
@Slf4j
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "service_node_info")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeInfo implements Serializable {

    @Id
    @Schema(description = "ID")
    @Column(name = "id", updatable = false)
    private String id;

    @Schema(description = "业务节点登记名称")
    @Column(name = "entry_name")
    private String entryName;

    @Schema(description = "业务节点标识编码")
    @Column(name = "service_node_id")
    private String serviceNodeId;

    @Schema(description = "业务功能类型：1－应用侧基础设施，2－数据交易类，3－数据开发利用类，4－公共数据授权运营平台类，5－公共服务平台类")
    @Column(name = "type")
    private String type;

    @Schema(description = "业务功能类型描述")
    @Column(name = "type_description")
    private String typeDescription;

    @Schema(description = "业务节点IP地址")
    @Column(name = "ip")
    private String ip;

    @Schema(description = "业务节点域名")
    @Column(name = "domain_name")
    private String domainName;

    @Schema(description = "业务节点接口地址")
    @Column(name = "api_url")
    private String apiUrl;

    @Schema(description = "业务功能简介")
    @Column(name = "introduction")
    private String introduction;

    @Schema(description = "业务节点版本")
    @Column(name = "version")
    private String version;

    @Schema(description = "备注")
    @Column(name = "reserve_notes")
    private String reserveNotes;

    @Schema(description = "所属法人或其他组织名称")
    @Column(name = "enterprise_name")
    private String enterpriseName;

    @Schema(description = "所属法人或其他组织身份标识码")
    @Column(name = "enterprise_identity_id")
    private String enterpriseIdentityId;

    @Schema(description = "状态：APPLY（待审批）APPROVED（已通过）REJECTED（已拒绝）")
    @Column(name = "process_status")
    private String processStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "process_time")
    private Date processTime;

    @Schema(description = "扩展字段")
    @Column(name = "extend")
    private String extend;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    private Date updateTime;

    public void copyProperties(ServiceNodeProcessStatusVO serviceNodeProcessStatusVO) {
        BeanUtils.copyProperties(serviceNodeProcessStatusVO, this, "processStatus", "processTime");
        this.setProcessStatus(getApprovalStatus(serviceNodeProcessStatusVO.getProcessStatus()).name());
        if (!ObjectUtils.isEmpty(serviceNodeProcessStatusVO.getProcessTime())) {
            try {
                Date processTime = new Date(Long.parseLong(serviceNodeProcessStatusVO.getProcessTime()));
                this.setProcessTime(processTime);
            } catch (Exception e) {
                log.error("format processTime:{} error，", serviceNodeProcessStatusVO.getProcessTime() + e);
            }
        }
    }

    public static ApprovalStatus getApprovalStatus(String processStatus) {
        return switch (processStatus) {
            case "0" -> ApprovalStatus.APPLY;
            case "1" -> ApprovalStatus.APPROVED;
            case "2" -> ApprovalStatus.REJECTED;
            default -> ApprovalStatus.REJECTED;
        };
    }
}
