package com.ailpha.ailand.dataroute.endpoint.statistics;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.third.output.StatisticRemote;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final StatisticRemote statisticRemote;
    public final UserRepository userRepository;

    public StatisticsResponse statistics() {
        CommonResult<StatisticsResponse> statistic = statisticRemote.statistic();
        Assert.isTrue(statistic.isSuccess(), statistic.getMsg());
        StatisticsResponse data = statistic.getData();
        StatisticsResponse.Overview overview = data.getOverview();
        overview.setUserCount((int) userRepository.count());
        data.setOverview(overview);
        return data;
    }


}