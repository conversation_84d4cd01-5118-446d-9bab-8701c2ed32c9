package com.ailpha.ailand.dataroute.endpoint.statistics;

import com.ailpha.ailand.dataroute.endpoint.base.PrivatePlatformRsp;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetPrepareStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.QDeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRepository;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StatisticsService {

    private static final QDeliveryScene qDeliveryScene = QDeliveryScene.deliveryScene;

    private final JPAQueryFactory queryFactory;
    private final UserRepository userRepository;
    private final OrderRecordRepository orderRecordRepository;
    private final DeliverySceneRepository deliverySceneRepository;
    private final DataResourceRepository dataResourceRepository;
    private final DataProductRepository productRepository;

    public StatisticsResponse statistics() {
        StatisticsResponse statisticsResponse = StatisticsResponse.builder().build();
        StatisticsResponse.Overview overview = new StatisticsResponse.Overview();
         overview.setUserCount(userRepository.countByDeleted(Boolean.FALSE));

        overview.setDataResourceRegistCount((int) dataResourceRepository.count(Example.of(DataResource.builder().itemStatus("item_status2").build())));
        overview.setDataProductRegistCount((int) productRepository.count(Example.of(DataProduct.builder().itemStatus("item_status2").build())));
        overview.setDataProductPublishCount((int) productRepository.count(DataProduct.publishStatusIs(null, "2")));

        // 总订单数量
        overview.setOrderCount(orderRecordRepository.queryCount());
        statisticsResponse.setOverview(overview);
        List<Object[]> countDataResourceByUsernameTop10 = dataResourceRepository.countDataResourceByUsernameTop10();
        LinkedHashMap<String, Long> countDataResourceByUsernameTop10Map = new LinkedHashMap<>();
        countDataResourceByUsernameTop10.forEach(arr -> {
            if ((Long) arr[1] > 0) {
                countDataResourceByUsernameTop10Map.put((String) arr[0], (Long) arr[1]);
            }
        });
        statisticsResponse.setResourceStatistics(StatisticsResponse.StatisticResource.builder()
                .dataAssetCount((int) dataResourceRepository.count(DataResource.itemStatusIs(DataResource.dataType1Is(null, "数据集"), "item_status2")))
                .pictureCount((int) dataResourceRepository.count(DataResource.itemStatusIs(DataResource.dataType1Is(null, "图像"), "item_status2")))
                .textCount((int) dataResourceRepository.count(DataResource.itemStatusIs(DataResource.dataType1Is(null, "文件"), "item_status2")))
                .userResourceCount(countDataResourceByUsernameTop10Map)
                .build());
        Map<DeliveryMode, Integer> deliveryModeCounts = new HashMap<>();
        for (DeliveryMode deliveryMode : DeliveryMode.values()) {
            if (deliveryMode.name().startsWith("TEE") && deliveryModeCounts.containsKey(DeliveryMode.TEE)) {
                continue;
            }
            if (deliveryMode.name().startsWith("MPC_")) {
                continue;
            }
            deliveryMode = deliveryMode.name().startsWith("TEE") ? DeliveryMode.TEE : deliveryMode;
            deliveryModeCounts.put(deliveryMode, (int) productRepository.count(DataProduct.itemStatusIs(DataProduct.deliveryModeIs(null, deliveryMode), "item_status2")));
        }
        List<String> deliveryMethods = List.of("01", "02", "03");
        Map<String, Integer> deliveryMethodCounts = new HashMap<>();
        for (String deliveryMethod : deliveryMethods) {
            deliveryMethodCounts.put(deliveryMethod, (int) productRepository.count(DataProduct.itemStatusIs(DataProduct.deliveryMethodIs(null, deliveryMethod), "item_status2")));
        }
        List<Object[]> dataProductCountByUsernameMap = productRepository.countDataProductByUsernameTop10();
        LinkedHashMap<String, Map<String, Long>> countDataProductByUsernameMap = new LinkedHashMap<>();
        dataProductCountByUsernameMap.forEach(arr -> {
            if ((Long) arr[1] > 0) {
                long registCount = productRepository.count(Example.of(DataProduct.builder().username((String) arr[0]).itemStatus("item_status2").build()));
                long publishCount = productRepository.count(DataProduct.publishStatusIs(DataProduct.usernameIs(null, (String) arr[0]), "2"));
                countDataProductByUsernameMap.put((String) arr[0], Map.of(
                        "registCount", registCount,
                        "publishCount", publishCount));
            }
        });

        statisticsResponse.setProductStatistics(StatisticsResponse.StatisticProduct.builder()
                .dataAssetCount((int) productRepository.count(DataProduct.publishStatusIs(DataProduct.dataType1Is(null, "数据集"), "2")))
                .pictureCount((int) productRepository.count(DataProduct.publishStatusIs(DataProduct.dataType1Is(null, "图像"), "2")))
                .textCount((int) productRepository.count(DataProduct.publishStatusIs(DataProduct.dataType1Is(null, "文件"), "2")))
                .modelCount((int) productRepository.count(DataProduct.publishStatusIs(DataProduct.dataType1Is(null, "模型"), "2")))
                .deliveryMethodCount(deliveryMethodCounts)
                .deliveryModeCount(deliveryModeCounts)
                .userRegistAndPublishCount(countDataProductByUsernameMap)
                .build());

        // 使用场景分组统计
        List<StatisticsResponse.UseScene> useScenes = useScene();
        statisticsResponse.setUseScene(useScenes);
        return statisticsResponse;
    }

    /**
     * 使用场景分组统计
     */
    public List<StatisticsResponse.UseScene> useScene() {
        List<StatisticsResponse.UseScene> useScenes = new ArrayList<>();
        // 步骤1: 按 createUser 分组，取「创建记录数最多的前 10 个用户」
        List<String> top10CreateUsers = queryFactory
                .from(qDeliveryScene)
                .groupBy(qDeliveryScene.createUser)
                // 按「分组后数量」倒序，取前 10
                .orderBy(qDeliveryScene.createUser.count().desc())
                .limit(10)
                .select(qDeliveryScene.createUser)
                .fetch();
        if (ObjectUtils.isEmpty(top10CreateUsers)) {
            return useScenes;
        }

        List<User> users = userRepository.findAllById(top10CreateUsers);
        Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getId, user -> user, (s1, s2) -> s2));

        // 步骤2: 遍历 top10CreateUsers，逐个按 deliveryType 分组统计数量
        for (String createUser : top10CreateUsers) {
            // 按 createUser + deliveryType 分组统计
            List<Tuple> tupleList = queryFactory
                    .from(QDeliveryScene.deliveryScene)
                    .where(QDeliveryScene.deliveryScene.createUser.eq(createUser))
                    .groupBy(
                            QDeliveryScene.deliveryScene.createUser,
                            QDeliveryScene.deliveryScene.deliveryType
                    )
                    .select(
                            QDeliveryScene.deliveryScene.createUser,
                            QDeliveryScene.deliveryScene.deliveryType,
                            QDeliveryScene.deliveryScene.deliveryType.count()
                    )
                    .fetch();

            // 遍历统计结果，封装到自定义响应对象（如 StatisticsResponse）
            List<StatisticsResponse.UseSceneStatistic> useSceneStatistic = new ArrayList<>();
            if (!ObjectUtils.isEmpty(tupleList)) {
                for (Tuple tuple : tupleList) {
                    DeliveryType type = tuple.get(QDeliveryScene.deliveryScene.deliveryType);
                    Long count = tuple.get(QDeliveryScene.deliveryScene.deliveryType.count());

                    StatisticsResponse.UseSceneStatistic statistic = StatisticsResponse.UseSceneStatistic.builder()
                            .deliveryType(type).count(count).build();
                    useSceneStatistic.add(statistic);
                }
            }

            String username = userMap.get(createUser).getAccount();
            StatisticsResponse.UseScene useScene = StatisticsResponse.UseScene.builder()
                    .createUsername(username).useSceneStatistic(useSceneStatistic).build();
            useScenes.add(useScene);
        }
        return useScenes;
    }

    public PrivatePlatformRsp privatePlatformStat(String type) {
        PrivatePlatformRsp rsp = new PrivatePlatformRsp();

        DeliveryMode deliveryMode;
        List<DeliveryType> deliveryTypeList = new ArrayList<>();
        if ("tee".equals(type)) {
            deliveryMode = DeliveryMode.TEE;
            deliveryTypeList = Arrays.asList(DeliveryType.TEE_ONLINE, DeliveryType.TEE_OFFLINE, DeliveryType.TEE_MODEL_OPTIMIZE, DeliveryType.TEE_MODEL_PREDICT);
        } else {
            deliveryMode = DeliveryMode.MPC;
            deliveryTypeList = Arrays.asList(DeliveryType.MPC_PRIVATE_INFORMATION_RETRIEVAL, DeliveryType.MPC_PRIVATE_SET_INTERSECTION, DeliveryType.MPC_CIPHER_TEXT_COMPUTE);

        }
        Specification<DataProduct> specification = DataProduct.prepareStatusIs(null, DataAssetPrepareStatus.AVAILABLE);
        specification = DataProduct.itemStatusNot(specification, "item_status4");
        Integer dataAssetCount = (int) productRepository.count(DataProduct.deliveryModeIs(specification, deliveryMode));
        Integer deliverySceneCount = (int) deliverySceneRepository.countAllByDeliveryTypeIn(deliveryTypeList);

        rsp.setDataAssetCount(dataAssetCount);
        rsp.setDeliverySceneCount(deliverySceneCount);
        return rsp;
    }
}