package com.ailpha.ailand.dataroute.endpoint.order.remote.request;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:10
 */
@Data
public class OrderPageQuery {

    private Integer pageSize;          // 分页大小
    private Integer pageNum;           // 当前页数
    private String srcPlatformId;     // 来源平台标识 —— 赣州 对应 我们平台的 数由器id
    private String platformId;     // 连接器标识/平台标识  0516 修改：要求和 srcPlatformId 值一致
    private String srcPlatformOrderNo;// 来源平台订单标识
    private String itemName;          // 商品名称
    private String srcProductId;       // 来源产品标识 ⭐️ 产品唯一ID，一个产品只能有一种交付方式 ⭐️
    private String industryType;       // 行业分类，详见附录《行业代码表》
    private String region;            // 地域分类，详见附录《全国区划表》
    private String productType;       // 产品类型（附录字典项）⭐️ 01:数据集  02:API产品  03:数据应用  04:数据报告  05:其他 ⭐️
    private String producerId;       // 提供方标识
    private String consumerId;        // 使用方标识
    private String contractId;        // 合同 ID
    private String producerCode;      // 提供方编码
    private String consumerCode;      // 使用方编码
    private String dpe;               // 提供方连接器
    private String dce;               // 使用方连接器
    private String contractNo;        // 合同编号
    private String orderStatus;        // 订单状态（附录字典项order status） ⭐️ 1: 已付款  0:未付款 ⭐️

}
