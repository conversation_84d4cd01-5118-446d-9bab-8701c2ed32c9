package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import lombok.Data;

@Data
public class EnterpriseInfoResponse {
    BaseInfo baseInfo;
    ExtendInfo extendInfo;

    @Data
    public static class BaseInfo {
        // identityId 唯一身份标识
        String identityId;
        // enterpriseName 法人或其他组织名称
        String enterpriseName;
        // 统一社会信用代码
        String enterpriseCode;
        // 法人或其他组织类型
        String enterpriseType;
        // 经营期限起始(yyyy-MM-dd)
        String operatingPeriodBegin;
        // 经营期限截止(yyyy-MM-dd
        String operatingPeriodEnd;
        // 实名认证方式
        String authType;
        // 实名认证状态
        String authStatus;
        // 法定代表人或负责人姓名
        String legalPerson;
        // 法定代表人或负责人证件号
        String legalPersonCertno;
        // 法定代表人或负责人实名等级
        String legalPersonAuthLevel;
        // 身份状态：0-不可用 1-可用
        String identityStatus;
        // 身份颁发时间
        String authTime;
    }

    @Data
    public static class ExtendInfo {
        // 注册地址
        String enterpriseAddress;
        // 注册金额
        String regAmount;
        // 注册日期(yyyy-MM-dd
        String regDate;
        // 经营范围
        String businessScope;
        // 行业类型
        String industryCategory;
        // 电子营业执照 Base64 编码后的字符串
        String businessLicense;
        // 电子营业执照-文件格式（png，jpg等）
        String businessLicenseType;
        // 法定代表人或负责人手机号
        String legalPersonPhone;
        // 法定代表人或负责人邮箱
        String legalPersonEmail;
        // 法定代表人或负责人身份状态
        String legalPersonStatus;
    }
}
