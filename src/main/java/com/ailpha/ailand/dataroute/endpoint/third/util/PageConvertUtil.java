package com.ailpha.ailand.dataroute.endpoint.third.util;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.third.response.PageResponse;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/20 20:12
 */
public class PageConvertUtil {


    /**
     * 数瀚 Page 转换成本平台 Page 对象
     */
    public static <T> PageResult<T> convert(CommonResult<PageResponse<T>> result) {
        PageResponse<T> response = result.getData();
        if (response == null) {
            return new PageResult<>(Collections.emptyList(), 0);
        }
        List<T> record = response.getRecords() == null ? Collections.emptyList() : response.getRecords();
        return new PageResult<>(record, response.getTotal());
    }

}
