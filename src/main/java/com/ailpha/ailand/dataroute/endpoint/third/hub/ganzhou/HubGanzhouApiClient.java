package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SignUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.restclient.BufferingClientHttpResponseWrapper;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataProductInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataResourceInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.FileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.PlatformListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import com.dbapp.rest.constant.OpenApiConstant;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;

import static com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig.acceptsUntrustedCertsHttpClient;

/**
 * 枢纽（数瀚）接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HubGanzhouApiClient {
    private final ObjectMapper objectMapper;

    private RestClient restClient() {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        try {
            requestFactory.setHttpClient(acceptsUntrustedCertsHttpClient());
        } catch (Exception ignore) {
        }
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(List.of(
                MediaType.APPLICATION_JSON, new MediaType("application", "*+json"),
                MediaType.TEXT_PLAIN
        ));
        return RestClient.builder()
                .requestFactory(requestFactory)
                .messageConverters(httpMessageConverters -> httpMessageConverters.addFirst(mappingJackson2HttpMessageConverter))
                .baseUrl(getHubInfo().getUrl())
                .requestInterceptor((request, body, execution) -> {
                    long timestamp = System.currentTimeMillis();
                    NodeDTO.HubInfo hubInfo = getHubInfo();
                    // 将请求体JSON转为Map并合并到paramMap
                    Map<String, Object> bodyMap = JSONUtil.toBean(body.length == 0 ? "{}" : new String(body, StandardCharsets.UTF_8), new TypeReference<Map<String, Object>>() {
                    }.getType(), true);
                    Map<String, Object> paramMap = new LinkedHashMap<>(bodyMap);
                    paramMap.put(SignUtil.ACCESS_KEY, hubInfo.getAk());
                    paramMap.put(SignUtil.TIMESTAMP, String.valueOf(timestamp));
                    paramMap.put(SignUtil.NONCE, SignUtil.getRandomString(32));

                    String sign = SignUtil.createSign(paramMap, hubInfo.getSk());
                    paramMap.put(OpenApiConstant.SIGN, sign);
                    if (log.isDebugEnabled()) {
                        log.debug("枢纽(赣州)接口 {} 请求体 {}", request.getURI(), JSONUtil.toJsonStr(paramMap));
                    }
                    byte[] bytes = JSONUtil.toJsonStr(paramMap).getBytes(StandardCharsets.UTF_8);
                    request.getHeaders().set("Content-Length", String.valueOf(bytes.length));
                    ClientHttpResponse response = execution.execute(request, bytes);
                    BufferingClientHttpResponseWrapper httpResponseWrapper = new BufferingClientHttpResponseWrapper(response);
                    String responseBody = IOUtils.toString(httpResponseWrapper.getBody(), Charset.defaultCharset());
                    try {
                        JsonNode jsonNode = JacksonUtils.readTree(responseBody);
                        if (log.isDebugEnabled()) {
                            if (responseBody.length() > 1024 * 10) {
                                log.debug("赣州接口响应 success: {}, code: {}, message: {}, dataSize: {}", jsonNode.get("success"), jsonNode.get("code"),
                                        jsonNode.get("message"), jsonNode.get("data") != null && jsonNode.get("data").get("total") != null ? jsonNode.get("data").get("total") : 0);
                            } else {
                                log.debug("赣州接口响应 {}", jsonNode);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("赣州响应体解析失败 {}", responseBody, e);
                    }
                    return httpResponseWrapper;
                })
                .build();
    }

    protected NodeDTO.HubInfo getHubInfo() {
        NodeDTO serviceFirstNode = SpringUtil.getBean(NodeService.class).getFirstNode();
        NodeDTO.HubInfo hubInfo = serviceFirstNode.getHubInfo();
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotEmpty(hubInfo.getUrl()), "请先添加功能节点");
        return hubInfo;
    }

    private static DataProductApi hubGanzhouDataProductApi;

    private synchronized DataProductApi dataProductApi() {
        if (!Objects.isNull(hubGanzhouDataProductApi)) {
            return hubGanzhouDataProductApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubGanzhouDataProductApi = factory.createClient(DataProductApi.class);
        return hubGanzhouDataProductApi;
    }

    private static DataResourceApi hubGanzhouDataResourceApi;

    private synchronized DataResourceApi dataResourceApi() {
        if (!Objects.isNull(hubGanzhouDataResourceApi)) {
            return hubGanzhouDataResourceApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubGanzhouDataResourceApi = factory.createClient(DataResourceApi.class);
        return hubGanzhouDataResourceApi;
    }

    public SuccessResponse<List<DataResourceInfo>> allMarketDataResource(DataAssetQuery dataAssetQuery) {
        if (StringUtils.isEmpty(dataAssetQuery.getRouteId())) {
            NodeDTO serviceFirstNode = SpringUtil.getBean(NodeService.class).getFirstNode();
            dataAssetQuery.setRouteId(serviceFirstNode.getNodeId());
        }
        ResponseListWrapper<DataResourceInfo> dataResourcePage = dataResourceApi().dataCatalogQuery(
                DataResourceCatalogQuery.builder()
                        .pageSize((int) dataAssetQuery.getSize())
                        .pageNum((int) dataAssetQuery.getNum())
                        .resourceName(dataAssetQuery.getAssetName())
                        .outerResourceId(dataAssetQuery.getOuterResourceId())
                        .platformId(dataAssetQuery.getRouteId())
//                        .dataType() TODO 无对应字段
                        .resourceType(dataAssetQuery.getResourceType())
                        .industry(dataAssetQuery.getIndustry())
//                        .resourceOwnerId() TODO
                        .dataSource(dataAssetQuery.getDataSource())
                        .resourceStatus(dataAssetQuery.getResourceStatus())
                        .build()
        );
        if (dataResourcePage == null || dataResourcePage.getCode() != 200) {
            log.error("查询数据资源列表: {}, {}", JSONUtil.toJsonStr(dataAssetQuery), JSONUtil.toJsonStr(dataResourcePage));
        }
        Assert.isTrue(dataResourcePage != null, "查询数据资源列表失败");
        Assert.isTrue(dataResourcePage.getCode() == 200, dataResourcePage.getMsg());
        return SuccessResponse.success(dataResourcePage.getRows())
                .total(Long.valueOf(dataResourcePage.getTotal()))
                .page(Page.of(dataAssetQuery.getNum(), dataAssetQuery.getSize()))
                .build();
    }

    public SuccessResponse<List<DataProductInfo>> allMarketDataProduct(DataAssetQuery dataAssetQuery) {
        if (StringUtils.isEmpty(dataAssetQuery.getRouteId())) {
            NodeDTO serviceFirstNode = SpringUtil.getBean(NodeService.class).getFirstNode();
            dataAssetQuery.setRouteId(serviceFirstNode.getNodeId());
        }
        ResponseListWrapper<DataProductInfo> dataProductPage = dataProductApi().dataCatalogQuery(
                DataProductCatalogQuery.builder()
                        .pageSize((int) dataAssetQuery.getSize())
                        .pageNum((int) dataAssetQuery.getNum())
                        .platformId(dataAssetQuery.getRouteId())
                        .outerProductId(dataAssetQuery.getOuterProductId())
                        .productType(dataAssetQuery.getProductType())
                        .productName(dataAssetQuery.getAssetName())
                        .industry(dataAssetQuery.getIndustry())
                        .productRegion(dataAssetQuery.getProductRegion())
                        .deliveryMethod(dataAssetQuery.getDeliveryMethod())
                        .dataSubject(dataAssetQuery.getDataSubject())
                        .entityId(dataAssetQuery.getEntityId())
                        .entityCode(dataAssetQuery.getEntityCode())
                        .productCode(dataAssetQuery.getProductCode())
                        .productStatus(dataAssetQuery.getProductStatus())
                        .build()
        );
        if (dataProductPage == null || dataProductPage.getCode() != 200) {
            log.error("查询数据产品列表: {}, {}", JSONUtil.toJsonStr(dataAssetQuery), JSONUtil.toJsonStr(dataProductPage));
        }
        Assert.isTrue(dataProductPage != null, "查询数据产品列表失败");
        Assert.isTrue(dataProductPage.getCode() == 200, dataProductPage.getMsg());
        return SuccessResponse.success(dataProductPage.getRows())
                .total(Long.valueOf(dataProductPage.getTotal()))
                .page(Page.of(dataAssetQuery.getNum(), dataAssetQuery.getSize()))
                .build();
    }

    public String dataProductInfoRegist(DataProductInfoRegist registRequest) {
        ResponseWrapper<String> dataRegistResponse = dataProductApi().dataProductInfoRegist(registRequest);

        if (dataRegistResponse == null || dataRegistResponse.getCode() != 200) {
            log.error("数据产品登记失败: {}, {}", JSONUtil.toJsonStr(registRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null, "数据产品登记失败");
        Assert.isTrue(dataRegistResponse.getCode() == 200, dataRegistResponse.getMsg());
        return dataRegistResponse.getData();
    }

    public DataAssetUpdateVO dataProductInfoUpdate(DataProductInfoUpdate updateRequest) {
        ResponseWrapper<DataAssetUpdateVO> dataProductInfoUpdate = dataProductApi().dataProductInfoUpdate(updateRequest);
        if (dataProductInfoUpdate == null
//                || !dataProductInfoUpdate.isSuccess()
        ) {
            log.error("数据产品更新失败: {}, {}", JSONUtil.toJsonStr(updateRequest), JSONUtil.toJsonStr(dataProductInfoUpdate));
        }
        Assert.isTrue(dataProductInfoUpdate != null
//                && dataProductInfoUpdate.isSuccess()
                , dataProductInfoUpdate != null ? dataProductInfoUpdate.getMsg() : "数据产品更新失败");
        return dataProductInfoUpdate.getData();
    }

    public DataAssetRevokeVO dataProductInfoRevoke(String productCode) {
        ResponseWrapper<DataAssetRevokeVO> dataProductRevokeResponse = dataProductApi().dataProductInfoRevoke(DataProductInfoRevoke.builder().productCode(productCode).build());
        if (dataProductRevokeResponse == null
//                || !dataProductRevokeResponse.isSuccess()
        ) {
            log.error("数据产品注销失败: {}, {}", productCode, JSONUtil.toJsonStr(dataProductRevokeResponse));
        }
        Assert.isTrue(dataProductRevokeResponse != null
//                && dataProductRevokeResponse.isSuccess()
                , dataProductRevokeResponse != null ? dataProductRevokeResponse.getMsg() : "数据产品注销失败");
        return dataProductRevokeResponse.getData();
    }

    public DataProductInfo getProductInfo(String productCode) {
        ResponseWrapper<DataProductInfo> byProductInfo = dataProductApi().getByProductInfo(GetByProductInfo.builder()
                .productCode(productCode)
                .build());
        if (byProductInfo == null || byProductInfo.getCode() != 200) {
            log.error("获取产品信息失败: {}, {}", productCode, JSONUtil.toJsonStr(byProductInfo));
        }
        Assert.isTrue(byProductInfo != null, "获取产品信息失败");
        Assert.isTrue(byProductInfo.getCode() == 200, byProductInfo.getMsg());
        return byProductInfo.getData();
    }


    public String dataResourceRegistry(DataResourceRegistry registryRequest) {
        ResponseWrapper<String> dataRegistResponse = dataResourceApi().dataResourceRegistry(registryRequest);

        if (dataRegistResponse == null || dataRegistResponse.getCode() != 200) {
            log.error("数据资源登记失败: {}, {}", JSONUtil.toJsonStr(registryRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null, "数据资源登记失败");
        Assert.isTrue(dataRegistResponse.getCode() == 200, dataRegistResponse.getMsg());
        return dataRegistResponse.getData();
    }

    public DataResourceInfo dataResourceRegistryUpdate(DataResourceRegistryUpdate updateRequest) {
        ResponseWrapper<DataResourceInfo> dataRegistResponse = dataResourceApi().dataResourceRegistryUpdate(updateRequest);
        if (dataRegistResponse == null
//                || !dataRegistResponse.isSuccess()
        ) {
            log.error("数据资源更新失败: {}, {}", JSONUtil.toJsonStr(updateRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null
//                && dataRegistResponse.isSuccess()
                , dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据资源更新失败");
        return dataRegistResponse.getData();
    }

    public DataResourceInfo getResourceInfo(String resourceCode) {
        ResponseWrapper<DataResourceInfo> byProductInfo = dataResourceApi().getByResourceInfo(GetByResourceInfo.builder()
                .resourceCode(resourceCode)
                .build());
        if (byProductInfo == null || byProductInfo.getCode() != 200) {
            log.error("获取资源信息失败: {}, {}", resourceCode, JSONUtil.toJsonStr(byProductInfo));
        }
        Assert.isTrue(byProductInfo != null, "获取资源信息失败");
        Assert.isTrue(byProductInfo.getCode() == 200, byProductInfo.getMsg());
        return byProductInfo.getData();
    }

    public void dataResourceRegistryRevoke(String resourceCode) {
        ResponseWrapper<Void> dataRegistResponse = dataResourceApi().dataResourceRegistryRevoke(DataResourceRegistryRevoke.builder().resourceCode(resourceCode).build());
        if (dataRegistResponse == null
//                || !dataRegistResponse.isSuccess()
        ) {
            log.error("数据资源注销失败: {}, {}", resourceCode, JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null
//                && dataRegistResponse.isSuccess()
                , dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据资源注销失败");
//        return dataRegistResponse.getData();
    }

    public void publishProduct(PublishProduct publishRequest) {
        ResponseWrapper<DataProductPublishVO> publishProductResult = dataProductApi().publishProduct(publishRequest);
        if (publishProductResult == null || publishProductResult.getCode() != 200) {
            log.error("数据产品目录发布失败: {}", JSONUtil.toJsonStr(publishProductResult));
        }
        Assert.isTrue(publishProductResult != null, "数据产品目录发布失败");
        Assert.isTrue(publishProductResult.getCode() == 200, publishProductResult.getMsg());
    }

    public void publishResource(PublishProduct publishRequest) {
        ResponseWrapper<DataProductPublishVO> publishProductResult = dataResourceApi().publishResource(publishRequest);
        if (publishProductResult == null || publishProductResult.getCode() != 200) {
            log.error("数据资源目录发布失败: {}", JSONUtil.toJsonStr(publishProductResult));
        }
        Assert.isTrue(publishProductResult != null, "数据产品目录发布失败");
        Assert.isTrue(publishProductResult.getCode() == 200, publishProductResult.getMsg());
    }

    public List<PlatformListVO> getPlatformList() {
        ResponseWrapper<List<PlatformListVO>> platformListResponse = dataProductApi().platformList();
        if (platformListResponse == null || platformListResponse.getCode() != 200) {
            log.error("获取发布平台列表失败: {}", JSONUtil.toJsonStr(platformListResponse));
        }
        Assert.isTrue(platformListResponse != null, "获取发布平台列表失败");
        Assert.isTrue(platformListResponse.getCode() == 200, platformListResponse.getMsg());
        return platformListResponse.getData();
    }

    public FileUploadResponse fileUpload(File file) {
        ResponseWrapper<FileUploadResponse> filedUploadResponse = dataProductApi().fileUpload(FileUploadRequest.builder()
                .base64Encoded(encodeFileToBase64(file))
                .fileName(file.getName())
                .build());

        if (filedUploadResponse == null || filedUploadResponse.getCode() != 200) {
            log.error("文件上传失败: {}", JSONUtil.toJsonStr(filedUploadResponse));
        }
        Assert.isTrue(filedUploadResponse != null, "文件上传失败");
        Assert.isTrue(filedUploadResponse.getCode() == 200, filedUploadResponse.getMsg());

        return filedUploadResponse.getData();
    }

    private static String encodeFileToBase64(File file) {
        try {
            byte[] fileContent = Files.readAllBytes(file.toPath());
            return Base64.getEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            throw new IllegalStateException("could not read file " + file, e);
        }
    }
}
