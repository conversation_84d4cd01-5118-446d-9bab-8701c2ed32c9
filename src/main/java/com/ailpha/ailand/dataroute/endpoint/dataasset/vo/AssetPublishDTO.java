package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2024/11/17 14:32
 * 数据资产上下架
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetPublishDTO {

    /**
     * 登记平台商品ID
     */
    String platformProductId;

    /**
     * 登记平台id
     */
    String sourcePlatformId;

    /**
     * 商品上架状态：2->上架；1->下架;
     */
    Integer publishStatus;
}
