package com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan;


import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.InternalReturnCode;

import java.io.Serializable;

public interface IResult<T> extends Serializable {

    Integer getStatusCode();

    T getData();

    String getMessage();

    default boolean isSuccess() {
        return InternalReturnCode.SUCCESS.getCode().equals(getStatusCode());
    }

}
