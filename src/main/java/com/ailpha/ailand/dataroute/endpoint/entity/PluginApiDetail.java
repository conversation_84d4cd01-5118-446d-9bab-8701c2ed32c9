package com.ailpha.ailand.dataroute.endpoint.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiMarkTypeEnums;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:49
 * @Description:
 */
@Data
@Entity
@Table(name = "t_plugin_api_detail")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PluginApiDetail {
    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    Long id;

    @Column(name = "plugin_id")
    Long pluginId;

    @Enumerated(EnumType.STRING)
    PluginApiMarkTypeEnums apiMark;

    String apiName;

    String apiUrl;

    String createUser;

    Date createTime;

    Date updateTime;

    Boolean enabled;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "plugin_id", referencedColumnName = "id", insertable = false, updatable = false)
    PluginDetail pluginDetail;
}
