package com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Date;

/**
 * 数据接入任务实体
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_data_update_task")
public class DataUpdateTask {
    /**
     * 任务ID
     */
    @Id
    private String id;

    /**
     * 关联的数据产品ID
     */
    @Column(name = "data_product_id", nullable = false)
    private String dataProductId;

    /**
     * 数据更新方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "data_update_type", nullable = false)
    private UpdateWay dataUpdateType;

    /**
     *
     */
    @Column(name = "cron_expression", nullable = false)
    String cronExpression;

    /**
     * 最近一次任务的最终执行状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "last_task_status")
    private DataUpdateStatus lastTaskStatus;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    private Date updateTime;

    /**
     * 扩展字段
     */
    @Column(name = "ext")
    private String ext;

    /**
     * 任务基本信息
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "base_info", columnDefinition = "json")
    private DataUpdateTaskBaseInfo baseInfo;

    /**
     * 最近一次任务历史id
     */
    @Column(name = "latest_task_logId")
    private String latestTaskLogId;

    /**
     * 任务执行次数
     */
    @Column(name = "task_log_num")
    private Integer taskLogNum;
}