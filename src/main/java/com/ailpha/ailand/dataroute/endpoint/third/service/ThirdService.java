package com.ailpha.ailand.dataroute.endpoint.third.service;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.GetNonceResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.TokenResponse;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.third.request.ContractExecuteCallbackRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.LogReportReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneApiIdRelateReq;
import com.dbapp.rest.openapi.AppToken;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:44
 */
public interface ThirdService {

    CommonResult<Boolean> contractExecuteSuccessCallback(ContractExecuteCallbackRequest request);

    void deliveryRegister(LogReportReq logReportReq, SceneApiIdRelateReq relateReq, String localCompanyId);

    GetNonceResponse generateUuidWithCertificate(String certificate);

    TokenResponse generateTokenWithNonce(String nonce);

    // 检查token
    Boolean checkToken(String token);

    AppToken generateToken(BaseCapabilityType type, String certificate, NodeDTO.HubInfo hubInfo, String metaData);
}
