package com.ailpha.ailand.dataroute.endpoint.third.service;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.third.request.ContractExecuteCallbackRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.LogReportReq;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:44
 */
public interface ThirdService {

    /**
     * @param logReportReqs
     * @return
     */
    CommonResult<Boolean> logReport(List<LogReportReq> logReportReqs);

    CommonResult<Boolean> contractExecuteSuccessCallback(ContractExecuteCallbackRequest request);

}
