package com.ailpha.ailand.dataroute.endpoint.third.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * 2025/3/6
 */
@Slf4j
public class AiSortAESUtil {

    //可配置到Constant中，并读取配置文件注入,16位,自己定义
    private static final String KEY = "com.govern.admin";
    //参数分别代表 算法名称/加密模式/数据填充方式
    private static final String ALGORITHMSTR = "AES/%s/NoPadding";

    public enum AesEncryptMode {

        GCM,
        ECB;
    }

    /**
     * 加密
     *
     * @param content    加密的字符串
     * @param encryptKey key值
     */
    public static String encrypt(String content, String encryptKey, AesEncryptMode mode) throws Exception {
        String alg = String.format(ALGORITHMSTR, mode.name());
        if (mode == AesEncryptMode.GCM) {
            SecretKeySpec secretKeySpec = new SecretKeySpec(encryptKey.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance(alg);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
            byte[] iv = cipher.getIV();
            byte[] encryptData = cipher.doFinal(content.getBytes("utf-8"));
            byte[] message = new byte[12 + content.getBytes("utf-8").length + 16];
            System.arraycopy(iv, 0, message, 0, 12);
            System.arraycopy(encryptData, 0, message, 12, encryptData.length);
            return Base64.encodeBase64String(message);
        } else if (mode == AesEncryptMode.ECB) {
            Cipher cipher = Cipher.getInstance(alg);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));
            byte[] b = cipher.doFinal(content.getBytes("utf-8"));
            return Base64.encodeBase64String(b);
        }
        return null;
    }

    /**
     * 解密
     *
     * @param encryptStr 解密的字符串
     * @param decryptKey 解密的key值
     */
    public static String decrypt(String encryptStr, String decryptKey, AesEncryptMode mode) throws Exception {
        String alg = String.format(ALGORITHMSTR, mode.name());
        if (mode == AesEncryptMode.GCM) {
            byte[] encryptBytes = Base64.decodeBase64(encryptStr);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, encryptBytes, 0, 12);
            Cipher cipher = Cipher.getInstance(alg);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"), gcmParameterSpec);
            byte[] decryptBytes = cipher.doFinal(encryptBytes, 12, encryptBytes.length - 12);
            return new String(decryptBytes);
        } else if (mode == AesEncryptMode.ECB) {
            Cipher cipher = Cipher.getInstance(alg);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
            byte[] encryptBytes = Base64.decodeBase64(encryptStr);
            byte[] decryptBytes = cipher.doFinal(encryptBytes);
            return new String(decryptBytes);
        }
        return null;
    }

    public static String encrypt(String content, AesEncryptMode mode) throws Exception {
        return encrypt(content, KEY, mode);
    }

    public static String decrypt(String encryptStr, AesEncryptMode mode) {
        try {
            return decrypt(encryptStr, KEY, mode);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败：" + e.getLocalizedMessage());
        }
    }

    public static String encrypt(String content) throws Exception {
        return encrypt(content, AesEncryptMode.GCM);
    }

    public static String decrypt(String encryptStr) {
        return decrypt(encryptStr, AesEncryptMode.GCM);
    }

    public static void main(String[] args) {
        String decrypt = decrypt("PSFnBAEy+7bh6xWFZ3/pZeut9PdK8A20hPagAfIvh2j4LAp7M5fB4R/5cUU=");
        log.info("decrypt:{}", decrypt);
    }
}
