package com.ailpha.ailand.dataroute.endpoint.third.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "data-route.gateway")
public class GatewayConfig {

    @Deprecated
    private String managerBaseUrl;

    private String serverBaseUrl;

    private String accessKeyId;

    private String accessKeySecret;
}
