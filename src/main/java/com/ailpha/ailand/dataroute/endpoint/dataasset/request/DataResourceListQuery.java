package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资源列表筛选条件")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceListQuery {
    @Schema(description = "资产类型")
    AssetType type;
    @Schema(description = "数据资源名称")
    String resourceName;
    @Schema(description = "数据类型：结构化数据、非结构化数据、模型")
    DataType dataType;
    @Schema(description = "审批状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
    @Schema(description = "登记提交时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationSubmitTimeStart;
    @Schema(description = "登记提交时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationSubmitTimeEnd;
    @Schema(description = "登记时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationTimeStart;
    @Schema(description = "登记时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationTimeEnd;
    @JsonIgnore
    String routeId;
    @JsonIgnore
    String userId;
    @JsonIgnore
    String userName;

    @Builder.Default
    @JsonProperty("$page")
    long num = 1;
    @Builder.Default
    @JsonProperty("$size")
    long size = 10;
}
