package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormStatusQuery {
    /**
     * 业务ID
     */
    String processId;
    /**
     * 业务 ID 类型
     * 1：数据资源登记
     * 2：数据产品登记
     * 3：数据产品上架
     * 4：主体身份注册
     * 5：连接器注册
     * 6：业务节点登记
     */
    String processType;
}
