package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiMarkTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.TraderUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataResource;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataResourceRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.FileSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.PlugDetailService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetPublishDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetReportDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.BusinessReportDTO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.invoke.api.CommonException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @author: sunsas.yu
 * @date: 2024/11/28 10:26
 * @Description:
 */
@Service
@Slf4j
public class TraderServiceImpl implements TraderService {

    @Resource
    @Lazy
    private PlugDetailService plugDetailService;

    @Resource
    private RouterService routerService;

    @Resource
    private UserService userService;

    @Resource
    private DataResourceRepository dataResourceRepository;
    @Resource
    private DataProductRepository dataProductRepository;
    @Resource
    private DataAssetMapper dataAssetMapper;

    @Value("${trader.platform-code}")
    private String platformCode;

    @Resource
    private FilesStorageServiceImpl filesStorageService;

    @Value("${ailand.asset-file.keep}")
    private boolean keepAssetFile;

    private DataAsset getLocalDataAssetById(String assetId) {
        Optional<DataProduct> optionalDataProduct = dataProductRepository.findById(assetId);
        if (optionalDataProduct.isPresent()) {
            return dataAssetMapper.dataProductToDataAsset(optionalDataProduct.get());
        }
        Optional<DataResource> optionalDataResource = dataResourceRepository.findById(assetId);
        if (optionalDataResource.isPresent()) {
            return dataAssetMapper.dataResourceToDataAsset(optionalDataResource.get());
        }
        throw new CommonException("未找到 id 为 {}" + assetId + " 的本地资源或产品");
    }

    @Override
    public DataAsset dataAssetRegisterTrader(String assetId) {
        DataAsset localDataAssetById = getLocalDataAssetById(assetId);
        DataAsset.ExtraData extraData = localDataAssetById.getExtraData();
        List<Long> exchangePluginIds = extraData.getExchangePluginIds();
        if (CollectionUtils.isEmpty(exchangePluginIds)) {
            log.info("未绑定交易所插件，忽略本次上报");
            return localDataAssetById;
        }
        log.info("资产审批通过，上报交易所：id:{}", assetId);
        try {
            // step 1：查询有效开启的交易所
            List<PluginDetail> pluginDetailList = plugDetailService.findAllByIdIn(exchangePluginIds);

            if (CollectionUtils.isEmpty(pluginDetailList)) {
                log.info("未找到对应交易所插件，忽略本次上报：{}", exchangePluginIds);
                return localDataAssetById;
            }
            // 创建插件的用户为企业管理员
            String userId = pluginDetailList.stream().findFirst().get().getCreateUser();
            // step 2：封装数据资产信息
            AssetReportDTO reportDTO = transferReportDTO(localDataAssetById, userId);

            // step 3：上报
            for (PluginDetail pluginDetail : pluginDetailList) {
                String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.save_data_asset, platformCode);
                log.info("数据资产登记接口返回：{}", body);
            }
        } catch (Exception e) {
            log.error("e:", e);
        }
        return localDataAssetById;
    }

    @Override
    public void businessReportingTrader(PluginDetail pluginDetail) {
        log.info("新增交易所：{}，数商上报", pluginDetail.getName());

        try {
            // step 1：查询有效开启的交易所
            CompanyDTO companyDTO = routerService.currentCompany();
            BusinessReportDTO businessReportDTO = new BusinessReportDTO();
            businessReportDTO.setCompanyName(companyDTO.getOrganizationName());
            businessReportDTO.setCompanySucc(companyDTO.getCreditCode());
            businessReportDTO.setSourcePlatformId(platformCode);
            businessReportDTO.setPlatformCompanyId(LoginContextHolder.currentUser().getCompany().getNodeId());
            businessReportDTO.setDataCompanyType(1);

            UserDetailsResponse userDetail = userService.userDetail(pluginDetail.getCreateUser());
            BusinessReportDTO.CompanyLink companyLink = new BusinessReportDTO.CompanyLink();
            companyLink.setCompanyLinkMobile(userDetail.getPhone());
            companyLink.setCompanyLinkName(userDetail.getRealName());
            businessReportDTO.setCompanyLinkParams(Collections.singletonList(companyLink));


            // step 2：上报
            String body = TraderUtils.traderReport(businessReportDTO, pluginDetail, PluginApiMarkTypeEnums.business_reporting, platformCode);
            log.info("数商登记接口返回：{}", body);
        } catch (Exception e) {
            log.error("e:", e);
        }
    }

    @Override
    public void dataAssetUpdateTrader(Boolean isUpOrOffLine, Boolean isUp, String assetId) {
        DataAsset localDataAssetById = getLocalDataAssetById(assetId);
        DataAsset.ExtraData extraData = localDataAssetById.getExtraData();
        List<Long> exchangePluginIds = extraData.getExchangePluginIds();
        if (CollectionUtils.isEmpty(exchangePluginIds)) {
            log.info("未绑定交易所插件，忽略本次上报");
            return;
        }
        List<PluginDetail> pluginDetailList = plugDetailService.findAllByIdIn(exchangePluginIds);

        if (CollectionUtils.isEmpty(pluginDetailList)) {
            log.info("未找到对应交易所插件，忽略本次上报：{}", exchangePluginIds);
            return;
        }
        try {
            if (isUpOrOffLine) {
                dataAssetPublishTrader(assetId, pluginDetailList, isUp);
            } else {
                dataAssetUpdateTrader(localDataAssetById, pluginDetailList);
            }
        } catch (Exception e) {
            log.error("e:", e);
        }
    }

    @Override
    public Boolean manualActiveTrader(BusinessReportDTO businessReportDTO) {
        log.info("手动上报交易所：{}", businessReportDTO);
        List<PluginDetail> pluginDetails = plugDetailService.listByParam(true, PluginApiTypeEnums.EXCHANGE);
        try {

            // step 3：上报
            for (PluginDetail pluginDetail : pluginDetails) {
                String body = TraderUtils.traderReport(businessReportDTO, pluginDetail, PluginApiMarkTypeEnums.business_reporting, platformCode);
                log.info("数据资产登记接口返回：{}", body);
            }
            return true;
        } catch (Exception e) {
            log.error("e:", e);
        }
        return false;
    }

    @Override
    public Boolean manualDataAssetPublishTrader(AssetPublishDTO reportDTO) {
        log.info("手动上报交易所：{}", reportDTO);
        List<PluginDetail> pluginDetails = plugDetailService.listByParam(true, PluginApiTypeEnums.EXCHANGE);
        try {

            // step 3：上报
            for (PluginDetail pluginDetail : pluginDetails) {
                String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.publish_status, platformCode);
                log.info("上下架接口返回：{}", body);
            }
            return true;
        } catch (Exception e) {
            log.error("e:", e);
        }
        return false;
    }

    @Override
    public Boolean manualDataAssetRegisterTrader(AssetReportDTO reportDTO) {
        log.info("手动上报交易所：{}", reportDTO);
        List<PluginDetail> pluginDetails = plugDetailService.listByParam(true, PluginApiTypeEnums.EXCHANGE);
        try {

            // step 3：上报
            for (PluginDetail pluginDetail : pluginDetails) {
                String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.save_data_asset, platformCode);
                log.info("新增资产接口返回：{}", body);
            }
            return true;
        } catch (Exception e) {
            log.error("e:", e);
        }
        return false;
    }

    @Override
    public Boolean manualDataAssetUpdateTrader(AssetReportDTO reportDTO) {
        log.info("手动上报交易所：{}", reportDTO);
        List<PluginDetail> pluginDetails = plugDetailService.listByParam(true, PluginApiTypeEnums.EXCHANGE);
        try {

            // step 3：上报
            for (PluginDetail pluginDetail : pluginDetails) {
                String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.update_data_asset, platformCode);
                log.info("修改资产接口返回：{}", body);
            }
            return true;
        } catch (Exception e) {
            log.error("e:", e);
        }
        return false;
    }

    private AssetReportDTO transferReportDTO(DataAsset dataAsset, String userId) {
        AssetReportDTO reportDTO = new AssetReportDTO();
        // 这两目前写死
        AssetReportDTO.BillDetail billDetail = new AssetReportDTO.BillDetail();
        billDetail.setTimes(100);
        billDetail.setPrice(1000);
        reportDTO.setBillDetail(JSONUtil.toJsonStr(Collections.singletonList(billDetail)));
        reportDTO.setBillingType("按次");

        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        reportDTO.setCompanyName(company.getOrganizationName());
        reportDTO.setCompanySucc(company.getCreditCode());
        reportDTO.setDataCompanyType(TraderUtils.getDataCompanyType(null));
        reportDTO.setPlatformCompanyId(dataAsset.getRouterId());
        reportDTO.setPlatformProductId(String.valueOf(dataAsset.getAssetId()));
        reportDTO.setProductName(dataAsset.getAssetName());
        reportDTO.setSourcePlatformId(platformCode);
        reportDTO.setStructData(1);


        UserDetailsResponse userDetail = userService.userDetail(userId);
        BusinessReportDTO.CompanyLink companyLink = new BusinessReportDTO.CompanyLink();
        companyLink.setCompanyLinkMobile(userDetail.getPhone());
        companyLink.setCompanyLinkName(userDetail.getRealName());
        reportDTO.setCompanyLinkParams(Collections.singletonList(companyLink));
        return reportDTO;
    }


    /**
     * 功能描述: 数据资产上下架调用
     *
     * @param assetId dataAsset
     * @return void
     * <AUTHOR>
     * @date 2024/11/20 20:27
     */
    private void dataAssetPublishTrader(String assetId, List<PluginDetail> pluginDetailList, Boolean isUp) throws NoSuchAlgorithmException {
        log.info("上下架资产信息，上报交易所：id:{}, isUp:{}", assetId, isUp);

        AssetPublishDTO reportDTO = new AssetPublishDTO();
        reportDTO.setSourcePlatformId(platformCode);
        reportDTO.setPlatformProductId(assetId);
        reportDTO.setPublishStatus(isUp ? 2 : 1);

        // step 3：上报
        for (PluginDetail pluginDetail : pluginDetailList) {
            String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.publish_status, platformCode);
            log.info("数据资产上下架接口返回：{}", body);
        }
    }

    /**
     * 功能描述: 数据资产修改调用
     *
     * @param dataAsset dataAsset
     * @return void
     * <AUTHOR>
     * @date 2024/11/20 20:28
     */
    private void dataAssetUpdateTrader(DataAsset dataAsset, List<PluginDetail> pluginDetailList) throws NoSuchAlgorithmException {
        log.info("修改资产信息，上报交易所：{}", dataAsset.getAssetId());
        String userId = pluginDetailList.stream().findFirst().get().getCreateUser();

        // step 2：查询数据资产信息
        AssetReportDTO reportDTO = transferReportDTO(dataAsset, userId);

        // step 3：上报
        for (PluginDetail pluginDetail : pluginDetailList) {
            String body = TraderUtils.traderReport(reportDTO, pluginDetail, PluginApiMarkTypeEnums.update_data_asset, platformCode);
            log.info("数据资产修改接口返回：{}", body);
        }
    }

    @Override
    public void deleteFileSource(String userId, FileSourceMetadata fileSourceMetadata) {
        if (keepAssetFile) {
            return;
        }
        try {
            if (!ObjectUtils.isEmpty(fileSourceMetadata.getDataAssetFilePath())) {
                Path dataAssetFilePath = Paths.get(fileSourceMetadata.getDataAssetFilePath());
                File dataAssetFile = dataAssetFilePath.toFile();
                if (Files.deleteIfExists(dataAssetFilePath)) {
                    log.info("成功删除数据文件:{}", dataAssetFilePath);
                }
                Path teeFilePath = filesStorageService.getRootPath().resolve("dataAsset").resolve(userId)
                        .resolve(String.format("TEE-%s", dataAssetFile.getName()));
                if (Files.deleteIfExists(teeFilePath)) {
                    log.info("成功删除数据文件:{}", teeFilePath);
                }
                Path mpcFilePath = filesStorageService.getRootPath().resolve("dataAsset").resolve(userId)
                        .resolve(String.format("MPC-%s", dataAssetFile.getName()));
                if (Files.deleteIfExists(mpcFilePath)) {
                    log.info("成功删除数据文件:{}", mpcFilePath);
                }
            }
        } catch (IOException e) {
            log.warn("删除数据文件异常，error:", e);
        }
    }
}
