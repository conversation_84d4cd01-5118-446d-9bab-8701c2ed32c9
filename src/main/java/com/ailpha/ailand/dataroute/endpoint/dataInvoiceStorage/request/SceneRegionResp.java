package com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneRegionResp {

    @Schema(description = "地理行政区：华北、华东、华南、西北、东北、西南、华中")
    private String regionArea;

    @Schema(description = "一级行政区：省、自治区、直辖市、特别行政区")
    private String region1;

    @Schema(description = "二级行政区：地级市、地区、自治州、盟")
    private String region2;

    @Schema(description = "三级行政区：市辖区、县级市、县、自治县、旗、特区、林区")
    private String region3;
}
