package com.ailpha.ailand.dataroute.endpoint.node.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.LicenseRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterAgentRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ConnectorChangeRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.NetworkLinkRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ReginNodeResolutionRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorChangeResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ImportLicenseResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ReginNodeResolution;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.ImportLicenseRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.LicenseDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeResponse;
import com.ailpha.ailand.dataroute.endpoint.node.domain.Node;
import com.ailpha.ailand.dataroute.endpoint.node.domain.NodeStatus;
import com.ailpha.ailand.dataroute.endpoint.node.domain.QNode;
import com.ailpha.ailand.dataroute.endpoint.node.dto.*;
import com.ailpha.ailand.dataroute.endpoint.node.repository.NodeRepository;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn("FlywayConfig")
public class NodeService {
    private final NodeRepository nodeRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final LicenseRemoteService licenseRemoteService;
    private final AiLandProperties aiLandProperties;
    private final RouterAgentRemoteService routerAgentRemoteService;
    private final RouterService routerService;
    private final Cache<Long, NodeDTO> HUB_NODE_CACHE = new LRUCache<>(100);
    private final RouterManagerRemoteService routerManagerRemoteService;
    private final Cache<String, String> ganZhouRouteResolveCache = new LRUCache<>(200);

    @PostConstruct
    private void init() {
        nodeRepository.findAll().forEach(n -> HUB_NODE_CACHE.put(n.getId(), convertToDTO(n)));
    }

    public NodeDTO createNode(NodeRequest request) {
        if (StringUtils.isEmpty(request.getNodeName()))
            request.setNodeName(UuidUtils.uuid32());
        if (StringUtils.isEmpty(request.getNodeType()))
            request.setNodeType("区域功能节点");
        JSONObject entries = new JSONObject();
        switch (request.getLinkType()) {
            case other -> {
                entries.set("linkType", request.getLinkType());
                JSONObject server = new JSONObject();
                server.set("url", request.getOther().getHubUrl());
                server.set("ak", request.getOther().getAk());
                server.set("sk", request.getOther().getSk());
                entries.set("server", server);
                entries.set("nodeId", request.getOther().getNodeId());
                // 更新连接器地址信息
                NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
                hubInfo.setAk(request.getOther().getAk());
                hubInfo.setSk(request.getOther().getSk());
                hubInfo.setUrl(request.getOther().getHubUrl());
                hubInfo.setAuthType("sign");
                MDC.put("hubInfo", JSONUtil.toJsonStr(hubInfo));
                ReginNodeResolutionRequest resolutionRequest = new ReginNodeResolutionRequest();
                resolutionRequest.setConnectorId(request.getOther().getNodeId());
                NodeDTO.HubInfo hubInfo1 = new NodeDTO.HubInfo();
                hubInfo1.setAk(request.getOther().getAk());
                hubInfo1.setSk(request.getOther().getSk());
                hubInfo1.setUrl(request.getOther().getHubUrl());
                MDC.put("hubInfo", JSONUtil.toJsonStr(hubInfo1));
                com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<ReginNodeResolution> reginedNodeResolution
                        = routerManagerRemoteService.reginNodeResolution(resolutionRequest);
                Assert.isTrue(reginedNodeResolution.isSuccess(), "查询连接器对接信息异常：" + reginedNodeResolution.getMessage());
                ConnectorChangeRequest connectorChangeReport = new ConnectorChangeRequest();
                connectorChangeReport.setConnectorId(request.getOther().getAk());
                connectorChangeReport.setChangeNotes("安恒-修改连接器信息测试");
                connectorChangeReport.setConnectorAddressA(request.getIp());
                connectorChangeReport.setConnectorDeploymentAddress(request.getIp());
                connectorChangeReport.setConnectorOwnerId(reginedNodeResolution.getData().getConnectorOwnerId());
                com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<ConnectorChangeResponse> connectorChangeResponseCommonResult
                        = routerManagerRemoteService.connectorChangeReport(connectorChangeReport);
                Assert.isTrue(connectorChangeResponseCommonResult.isSuccess(), "连接器信息上报异常：" + connectorChangeResponseCommonResult.getMessage());
            }
            case import_file -> {
                String licensePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + request.getImportFile().getFileId();
                LicenseDTO license = JSONUtil.toBean(FileUtil.readUtf8String(licensePath), LicenseDTO.class);
                CommonResult<NodeResponse> nodeInfo = licenseRemoteService.initRoute("7");
                Assert.isTrue(nodeInfo.isSuccess(), String.format("获取当前连接器信息异常：%s", nodeInfo.getMessage()));
                if (StringUtils.isNotEmpty(license.getEncryptedBusinessInfo())) {
                    ImportLicenseRequest importLicenseReq = new ImportLicenseRequest();
                    importLicenseReq.setCaCertificate(license.getCaCertificate());
                    importLicenseReq.setRouteCertificate(license.getRouteCertificate());
                    importLicenseReq.setEncryptedBusinessInfo(license.getEncryptedBusinessInfo());
                    CommonResult<ImportLicenseResponse> importLicense = licenseRemoteService.importLicense(importLicenseReq);
                    Assert.isTrue(importLicense.isSuccess(), "校验证书失败:" + importLicense.getMessage());
                }
                entries.set("linkType", request.getLinkType());
                entries.set("url", license.getCentreLocation());
                entries.set("server", license.getServer());
                entries.set("routeCertSerialNumber", license.getRouteCertSerialNumber());
                entries.set("routeCertificate", license.getCaCertificate());
                entries.set("caCertificate", license.getCaCertificate());
                entries.set("exposeUrl", initExposeUrl(nodeInfo.getData().getRouterId(), license.getIamIp(), license.getRouteCertSerialNumber()) + ":" + SpringUtil.getProperty("server.port"));
                entries.set("nodeId", nodeInfo.getData().getRouterId());
            }
            case manual_input -> {
                entries.set("linkType", request.getLinkType());
                entries.set("url", request.getManualInput().getUrl());
                JSONObject server = new JSONObject();
                server.set("publicKey", request.getManualInput().getPublicKey());
                entries.set("server", server);
                entries.set("nodeId", request.getManualInput().getNodeId());
                entries.set("exposeUrl", request.getManualInput().getIp());
            }
        }

        Node node = new Node();
        node.setNodeId(UUID.randomUUID().toString());
        node.setNodeName(request.getNodeName());
        node.setNodeType(request.getNodeType());
        node.setStatus(NodeStatus.ACTIVE);
        node.setAccessTime(new Date());
        node.setExt(entries.toString());

        nodeRepository.saveAndFlush(node);
        NodeDTO nodeDTO = convertToDTO(node);
        HUB_NODE_CACHE.put(node.getId(), nodeDTO);
        SpringUtil.getBean(CompanyService.class).initCompany(node.getId(), request.getOther().getNodeId());
        return nodeDTO;
    }


    public String connectorDeploymentAddress(String routerId) {
        // todo 如果没登录 上下文需要塞 hubInfo
        // MDC.put("hubInfo", JSONUtil.toJsonStr(nodeInfo.getHubInfo()));

        String connectorDeploymentAddress = ganZhouRouteResolveCache.get(routerId);
        if (StringUtils.isNotBlank(connectorDeploymentAddress) && !connectorDeploymentAddress.contains("null")) {
            return connectorDeploymentAddress;
        }
        ReginNodeResolutionRequest resolutionRequest = new ReginNodeResolutionRequest();
        resolutionRequest.setConnectorId(routerId);
        // 他们限制并发 —— 做一下缓存
        com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<ReginNodeResolution> reginedNodeResolution = routerManagerRemoteService.reginNodeResolution(resolutionRequest);
        if (!reginedNodeResolution.isSuccess()) {
            ThreadUtil.safeSleep(5000);
            reginedNodeResolution = routerManagerRemoteService.reginNodeResolution(resolutionRequest);
        }
        connectorDeploymentAddress = reginedNodeResolution.getData().getConnectorDeploymentAddress();
        log.info("文件下载转发地址:{}", connectorDeploymentAddress);

        ganZhouRouteResolveCache.put(routerId, connectorDeploymentAddress);
        return connectorDeploymentAddress;
    }


    public String initExposeUrl(String nodeId, String centralIP, String certId) {
        String currentRouteVirtualIp = routerService.currentRouteVirtualIp();
        if (StringUtils.isNotEmpty(currentRouteVirtualIp)
                && (StringUtils.contains(currentRouteVirtualIp, "utun99"))) {
            NetworkLinkRequest networkLinkRequest = new NetworkLinkRequest();
            // todo 这里不合理
            networkLinkRequest.setCompanyId(String.valueOf(System.currentTimeMillis()));
            networkLinkRequest.setRouterId(nodeId);
            networkLinkRequest.setCentralIP(centralIP);
            networkLinkRequest.setCertId(certId);
            CommonResult<Void> startNetLink = routerAgentRemoteService.startNetLink(networkLinkRequest);
            Assert.isTrue(startNetLink.isSuccess(), "组网失败：" + startNetLink.getMsg());
        }

        return routerService.currentRouteVirtualIp();
    }

    public NodeDTO getFirstNode() {
        return HUB_NODE_CACHE.get(1L);
    }

    @Transactional
    public NodeDTO updateNode(Long id, NodeRequest request) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        node.setNodeName(request.getNodeName());
        node.setNodeType(request.getNodeType());

        nodeRepository.saveAndFlush(node);
        return convertToDTO(node);
    }

    @Transactional
    public void deleteNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        nodeRepository.delete(node);
    }

    @Transactional
    public NodeDTO activateNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        node.setStatus(NodeStatus.ACTIVE);
        node.setAccessTime(new Date());

        nodeRepository.saveAndFlush(node);
        return convertToDTO(node);
    }

    @Transactional
    public NodeDTO deactivateNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        node.setStatus(NodeStatus.INACTIVE);

        nodeRepository.saveAndFlush(node);
        return convertToDTO(node);
    }

    public NodeDTO getNode(Long id) {
        NodeDTO nodeDTO = HUB_NODE_CACHE.get(id);
        if (ObjectUtil.isNull(nodeDTO)) {
            Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));
            return convertToDTO(node);
        } else
            return nodeDTO;
    }

    public SuccessResponse<List<NodeDTO>> listNodes(NodePageRequest request) {
        QNode qNode = QNode.node;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (ObjectUtil.isNotNull(request.getId()))
            booleanBuilder.and(qNode.id.eq(request.getId()));

        // 根据节点名称查询
        if (StringUtils.isNotEmpty(request.getNodeName())) {
            booleanBuilder.and(qNode.nodeName.contains(request.getNodeName()));
        }

        // 根据节点类型查询
        if (StringUtils.isNotEmpty(request.getNodeType())) {
            booleanBuilder.and(qNode.nodeType.eq(request.getNodeType()));
        }

        // 根据节点状态查询
        if (request.getStatus() != null) {
            booleanBuilder.and(qNode.status.eq(request.getStatus()));
        }
        if (ObjectUtil.isNotNull(request.getStartTime()) && ObjectUtil.isNotNull(request.getEndTime()))
            booleanBuilder.and(qNode.accessTime.between(request.getStartTime(), request.getEndTime()));
        // 计算总记录数
        long total = jpaQueryFactory
                .from(qNode)
                .select(qNode.countDistinct())
                .where(booleanBuilder)
                .fetch().getFirst();
        // 构建查询
        List<NodeDTO> nodeDTOList = jpaQueryFactory
                .selectFrom(qNode)
                .where(booleanBuilder)
                .orderBy(qNode.accessTime.desc())
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .fetch()
                .stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return SuccessResponse.success(nodeDTOList).total(total).build();
    }

    private NodeDTO convertToDTO(Node node) {
        NodeDTO dto = new NodeDTO();
        dto.setId(node.getId());
        dto.setNodeId(node.getNodeId());
        dto.setNodeName(node.getNodeName());
        dto.setNodeType(node.getNodeType());
        dto.setStatus(node.getStatus());
        dto.setAccessTime(node.getAccessTime());
        JSONObject ext = JSONUtil.parseObj(node.getExt());
        NodeDTO.ConnectorInfo connectorInfo = new NodeDTO.ConnectorInfo();
        connectorInfo.setExposeUrl(ext.getStr("exposeUrl"));
        dto.setConnectorInfo(connectorInfo);
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(ext.getStr("url"));
        switch (ext.getStr("linkType")) {
            case "import_file" -> {
                hubInfo.setAuthType("sign");
                hubInfo.setAk(ext.getByPath("server.appKey", String.class));
                hubInfo.setSk(ext.getByPath("server.appSecret", String.class));
                hubInfo.setCertificateNo(ext.getStr("routeCertSerialNumber"));
            }
            case "other" -> {
                hubInfo.setUrl(ext.getByPath("server.url", String.class));
                hubInfo.setAuthType("sign");
                hubInfo.setAk(ext.getByPath("server.ak", String.class));
                hubInfo.setSk(ext.getByPath("server.sk", String.class));
            }
            case "manual_input" -> {
                hubInfo.setAuthType("publicKey");
                hubInfo.setPublicKey(ext.getStr("publicKey"));
            }
        }
        dto.setHubInfo(hubInfo);
        return dto;
    }

    public SuccessResponse<List<NodeSimpleDTO>> pageNodesSimple(NodeSimplePageRequest request) {
        QNode qNode = QNode.node;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        // 根据节点名称查询
        if (StringUtils.isNotBlank(request.getNodeName())) {
            booleanBuilder.and(qNode.nodeName.contains(request.getNodeName()));
        }

        // 根据节点类型查询
        if (StringUtils.isNotBlank(request.getNodeType())) {
            booleanBuilder.and(qNode.nodeType.eq(request.getNodeType()));
        }

        // 计算总记录数
        long total = jpaQueryFactory.from(qNode).select(qNode.countDistinct())
                .where(booleanBuilder)
                .fetch().getFirst();

        // 查询数据
        List<Node> nodes = jpaQueryFactory
                .selectFrom(qNode)
                .where(booleanBuilder)
                .orderBy(qNode.accessTime.desc())
                .offset(request.getOffset())
                .limit(request.getSize())
                .fetch();

        // 转换为DTO
        List<NodeSimpleDTO> content = nodes.stream()
                .map(node -> {
                    NodeSimpleDTO dto = new NodeSimpleDTO();
                    dto.setId(node.getId());
                    dto.setNodeName(node.getNodeName());
                    dto.setNodeType(node.getNodeType());
                    return dto;
                })
                .collect(Collectors.toList());
        return SuccessResponse.success(content).total(total).build();
    }
}