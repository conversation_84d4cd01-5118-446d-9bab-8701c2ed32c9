package com.ailpha.ailand.dataroute.endpoint.node.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.setting.yaml.YamlUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.LicenseRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterAgentRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.NetworkLinkRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ImportLicenseResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.ImportLicenseRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.LicenseDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.MeshConfig;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeResponse;
import com.ailpha.ailand.dataroute.endpoint.node.LinkType;
import com.ailpha.ailand.dataroute.endpoint.node.domain.Node;
import com.ailpha.ailand.dataroute.endpoint.node.domain.NodeStatus;
import com.ailpha.ailand.dataroute.endpoint.node.domain.QNode;
import com.ailpha.ailand.dataroute.endpoint.node.dto.*;
import com.ailpha.ailand.dataroute.endpoint.node.repository.NodeRepository;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.yaml.snakeyaml.DumperOptions;

import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn("FlywayConfig")
public class NodeService {
    private final NodeRepository nodeRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final LicenseRemoteService licenseRemoteService;
    private final AiLandProperties aiLandProperties;
    private final RouterAgentRemoteService routerAgentRemoteService;
    private final RouterService routerService;
    private final Cache<Long, NodeDTO> HUB_NODE_CACHE = new LRUCache<>(100);

    @PostConstruct
    private void init() {
        nodeRepository.findAll().forEach(n -> HUB_NODE_CACHE.put(n.getId(), convertToDTO(n)));
    }

    public NodeDTO createNode(NodeRequest request) {
        if (StringUtils.isEmpty(request.getNodeName()))
            request.setNodeName(UuidUtils.uuid32());
        if (StringUtils.isEmpty(request.getNodeType()))
            request.setNodeType("区域功能节点");
        JSONObject entries = new JSONObject();
        if (ObjectUtil.equals(request.getLinkType(), LinkType.import_file)) {
            String licensePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + request.getImportFile().getFileId();
            LicenseDTO license = JSONUtil.toBean(FileUtil.readUtf8String(licensePath), LicenseDTO.class);
            CommonResult<NodeResponse> nodeInfo = licenseRemoteService.initRoute("");
            Assert.isTrue(nodeInfo.isSuccess(), String.format("获取当前连接器信息异常：%s", nodeInfo.getMessage()));
            Assert.isTrue(StringUtils.isNotEmpty(license.getRouteCertificate()) && StringUtils.isNotEmpty(license.getRouteCertSerialNumber()), "证书文件内容非法，请重新上传");
            if (StringUtils.isNotEmpty(license.getEncryptedBusinessInfo())) {
                ImportLicenseRequest importLicenseReq = new ImportLicenseRequest();
                importLicenseReq.setCaCertificate(license.getCaCertificate());
                importLicenseReq.setRouteCertificate(license.getRouteCertificate());
                importLicenseReq.setEncryptedBusinessInfo(license.getEncryptedBusinessInfo());
                CommonResult<ImportLicenseResponse> importLicense = licenseRemoteService.importLicense(importLicenseReq);
                Assert.isTrue(importLicense.isSuccess(), "校验证书失败:" + importLicense.getMessage());
            }
            entries.set("filename", FileUtil.getName(licensePath));
            entries.set("linkType", request.getLinkType());
            entries.set("url", license.getCentreLocation());
            entries.set("server", license.getServer());
            entries.set("routeCertSerialNumber", license.getRouteCertSerialNumber());
            entries.set("routeCertificate", license.getRouteCertificate());
            entries.set("caCertificate", license.getCaCertificate());
            entries.set("exposeUrl", initExposeUrl(nodeInfo.getData().getRouterId(), license.getIamIp(), license.getRouteCertSerialNumber(), "", "") + ":" + SpringUtil.getProperty("server.port"));
            entries.set("nodeId", nodeInfo.getData().getRouterId());
        } else {
            entries.set("linkType", request.getLinkType());
            entries.set("url", request.getManualInput().getUrl());
            JSONObject server = new JSONObject();
            server.set("publicKey", request.getManualInput().getPublicKey());
            entries.set("server", server);
            entries.set("nodeId", request.getManualInput().getNodeId());
            entries.set("exposeUrl", request.getManualInput().getIp());
        }
        Node node = new Node();
        node.setNodeId(UUID.randomUUID().toString());
        node.setNodeName(request.getNodeName());
        node.setNodeType(request.getNodeType());
        node.setStatus(NodeStatus.ACTIVE);
        node.setAccessTime(new Date());
        node.setExt(entries.toString());

        nodeRepository.saveAndFlush(node);
        NodeDTO nodeDTO = convertToDTO(node);
        HUB_NODE_CACHE.put(node.getId(), nodeDTO);
        return nodeDTO;
    }

    public static void main(String[] args) {
        String configPath = "D:\\disk-data\\/test.yml";
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> map2 = new HashMap<>();
        Map<String, Object> map3 = new HashMap<>();
        map2.put("type", "value");
        map2.put("vip", "value");
        map3.put("registry_info", map2);
        map.put("mesh", map3);
        YamlUtil.dump(BeanUtil.beanToMap(map), FileUtil.getWriter(configPath, Charset.defaultCharset(), false));
    }

    public String initExposeUrl(String nodeId, String centralIP, String certId, String networkAccessType, String ip) {
        // 错误地址：Device "utun99" does not exist.
        switch (networkAccessType) {
            case "互联网", "专线" -> {
                String configPath = "/usr/local/application/aitrust-mesh/config-mesh.yml";
                boolean exist = FileUtil.exist(configPath);
                if (!exist) {
                    Map<String, Object> meshConfigMap = new HashMap<>();
                    Map<String, Object> meshMap = new HashMap<>();
                    Map<String, Object> registryMap = new HashMap<>();
                    registryMap.put("vip", ip);
                    registryMap.put("type", networkAccessType);
                    meshMap.put("registry_info", registryMap);
                    meshConfigMap.put("mesh", meshMap);
                    YamlUtil.dump(meshConfigMap, FileUtil.getWriter(configPath, Charset.defaultCharset(), false));
                }
                return ip;
            }
            case "组网" -> {
                if (!alreadyNetworkLink()) {
                    NetworkLinkRequest networkLinkRequest = new NetworkLinkRequest();
                    // todo 这里不合理
                    networkLinkRequest.setCompanyId(String.valueOf(System.currentTimeMillis()));
                    networkLinkRequest.setRouterId(nodeId);
                    networkLinkRequest.setCentralIP(centralIP);
                    networkLinkRequest.setCertId(certId);
                    CommonResult<Void> startNetLink = routerAgentRemoteService.startNetLink(networkLinkRequest);
                    Assert.isTrue(startNetLink.isSuccess(), "组网失败：" + startNetLink.getMsg());
                    // 组网成功之后 可能还没有生成虚拟网卡
                    int loopCount = 0;
                    while (!alreadyNetworkLink()) {
                        if (loopCount > 3) {
                            log.error("尝试获取虚ip超过最大执行次数");
                            throw new RestfulApiException("获取mesh服务组网虚拟IP异常，请联系管理员");
                        }
                        ThreadUtil.safeSleep(1000);
                        loopCount++;
                    }
                }
                String vip = routerService.currentRouteVirtualIp();
                Assert.isTrue(vip.startsWith("10.128"), "连接器虚拟IP生成失败，请联系管理员");
                return vip;
            }
            default -> throw new RestfulApiException("请选择网络接入方式");
        }
    }

    public Boolean alreadyNetworkLink() {
        // 服务是否正常
        String vip = RuntimeUtil.execForStr("bash", "-c", "ip addr show utun99 | grep -w inet | grep -v '127.0.0.1' | awk '{print $2}' | cut  -d '/' -f 1");
        // 配置文件是否存在
        String popConfigPath = "/usr/local/application/aitrust-mesh/config-mesh.yml";
        boolean popConfigExist = FileUtil.exist(popConfigPath);
        return StringUtils.isNotEmpty(vip) && StringUtils.startsWith(vip, "10.128") && popConfigExist;
    }

    @Transactional
    public NodeDTO updateNode(Long id, UpdateNodeRequest request) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        node.setNodeName(request.getNodeName());
        node.setNodeType(request.getNodeType());

        nodeRepository.saveAndFlush(node);
        return convertToDTO(node);
    }

    @Transactional
    public void deleteNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        nodeRepository.delete(node);
    }

    @Transactional
    public NodeDTO activateNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        node.setStatus(NodeStatus.ACTIVE);
        node.setAccessTime(new Date());

        nodeRepository.saveAndFlush(node);
        return convertToDTO(node);
    }

    /**
     * 停用功能节点。
     * 在停用前会校验，确保系统中至少保留一个启用状态的功能节点。
     *
     * @param id 要停用的节点的ID
     * @return 更新后的节点数据传输对象 (NodeDTO)
     * @throws RestfulApiException 如果节点不存在，或者停用操作会导致没有活动的节点
     */
    @Transactional
    public NodeDTO deactivateNode(Long id) {
        Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));

        // 校验：如果当前节点是激活状态，并且是最后一个激活的节点，则不允许停用
        if (node.getStatus() == NodeStatus.ACTIVE) {
            long activeNodesCount = nodeRepository.countByStatus(NodeStatus.ACTIVE);
            if (activeNodesCount <= 1) {
                throw new RestfulApiException("停用失败：至少需要保留一个启用状态的功能节点");
            }
        }

        node.setStatus(NodeStatus.INACTIVE);

        nodeRepository.saveAndFlush(node);
        // 更新缓存
        NodeDTO deactivatedNodeDTO = convertToDTO(node);
        HUB_NODE_CACHE.put(node.getId(), deactivatedNodeDTO);
        return deactivatedNodeDTO;
    }

    public NodeDTO getNode(Long id) {
        NodeDTO nodeDTO = HUB_NODE_CACHE.get(id);
        if (ObjectUtil.isNull(nodeDTO)) {
            Node node = nodeRepository.findById(id).orElseThrow(() -> new RestfulApiException("节点不存在"));
            return convertToDTO(node);
        } else
            return nodeDTO;
    }

    public NodeDTO getRandomNode() {
        NodeDTO randomNode = null;
        for (NodeDTO nodeDTO : HUB_NODE_CACHE) {
            randomNode = nodeDTO;
            if (randomNode.getStatus().equals(NodeStatus.ACTIVE))
                return randomNode;
        }
        return randomNode;
    }

    public SuccessResponse<List<NodeDTO>> listNodes(NodePageRequest request) {
        QNode qNode = QNode.node;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (ObjectUtil.isNotNull(request.getId()))
            booleanBuilder.and(qNode.id.eq(request.getId()));

        // 根据节点名称查询
        if (StringUtils.isNotEmpty(request.getNodeName())) {
            booleanBuilder.and(qNode.nodeName.contains(request.getNodeName()));
        }

        // 根据节点类型查询
        if (StringUtils.isNotEmpty(request.getNodeType())) {
            booleanBuilder.and(qNode.nodeType.eq(request.getNodeType()));
        }

        // 根据节点状态查询
        if (request.getStatus() != null) {
            booleanBuilder.and(qNode.status.eq(request.getStatus()));
        }
        if (ObjectUtil.isNotNull(request.getStartTime()) && ObjectUtil.isNotNull(request.getEndTime()))
            booleanBuilder.and(qNode.accessTime.between(request.getStartTime(), request.getEndTime()));
        // 计算总记录数
        long total = jpaQueryFactory
                .from(qNode)
                .select(qNode.countDistinct())
                .where(booleanBuilder)
                .fetch().getFirst();
        // 构建查询
        List<NodeDTO> nodeDTOList = jpaQueryFactory
                .selectFrom(qNode)
                .where(booleanBuilder)
                .orderBy(qNode.accessTime.desc())
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .fetch()
                .stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        return SuccessResponse.success(nodeDTOList).total(total).build();
    }

    private NodeDTO convertToDTO(Node node) {
        NodeDTO dto = new NodeDTO();
        dto.setId(node.getId());
        dto.setNodeId(node.getNodeId());
        dto.setNodeName(node.getNodeName());
        dto.setNodeType(node.getNodeType());
        dto.setStatus(node.getStatus());
        dto.setAccessTime(node.getAccessTime());
        JSONObject ext = JSONUtil.parseObj(node.getExt());
        NodeDTO.ConnectorInfo connectorInfo = new NodeDTO.ConnectorInfo();
        connectorInfo.setExposeUrl(ext.getStr("exposeUrl"));
        dto.setConnectorInfo(connectorInfo);
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(ext.getStr("url"));
        if (StringUtils.equals(ext.getStr("linkType"), "import_file")) {
            hubInfo.setAuthType("sign");
            hubInfo.setAk(ext.getByPath("server.appKey", String.class));
            hubInfo.setSk(ext.getByPath("server.appSecret", String.class));
            hubInfo.setCertificateNo(ext.getStr("routeCertSerialNumber"));
            hubInfo.setCertificate(ext.getStr("routeCertificate"));
            hubInfo.setFilename(ext.getStr("filename"));
        } else {
            hubInfo.setAuthType("publicKey");
            hubInfo.setPublicKey(ext.getStr("publicKey"));
        }
        dto.setHubInfo(hubInfo);
        return dto;
    }

    public SuccessResponse<List<NodeSimpleDTO>> pageNodesSimple(NodeSimplePageRequest request) {
        QNode qNode = QNode.node;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        // 根据节点名称查询
        if (StringUtils.isNotBlank(request.getNodeName())) {
            booleanBuilder.and(qNode.nodeName.contains(request.getNodeName()));
        }

        // 根据节点类型查询
        if (StringUtils.isNotBlank(request.getNodeType())) {
            booleanBuilder.and(qNode.nodeType.eq(request.getNodeType()));
        }

        // 计算总记录数
        long total = jpaQueryFactory.from(qNode).select(qNode.countDistinct())
                .where(booleanBuilder)
                .fetch().getFirst();

        // 查询数据
        List<Node> nodes = jpaQueryFactory
                .selectFrom(qNode)
                .where(booleanBuilder)
                .orderBy(qNode.accessTime.desc())
                .offset(request.getOffset())
                .limit(request.getSize())
                .fetch();

        // 转换为DTO
        List<NodeSimpleDTO> content = nodes.stream()
                .map(node -> {
                    NodeSimpleDTO dto = new NodeSimpleDTO();
                    dto.setId(node.getId());
                    dto.setNodeName(node.getNodeName());
                    dto.setNodeType(node.getNodeType());
                    return dto;
                })
                .collect(Collectors.toList());
        return SuccessResponse.success(content).total(total).build();
    }
}