package com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam;

import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import lombok.Data;

@Data
public class UpdateUserRequest {
    String uuid;
    String userName;
    String accountNumber;
    String bimRequestId;
    String password;
    Integer status = 1;
    public UpdateUserRequest() {
        this.bimRequestId = UuidUtils.uuid32();
    }
}
