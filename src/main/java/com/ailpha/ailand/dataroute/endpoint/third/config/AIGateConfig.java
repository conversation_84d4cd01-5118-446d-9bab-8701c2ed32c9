package com.ailpha.ailand.dataroute.endpoint.third.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "data-route.aigate")
public class AIGateConfig {
    private Boolean enabled = false;
    @Deprecated
    private String baseUrl = "https://127.0.0.1:6443";

    private String accessKeyId;
    private String accessKeySecret;
}
