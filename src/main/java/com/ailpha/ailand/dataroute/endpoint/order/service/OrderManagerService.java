package com.ailpha.ailand.dataroute.endpoint.order.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.SSEMessageService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneListResp;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.domain.*;
import com.ailpha.ailand.dataroute.endpoint.order.mapstruct.OrderMapstruct;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderListVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderVO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderBuyerListReq;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderCreateReq;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderSellerListReq;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.CatalogQueryDataProduct;
import com.ailpha.ailand.dataroute.endpoint.third.input.TerminalContractRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.*;
import com.ailpha.ailand.dataroute.endpoint.third.request.AssetBeneficiaryRelDTOListReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.AssetBeneficiaryRelDTOReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.OrderRecordsReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.OrderSceneRefResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.OrderRecordsResp;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.minio.admin.MinioAdminClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderManagerService {

    private final OrderMapstruct orderMapstruct;
    private final DigitalCertificateRemote digitalCertificateRemote;
    private final HubOrderRemote hubOrderRemote;
    private final DataHubRemoteService dataHubRemoteService;
    private final SSEMessageService sseMessageService;
    private final HubDeliverySceneRemote hubDeliverySceneRemote;
    private final GatewayWebApi gatewayWebApi;
    private final DataProductRepository dataProductRepository;
    private final RouterService routerService;

    private final MinioAdminClient minioAdminClient;
    private final HubShuHanApiClient shuHanApiClient;
    private final DataProductService dataProductService;
    private final JPAQueryFactory queryFactory;
    private final OrderRecordRepository orderRecordRepository;
    private final AssetBeneficiaryRepository assetBeneficiaryRepository;

    /**
     * 创建订单合同
     */
    public void create(OrderCreateReq orderCreateReq) {
        paramCheck(orderCreateReq);

        // 获取当前用户信息
        UserDTO currentUser = LoginContextHolder.currentUser();
        // 当前企业信息
        NodeInfoResponse currentNode = routerService.currentNode();

        List<String> assetIds = orderCreateReq.getAssetIds();

        // 检查：调用中心接口，获取订单列表，条件：资产ID、当前用户ID，存在状态：APPLY（待审批）APPROVED（通过），不可申请
        AssetBeneficiaryRelDTOListReq assetBeneficiaryRelDTOListReq = AssetBeneficiaryRelDTOListReq.builder().assetIds(assetIds).beneficiaryId(currentUser.getId()).build();
        SuccessResponse<List<OrderApprovalRecord>> orderRecordsResponse = hubOrderRemote.selectOrderApprovalRecordWhereAssetIdInAndBeneficiaryId(assetBeneficiaryRelDTOListReq);
        List<OrderApprovalRecord> assetBeneficiaryRelList = orderRecordsResponse.getData();
        if (!ObjectUtils.isEmpty(assetBeneficiaryRelList)) {
            Map<String, OrderApprovalRecord> assetOrderMap = new HashMap<>(assetBeneficiaryRelList.size());
            for (OrderApprovalRecord orderRecord : assetBeneficiaryRelList) {
                if (ObjectUtils.isEmpty(assetOrderMap.get(orderRecord.getAssetId()))) {
                    assetOrderMap.put(orderRecord.getAssetId(), orderRecord);
                }
            }
            for (Map.Entry<String, OrderApprovalRecord> entry : assetOrderMap.entrySet()) {
                boolean allow = !OrderStatus.APPLY.toString().equals(entry.getValue().getStatus()) && !OrderStatus.APPROVED.toString().equals(entry.getValue().getStatus());
                if (!allow) {
                    throw new IllegalArgumentException(String.format("资产：%s，存在有效订单，不可重复申请", entry.getValue().getAssetName()));
                }
            }
        }

        Map<String, DataProductVO> dataAssetMap = new HashMap<>(assetIds.size());
        for (String assetId : assetIds) {
            // 查资产详情
//            DataProductVO dataProduct = dataProductService.getDataProduct(assetId);
            // 校验资产状态 —— 上架状态放到业务节点了，可以上架到多个业务节点 （这里不做校验）todo: 会有问题
//            Assert.isTrue(PushStatus.ONLINE.equals(dataAsset.getPushStatus()), String.format("资产：%s，非上架状态，无法申请", dataAsset.getAssetName()));
            DataProductVO dataProduct = dataProductService.getDataProductByOldAssetId(assetId);
            dataAssetMap.put(assetId, dataProduct);
        }
        // 生成订单合同审批记录
        long timestamp = System.currentTimeMillis();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        List<OrderApprovalRecord> orderApprovalRecords = new ArrayList<>(2);
        for (String assetId : assetIds) {
            String id = String.format("%s%s%s%s", "DDHT", format.format(timestamp), timestamp, UUID.randomUUID().toString().replace("-", "").substring(0, 7).toUpperCase());
            OrderApprovalRecord orderApprovalRecord = orderMapstruct.initOrderApprovalRecord(orderCreateReq, id, assetId, currentUser, currentNode, dataAssetMap);
            orderApprovalRecords.add(orderApprovalRecord);
        }
        // 调用中心接口：存储订单审批记录
        SuccessResponse<Integer> rowsResponse = hubOrderRemote.insertIntoOrderApprovalRecords(orderApprovalRecords);
        sseMessageService.notifyOrderApplyMessage(orderApprovalRecords, orderCreateReq);
    }

    private void paramCheck(OrderCreateReq orderCreateReq) {
        final String meteringWay = orderCreateReq.getMeteringWay();
        if (StringUtils.equals(meteringWay, "按次")) {
            final BigInteger allowance = orderCreateReq.getAllowance();
            Assert.isTrue(allowance != null && allowance.longValue() > 0, "计量计费方案为按次时【使用次数】不能为空");
        }

        if (StringUtils.equals(meteringWay, "按周期")) {
            final Date expireDate = orderCreateReq.getExpireDate();
            Assert.isTrue(expireDate != null, "计量计费方案为按周期时【订单有效期】输入非法");
            Assert.isTrue(DateUtil.compare(expireDate, new Date()) > 0, "订单有效期不得早于当前时间");
        }

    }


    /**
     * 买方-订单合同列表
     */
    public SuccessResponse<List<OrderListVO>> buyerList(OrderBuyerListReq orderBuyerListReq) {
        String currentUserId = LoginContextHolder.currentUser().getId();
        OrderRecordsReq orderRecordsReq = OrderRecordsReq.builder().beneficiaryId(currentUserId).assetName(orderBuyerListReq.getAssetName())
                .deliveryMode(orderBuyerListReq.getDeliveryMode()).status(orderBuyerListReq.getStatus())
                .page(orderBuyerListReq.getNum()).offset((orderBuyerListReq.getNum() - 1L) * orderBuyerListReq.getSize()).size(orderBuyerListReq.getSize()).build();
        return orderVOList(orderRecordsReq);
    }

    /**
     * 订单详情
     */
    public OrderVO detail(String orderId) {
        // 查询订单合同，条件：订单ID
        OrderApprovalRecord orderApprovalRecord = orderRecordRepository.findById(orderId).orElseThrow(() -> new RestfulApiException("未找到订单【" + orderId + "】信息"));

        // 调用中心接口：获取资产详情，条件：资产ID
        String extend = orderApprovalRecord.getExtend();
        OderRecordExtend oderRecordExtend = JSONUtil.toBean(extend, OderRecordExtend.class);

        // 查询订单 apiKey
        AssetBeneficiaryRel beneficiaryRel = assetBeneficiaryRepository.findFirstByOrderId(orderId);
        String beneficiaryExtend = beneficiaryRel.getExtend();

        // 查资产详情
        DataProductVO dataProduct = dataProductService.getDataProductByDataProductPlatformId(oderRecordExtend.getDataProductPlatformId());

        return orderMapstruct.toOrderVO(orderApprovalRecord, beneficiaryExtend, dataProduct);
    }

    /**
     * 数据发票存证记录
     */
    public SuccessResponse<List<DeliveryListResponse>> certificateRecord(String orderId, Long page, Long size) {
        // 调用中心接口：查询场景交付记录列表-获取第三方交付业务ID 场景交付ID:资产ID，条件：订单ID
        CommonResult<List<OrderSceneRefResp>> orderNoRelateSceneResponse = hubDeliverySceneRemote.orderNoRelateScene(Collections.singletonList(orderId));
        List<OrderSceneRefResp> orderSceneRefList = orderNoRelateSceneResponse.getData();
        if (ObjectUtils.isEmpty(orderSceneRefList)) {
            SuccessResponse<List<DeliveryListResponse>> response = SuccessResponse.success(null).total(0L).build();
            response.setData(new ArrayList<>());
            return response;
        }
        List<String> thirdDeliveryIds = new ArrayList<>(orderSceneRefList.size());
        for (OrderSceneRefResp orderSceneRefResp : orderSceneRefList) {
            SceneListResp.DataAssetSceneRef assetSceneRef = orderSceneRefResp.getAssetSceneRef();
            thirdDeliveryIds.add(String.format("%s:%s", orderSceneRefResp.getSceneId(), assetSceneRef.getAssetId()));
        }

        // 调用数字证书接口，获取交付登记记录
        DeliveryListRequest deliveryListRequest = DeliveryListRequest.builder()
                .thirdDeliveryIds(thirdDeliveryIds).page(page).size(size).build();
        PageResult<DeliveryListResponse> deliveryListResponsePageResult = digitalCertificateRemote.deliveryList(deliveryListRequest);
        return SuccessResponse.success(deliveryListResponsePageResult.getData())
                .page(new Page(deliveryListRequest.getPage(), deliveryListResponsePageResult.getTotal()))
                .total(deliveryListResponsePageResult.getTotal())
                .build();
    }

    /**
     * 卖方-订单合同列表
     */
    public SuccessResponse<List<OrderListVO>> sellerList(OrderSellerListReq orderSellerListReq) {
        String currentUserId = LoginContextHolder.currentUser().getId();
        OrderRecordsReq orderRecordsReq = OrderRecordsReq.builder().approverId(currentUserId).assetName(orderSellerListReq.getAssetName())
                .beneficiaryEnterpriseName(orderSellerListReq.getBeneficiaryEnterpriseName()).status(orderSellerListReq.getStatus())
                .page(orderSellerListReq.getNum()).offset((orderSellerListReq.getNum() - 1L) * orderSellerListReq.getSize()).size(orderSellerListReq.getSize()).build();
        return orderVOList(orderRecordsReq);
    }

    private OrderRecordsResp findOrderRecords(OrderRecordsReq orderRecordsReq) {
        QOrderApprovalRecord qoar = QOrderApprovalRecord.orderApprovalRecord;
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (StringUtils.isNotBlank(orderRecordsReq.getAssetName())) {
            booleanBuilder.and(qoar.assetName.like("%" + orderRecordsReq.getAssetName() + "%"));
        }
        if (Objects.nonNull(orderRecordsReq.getDeliveryMode())) {
            booleanBuilder.and(qoar.deliveryMode.eq(orderRecordsReq.getDeliveryMode().name()));
        }

        String userId = LoginContextHolder.currentUser().getId();
        booleanBuilder.and(qoar.beneficiaryId.eq(userId));

        long total = queryFactory.selectFrom(qoar).where(booleanBuilder).fetchCount();

        OrderRecordsResp resp = new OrderRecordsResp();
        List<OrderRecordDTO> list = queryFactory.selectFrom(qoar).where(booleanBuilder).orderBy(qoar.createTime.desc()).offset((orderRecordsReq.getPage() - 1) * orderRecordsReq.getSize())
                .limit(orderRecordsReq.getSize()).fetch().stream().map(orderMapstruct::OrderApprovalRecordToOrderRecordDTO).toList();

        resp.setData(list);
        resp.setTotal(total);
        return resp;
    }

    public SuccessResponse<List<OrderListVO>> orderVOList(OrderRecordsReq orderRecordsReq) {
        List<OrderListVO> orderListVOS = new ArrayList<>();

        // 分页查询订单合同列表，条件：资产ID列表，订单状态
        OrderRecordsResp orderRecordsResp = findOrderRecords(orderRecordsReq);

        long total = 0;
        if (!ObjectUtils.isEmpty(orderRecordsResp) && !ObjectUtils.isEmpty(orderRecordsResp.getData())) {
            List<OrderRecordDTO> orderRecords = orderRecordsResp.getData();

            List<DataProductVO> dataAssetList = new ArrayList<>();

            List<OrderRecordDTO> listProduct = orderRecords.stream().filter(orderRecordDTO -> orderRecordDTO.getType() == AssetType.PRODUCT).toList();
            if (!ObjectUtils.isEmpty(listProduct)) {
                for (OrderRecordDTO orderRecordDTO : listProduct) {
                    // 查资产详情
                    String extend = orderRecordDTO.getExtend();
                    OderRecordExtend oderRecordExtend = JSONUtil.toBean(extend, OderRecordExtend.class);
                    DataProductVO dataProduct = dataProductService.getDataProductByDataProductPlatformId(oderRecordExtend.getDataProductPlatformId());
                    dataAssetList.add(dataProduct);
                }
                log.debug("查询指定产品 id 返回： {}", JSONUtil.toJsonStr(dataAssetList));
            }


            Map<String, DataProductVO> dataAssetVOMap = new HashMap<>();
            for (DataProductVO dataAsset : dataAssetList) {
                dataAssetVOMap.put(dataAsset.getId(), dataAsset);
            }
            for (OrderRecordDTO orderRecord : orderRecords) {
                OrderListVO orderListVO = orderMapstruct.toOrderListVO(orderRecord, dataAssetVOMap.get(orderRecord.getAssetId()));
                orderListVOS.add(orderListVO);
            }
            total = orderRecordsResp.getTotal();
        }
        return SuccessResponse.success(orderListVOS).page(new Page(orderRecordsReq.getPage(), orderRecordsReq.getSize(), orderRecordsReq.getOffset())).total(total).build();
    }


    private List<CatalogQueryDataProduct> queryDataAssetListByType(DataAssetQuery dataAssetQuery) {
        // 调用中心接口：获取资产信息-资产创建人用户ID，条件：资产ID列表
        SuccessResponse<List<CatalogQueryDataProduct>> allMarketDataProductPublished = shuHanApiClient.allMarketDataProductPublished(dataAssetQuery);
        return allMarketDataProductPublished.getData();
    }

    private final MPCRemote mpcRemote;
    private final TeeRemote teeRemote;

    public void terminalContract(List<String> orderIds, List<String> assetIds) {
        List<String> mpcDeliveryIds = new ArrayList<>();
        List<String> teeDeliveryIds = new ArrayList<>();
        if (!ObjectUtils.isEmpty(orderIds)) {
            CommonResult<List<OrderSceneRefResp>> orderNoRelateScene = hubDeliverySceneRemote.orderNoRelateScene(orderIds);
            Assert.isTrue(orderNoRelateScene.isSuccess(), "获取订单相关交付场景数据异常：" + orderNoRelateScene.getMsg());
            if (!ObjectUtils.isEmpty(orderNoRelateScene.getData())) {
                orderNoRelateScene.getData().forEach(d -> {
                    switch (d.getDeliveryMode()) {
                        case TEE_ONLINE, TEE_OFFLINE -> teeDeliveryIds.add(d.getSceneId());
                        case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION ->
                                mpcDeliveryIds.add(d.getSceneId());
                    }
                });
            }
        }

        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        boolean mpcEnable = baseCapabilityManager.platformEnable(BaseCapabilityType.MPC);
        boolean teeEnable = baseCapabilityManager.platformEnable(BaseCapabilityType.TEE);

        TerminalContractRequest terminalContractRequest = new TerminalContractRequest();
        if (!mpcDeliveryIds.isEmpty() || !ObjectUtils.isEmpty(assetIds)) {
            if (mpcEnable) {
                terminalContractRequest.setSceneIds(mpcDeliveryIds);
                terminalContractRequest.setAssetIds(assetIds);
                mpcRemote.terminalContract(terminalContractRequest);
            }
        }
        if (!teeDeliveryIds.isEmpty() || !ObjectUtils.isEmpty(assetIds)) {
            if (teeEnable) {
                terminalContractRequest.setSceneIds(teeDeliveryIds);
                terminalContractRequest.setAssetIds(assetIds);
                teeRemote.terminalContract(terminalContractRequest);
            }
        }
    }

    /**
     * 更新订单状态
     */
    public void updateStatus(String orderId, OrderStatus updateStatus) {
        // 调用中心接口：查询订单信息，条件：订单ID
        SuccessResponse<OrderApprovalRecord> orderApprovalRecordResponse = hubOrderRemote.selectOrderApprovalRecordWhereId(orderId);
        OrderApprovalRecord orderApprovalRecord = orderApprovalRecordResponse.getData();
        Assert.isTrue(!ObjectUtils.isEmpty(orderApprovalRecord), "订单ID不存在");
        Assert.isTrue(OrderStatus.APPLY.toString().equals(orderApprovalRecord.getStatus()) || OrderStatus.APPROVED.toString().equals(orderApprovalRecord.getStatus()), "该订单状态已更新，请刷新页面后操作");

        // 调用中心接口：获取资产详情，条件：资产ID
        DataProduct dataProduct = dataProductRepository.getReferenceById(orderApprovalRecord.getAssetId());
//        DataAsset dataAsset = getDataAssetByType(orderApprovalRecord.getType(), orderApprovalRecord.getAssetId());

        AssetBeneficiaryRel assetBeneficiaryRel;
        orderApprovalRecord.setUpdateTime(new Date());
        if (OrderStatus.REJECTED.equals(updateStatus) || OrderStatus.TERMINATED.equals(updateStatus)) {
            orderApprovalRecord.setStatus(updateStatus.toString());
            orderApprovalRecord.setChangeStatus(true);
            SuccessResponse<Integer> rows = hubOrderRemote.updateOrderApprovalRecord(orderApprovalRecord);
            if (OrderStatus.TERMINATED.equals(updateStatus)) {
                // TEE MPC 合约终止
                terminalContract(Collections.singletonList(orderId), null);
            }
        } else if (OrderStatus.APPROVED.equals(updateStatus)) {
            orderApprovalRecord.setStatus(updateStatus.toString());
            orderApprovalRecord.setApproveTime(new Date());
            // 调用中心接口：查询资产获益人信息，条件：资产ID、获益人ID
            AssetBeneficiaryRelDTOReq assetBeneficiaryRelDTOReq = AssetBeneficiaryRelDTOReq.builder().assetId(orderApprovalRecord.getAssetId()).beneficiaryId(orderApprovalRecord.getBeneficiaryId()).build();
            SuccessResponse<AssetBeneficiaryRel> assetBeneficiaryRelResponse = hubOrderRemote.selectAssetBeneficiaryRelWhereAssetIdAndBeneficiaryId(assetBeneficiaryRelDTOReq);
            assetBeneficiaryRel = assetBeneficiaryRelResponse.getData();
            if (!ObjectUtils.isEmpty(assetBeneficiaryRel)) {
                // 更新获益人当前订单
                assetBeneficiaryRel.setOrderId(orderApprovalRecord.getId());
            } else {
                if (dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.API)
                        || dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.TEE_ONLINE)
                ) {
                    String gatewayServiceRouteId = dataProduct.getDataExt().getGatewayServiceRouteId();
                    // 调用API网关接口：给买家分配AppKey
                    String currentUsername = LoginContextHolder.currentUser().getUsername();
                    String apiKey = gatewayWebApi.generateAPIKeyForOrder(currentUsername, gatewayServiceRouteId);
                    log.debug("用户授权资产【{}】 apiKey ：{}", dataProduct.getDataProductName(), apiKey);
                    if (StringUtils.isBlank(apiKey)) {
                        throw new RestfulApiException("添加API授权key失败");
                    }
                    AssetBeneficiaryExtend assetBeneficiaryExtend = AssetBeneficiaryExtend.builder().apiKey(apiKey).build();
                    assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, assetBeneficiaryExtend);
                } else if (dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.FILE_DOWNLOAD)) {
                    try {
                        String accessKey = RandomUtil.randomString(20);
                        String secretKey = RandomUtil.randomString(40);
                        // NOTE: use orderId as accessKey
                        minioAdminClient.addServiceAccount(accessKey, secretKey, null, JacksonUtils.json2map("{\n" +
                                " \"Version\": \"2012-10-17\",\n" +
                                " \"Statement\": [\n" +
                                "  {\n" +
                                "   \"Effect\": \"Allow\",\n" +
                                "   \"Action\": [\n" +
                                "    \"s3:GetBucketLocation\",\n" +
                                "    \"s3:GetObject\",\n" +
                                "    \"s3:ListBucket\"\n" +
                                "   ],\n" +
                                "   \"Resource\": [\n" +
                                "    \"arn:aws:s3:::" + dataProduct.getUserId() + "\",\n" +
                                "    \"arn:aws:s3:::" + dataProduct.getUserId() + "/" + dataProduct.getId() + "/*\"\n" +
                                "   ]\n" +
                                "  }\n" +
                                " ]\n" +
                                "}"), null, null, null);
                        AssetBeneficiaryExtend assetBeneficiaryExtend = AssetBeneficiaryExtend.builder().apiKey(String.format("%s@%s", accessKey, secretKey)).build();
                        assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, assetBeneficiaryExtend);
                    } catch (Exception e) {
                        log.error("添加minio accessKey失败", e);
                        throw new RestfulApiException("添加minio accessKey失败", e);
                    }
                } else
                    assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, AssetBeneficiaryExtend.builder().build());
            }
            // 调用中心接口：更新订单审批记录、存储/更新资产获益人信息
            AssetBeneficiaryOderTableDTO assetBeneficiaryOderTableDTO = AssetBeneficiaryOderTableDTO.builder()
                    .orderApprovalRecord(orderApprovalRecord).assetBeneficiaryRel(assetBeneficiaryRel).build();
            SuccessResponse<Integer> integerResponse = hubOrderRemote.replaceIntoOrderApprovalAssetBeneficiary(assetBeneficiaryOderTableDTO);
        }
        // 发送消息
        sseMessageService.notifyOrderAuditMessage(orderApprovalRecord, updateStatus);
    }

    private final static ConcurrentHashMap<String, String> MINIO_API_KEY_CACHE = new ConcurrentHashMap<>();


    /**
     * 赣州定制：授权 minio
     */
    public String authMinioApiKey(String productId, String productUserId) throws Exception {

        String accessKey = RandomUtil.randomString(20);
        String secretKey = RandomUtil.randomString(40);
        // NOTE: use orderId as accessKey
        minioAdminClient.addServiceAccount(accessKey, secretKey, null, JacksonUtils.json2map("{\n" +
                " \"Version\": \"2012-10-17\",\n" +
                " \"Statement\": [\n" +
                "  {\n" +
                "   \"Effect\": \"Allow\",\n" +
                "   \"Action\": [\n" +
                "    \"s3:GetBucketLocation\",\n" +
                "    \"s3:GetObject\",\n" +
                "    \"s3:ListBucket\"\n" +
                "   ],\n" +
                "   \"Resource\": [\n" +
                "    \"arn:aws:s3:::" + productUserId + "\",\n" +
                "    \"arn:aws:s3:::" + productUserId + "/" + productId + "/*\"\n" +
                "   ]\n" +
                "  }\n" +
                " ]\n" +
                "}"), null, null, null);
        return String.format("%s@%s", accessKey, secretKey);
//        return apiKey;
    }

    /**
     * 赣州定制：授权网关接口apiKey
     */
    public String authGatewayApiKey(String productName, String apiOwnerUsername, String gatewayServiceRouteId) {
        // 调用API网关接口：给买家分配AppKey
        String apiKey = gatewayWebApi.generateAPIKeyForOrder(apiOwnerUsername, gatewayServiceRouteId);
        log.debug("用户授权资产【{}】 apiKey ：{}", productName, apiKey);
        if (StringUtils.isBlank(apiKey)) {
            throw new RestfulApiException("获取API授权key失败");
        }
        return apiKey;
    }
}
