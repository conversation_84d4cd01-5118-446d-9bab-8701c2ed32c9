package com.ailpha.ailand.dataroute.endpoint.order.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryListResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.OrderClassify;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.SSEMessageService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.QDeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.QSceneAsset;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.SceneAssetRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneAssetResp;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.impl.DeliveryServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.home.*;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.domain.*;
import com.ailpha.ailand.dataroute.endpoint.order.mapstruct.OrderMapstruct;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderConfigDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderSuccessDeliveryRequest;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.TradingStrategyDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.repository.AssetBeneficiaryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderDeliveryRepository;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.vo.*;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderBuyerListReq;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderCreateReq;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderSellerListReq;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.input.TerminalContractRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.*;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.OrderRecordsResp;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.minio.admin.MinioAdminClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType.MPC_PRIVATE_INFORMATION_RETRIEVAL;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderManagerService {

    private final OrderMapstruct orderMapstruct;
    private final DigitalCertificateRemote digitalCertificateRemote;
    private final HubOrderRemote hubOrderRemote;
    private final SSEMessageService sseMessageService;
    private final GatewayWebApi gatewayWebApi;
    private final DataProductRepository dataProductRepository;
    private final RouterService routerService;

    private final MinioAdminClient minioAdminClient;
    private final HubShuHanApiClient shuHanApiClient;
    private final DataProductService dataProductService;
    private final JPAQueryFactory queryFactory;
    private final OrderRecordRepository orderRecordRepository;
    private final AssetBeneficiaryRepository assetBeneficiaryRepository;

    private final EndpointRemote endpointRemote;
    private final OrderDeliveryRepository orderDeliveryRepository;
    private final SceneAssetRepository sceneAssetRepository;
    private final ServiceNodeRemoteService nodeRemoteService;

    /**
     * 创建订单合同
     */
    @Deprecated
    public void create(OrderCreateReq orderCreateReq) {
        paramCheck(orderCreateReq);

        // 获取当前用户信息
        UserDTO currentUser = LoginContextHolder.currentUser();
        // 当前企业信息
        NodeInfoResponse currentNode = routerService.currentNode();

        List<String> assetIds = orderCreateReq.getAssetIds();

        // 检查：调用中心接口，获取订单列表，条件：资产ID、当前用户ID，存在状态：APPLY（待审批）APPROVED（通过），不可申请
        AssetBeneficiaryRelDTOListReq assetBeneficiaryRelDTOListReq = AssetBeneficiaryRelDTOListReq.builder().assetIds(assetIds).beneficiaryId(currentUser.getId()).build();
        SuccessResponse<List<OrderApprovalRecord>> orderRecordsResponse = hubOrderRemote.selectOrderApprovalRecordWhereAssetIdInAndBeneficiaryId(assetBeneficiaryRelDTOListReq);
        List<OrderApprovalRecord> assetBeneficiaryRelList = orderRecordsResponse.getData();
        if (!ObjectUtils.isEmpty(assetBeneficiaryRelList)) {
            Map<String, OrderApprovalRecord> assetOrderMap = new HashMap<>(assetBeneficiaryRelList.size());
            for (OrderApprovalRecord orderRecord : assetBeneficiaryRelList) {
                if (ObjectUtils.isEmpty(assetOrderMap.get(orderRecord.getAssetId()))) {
                    assetOrderMap.put(orderRecord.getAssetId(), orderRecord);
                }
            }
            for (Map.Entry<String, OrderApprovalRecord> entry : assetOrderMap.entrySet()) {
                boolean allow = !OrderStatus.APPLY.toString().equals(entry.getValue().getStatus()) && !OrderStatus.APPROVED.toString().equals(entry.getValue().getStatus());
                if (!allow) {
                    throw new IllegalArgumentException(String.format("资产：%s，存在有效订单，不可重复申请", entry.getValue().getAssetName()));
                }
            }
        }

        Map<String, DataProductVO> dataAssetMap = new HashMap<>(assetIds.size());
        for (String assetId : assetIds) {
            // 查资产详情
//            DataProductVO dataProduct = dataProductService.getDataProduct(assetId);
            // 校验资产状态 —— 上架状态放到业务节点了，可以上架到多个业务节点 （这里不做校验）todo: 会有问题
//            Assert.isTrue(PushStatus.ONLINE.equals(dataAsset.getPushStatus()), String.format("资产：%s，非上架状态，无法申请", dataAsset.getAssetName()));
            // DataProductVO dataProduct = dataProductService.getDataProductByOldAssetId(assetId);
            DataProductVO dataProduct = new DataProductVO();
            dataAssetMap.put(assetId, dataProduct);
        }
        // 生成订单合同审批记录
        long timestamp = System.currentTimeMillis();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        List<OrderApprovalRecord> orderApprovalRecords = new ArrayList<>(2);
        for (String assetId : assetIds) {
            String id = String.format("%s%s%s%s", "DDHT", format.format(timestamp), timestamp, UUID.randomUUID().toString().replace("-", "").substring(0, 7).toUpperCase());
            OrderApprovalRecord orderApprovalRecord = orderMapstruct.initOrderApprovalRecord(orderCreateReq, id, assetId, currentUser, currentNode, dataAssetMap);
            orderApprovalRecords.add(orderApprovalRecord);
        }
        // 调用中心接口：存储订单审批记录
        // SuccessResponse<Integer> rowsResponse = hubOrderRemote.insertIntoOrderApprovalRecords(orderApprovalRecords);
//        sseMessageService.notifyOrderApplyMessage(orderApprovalRecords, orderCreateReq);
    }

    public String addSystemOrder(DataProductVO dataProductVO, UserDTO userLocal) {
        final OrderApprovalRecord orderApprovalRecord = orderRecordRepository.findFirstByAssetIdAndBeneficiaryIdAndClassify(dataProductVO.getId(), userLocal.getId(), OrderClassify.SYSTEM);
        if (orderApprovalRecord != null) {
            return orderApprovalRecord.getId();
        }

        // 发起方企业信息
        CompanyDTO companyBeneficiary = userLocal.getCompany();
        String routeId = companyBeneficiary.getNodeId();
        Long companyId = companyBeneficiary.getId();

        // 数据产品 企业信息
        CompanyDTO company = dataProductVO.getProvider().getCompany();

        long timestamp = System.currentTimeMillis();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String id = String.format("%s%s%s%s", "DDHT", format.format(timestamp), timestamp, UUID.randomUUID().toString().replace("-", "").substring(0, 7).toUpperCase());
        OrderApprovalRecord record = OrderApprovalRecord.builder()
                .id(id)
                .classify(OrderClassify.SYSTEM)
                .type(AssetType.PRODUCT)
                .assetId(dataProductVO.getId())
                .assetName(dataProductVO.getDataProductName())
                .deliveryMode("")
                // 使用连接器唯一用户id
                .beneficiaryId(userLocal.getId())
                .beneficiaryUsername(userLocal.getUsername())
                .beneficiaryRouterId(routeId)
                .beneficiaryEnterpriseName(companyBeneficiary.getOrganizationName())
                .beneficiaryEnterpriseProperty(companyBeneficiary.getIndustryType())
                .approverId(dataProductVO.getUserId())
                .approverUsername(dataProductVO.getProvider().getUsername())
                .approverRouterId(dataProductVO.getProvider().getRouterId())
                .approverEnterpriseName(company.getOrganizationName())
                // 访问控制配置

                .chargingWay("预付费")
                // 按需、按时间 —— 对应 MeasurementMethod
                .meteringWay("")
                .allowance(null)
                .expireDate(DateUtil.parse("9999-12-31 00:00:00", DatePattern.NORM_DATETIME_FORMAT))
                .successfulUsage(new BigInteger("0"))
                .status("APPROVED")
                .createTime(new Date())
                .updateTime(new Date())
                .approveTime(new Date())
                .pullTime(new Date())
                .build();

        OderRecordExtend recordExtend = OderRecordExtend.builder()
                .callPrice("面议")
                // 对应 MeasurementUint
                .cycleWay("包年")
                .price("")
                .deliveryModeStandards(dataProductVO.getDeliveryMethod())
                .beneficiaryCreditCode(companyBeneficiary.getCreditCode())
                .approverCreditCode(company.getCreditCode())
                .beneficiaryCompanyId(String.valueOf(companyId))
                .approverCompanyId(String.valueOf(company.getId()))
                .dataProductPlatformId(dataProductVO.getDataProductPlatformId())
                // 扩展字段
                .productPrice(String.valueOf(dataProductVO.getPrice()))
                .mpcPurposes(dataProductVO.getMpcPurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .teePurposes(dataProductVO.getTeePurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .productionType(dataProductVO.getType())
                .dataType(dataProductVO.getDataType() == null ? null : dataProductVO.getDataType().name())
                .dataType1(dataProductVO.getDataType1())
                .isLLM(dataProductVO.getIsLLM())
                .summary(dataProductVO.getDescription())
                .providerOrg(company.getOrganizationName())
                .build();
        if (dataProductVO.getDeliveryModes().contains(DeliveryMode.TEE) || dataProductVO.getDeliveryModes().contains(DeliveryMode.MPC)) {
            List<String> collect = dataProductVO.getDeliveryModes().stream().map(Enum::name).toList();
            List<String> collect1 = dataProductVO.getTeePurpose().stream().map(Enum::name).collect(Collectors.toList());
            collect1.addAll(collect);
            recordExtend.setDeliveryModes(String.join(",", collect1));
        } else {
            recordExtend.setDeliveryModes(dataProductVO.getDeliveryModes().stream().map(Enum::name).collect(Collectors.joining(",")));
        }
        record.setExtend(recordExtend);

        AssetBeneficiaryRel rel = AssetBeneficiaryRel.builder()
                .id(UUID.randomUUID().toString().replace("-", ""))
                .assetId(record.getAssetId())
                .beneficiaryId(record.getBeneficiaryId())
                .orderId(record.getId())
                // 可以不填充
                .extend(new AssetBeneficiaryExtend())
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        SpringUtil.getBean(OrderResolveService.class).save(new OrderResolveDTO(record, rel));
        return id;
    }


    private void paramCheck(OrderCreateReq orderCreateReq) {
        final String meteringWay = orderCreateReq.getMeteringWay();
        if (StringUtils.equals(meteringWay, "按次")) {
            final BigInteger allowance = orderCreateReq.getAllowance();
            Assert.isTrue(allowance != null && allowance.longValue() > 0, "计量计费方案为按次时【使用次数】不能为空");
        }

        if (StringUtils.equals(meteringWay, "按周期")) {
            final Date expireDate = orderCreateReq.getExpireDate();
            Assert.isTrue(expireDate != null, "计量计费方案为按周期时【订单有效期】输入非法");
            Assert.isTrue(DateUtil.compare(expireDate, new Date()) > 0, "订单有效期不得早于当前时间");
        }

    }


    /**
     * 买方-订单合同列表
     */
    public SuccessResponse<List<OrderListVO>> buyerList(OrderBuyerListReq orderBuyerListReq) {
        String currentUserId = LoginContextHolder.currentUser().getId();
        OrderRecordsReq orderRecordsReq = OrderRecordsReq.builder().beneficiaryId(currentUserId).assetName(orderBuyerListReq.getAssetName())
                .deliveryMode(orderBuyerListReq.getDeliveryMode()).deliveryMethod(orderBuyerListReq.getDeliveryMethod()).deliverySceneMode(orderBuyerListReq.getDeliverySceneMode()).status(orderBuyerListReq.getStatus())
                .page(orderBuyerListReq.getNum()).offset((orderBuyerListReq.getNum() - 1L) * orderBuyerListReq.getSize()).size(orderBuyerListReq.getSize()).build();
        return orderVOList(orderRecordsReq);
    }

    /**
     * 订单详情
     */
    public OrderVO detail(String orderId) {
        OrderApprovalRecord orderApprovalRecord = orderRecordRepository.findById(orderId).orElseThrow(() -> new RestfulApiException("未查到有效订单"));

        String dataProductPlatformId = orderApprovalRecord.getExtend().getDataProductPlatformId();

        // 查询分配的API网关权限key
        AssetBeneficiaryRel assetBeneficiaryRel = assetBeneficiaryRepository.findFirstByOrderIdOrderByCreateTimeDesc(orderId);
        AssetBeneficiaryExtend beneficiaryExtend = assetBeneficiaryRel.getExtend();

        // 查资产详情
        String currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        DataProductVO dataProduct = dataProductService.getDataProductByDataProductPlatformId(dataProductPlatformId, currentNodeId);

        OrderVO orderVO = orderMapstruct.toOrderVO(orderApprovalRecord, beneficiaryExtend, dataProduct);

        try {
            OrderConfigDTO orderConfigDTO = new OrderConfigDTO();
            orderConfigDTO.setOrderIds(orderId);
            orderConfigDTO.setUrl(orderApprovalRecord.getExtend().getServiceNodeUrl());

            ShuhanResponse<List<OrderConfigVO>> result = nodeRemoteService.accessConfig(orderConfigDTO);

            Assert.isTrue(result.isSuccess(), "获取订单状态异常");
            List<OrderConfigVO> orderConfigVOList = result.getData();
            Map<String, OrderConfigVO> configVOMap = orderConfigVOList.stream().collect(Collectors.toMap(OrderConfigVO::getOrderId, orderConfigVO -> orderConfigVO, (a, b) -> b));
            OrderConfigVO orderConfigVO = configVOMap.get(orderId);
            orderVO.setStatus(orderConfigVO == null ? "" : orderConfigVO.changeOrderStatus());

        } catch (Exception e) {
            log.error("获取订单状态异常：", e);
        }

        return orderVO;
    }

    /**
     * 数据发票存证记录
     */
    public SuccessResponse<List<DeliveryListResponse>> certificateRecord(String orderId, Long page, Long size) {
        // 调用中心接口：查询场景交付记录列表-获取第三方交付业务ID 场景交付ID:资产ID，条件：订单ID

        List<String> thirdDeliveryIds = new ArrayList<>();
        String nodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        boolean exists = orderRecordRepository.queryCountByBeneficiaryRouterId(orderId, nodeId) > 0;
        if (exists) {
            List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByOrderId(orderId);
            if (ObjectUtils.isEmpty(sceneAssetList)) {
                SuccessResponse<List<DeliveryListResponse>> response = SuccessResponse.success(null).total(0L).build();
                response.setData(new ArrayList<>());
                return response;
            }
            for (SceneAsset sceneAsset : sceneAssetList) {
                thirdDeliveryIds.add(String.format("%s:%s", sceneAsset.getDeliverySceneId(), sceneAsset.getDataAssetId()));
            }

        } else {
            List<OrderDeliveryRelation> deliveryRelationList = orderDeliveryRepository.findAllByOrderId(orderId);

            if (ObjectUtils.isEmpty(deliveryRelationList)) {
                SuccessResponse<List<DeliveryListResponse>> response = SuccessResponse.success(null).total(0L).build();
                response.setData(new ArrayList<>());
                return response;
            }

            for (OrderDeliveryRelation deliveryRelation : deliveryRelationList) {
                thirdDeliveryIds.add(String.format("%s:%s", deliveryRelation.getDeliverySceneId(), deliveryRelation.getAssetId()));
            }
        }

        // 调用数字证书接口，获取交付登记记录
        DeliveryListRequest deliveryListRequest = DeliveryListRequest.builder()
                .thirdDeliveryIds(thirdDeliveryIds).page(page).size(size).build();
        PageResult<DeliveryListResponse> deliveryListResponsePageResult = digitalCertificateRemote.deliveryList(deliveryListRequest);
        return SuccessResponse.success(deliveryListResponsePageResult.getData())
                .page(new Page(deliveryListRequest.getPage(), deliveryListResponsePageResult.getTotal()))
                .total(deliveryListResponsePageResult.getTotal())
                .build();
    }

    /**
     * 卖方-订单合同列表
     */
    public SuccessResponse<List<OrderListVO>> sellerList(OrderSellerListReq orderSellerListReq) {
        String currentUserId = LoginContextHolder.currentUser().getId();
        OrderRecordsReq orderRecordsReq = OrderRecordsReq.builder().approverId(currentUserId).assetName(orderSellerListReq.getAssetName())
                .beneficiaryEnterpriseName(orderSellerListReq.getBeneficiaryEnterpriseName()).status(orderSellerListReq.getStatus())
                .page(orderSellerListReq.getNum()).offset((orderSellerListReq.getNum() - 1L) * orderSellerListReq.getSize()).size(orderSellerListReq.getSize()).build();

        SuccessResponse<List<OrderListVO>> response = orderVOList(orderRecordsReq);
        List<OrderListVO> list = response.getData();
        sellerOrderStatus(list);

        return response;
    }

    private void sellerOrderStatus(List<OrderListVO> list) {
        // 分组查询订单状态
        if (!ObjectUtils.isEmpty(list)) {

            Map<String, List<OrderListVO>> ordersGroupedByServiceNodeUrl = list.stream()
                    .collect(Collectors.groupingBy(
                            order -> {
                                if (order.getOrderConfig() != null && order.getOrderConfig().getServiceNodeUrl() != null) {
                                    return order.getOrderConfig().getServiceNodeUrl();
                                }
                                // 处理 null 或空值的情况
                                return "";
                            }
                    ));

            // 使用分组结果
            ordersGroupedByServiceNodeUrl.forEach((serviceNodeUrl, orders) -> {
                log.debug("Service Node URL: {}  orders.size(): {}", serviceNodeUrl, orders.size());

                String orderIds = orders.stream().map(OrderListVO::getOrderId).collect(Collectors.joining(","));

                try {
                    OrderConfigDTO orderConfigDTO = new OrderConfigDTO();
                    orderConfigDTO.setOrderIds(orderIds);
                    orderConfigDTO.setUrl(serviceNodeUrl);

                    ShuhanResponse<List<OrderConfigVO>> result = nodeRemoteService.accessConfig(orderConfigDTO);

                    Assert.isTrue(result.isSuccess(), "获取订单状态异常");
                    List<OrderConfigVO> orderConfigVOList = result.getData();
                    Map<String, OrderConfigVO> configVOMap = orderConfigVOList.stream().collect(Collectors.toMap(OrderConfigVO::getOrderId, orderConfigVO -> orderConfigVO, (a, b) -> b));
                    orders.forEach(order -> {
                        OrderConfigVO orderConfigVO = configVOMap.get(order.getOrderId());
                        order.setStatus(orderConfigVO == null ? "" : orderConfigVO.changeOrderStatus());
                    });

                } catch (Exception e) {
                    log.error("获取订单状态异常：", e);
                }
            });
        }
    }

    public OrderRecordsResp findOrderRecords(OrderRecordsReq orderRecordsReq) {
        QOrderApprovalRecord qoar = QOrderApprovalRecord.orderApprovalRecord;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        booleanBuilder.and(qoar.classify.eq(OrderClassify.NORMAL));
        if (StringUtils.isNotBlank(orderRecordsReq.getBeneficiaryId())) {
            booleanBuilder.and(qoar.beneficiaryId.eq(orderRecordsReq.getBeneficiaryId()));
        }
        if (StringUtils.isNotBlank(orderRecordsReq.getApproverId())) {
            booleanBuilder.and(qoar.approverId.eq(orderRecordsReq.getApproverId()));
        }
        if (StringUtils.isNotBlank(orderRecordsReq.getStatus())) {
            booleanBuilder.and(qoar.status.eq(orderRecordsReq.getStatus()));
        }

        if (StringUtils.isNotBlank(orderRecordsReq.getAssetName())) {
            booleanBuilder.and(qoar.assetName.like("%" + orderRecordsReq.getAssetName() + "%"));
        }
        if (Objects.nonNull(orderRecordsReq.getDeliveryMode())) {
            booleanBuilder.and(qoar.deliveryMode.eq(orderRecordsReq.getDeliveryMode().name()));
        }
        if (StringUtils.isNotBlank(orderRecordsReq.getDeliveryMethod())) {
            booleanBuilder.and(qoar.extend.deliveryModeStandards.eq(orderRecordsReq.getDeliveryMethod()));
        }
        if (Objects.nonNull(orderRecordsReq.getDeliverySceneMode())) {
            booleanBuilder.and(qoar.extend.deliveryModes.contains(orderRecordsReq.getDeliverySceneMode().name()));
        }

        long total = queryFactory.selectFrom(qoar).where(booleanBuilder).fetchCount();

        OrderRecordsResp resp = new OrderRecordsResp();
        List<OrderRecordDTO> list = queryFactory.selectFrom(qoar).where(booleanBuilder).orderBy(qoar.createTime.desc()).offset((orderRecordsReq.getPage() - 1) * orderRecordsReq.getSize())
                .limit(orderRecordsReq.getSize()).fetch().stream().map(orderMapstruct::OrderApprovalRecordToOrderRecordDTO).toList();

        resp.setData(list);
        resp.setTotal(total);
        return resp;
    }

    public SuccessResponse<List<OrderListVO>> orderVOList(OrderRecordsReq orderRecordsReq) {
        List<OrderListVO> orderListVOS = new ArrayList<>();

        // 调用中心接口：分页查询订单合同列表，条件：资产ID列表，订单状态
        // 平替一下，走本地库查询
//        SuccessResponse<OrderRecordsResp> orderApprovalRecordsResponse = hubOrderRemote.orderApprovalRecordsPage(orderRecordsReq);
        OrderRecordsResp orderRecordsResp = findOrderRecords(orderRecordsReq);


        String currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        long total = 0;
        if (!ObjectUtils.isEmpty(orderRecordsResp) && !ObjectUtils.isEmpty(orderRecordsResp.getData())) {
            List<OrderRecordDTO> orderRecords = orderRecordsResp.getData();

            List<DataProductVO> dataAssetList = new ArrayList<>();

            List<OrderRecordDTO> listProduct = orderRecords.stream().filter(orderRecordDTO -> orderRecordDTO.getType() == AssetType.PRODUCT).toList();
            if (!ObjectUtils.isEmpty(listProduct)) {
                List<String> dataproductPlatformIdList = listProduct.stream().map(orderRecordDTO -> {
                    OderRecordExtend oderRecordExtend = orderRecordDTO.getExtend();
                    return oderRecordExtend.getDataProductPlatformId();
                }).distinct().toList();
                log.debug("查询订单产品 id: [{}]", dataproductPlatformIdList);
                for (String assetId : dataproductPlatformIdList) {
                    // 查资产详情
//                    DataProductVO dataProduct = dataProductService.getDataProduct(assetId);
                    DataProductVO dataProduct = dataProductService.getDataProductByDataProductPlatformId(assetId, currentNodeId);
                    dataAssetList.add(dataProduct);
                }
                // log.debug("查询指定产品 id 返回： {}", JSONUtil.toJsonStr(dataAssetList));
            }


            Map<String, DataProductVO> dataAssetVOMap = new HashMap<>();
            for (DataProductVO dataAsset : dataAssetList) {
                dataAssetVOMap.put(dataAsset.getId(), dataAsset);
            }
            for (OrderRecordDTO orderRecord : orderRecords) {
                OrderListVO orderListVO = orderMapstruct.toOrderListVO(orderRecord, dataAssetVOMap.get(orderRecord.getAssetId()));
                orderListVOS.add(orderListVO);
            }
            total = orderRecordsResp.getTotal();
        }
        return SuccessResponse.success(orderListVOS).page(new Page(orderRecordsReq.getPage(), orderRecordsReq.getSize(), orderRecordsReq.getOffset())).total(total).build();
    }

    public SuccessResponse<List<StatisticDeliveryRecordVO>> statisticAssetOrderDeliveryPage(StatisticAssetOrderDeliveryPageReq statisticAssetOrderDeliveryPageReq) {
        List<StatisticDeliveryRecordVO> statisticDeliveryRecords = new ArrayList<>();

        List<String> deliveryModes = null;
        if (!ObjectUtils.isEmpty(statisticAssetOrderDeliveryPageReq.getDeliveryMode())) {
            deliveryModes = (DeliveryMode.MPC.equals(statisticAssetOrderDeliveryPageReq.getDeliveryMode()) ?
                    Arrays.asList(MPC_PRIVATE_INFORMATION_RETRIEVAL.name(), DeliveryType.MPC_PRIVATE_SET_INTERSECTION.name(), DeliveryType.MPC_PRIVATE_SET_INTERSECTION.name())
                    : Collections.singletonList(statisticAssetOrderDeliveryPageReq.getDeliveryMode().name()));
        }
        StatisticAssetOrderDeliveryReq statisticAssetOrderDeliveryReq = StatisticAssetOrderDeliveryReq.builder().assetId(statisticAssetOrderDeliveryPageReq.getAssetId())
                .beneficiaryEnterpriseName(statisticAssetOrderDeliveryPageReq.getBeneficiaryEnterpriseName()).orderId(statisticAssetOrderDeliveryPageReq.getOrderId()).deliveryMode(deliveryModes)
                .page(statisticAssetOrderDeliveryPageReq.getNum()).offset((statisticAssetOrderDeliveryPageReq.getNum() - 1L) * statisticAssetOrderDeliveryPageReq.getSize()).size(statisticAssetOrderDeliveryPageReq.getSize()).build();

        SuccessResponse<StatisticDeliveryRecordsResp> statisticDeliveryRecordsResponse = hubOrderRemote.statisticAssetOrderDeliveryPage(statisticAssetOrderDeliveryReq);
        StatisticDeliveryRecordsResp statisticDeliveryRecordsResp = statisticDeliveryRecordsResponse.getData();
        long total = 0;
        if (!ObjectUtils.isEmpty(statisticDeliveryRecordsResp) && !ObjectUtils.isEmpty(statisticDeliveryRecordsResp.getData())) {
            for (StatisticDeliveryRecord statisticDeliveryRecord : statisticDeliveryRecordsResp.getData()) {
                statisticDeliveryRecords.add(orderMapstruct.toStatisticDeliveryRecordVO(statisticDeliveryRecord));
            }
            total = statisticDeliveryRecordsResp.getTotal();
        }
        return SuccessResponse.success(statisticDeliveryRecords).page(new Page(statisticAssetOrderDeliveryReq.getPage(), statisticAssetOrderDeliveryPageReq.getSize(), statisticAssetOrderDeliveryPageReq.getOffset())).total(total).build();
    }

    private final MPCRemote mpcRemote;
    private final TeeRemote teeRemote;

    public void terminalContract(String localCompanyId, List<String> orderIds, List<String> assetIds) {
        List<String> mpcDeliveryIds = new ArrayList<>();
        List<String> teeDeliveryIds = new ArrayList<>();

        log.info("info to terminal contract localCompanyId:{}, orderIds:{}, assetIds:{}", localCompanyId, orderIds, assetIds);
        if (!ObjectUtils.isEmpty(orderIds)) {

            List<DeliveryScene> sceneList = new ArrayList<>();
            for (String orderId : orderIds) {
                List<DeliveryScene> tmp = findDeliverySceneByOrderId(orderId);
                if (CollectionUtil.isNotEmpty(tmp)) {
                    sceneList.addAll(tmp);
                }
            }

            if (!ObjectUtils.isEmpty(sceneList)) {
                sceneList.forEach(d -> {
                    log.info("delivery [{}] scene type: {}", d.getId(), d.getDeliveryType());
                    switch (d.getDeliveryType()) {
                        case TEE_ONLINE, TEE_OFFLINE, TEE_MODEL_OPTIMIZE, TEE_MODEL_PREDICT ->
                                teeDeliveryIds.add(d.getId());
                        case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION ->
                                mpcDeliveryIds.add(d.getId());
                    }
                });
            }
        }

        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        boolean mpcEnable = baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.MPC);
        boolean teeEnable = baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TEE);
        log.info("info to terminal contract mpcEnable:{}, teeEnable:{}", mpcEnable, teeEnable);

        TerminalContractRequest terminalContractRequest = new TerminalContractRequest();
        log.info("info to terminal contract mpcDeliveryIds:{} ", mpcDeliveryIds);
        if (!mpcDeliveryIds.isEmpty() || !ObjectUtils.isEmpty(assetIds)) {
            if (mpcEnable) {
                terminalContractRequest.setSceneIds(mpcDeliveryIds);
                terminalContractRequest.setAssetIds(assetIds);
                mpcRemote.terminalContract(localCompanyId, terminalContractRequest);
            }
        }
        log.info("info to terminal contract teeDeliveryIds:{} ", teeDeliveryIds);
        if (!teeDeliveryIds.isEmpty() || !ObjectUtils.isEmpty(assetIds)) {
            if (teeEnable) {
                terminalContractRequest.setSceneIds(teeDeliveryIds);
                terminalContractRequest.setAssetIds(assetIds);
                teeRemote.terminalContract(localCompanyId, terminalContractRequest);
            }
        }
    }


    /**
     * 找到所有订单关联的场景交付
     */
    public List<DeliveryScene> findDeliverySceneByOrderId(String orderId) {
        QDeliveryScene qDeliveryScene = QDeliveryScene.deliveryScene;
        QSceneAsset qSceneAsset = QSceneAsset.sceneAsset;
        List<String> list = queryFactory.selectDistinct(qDeliveryScene.id)
                .from(qDeliveryScene)
                .leftJoin(qSceneAsset).on(qSceneAsset.deliverySceneId.eq(qDeliveryScene.id))
                .where(qSceneAsset.orderId.eq(orderId).and(qDeliveryScene.sceneStatus.in("COMPLETED", "RUNNING"))).fetch();
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        } else {
            return SpringUtil.getBean(DeliverySceneRepository.class).findAllById(list);
        }
    }

    /**
     * 更新订单状态
     */
    public void updateStatus(String orderId, OrderStatus updateStatus) {
        // 调用中心接口：查询订单信息，条件：订单ID
        SuccessResponse<OrderApprovalRecord> orderApprovalRecordResponse = hubOrderRemote.selectOrderApprovalRecordWhereId(orderId);
        OrderApprovalRecord orderApprovalRecord = orderApprovalRecordResponse.getData();
        Assert.isTrue(!ObjectUtils.isEmpty(orderApprovalRecord), "订单ID不存在");
        Assert.isTrue(OrderStatus.APPLY.toString().equals(orderApprovalRecord.getStatus()) || OrderStatus.APPROVED.toString().equals(orderApprovalRecord.getStatus()), "该订单状态已更新，请刷新页面后操作");

        // 调用中心接口：获取资产详情，条件：资产ID
        DataProduct dataProduct = dataProductRepository.getReferenceById(orderApprovalRecord.getAssetId());
//        DataAsset dataAsset = getDataAssetByType(orderApprovalRecord.getType(), orderApprovalRecord.getAssetId());

        AssetBeneficiaryRel assetBeneficiaryRel;
        orderApprovalRecord.setUpdateTime(new Date());
        if (OrderStatus.REJECTED.equals(updateStatus) || OrderStatus.TERMINATED.equals(updateStatus)) {
            orderApprovalRecord.setStatus(updateStatus.toString());
            orderApprovalRecord.setChangeStatus(true);
            SuccessResponse<Integer> rows = hubOrderRemote.updateOrderApprovalRecord(orderApprovalRecord);
            if (OrderStatus.TERMINATED.equals(updateStatus)) {
                // TEE MPC 合约终止
                String localCompanyId = null;
                if (LoginContextHolder.isLogin()) {
                    UserDTO currentUser = LoginContextHolder.currentUser();
                    localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
                }
                terminalContract(localCompanyId, Collections.singletonList(orderId), null);
            }
        } else if (OrderStatus.APPROVED.equals(updateStatus)) {
            orderApprovalRecord.setStatus(updateStatus.toString());
            orderApprovalRecord.setApproveTime(new Date());
            // 调用中心接口：查询资产获益人信息，条件：资产ID、获益人ID
            AssetBeneficiaryRelDTOReq assetBeneficiaryRelDTOReq = AssetBeneficiaryRelDTOReq.builder().assetId(orderApprovalRecord.getAssetId()).beneficiaryId(orderApprovalRecord.getBeneficiaryId()).build();
            SuccessResponse<AssetBeneficiaryRel> assetBeneficiaryRelResponse = hubOrderRemote.selectAssetBeneficiaryRelWhereAssetIdAndBeneficiaryId(assetBeneficiaryRelDTOReq);
            assetBeneficiaryRel = assetBeneficiaryRelResponse.getData();
            if (!ObjectUtils.isEmpty(assetBeneficiaryRel)) {
                // 更新获益人当前订单
                assetBeneficiaryRel.setOrderId(orderApprovalRecord.getId());
            } else {
                if (dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.API)
                        || dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.TEE_ONLINE)
                ) {
                    String gatewayServiceRouteId = dataProduct.getDataExt().getGatewayServiceRouteId();
                    // 调用API网关接口：给买家分配AppKey
                    String currentUsername = LoginContextHolder.currentUser().getUsername();
                    String apiKey = gatewayWebApi.generateAPIKeyForOrder(currentUsername, gatewayServiceRouteId);
                    log.debug("用户授权资产【{}】 apiKey ：{}", dataProduct.getDataProductName(), apiKey);
                    if (StringUtils.isBlank(apiKey)) {
                        throw new RestfulApiException("添加API授权key失败");
                    }
                    AssetBeneficiaryExtend assetBeneficiaryExtend = AssetBeneficiaryExtend.builder().apiKey(apiKey).build();
                    assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, assetBeneficiaryExtend);
                } else if (dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.FILE_DOWNLOAD)) {
                    try {
                        String accessKey = RandomUtil.randomString(20);
                        String secretKey = RandomUtil.randomString(40);
                        // NOTE: use orderId as accessKey
                        minioAdminClient.addServiceAccount(accessKey, secretKey, null, JacksonUtils.json2map("{\n" +
                                " \"Version\": \"2012-10-17\",\n" +
                                " \"Statement\": [\n" +
                                "  {\n" +
                                "   \"Effect\": \"Allow\",\n" +
                                "   \"Action\": [\n" +
                                "    \"s3:GetBucketLocation\",\n" +
                                "    \"s3:GetObject\",\n" +
                                "    \"s3:ListBucket\"\n" +
                                "   ],\n" +
                                "   \"Resource\": [\n" +
                                "    \"arn:aws:s3:::" + dataProduct.getUserId() + "\",\n" +
                                "    \"arn:aws:s3:::" + dataProduct.getUserId() + "/" + dataProduct.getId() + "/*\"\n" +
                                "   ]\n" +
                                "  }\n" +
                                " ]\n" +
                                "}"), null, null, null);
                        AssetBeneficiaryExtend assetBeneficiaryExtend = AssetBeneficiaryExtend.builder().apiKey(String.format("%s@%s", accessKey, secretKey)).build();
                        assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, assetBeneficiaryExtend);
                    } catch (Exception e) {
                        log.error("添加minio accessKey失败", e);
                        throw new RestfulApiException("添加minio accessKey失败", e);
                    }
                } else
                    assetBeneficiaryRel = orderMapstruct.initAssetBeneficiaryRel(orderApprovalRecord, AssetBeneficiaryExtend.builder().build());
            }
            // 调用中心接口：更新订单审批记录、存储/更新资产获益人信息
            AssetBeneficiaryOderTableDTO assetBeneficiaryOderTableDTO = AssetBeneficiaryOderTableDTO.builder()
                    .orderApprovalRecord(orderApprovalRecord).assetBeneficiaryRel(assetBeneficiaryRel).build();
            SuccessResponse<Integer> integerResponse = hubOrderRemote.replaceIntoOrderApprovalAssetBeneficiary(assetBeneficiaryOderTableDTO);
        }
        // 发送消息
        sseMessageService.notifyOrderAuditMessage(orderApprovalRecord, updateStatus);
    }


    @Transactional(rollbackFor = Exception.class)
    public void recordKey(GenerateKeyRequest request, String result) {
        String orderId = request.getOrderId();
        log.info("seller key info orderId: {}", orderId);
        AssetBeneficiaryRel assetBeneficiaryRel = assetBeneficiaryRepository.findFirstByOrderIdOrderByCreateTimeDesc(orderId);
        AssetBeneficiaryExtend extend;
        if (assetBeneficiaryRel != null) {
            AssetBeneficiaryExtend beneficiaryExtend = assetBeneficiaryRel.getExtend();
            extend = beneficiaryExtend == null ? new AssetBeneficiaryExtend() : beneficiaryExtend;
        } else {
            extend = new AssetBeneficiaryExtend();

            assetBeneficiaryRel = AssetBeneficiaryRel.builder()
                    .id(UUID.randomUUID().toString().replace("-", ""))
                    .orderId(orderId)
                    // 可以不填充
                    .extend(extend)
                    .build();
        }
        log.info("current order key info: {}", extend);
        switch (request.getDeliveryType()) {
            case API -> extend.setApiKey(result);
            case FILE_DOWNLOAD -> extend.setAccessKey(result);
        }
        log.info("after order key info: {}", extend);

        assetBeneficiaryRel.setExtend(extend);
        assetBeneficiaryRepository.save(assetBeneficiaryRel);
    }

    /**
     * 赣州定制：授权 minio
     */
    public String authMinioApiKey(String productId, String productUserId) throws Exception {

        String accessKey = RandomUtil.randomString(20);
        String secretKey = RandomUtil.randomString(40);
        // NOTE: use orderId as accessKey
        minioAdminClient.addServiceAccount(accessKey, secretKey, null, JacksonUtils.json2map("{\n" +
                " \"Version\": \"2012-10-17\",\n" +
                " \"Statement\": [\n" +
                "  {\n" +
                "   \"Effect\": \"Allow\",\n" +
                "   \"Action\": [\n" +
                "    \"s3:GetBucketLocation\",\n" +
                "    \"s3:GetObject\",\n" +
                "    \"s3:ListBucket\"\n" +
                "   ],\n" +
                "   \"Resource\": [\n" +
                "    \"arn:aws:s3:::" + productUserId + "\",\n" +
                "    \"arn:aws:s3:::" + productUserId + "/" + productId + "/*\"\n" +
                "   ]\n" +
                "  }\n" +
                " ]\n" +
                "}"), null, null, null);
        return String.format("%s@%s", accessKey, secretKey);
//        return apiKey;
    }

    /**
     * 赣州定制：授权网关接口apiKey
     */
    public String authGatewayApiKey(String productName, String apiOwnerUsername, String gatewayServiceRouteId) {
        // 调用API网关接口：给买家分配AppKey
        String apiKey = gatewayWebApi.generateAPIKeyForOrder(apiOwnerUsername, gatewayServiceRouteId);
        log.debug("用户授权资产【{}】 apiKey ：{}", productName, apiKey);
        if (StringUtils.isBlank(apiKey)) {
            throw new RestfulApiException("获取API授权key失败");
        }
        return apiKey;
    }


    /**
     * @param deliveryType    交付类型
     * @param platformIdAsset 数据产品所在连接器id
     * @param dataProductVO   数据产品
     */
    public String generateApiKey(DeliveryType deliveryType, String platformIdAsset, DataProductVO dataProductVO, String orderId) throws Exception {
        String currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        if (StringUtils.equals(platformIdAsset, currentNodeId)) {
            // 购买当前节点
            switch (deliveryType) {
                case API -> {
                    String dataProductName = dataProductVO.getDataProductName();
                    String username = dataProductVO.getProvider().getUsername();
                    String gatewayServiceRouteId = dataProductVO.getGatewayServiceRouteId();
                    log.info("generate api key info dataProductName: [{}]  username: [{}] gatewayServiceRouteId: [{}]", dataProductName, username, gatewayServiceRouteId);
                    return authGatewayApiKey(dataProductName, username, gatewayServiceRouteId);
                }
                case FILE_DOWNLOAD -> {
                    log.info("generate minio access key info dataProductId: [{}]  dataUserId: [{}]", dataProductVO.getId(), dataProductVO.getUserId());
                    return authMinioApiKey(dataProductVO.getId(), dataProductVO.getUserId());
                }
                default -> throw new RestfulApiException("暂不支持【" + deliveryType + "】交付方式");
            }
        } else {
            // 跨节点购买
            GenerateKeyRequest request = GenerateKeyRequest.builder()
                    .productId(dataProductVO.getId())
                    .productUserId(dataProductVO.getUserId())
                    .username(dataProductVO.getProvider().getUsername())
                    .dataProductName(dataProductVO.getDataProductName())
                    .gatewayServiceRouteId(dataProductVO.getGatewayServiceRouteId())
                    .orderId(orderId)
                    .deliveryType(deliveryType)
                    .build();
            final CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            final NodeDTO.HubInfo hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo(company.getId());
            request.setHubInfo(hubInfo);
            request.setTargetCompanyId(dataProductVO.getProvider().getCompany().getId());
            request.setTargetNodeId(dataProductVO.getProvider().getRouterId());
            CommonResult<String> response = SpringUtil.getBean(EndpointRemote.class).generateKey(request);
            Assert.isTrue(response.isSuccess(), "跨节点资产key获取异常");
            return response.getData();
        }
    }

    /**
     * 卖方接收 买方日志上报回调
     * 买方 做订单累加 同时，交付登记
     * 合约 完成回调买方
     */
    public void buyerOrderDeliverySuccess(SceneAssetResp sceneAssetResp, String hashedCode, DeliveryType deliveryType) {

        SceneAssetApiReq.ExtData ext = sceneAssetResp.getExt();
        String buyerCompanyId = ext.getBuyerCompanyId();
        String sellerCompanyId = ext.getSellerCompanyId();
        String routeId = ext.getRouteId();
        String hash = ext.getDataAssetFileHash();
        String orderId = sceneAssetResp.getOrderId();

        CompanyDTO companyDTO = SpringUtil.getBean(CompanyService.class).localCompany(Long.valueOf(buyerCompanyId));
        // 买方日志上报
        // 买方 —— 订单交付累计+1  次数到了——通知修改状态、 同一节点情况下 只有一条订单记录
        AsyncManager.getInstance().execute(() -> {

            boolean changeStatus = false;
            TenantContext.setCurrentTenant("tenant_" + buyerCompanyId);
            OrderApprovalRecord record = orderRecordRepository.getReferenceById(orderId);
            log.info("订单【{}】资产【{}】总调用次数【{}】本次成功调用次数【{}】", orderId, record.getAssetId(), record.getAllowance(), 1);
            record.setSuccessfulUsage(record.getSuccessfulUsage().add(BigInteger.valueOf(1)));

            if (record.getClassify() == OrderClassify.NORMAL) {
                if (StringUtils.equalsIgnoreCase(record.getMeteringWay(), "按需") && record.getAllowance().compareTo(record.getSuccessfulUsage()) <= 0) {
                    log.info("当前订单【{}】资产【{}】累计达到调用次数", record.getId(), record.getAssetId());
                    record.setStatus("COMPLETED");
                    record.setUpdateTime(new Date());
                    changeStatus = true;
                    // 合约终止
                    try {
                        terminalContract(buyerCompanyId, List.of(orderId), null);
                    } catch (Exception e) {
                        log.error("通知合约终止异常：", e);
                    }
                    // 相应交付场景完成
                    SpringUtil.getBean(DeliveryServiceImpl.class).completeByOrderId(orderId);
                }
            }
            orderRecordRepository.save(record);

            if (record.getClassify() == OrderClassify.NORMAL) {

                BigInteger restCount = restCountCalc(record, changeStatus);

                if (!StringUtils.equals(buyerCompanyId, sellerCompanyId)) {
                    // 通知卖方
                    OrderSuccessDeliveryRequest request = OrderSuccessDeliveryRequest.builder().isSuccessDelivery(true).successfulUsage(record.getSuccessfulUsage()).orderId(orderId).build();
                    try {
                        request.setTargetNodeId(routeId);
                        request.setTargetCompanyId(Long.valueOf(sellerCompanyId));
                        request.setHubInfo(SpringUtil.getBean(CompanyService.class).getHubInfo(Long.valueOf(buyerCompanyId)));
                        CommonResult<Boolean> response = endpointRemote.orderDelivery(request);
                        Assert.isTrue(response.isSuccess(), "通知卖方订单交付次数累计失败");
                    } catch (Exception e) {
                        log.error("通知卖方订单交付次数累计失败 ", e);
                    }
                }

                // 交付登记
                SceneApiIdRelateReq deliveryApiIdRel = SceneApiIdRelateReq.builder()
                        .sceneId(sceneAssetResp.getDeliverId())
                        .dataAssetId(ext.getDataProductPlatformId())
                        .deliveryType(deliveryType)
                        .build();

                LogReportReq reportReq = LogReportReq.builder().assetRouteId(routeId).build();
                if (deliveryType == DeliveryType.FILE_DOWNLOAD) {
                    reportReq.setDataHash(hash);
                } else if (deliveryType == DeliveryType.API) {
                    reportReq.setDataHash(hashedCode);
                } else {
                    // todo
                    reportReq.setDataHash(hash);
                }

                SpringUtil.getBean(ThirdService.class).deliveryRegister(reportReq, deliveryApiIdRel, buyerCompanyId);

                // 日志回调流通利用平台
                OderRecordExtend extend = record.getExtend();
                String serviceNodeUrl = extend.getServiceNodeUrl();
                String tradingStrategyCode = extend.getTradingStrategyCode();
                logoReportBusiness(serviceNodeUrl, changeStatus, restCount, orderId, tradingStrategyCode, routeId, companyDTO);
            }
        });
    }

    private BigInteger restCountCalc(OrderApprovalRecord record, boolean changeStatus) {
        if (StringUtils.equalsIgnoreCase(record.getMeteringWay(), "按需")) {
            if (changeStatus) {
                return new BigInteger("0");
            } else {
                if (record.getAllowance().subtract(record.getSuccessfulUsage()).longValue() < 0) {
                    return new BigInteger("0");
                }
                return new BigInteger(record.getAllowance().subtract(record.getSuccessfulUsage()).toString());
            }
        }
        return null;
    }

    /**
     * 上报回调业务平台
     */
    private void logoReportBusiness(String serviceNodeUrl, boolean changeStatus, BigInteger restCount, String orderId, String tradingStrategyCode, String assetRouteId, CompanyDTO companyDTO) {
        OrderDelivery orderDelivery = new OrderDelivery();
        orderDelivery.setOrderId(orderId);
        orderDelivery.setDeliveryTime(String.valueOf(System.currentTimeMillis()));
        orderDelivery.setRestCount(restCount);
        orderDelivery.setUrl(serviceNodeUrl);
        orderDelivery.setCurrentNodeId(companyDTO.getNodeId());

        orderDelivery.setDeliveryLogReceive(new OrderDelivery.DeliveryLog(companyDTO.getNodeId(), "接收方"));
        orderDelivery.setDeliveryLogExport(new OrderDelivery.DeliveryLog(assetRouteId, "输出方"));


        try {
            if (changeStatus) {
                TradingStrategyDelivery strategyDelivery = new TradingStrategyDelivery();
                strategyDelivery.setTradingStrategyCode(tradingStrategyCode);
                strategyDelivery.setDeliveryStatus(4);
                strategyDelivery.setUrl(serviceNodeUrl);
                strategyDelivery.setCurrentNodeId(companyDTO.getNodeId());

                log.debug("订单状态完成 {}, {}", serviceNodeUrl, JSONUtil.toJsonStr(strategyDelivery));
                final ShuhanResponse<Boolean> result = nodeRemoteService.deliveryStatusReport(strategyDelivery);

                if (result == null || !result.isSuccess()) {
                    log.error("业务节点【{}】订单状态变更完成异常: {}", serviceNodeUrl, result != null ? result.getMessage() : "订单状态变更完成失败");
                    throw new RestfulApiException("订单状态变更完成失败");
                }
            }
        } catch (Exception e) {
            log.error("订单状态通知完成失败: ", e);
        }

        try {
            ShuhanResponse<Boolean> result = nodeRemoteService.deliveryLogReport(orderDelivery);
            log.debug("日志上报回调流通利用平台结果 {}", result);

            if (result == null || !result.isSuccess()) {
                log.error("日志上报回调流通利用平台【{}】日志回调流通利用平台异常: {}", serviceNodeUrl, result != null ? result.getMessage() : "日志回调流通利用平台失败");
                throw new RestfulApiException("日志回调流通利用平台失败");
            }
        } catch (Exception e) {
            log.error("日志回调流通利用平台失败: ", e);
        }
    }


    /**
     * 卖方同步订单问题：买方已经在交付了？我还没有创建好
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderDelivery(OrderSuccessDeliveryRequest request) {
        // todo 补偿机制 —— 多线程加锁
        if (request.getIsSuccessDelivery()) {
            orderRecordRepository.findById(request.getOrderId()).ifPresent(order -> {
                // 直接更新买方的使用次数
                order.setSuccessfulUsage(request.getSuccessfulUsage());
                orderRecordRepository.save(order);
            });
        } else {
            // 绑定交付
            OrderDeliveryRelation relation = OrderDeliveryRelation.builder()
                    .orderId(request.getOrderId())
                    .deliverySceneId(request.getDeliverySceneId())
                    .assetName(request.getAssetName())
                    .assetId(request.getAssetId())
                    .deliveryType(request.getDeliveryType().name())
                    .extend("{}")
                    .createTime(new Date())
                    .build();
            orderDeliveryRepository.save(relation);
        }
    }

}
