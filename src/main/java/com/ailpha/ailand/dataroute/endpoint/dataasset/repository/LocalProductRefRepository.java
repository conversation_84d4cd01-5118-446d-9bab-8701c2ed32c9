package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.LocalProductRef;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

public interface LocalProductRefRepository extends JpaRepository<LocalProductRef, String>, QuerydslPredicateExecutor<LocalProductRef>, JpaSpecificationExecutor<LocalProductRef> {

    LocalProductRef findFirstByAssetId(String assetId);

    LocalProductRef findFirstByProductPlatformId(String productPlatformId);

}
