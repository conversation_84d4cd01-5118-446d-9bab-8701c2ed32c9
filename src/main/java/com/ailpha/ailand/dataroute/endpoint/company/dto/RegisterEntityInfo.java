package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class RegisterEntityInfo {
    String organizationName;
    // 法人相关信息
    @Schema(description = "法人名称", example = "张三")
    private String legalRepresentativeName;
    String partyContactInfo;
    @Schema(description = "法人证件类型", example = "1")
    private String legalRepresentativeIdType;
    @Schema(description = "法人证件号码", example = "********90********")
    private String legalRepresentativeIdNumber;
    @Schema(description = "法人证件有效期 开始", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityStartDate;
    @Schema(description = "法人证件有效期 结束", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdValidityEndDate;
    @Schema(description = "法定代表人实名认证等级")
    private String legalRepresentativeAuthLevel;
    @Schema(description = "实名认证方式")
    private String authType;
    @Schema(description = "认证时间")
    String authTime;
    @Schema(description = "法人注册地址")
    private String registeredAddress;
    @Schema(description = "行业类型")
    private String industryType;
    @Schema(description = "经营期限起始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessStartDate;
    @Schema(description = "经营期限截止")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessEndDate;
    // 注册日期
    @Schema(description = "注册日期", example = "2023-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date registrationDate;
    // 注册资本
    @Schema(description = "注册资本", example = "100000000")
    String registeredCapital;
    // 经营范围
    @Schema(description = "经营范围", example = "软件开发")
    String businessScope;
    @Schema(description = "营业执照", example = "xxx/xxxx")
    String businessLicenseLocalUrl;
    String businessLicenseRemoteUrl;
    @Schema(description = "授权书", example = "xxx/xxxx")
    String authorizationLetterLocalUrl;
    String authorizationLetterRemoteUrl;
    // 身份状态
    @Schema(description = "身份状态", example = "0")
    String identityStatus;
    
    // 开户行
    @Schema(description = "开户行", example = "中国银行")
    private String bankName;
    
    // 银行卡号
    @Schema(description = "银行卡号", example = "6225********9012")
    private String bankAccount;
    
    // 传真
    @Schema(description = "传真", example = "010-********")
    private String fax;
    
    // 邮编
    @Schema(description = "邮编", example = "100000")
    private String postalCode;
    
    // 银行地址
    @Schema(description = "银行地址", example = "北京市朝阳区XX路XX号")
    private String bankAddress;
    
    // 户名
    @Schema(description = "户名", example = "XX有限公司")
    private String accountName;
}
