package com.ailpha.ailand.dataroute.endpoint.order.remote.request;

import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRequest;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDelivery extends ServiceNodeRequest {

    private String orderId;

    private String deliveryTime;

    private BigInteger restCount;

    DeliveryLog deliveryLogReceive;

    DeliveryLog deliveryLogExport;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DeliveryLog {
        String type = "连接器";
        String deliveryRole;
        String platformId;

        public DeliveryLog(String platformId, String deliveryRole) {
            this.platformId = platformId;
            this.deliveryRole = deliveryRole;
        }
    }

}
