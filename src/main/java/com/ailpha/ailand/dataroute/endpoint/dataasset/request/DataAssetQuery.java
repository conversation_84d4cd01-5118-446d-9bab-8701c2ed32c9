package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据集市数据资产列表筛选条件")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetQuery
//        extends Page
{
    @Schema(description = "资产类型")
    AssetType type;
    @Schema(description = "资产名称")
    String assetName;

    @Data
    public static class DataTypeFilter {
        @Schema(description = "数据类型：结构化数据、非结构化数据")
        DataType dataType;
        @Schema(description = "数据类型1：数据集 文件 图像 模型")
        String dataType1;
    }

    @Schema(description = "数据类型过滤")
    List<DataTypeFilter> dataTypeFilters;

    @Schema(description = "数据接入方式：API、数据库、文件")
    SourceType source;

    @Schema(description = "数源单位")
    String providerOrg;
    @Schema(description = "行业分类code")
    String industry;
    @Schema(description = "行业分类code列表")
    List<String> industryClassifyList;
    @Schema(description = "敏感等级")
    String sensitiveLevel;
    @Schema(description = "交付方式")
    DeliveryMode deliveryModes;
    @Schema(description = "数据资产用途")
    MPCPurpose mpcPurpose;
    @Schema(description = "审批状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
    @JsonIgnore
    DataAssetPrepareStatus dataAssetPrepareStatus;
    @Schema(description = "上下架状态")
    PushStatus pushStatus;
    @Schema(description = "上下架状态: 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销 5 业务节点待审批 6 业务节点通过 7 业务节点拒绝")
    String publishStatus;
    @JsonIgnore
    String routeId;
    @JsonIgnore
    String userId;
    @JsonIgnore
    String userName;
    /**
     * 资产ID列表（非必填，精确匹配）
     */
    @JsonIgnore
    List<String> assetIds;
    @Schema(description = "业务平台唯一标识（上架或者上架更新的时候需要传，上架到哪个业务平台）")
    String businessPlatformUniqueNo;
    /**
     * 是否删除
     */
    @Builder.Default
    Boolean isDelete = false;
    @Builder.Default
    Boolean currentRouter = true;
    @Schema(description = "是否只查询当前用户的数据资产 MPC隐私求交有这个限制")
    @Builder.Default
    Boolean currentUser = false;
    @Builder.Default
    @JsonProperty("$page")
    long num = 1;
    @Builder.Default
    @JsonProperty("$size")
    long size = 10;

}
