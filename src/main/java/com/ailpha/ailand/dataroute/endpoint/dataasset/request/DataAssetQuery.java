package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据集市数据资产列表筛选条件")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetQuery
//        extends Page
{
    @Schema(description = "资产类型")
    AssetType type;
    @Schema(description = "资产名称")
    String assetName;
    @Schema(description = "数据类型：结构化数据、非结构化数据、模型")
    DataType dataType;
    @Schema(description = "数据接入方式：API、数据库、文件")
    SourceType source;

    @Schema(description = "数源单位")
    String providerOrg;
    @Schema(description = "行业分类code")
    String industry;
    @Schema(description = "行业分类code列表")
    List<String> industryClassifyList;
    @Schema(description = "敏感等级")
    String sensitiveLevel;
    @Schema(description = "交付方式")
    DeliveryMode deliveryModes;
    @Schema(description = "交付方式：01-文件传输,02-数据流传输,03-API传输")
    String deliveryMethod;
    @Schema(description = "数据资产用途")
    MPCPurpose mpcPurpose;
    @Schema(description = "审批状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
    @JsonIgnore
    DataAssetPrepareStatus dataAssetPrepareStatus;
    @Schema(description = "上下架状态")
    PushStatus pushStatus;
    @Schema(description = "上下架状态: 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销")
    String publishStatus;
    @JsonIgnore
    String routeId;
    @JsonIgnore
    String userId;
    @JsonIgnore
    String userName;
    /**
     * 资产ID列表（非必填，精确匹配）
     */
    @JsonIgnore
    List<String> assetIds;
    @Schema(description = "业务平台唯一标识（上架或者上架更新的时候需要传，上架到哪个业务平台）")
    String businessPlatformUniqueNo;
    /**
     * 是否删除
     */
    @Builder.Default
    Boolean isDelete = false;
    @Builder.Default
    Boolean currentRouter = true;
    @Schema(description = "是否只查询当前用户的数据资产 MPC隐私求交有这个限制")
    @Builder.Default
    Boolean currentUser = false;
    @Builder.Default
    @JsonProperty("$page")
    long num = 1;
    @Builder.Default
    @JsonProperty("$size")
    long size = 10;

    @Schema(description = "来源平台内部标识")
    String outerProductId;
    @Schema(description = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他")
    String productType;
    @Schema(description = "地域分类，详见附录《全国区划表》")
    String productRegion;
    @Schema(description = "数据主体：01-个人信息,02-企业数据,03-公共数据")
    String dataSubject;
    @Schema(description = "主体 id")
    String entityId;
    @Schema(description = "主体编码")
    String entityCode;
    @Schema(description = "唯一编码")
    String productCode;
    @Schema(description = "产品状态 01 待登记，02 登记，03 撤销")
    String productStatus;
    @Schema(description = "来源平台内部标识")
    String outerResourceId;
    @Schema(description = "资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据")
    String resourceType;
    @Schema(description = "数据来源：01-原始取得,02-收集取得,03-交易取得，04-其他")
    String dataSource;
    @Schema(description = "资源状态：01 待登记 02 已登记 03 已撤销")
    String resourceStatus;
}
