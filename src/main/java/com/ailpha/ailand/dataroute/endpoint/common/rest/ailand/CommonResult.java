package com.ailpha.ailand.dataroute.endpoint.common.rest.ailand;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonResult<T> implements IResult<T> {

    @Serial
    private static final long serialVersionUID = 1581651691719242300L;

    @Setter
    private int code = InternalReturnCode.SUCCESS.getCode();

    @Setter
    @Getter
    private String message = "成功";

    /**
     * 数瀚接口专用
     */
    @Getter
    @Setter
    private String msg;

    @Setter
    @Getter
    private T data;

    private boolean success;

    public CommonResult() {
    }

    protected CommonResult(int code, String message, T data, boolean success) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
    }

    public CommonResult(T data) {
        this.data = data;
        if (data == null) {
            code = InternalReturnCode.FAIL.getCode();
            message = "失败!";
        }
    }

    public CommonResult(String message) {
        this.code = InternalReturnCode.FAIL.getCode();
        this.message = message;
    }

    public CommonResult(T data, String message) {
        this.data = data;
        this.message = message;
    }

    public CommonResult(T data, IReturnCode returnCode) {
        this.data = data;
        this.code = returnCode.getCode();
    }

    public CommonResult(IReturnCode returnCode, String message) {
        this.message = message;
        this.code = returnCode.getCode();
    }

    public static CommonResult<Void> SUCCESS() {
        CommonResult<Void> response = new CommonResult<>();
        response.setCode(InternalReturnCode.SUCCESS.getCode());
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data) {
        CommonResult<T> response = new CommonResult<>();
        response.setCode(InternalReturnCode.SUCCESS.getCode());
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data, String message) {
        CommonResult<T> response = SUCCESS(data);
        response.setMessage(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(String message, IReturnCode code) {
        CommonResult<T> response = new CommonResult<T>();
        response.setCode(code.getCode());
        response.setMessage(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(IReturnCode errorCode, String message, T data) {
        return new CommonResult<T>(errorCode.getCode(), message, data, false);
    }

    public static <T> CommonResult<T> FAIL() {
        return FAIL("", InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(String message) {
        return FAIL(message, InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(T data) {
        CommonResult<T> response = FAIL("失败");
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(T data, String message) {
        CommonResult<T> response = FAIL(message);
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(Exception e) {
        return FAIL(e.getMessage());
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public boolean isSuccess() {
        return success || InternalReturnCode.SUCCESS.getCode().equals(code);
    }

    public boolean isShuHanSuccess() {
        return success || code == 200;
    }

}
