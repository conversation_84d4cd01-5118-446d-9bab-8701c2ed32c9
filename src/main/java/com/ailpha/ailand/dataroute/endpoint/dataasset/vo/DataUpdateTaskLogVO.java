package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品更新任务记录")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataUpdateTaskLogVO {
    @Schema(description = "更新类型：单次、定时、手动")
    UpdateWay updateWay;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm")
    @Schema(description = "更新实现，任务开始时间")
    Date updateTime;

    @Schema(description = "状态：已创建、更新中、更新成功、更新失败")
    DataUpdateStatus updateStatus;

    @Schema(description = "日志")
    String taskLog;
}
