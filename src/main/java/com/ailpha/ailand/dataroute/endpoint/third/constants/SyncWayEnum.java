package com.ailpha.ailand.dataroute.endpoint.third.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @since 2020-04-09 15:31
 **/
public enum SyncWayEnum {

    FULL("FULL", 2, "全量更新"),
    INCREMENT("INCREMENT", 1, "增量更新");

    private final String type;
    private final Integer code;
    private final String desc;


    SyncWayEnum(String type, Integer code, String desc) {
        this.type = type;
        this.code = code;
        this.desc = desc;
    }

    public String getType() {
        return this.type;
    }

    @JsonValue
    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static SyncWayEnum getByType(String type) {
        SyncWayEnum[] var1 = values();
        for (SyncWayEnum item : var1) {
            if (Objects.equals(item.getType(), type)) {
                return item;
            }
        }
        return null;
    }

    @JsonCreator
    public static SyncWayEnum getByCode(Integer code) {
        SyncWayEnum[] var1 = values();
        for (SyncWayEnum item : var1) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

}
