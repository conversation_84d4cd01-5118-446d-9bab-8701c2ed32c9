package com.ailpha.ailand.dataroute.endpoint.plugin.vo;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @author: yuwenping
 * @date: 2025/5/14 16:44
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlockchainListResponse {
    private Long id;
    private String name;
    private String moduleType;
    private String description;
    private int status;
    private LocalDateTime createTime;
    private BlockchainPluginTypeEnum type;
    private String url;
}
