package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneSaveReq {

    @Schema(description = "用户id")
    String createUser;

    @Schema(description = "交付方式 ", requiredProperties = {"API", "FILE_DOWNLOAD", "MPC_CIPHER_TEXT_COMPUTE", "MPC_CIPHER_TEXT_COMPUTE", "MPC_PRIVATE_SET_INTERSECTION", "TEE_OFFLINE", "TEE_ONLINE"})
    DeliveryType deliveryType;

    @Schema(description = "数字证书 —— 合规场景id")
    String digitalSceneId;

    @Schema(description = "数字证书 —— 合规场景名称")
    String digitalSceneName;

    @Schema(description = "数据资产id")
    List<String> dataAssetIdList;
    @Schema(description = "合约配置")
    String contract;
}
