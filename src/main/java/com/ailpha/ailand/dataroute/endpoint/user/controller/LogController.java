package com.ailpha.ailand.dataroute.endpoint.user.controller;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpModule;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpModule;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpType;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.LogService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.LogResponse;
import com.ailpha.ailand.dataroute.endpoint.user.vo.QueryLogRequest;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("log")
@RequiredArgsConstructor
@Tag(name = "操作日志")
public class LogController {

    private final LogService logService;

    @PostMapping("page")
    @Operation(summary = "个人操作日志")
    public SuccessResponse<List<LogResponse>> list(@RequestBody QueryLogRequest request) {
        return logService.query(request);
    }

    @Data
    private static class OpModuleSelectorsVO {

        private List<String> opModules;

        private Boolean isAdmin;

        private String userName;

    }

    //获取操作日志的操作类型和操作模块
    @GetMapping(value = "/module")
    @Operation(summary = "获取操作日志的操作类型和操作模块")
    public SuccessResponse<OpModuleSelectorsVO> getOpModules() {
        List<String> opModules;
        opModules = Arrays.stream(InternalOpModule.values())
                .filter(one -> one.hasPermission(LoginContextHolder.currentUserRole()))
                .map(OpModule::bizName).collect(Collectors.toList());
        OpModuleSelectorsVO vo = new OpModuleSelectorsVO();
        vo.setOpModules(opModules);
        vo.setIsAdmin(LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN));
        vo.setUserName(LoginContextHolder.currentUser().getRealName());
        return SuccessResponse.success(vo).build();
    }

    //获取操作日志的操作类型和操作模块
    @GetMapping(value = "/types")
    public SuccessResponse<List<String>> getOpTypes(String opModule) {
        if (StringUtils.isEmpty(opModule)) {
            return SuccessResponse.success(List.of("")).build();
        }
        OpModule moduleEnum = InternalOpModule.getByName(opModule);
        if (moduleEnum == null) {
            return SuccessResponse.success(List.of("")).build();
        }
        List<OpType> opTypes = new ArrayList<>();
        for (OpType opType : InternalOpType.values()) {
            if (opType.module().equals(moduleEnum)) {
                opTypes.add(opType);
            }
        }
        return SuccessResponse.success(opTypes.stream().map(OpType::bizName).collect(Collectors.toList())).build();
    }
}
