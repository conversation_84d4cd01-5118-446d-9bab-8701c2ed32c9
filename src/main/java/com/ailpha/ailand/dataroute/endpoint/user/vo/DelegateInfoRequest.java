package com.ailpha.ailand.dataroute.endpoint.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class DelegateInfoRequest {
    // 经办人相关信息
    @Schema(description = "经办人姓名", example = "李四")
    private String delegateName;
    @Schema(description = "经办人证件类型", example = "ID_CARD")
    private String delegateIdType;
    @Schema(description = "经办人证件号码", example = "110101199001011234")
    private String delegateIdNumber;
    @Schema(description = "经办人证件有效期-开始日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityStartDate;
    @Schema(description = "经办人证件有效期-结束日期", example = "2025-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityEndDate;
    @Schema(description = "经办人联系方式", example = "13812345678")
    private String delegatePhone;
    @Schema(description = "经办人电子邮箱", example = "<EMAIL>")
    private String delegateEmail;
    @Schema(description = "经办人实名认证等级", example = "LEVEL_2")
    private String delegateAuthLevel;
    @Schema(description = "经办人实名认证方式", example = "FACE")
    private String delegateAuthType;
    @Schema(description = "经办人注册地址", example = "北京市朝阳区XX路XX号")
    private String delegateAddress;
    @Schema(description = "经办人行业类型", example = "软件和信息技术服务业")
    private String delegateIndustryType;
    @Schema(description = "委办任务范围", example = "负责公司业务对接及相关手续办理")
    private String delegateTaskScope;
    @Schema(description = "委办授权期限起始", example = "2024-01-01")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthStartDate;
    @Schema(description = "委办授权期限截止", example = "2024-12-31")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthEndDate;
    @Schema(description = "经办人备注", example = "特别授权事项说明")
    private String delegateRemark;
    @Schema(description = "授权书", example = "xxx/xxxx")
    String authorizationLetterLocalUrl;
    String authorizationLetterRemoteUrl;
}
