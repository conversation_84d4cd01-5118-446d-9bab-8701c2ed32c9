package com.ailpha.ailand.dataroute.endpoint.third.mapper;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.third.request.TEEDataset;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * 2024/12/10
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class TEEDatasetMapper {

    @Autowired
    private RouterService routerService;
    @Autowired
    private AiLandProperties aiLandProperties;
    @Autowired
    private FilesStorageServiceImpl filesStorageService;

    @Mapping(target = "assetId", source = "dataProduct.id")
    @Mapping(target = "name", source = "dataProduct.dataProductName")
    @Mapping(target = "type", source = "deliveryMode")
    @Mapping(target = "dataSchema", source = "dataProduct.dataExt.dataSchema")
    @Mapping(target = "filePath", ignore = true)
    @Mapping(target = "debugDataSource", source = "dataProduct.dataExt.debugDataSource")
    @Mapping(target = "debugFile", ignore = true)
    @Mapping(target = "separator", source = "dataProduct.dataExt.separator")
    @Mapping(target = "hasHeader", source = "dataProduct.dataExt.hasHeader")
    @Mapping(target = "purposeList", ignore = true)
    @Mapping(target = "url", source = "dataProduct.dataExt.apiSourceMetadata.url")
    @Mapping(target = "method", source = "dataProduct.dataExt.apiSourceMetadata.method")
    @Mapping(target = "params", source = "dataProduct.dataExt.apiSourceMetadata.params")
    @Mapping(target = "body", source = "dataProduct.dataExt.apiSourceMetadata.body")
    @Mapping(target = "bodyType", source = "dataProduct.dataExt.apiSourceMetadata.bodyType")
    @Mapping(target = "headers", source = "dataProduct.dataExt.apiSourceMetadata.headers")
    @Mapping(target = "response", source = "dataProduct.dataExt.apiSourceMetadata.response")
    @Mapping(target = "dataPath", source = "dataProduct.dataExt.apiSourceMetadata.dataPath")
    @Mapping(target = "userId", source = "dataProduct.userId") // 创建合约是数由器的用户id，获取用户信息还是数由器的用户id，那就用数由器用户id
    @Mapping(target = "userName", source = "dataProduct.username")
    @Mapping(target = "routerId", source = "dataProduct.provider.company.nodeId")
    @Mapping(target = "executorUrl", ignore = true)
    @Mapping(target = "ext", ignore = true)
    public abstract TEEDataset dataAssetToTeeDataset(DataProduct dataProduct, DeliveryMode deliveryMode);

    @AfterMapping
    void fillOtherInfo(DataProduct dataProduct, DeliveryMode deliveryMode, @MappingTarget TEEDataset teeDataset) {
        // 调试数据文件流
        if (!ObjectUtils.isEmpty(dataProduct.getDataExt().getDebugDataPath())) {
            String debugDataPath = dataProduct.getDataExt().getDebugDataPath();
            File debugFile = Paths.get(debugDataPath).toFile();
            if (debugFile.exists()) {
                teeDataset.setDebugFile(new FileSystemResource(debugFile));
            }
        }
        // 交付处理文件来源：复制源文件
        if (!ObjectUtils.isEmpty(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath())) {
            Path dataAssetFilePath = Paths.get(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
            File dataAssetFile = dataAssetFilePath.toFile();
            if (dataAssetFile.exists()) {
                Path teeFilePath = filesStorageService.getRootPath()
                        .resolve("dataProduct")
                        .resolve(dataProduct.getUserId())
                        .resolve(String.format("TEE-%s", dataAssetFile.getName()));
                teeFilePath = filesStorageService.saveTmpData2File(dataAssetFilePath, teeFilePath, false);
                teeDataset.setFilePath(teeFilePath.toFile().getAbsolutePath());
            }
        }
        // 业务领域
        JSONObject object = JSONUtil.parseObj(dataProduct.getDataExt().getIndustry1());
        teeDataset.setPurposeList(object.getStr("industryName"));
        // 前置机地址（ip端口）
        teeDataset.setExecutorUrl(String.format("%s:%s", routerService.currentRouteVirtualIp(), aiLandProperties.getExecutorServer().getPort()));
        // 扩展字段
        TEEDataset.Extend ext = new TEEDataset.Extend();
        ext.setSource(dataProduct.getSourceType());
        teeDataset.setExt(ext);
    }
}
