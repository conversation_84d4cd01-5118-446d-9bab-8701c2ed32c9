package com.ailpha.ailand.dataroute.endpoint.third.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * @Author: jackie.liu
 * @Date: 2021/6/7 9:03 上午
 */
public enum DataSourceTypeEnums {

    MYSQL("mysql", "mysql数据源"),

    ORACLE("oracle", "oracle数据源"),

    HDFS("hdfs", "hdfs数据源"),

    HIVE("hive", "hive数据源"),

    ODPS("odps", "odps数据源"),

    POSTGRESQL("postgresql", "postgresql数据源"),

    GAUSSDB200("gaussdb200", "gaussdb200数据源"),

    DM("dm", "达梦数据源"),

    KINGBASE("kingbase", "人大金仓数据源"),

    SERVER_FILE("serverfile", "前置机文件上传");

    private final String code;

    private final String desc;

    private DataSourceTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    @JsonCreator
    public static DataSourceTypeEnums getItem(String code) {
        for (DataSourceTypeEnums item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
