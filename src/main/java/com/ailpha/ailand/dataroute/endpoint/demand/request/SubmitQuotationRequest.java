package com.ailpha.ailand.dataroute.endpoint.demand.request;

import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class SubmitQuotationRequest {
    @Schema(description = "需求ID")
    Integer demandId;
    @Schema(hidden = true)
    String userId;
    @Schema(description = "报价")
    String price;
    @Schema(description = "解决方案文件ID")
    String solutionFileId;
    @Schema(description = "关联数据集")
    List<DataAssetDTO> dataAssets;
    @Schema(description = "报价")
    String description;
    @Schema(hidden = true)
    DemandSideDTO demandSide;
}
