package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DemandListDTO {
    Integer id;

    @Schema(description = "需求标题")
    private String title;

    @Schema(description = "数据描述")
    private String description;

    @Schema(description = "预算范围")
    private String budgetRange;
    @Schema(description = "预算范围")
    String dataType;
    List<String> tags;
    @Schema(description = "过期时间")
    private String expireDate;
    @Schema(description = "期望交付方式")
    private String expectedDeliveryMethod;
    String dataScale;
    @Schema(description = "质量要求")
    String qualityRequirements;

    @Schema(description = "需求主体名称")
    private String demandSubjectName;
    @Schema(description = "状态")
    private String status;
    @Schema(description = "是否关注")
    private Boolean collected;

}
