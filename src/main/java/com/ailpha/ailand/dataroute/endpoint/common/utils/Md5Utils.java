package com.ailpha.ailand.dataroute.endpoint.common.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * @author: sunsas.yu
 * @date: 2024/11/28 18:31
 * @Description:
 */
public class Md5Utils {
    private static final String MD5 = "MD5";

    public Md5Utils() {
    }

    public static String encrypt(String plainText) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance(MD5);
        byte[] messageByte = plainText.getBytes();
        byte[] md5Byte = md.digest(messageByte);
        StringBuilder sb = new StringBuilder();
        byte[] var5 = md5Byte;
        int var6 = md5Byte.length;

        for(int var7 = 0; var7 < var6; ++var7) {
            byte b = var5[var7];
            int number = b & 255;
            String str = Integer.toHexString(number);
            if (str.length() == 1) {
                sb.append("0");
            }

            sb.append(str);
        }

        return sb.toString();
    }

    public static String convert(String str) {
        char[] a = str.toCharArray();

        for(int i = 0; i < a.length; ++i) {
            a[i] = (char)(a[i] ^ 116);
        }

        return new String(a);
    }

    /**
     * 使用SHA-256计算哈希
     */
    public static String sha256Hash(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256计算失败", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
