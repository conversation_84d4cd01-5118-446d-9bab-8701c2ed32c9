package com.ailpha.ailand.dataroute.endpoint.dataasset.service;


import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAsset;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.FileSourceMetadata;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetPublishDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.AssetReportDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.BusinessReportDTO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;

/**
 * 交易所相关 service
 *
 * <AUTHOR>
 */
public interface TraderService {


    /**
     * 数据资产审批通过后 —— 注册登记
     *
     * @param assetId 数据资产
     */
    DataAsset dataAssetRegisterTrader(String assetId);


    /**
     * 新增插件配置后：数商交易所登录
     */
    void businessReportingTrader(PluginDetail pluginDetail);


    /**
     * 功能描述: 修改数据资产信息上报，由于上下架和修改要调用交易所里不同的接口，需要区分
     *
     * @param isUpOrOffLine isUpOrOffLine 是否为修改上下架
     * @param isUp          isUp 是否上架
     * @param assetId     dataResItem 修改的数据
     * @return void
     * <AUTHOR>
     * @date 2024/11/20 20:02
     */
    void dataAssetUpdateTrader(Boolean isUpOrOffLine, Boolean isUp, String assetId);

    /**
     * 功能描述: 手动调用交易所激活连接器
     *
     * @param businessReportDTO businessReportDTO
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2024/11/22 19:24
     */
    Boolean manualActiveTrader(BusinessReportDTO businessReportDTO);

    Boolean manualDataAssetPublishTrader(AssetPublishDTO reportDTO);

    Boolean manualDataAssetRegisterTrader(AssetReportDTO reportDTO);

    Boolean manualDataAssetUpdateTrader(AssetReportDTO reportDTO);

    void deleteFileSource(String userId, FileSourceMetadata fileSourceMetadata);
}
