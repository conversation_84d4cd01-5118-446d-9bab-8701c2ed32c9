package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/4/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeListVO implements Serializable {

    @Schema(description = "业务节点id")
    String serviceNodeId;

    @Schema(description = "业务节点名称")
    String serviceNodeName;

    @Schema(description = "业务节点位置")
    String serviceNodeLocation;
}
