package com.ailpha.ailand.dataroute.endpoint.order.domain;

import com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/11/16
 */
@Data
@Builder
@Entity
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_order_delivery_relation")
@AllArgsConstructor
public class OrderDeliveryRelation implements Serializable {

    @Id
    @UUID32
    @Column(name = "id")
    private String id;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    @Column(name = "order_id")
    private String orderId;


    @Column(name = "delivery_scene_id")
    private String deliverySceneId;

    /**
     * 资产名称
     */
    @Column(name = "asset_name")
    private String assetName;

    /**
     * 资产id
     */
    @Column(name = "asset_id")
    private String assetId;

    /**
     * 交付方式
     */
    @Column(name = "delivery_type")
    private String deliveryType;

    /**
     * 扩展字段json
     */
    @Schema(description = "扩展字段json")
    @Column(name = "extend")
    String extend;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

}