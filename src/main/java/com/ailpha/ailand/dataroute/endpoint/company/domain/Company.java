package com.ailpha.ailand.dataroute.endpoint.company.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "t_company")
@Schema(description = "企业实体")
public class Company {
    @Id
    @Schema(description = "企业ID")
    private Long id;

    @Column(name = "organization_name")
    @Schema(description = "企业名称")
    private String organizationName;

    @Column(name = "credit_code")
    @Schema(description = "统一社会信用代码")
    private String creditCode;

    @Column(name = "business_license")
    @Schema(description = "营业执照")
    private String businessLicense;

    @Enumerated(EnumType.STRING)
    @Schema(description = "企业状态")
    private CompanyStatus status = CompanyStatus.NOT_REVIEW;
    @Column(name = "company_id")
    String companyId;
    @Column(name = "node_id")
    @Schema(description = "节点ID")
    String nodeId;

    @Enumerated(EnumType.STRING)
    @Column(name = "access_type")
    @Schema(description = "接入主体类型")
    private AccessType accessType = AccessType.LEGAL_PERSON;

    @Column(name = "legal_representative_name")
    @Schema(description = "法定代表人姓名")
    private String legalRepresentativeName;

    @Column(name = "legal_representative_id_type")
    @Schema(description = "法定代表人证件类型")
    private String legalRepresentativeIdType;

    @Column(name = "legal_representative_id_number")
    @Schema(description = "法定代表人证件号码")
    private String legalRepresentativeIdNumber;

    @Column(name = "legal_representative_id_expiry")
    @Schema(description = "法定代表人证件有效期")
    private Date legalRepresentativeIdExpiry;

    @Column(name = "legal_representative_auth_level")
    @Schema(description = "法定代表人实名认证等级")
    private String legalRepresentativeAuthLevel;

    @Column(name = "auth_method")
    @Schema(description = "认证方式")
    private String authMethod;

    // 认证时间
    @Column(name = "auth_date")
    String authDate;
    // 身份状态
    @Column(name = "identity_status")
    String identityStatus;

    @Column(name = "registration_address")
    @Schema(description = "注册地址")
    private String registrationAddress;

    @Column(name = "industry_type")
    @Schema(description = "行业类型")
    private String industryType;

    @Column(name = "business_start_date")
    @Schema(description = "经营期限起始日期")
    private Date businessStartDate;

    @Column(name = "business_end_date")
    @Schema(description = "经营期限截止日期")
    private Date businessEndDate;
    // 注册日期
    @Column(name = "registration_date")
    String registrationDate;
    // 注册金额
    @Column(name = "registered_capital")
    String registeredCapital;
    // 经营范围
    @Column(name = "business_scope")
    String businessScope;
    // 授权书
    @Column(name = "authorization_letter")
    String authorizationLetter;

    @Column(name = "delegate_name")
    @Schema(description = "经办人姓名")
    private String delegateName;

    @Column(name = "delegate_id_type")
    @Schema(description = "经办人证件类型")
    private String delegateIdType;

    @Column(name = "delegate_id_number")
    @Schema(description = "经办人证件号码")
    private String delegateIdNumber;

    @Column(name = "delegate_id_expiry")
    @Schema(description = "经办人证件有效期")
    private Date delegateIdExpiry;

    @Column(name = "delegate_institution")
    @Schema(description = "经办人所属机构")
    private String delegateInstitution;

    @Column(name = "delegate_institution_code")
    @Schema(description = "经办人所属机构统一社会信用代码")
    private String delegateInstitutionCode;

    @Column(name = "delegate_contact")
    @Schema(description = "经办人联系方式")
    private String delegateContact;

    @Column(name = "delegate_email")
    @Schema(description = "经办人电子邮箱")
    private String delegateEmail;

    @Column(name = "delegate_auth_level")
    @Schema(description = "经办人实名认证等级")
    private String delegateAuthLevel;

    @Column(name = "delegate_auth_method")
    @Schema(description = "经办人认证方式")
    private String delegateAuthMethod;

    @Column(name = "delegate_registration_address")
    @Schema(description = "经办人注册地址")
    private String delegateRegistrationAddress;

    @Column(name = "delegate_industry_type")
    @Schema(description = "经办人行业类型")
    private String delegateIndustryType;

    @Column(name = "delegate_task_scope")
    @Schema(description = "经办人任务范围")
    private String delegateTaskScope;

    @Column(name = "delegate_authorization_start")
    @Schema(description = "授权开始日期")
    private Date delegateAuthorizationStart;

    @Column(name = "delegate_authorization_end")
    @Schema(description = "授权结束日期")
    private Date delegateAuthorizationEnd;

    @Column(name = "delegate_remarks")
    @Schema(description = "经办人备注")
    private String delegateRemarks;

    @Column(name = "is_permanent")
    @Schema(description = "是否永久有效")
    private Boolean isPermanent = false;

    @Column(name = "validity_end_date")
    @Schema(description = "有效期截止日期")
    private Date validityEndDate;

    @Column(name = "review_user_id")
    @Schema(description = "审核人ID")
    private String reviewUserId;

    @Column(name = "review_time")
    @Schema(description = "审核时间")
    private Date reviewTime;

    @Column(name = "review_remarks")
    @Schema(description = "审核备注")
    private String reviewRemarks;

    @Column(name = "refuse_reason")
    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Column(name = "created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @Column(name = "created_time")
    @Schema(description = "创建时间")
    private Date createdTime;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_time")
    @Schema(description = "更新时间")
    private Date updatedTime;

    @Schema(description = "是否删除")
    private Boolean deleted = false;

    @Column(name = "service_node_id")
    Long serviceNodeId;
    String ext;
}