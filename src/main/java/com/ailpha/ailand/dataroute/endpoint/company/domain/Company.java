package com.ailpha.ailand.dataroute.endpoint.company.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

/**
 * todo 等标准确定后 这里需要改造 删除一些废弃的字段
 */
@Data
@Entity
@Table(name = "t_company")
@Schema(description = "企业实体")
public class Company {
    @Id
    @Schema(description = "企业ID")
    private Long id;

    @Column(name = "company_id")
    String companyId;
    @Column(name = "node_id")
    @Schema(description = "节点ID")
    String nodeId;

    @Column(name = "created_by")
    @Schema(description = "创建人")
    private String createdBy;

    @Column(name = "created_time")
    @Schema(description = "创建时间")
    private Date createdTime;

    @Column(name = "updated_by")
    @Schema(description = "更新人")
    private String updatedBy;

    @Column(name = "updated_time")
    @Schema(description = "更新时间")
    private Date updatedTime;

    @Schema(description = "是否删除")
    private Boolean deleted = false;

    String ext;
}