package com.ailpha.ailand.dataroute.endpoint.logo.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UploadFileType;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.logo.request.LogoConfigRequest;
import com.ailpha.ailand.dataroute.endpoint.logo.service.LogoService;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @date 2025/4/1 13:49
 */
@Slf4j
@RestController
@Tag(name = "自定义logo")
@RequestMapping("/logo")
@RequiredArgsConstructor
public class CustomLogoController {

    public final LogoService logoService;

    public final RouterService routerService;

    private final FilesStorageServiceImpl filesStorageService;

    @Value("${ailand.endpoint.ip}")
    private String endpointIpPort;

    public final AiLandProperties aiLandProperties;


    /**
     * nginx配置data-route.conf添加
     * location ^~/_data-route/public/ {
     * alias /data/apps/file/data-route/public/;
     * }
     */
    @PostMapping("/upload")
    @Operation(summary = "上传附件")
    @PreAuthorize("hasAnyAuthority('COMPANY_ADMIN')")
    public SuccessResponse<String> upload(MultipartFile file, String bizType) {
        doCheck(file);

        // 获取登录用户的企业信息
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        String companyId = String.valueOf(company.getId());
        if (StringUtils.isEmpty(companyId)) {
            throw new RestfulApiException("登录用户企业ID为空");
        }

        Path path = transformFilepath(UploadFileType.valueOf(bizType), companyId, file.getOriginalFilename());
        if (FileUtil.exist(path.toFile())) {
            path = transformFilepath(UploadFileType.valueOf(bizType), companyId,
                    String.format("%s_%s.%s", FileUtil.getPrefix(file.getOriginalFilename()), IdUtil.nanoId(8), FileUtil.getSuffix(file.getOriginalFilename())));
        }

        // 把自定义logo上传到指定目录
        filesStorageService.save(file, path);

        // 返回logo代理地址
        String nginxUrl = String.format("%s%s%s/%s", endpointIpPort, Constants.NGINX_PUBLIC_PATH, FileUtil.FILE_SEPARATOR + companyId, path.toFile().getName());
        return SuccessResponse.success(nginxUrl).build();
    }

    @PostMapping("/update")
    @Operation(summary = "更新企业自定义logo")
    @OpLog(message = "更新企业自定义logo")
    @PreAuthorize("hasAnyAuthority('COMPANY_ADMIN')")
    public SuccessResponse<Boolean> uploadLogoConfig(@RequestBody LogoConfigRequest request) {
        OPLogContext.putOpType(InternalOpType.SAVE_CONFIGURATION);
        // 更新指定企业 logo
        logoService.uploadLogoConfig(request);
        return SuccessResponse.success(true).build();
    }

    private Path transformFilepath(UploadFileType fileType, String companyId, String filename) {
        if (fileType != null) {
            final String dir = Constants.PUBLIC_PATH + FileUtil.FILE_SEPARATOR + companyId;
            return Paths.get(dir + FileUtil.FILE_SEPARATOR + filename);
        } else {
            throw new RestfulApiException("不支持的上传类型");
        }
    }

    private void doCheck(MultipartFile file) {
        String fileType = FileUtil.getSuffix(file.getOriginalFilename());
        Assert.isTrue(ObjectUtil.isNotNull(file.getOriginalFilename()) && !file.getOriginalFilename().contains(".."), "非法的文件名称");
        if (aiLandProperties != null && aiLandProperties.getFileStorage().getLogoFileSuffix() != null) {
            Assert.isTrue(aiLandProperties.getFileStorage().getLogoFileSuffix().contains(fileType), "不支持的文件类型");
        }
    }


    /**
     * 获取自定义logo内容
     */
    @GetMapping("/customLogo")
    public SuccessResponse<LogoConfigRequest> customLogo() {
        return SuccessResponse.success(logoService.findCustomLogo()).build();
    }

}
