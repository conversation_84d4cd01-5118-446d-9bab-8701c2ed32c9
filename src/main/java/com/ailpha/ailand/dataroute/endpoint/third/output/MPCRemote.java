package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.PageResult;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.MpcContract;
import com.ailpha.ailand.dataroute.endpoint.third.input.TerminalContractRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.CommonResult;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.POST;

@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Sign(baseCapabilityType = BaseCapabilityType.MPC, tokenUrl = "/_ailand-mpc/third/app/token")
public interface MPCRemote {

    @POST("/_ailand-mpc/mpc/route/createAsset")
    CommonResult<Void> syncDataset(@Body MPCDataset mpcDataset);

    @POST("/_ailand-mpc/mpc/route/createContract")
    CommonResult<String> createContract(@Body MpcContract contract);

    @POST("/_ailand-mpc/mpc/route/terminalContract")
    CommonResult<Boolean> terminalContract(@Body TerminalContractRequest request);

    @POST("/_ailand-mpc/third/app/register")
    CommonResult<Boolean> appRegister(@Body ExternalPlatformAppKeyCreateReq request);

    @POST("/_ailand-mpc/mpc/route/updateAsset")
    CommonResult<Void> updateAsset(@Body UpdateRouteAssetRequest request);

    @POST("/_ailand-mpc/mpc/route/openapi/detail")
    CommonResult<OpenApiDetailVOForDataRoute> openapiDetail(@Body OpenApiDetailRequest openApiDetailRequest);

    @POST("/_ailand-mpc/mpc/route/openapi/list")
    PageResult<OpenApiListVO> openapiList(@Body OpenApiListRequest openApiListRequest);
}
