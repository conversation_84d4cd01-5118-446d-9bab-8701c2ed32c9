package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import jakarta.persistence.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/12/25
 */
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "external_platform_app_key_app_secret")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ExternalPlatformAppKeySecret implements Serializable {

    @Id
    @Schema(description = "ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(columnDefinition = "bigint COMMENT 'id'", updatable = false, nullable = false, unique = true)
    Long id;

    @Schema(description = "平台ID")
    @Column(name = "platform_id", columnDefinition = "VARCHAR(64) COMMENT '平台ID'")
    String platformId;

    @Schema(description = "平台服务地址（IP端口）")
    @Column(name = "platform_domain", columnDefinition = "VARCHAR(255) COMMENT '平台服务地址（IP端口）'")
    String platformDomain;

    @Schema(description = "平台类型")
    @Column(name = "platform_type", columnDefinition = "VARCHAR(255) COMMENT '平台类型'")
    @Enumerated(EnumType.STRING)
    PlatformType platformType;

    @Schema(description = "appKey")
    @Column(name = "app_key", columnDefinition = "VARCHAR(255) COMMENT 'appKey'")
    String appKey;

    @Schema(description = "appSecret")
    @Column(name = "app_secret", columnDefinition = "VARCHAR(255) COMMENT 'appSecret'")
    String appSecret;

    @JsonFormat(pattern = "yyyyMMdd HH:mm", timezone = "GMT+8")
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    Date createTime;

    @JsonFormat(pattern = "yyyyMMdd HH:mm", timezone = "GMT+8")
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    Date updateTime;
}
