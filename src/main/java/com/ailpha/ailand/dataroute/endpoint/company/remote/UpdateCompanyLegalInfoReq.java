package com.ailpha.ailand.dataroute.endpoint.company.remote;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UpdateCompanyLegalInfoReq {
    String applyIdCard;
    String applyName;

    String partyContactInfo;
    String applyType;
    String authenticationMode;
    String authenticationTime;
    String businessLicenseUrl;
    String businessLicenseUrlName;
    String certificationLevel;
    String companyCode;
    String companyName;
    String documentType;
    String examineInstitution;
    String identityState;
    String industry;
    @Schema(description = "法定代表人证件有效期：YYYY-MM-DD 至 YYYY-MM-DD")
    String certificateValidityPeriod;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date operateEndTime;
    String operateRange;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date operateStartTime;
    String partyAccountName;
    String partyBank;
    String partyBankAccountNumber;
    String partyBankAddress;
    String partyFax;
    String partyPostalCode;
    String registerAddress;
    String registerAmount;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    Date registerTime;
    String scope;
}
