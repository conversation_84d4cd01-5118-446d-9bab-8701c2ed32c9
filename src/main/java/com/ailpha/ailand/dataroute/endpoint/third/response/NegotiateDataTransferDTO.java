package com.ailpha.ailand.dataroute.endpoint.third.response;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/3 14:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NegotiateDataTransferDTO implements Serializable {

    @Schema(description = "业务节点ID")
    private String nodeId;

    @Schema(description = "交易合约标识")
    private String tradingStrategyCode;

    @Schema(description = "交易合约名称")
    private String tradingStrategyName;

    @Schema(description = "控制指令编号")
    private String ctrlInstructionId;

    @Schema(description = "交易合约控制指令")
    private String transactionExecutionStrategy;

    @Schema(description = "传输模式：当前仅支持【主动拉取】推送-1 拉取-2")
    private String transferMode;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExecutionStrategy implements Serializable {

        @Schema(description = "目标连接器")
        private String targetConnectorId;

        @Schema(description = "发起连接器")
        private String issuerConnectorId;

        @Schema(description = "发起时间")
        private String issuedTime;

        @Schema(description = "数据产品唯一标识")
        private String productId;

        @Schema(description = "场景交付id")
        private String deliverySceneId;

        private String sellerCompanyId;

        private String buyerCompanyId;

        private DeliveryType deliveryType;
    }

    public NegotiateDataTransferDTO.ExecutionStrategy getStringToTransactionExecutionStrategy() {
        if (StringUtils.isNotBlank(transactionExecutionStrategy)) {
            return JSONUtil.toBean(transactionExecutionStrategy, NegotiateDataTransferDTO.ExecutionStrategy.class);
        } else {
            return null;
        }
    }
}
