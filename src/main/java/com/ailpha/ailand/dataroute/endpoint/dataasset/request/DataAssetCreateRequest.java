package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.APIQueryWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产创建请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetCreateRequest {
    @Schema(description = "资产ID")
    String assetId;
    @NotEmpty
    @Schema(description = "资产名称")
    String assetName;
    @Schema(description = "资产中文名称")
    String assetNameCN;
    @Schema(description = "资源提供方")
    String provider;
    @Schema(description = "数源单位")
    String providerOrg;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "数据容量（单位 MB）")
    String capacity;
    @Schema(description = "数据覆盖范围")
    String dataCoverage;
    @Schema(description = "敏感等级")
    String sensitiveLevel;
    @Schema(description = "更新频率")
    String updateFrequency;
    @Schema(description = "标签")
    List<String> tag;
    @Schema(description = "数据覆盖周期开始时间")
    String dataCoverageTimeStart;
    @Schema(description = "数据覆盖周期结束时间")
    String dataCoverageTimeEnd;
    @Schema(description = "资源摘要")
    String describeMessage;
    // <<< 基本信息
    // >>> 接入配置
    @Schema(description = "数据类型")
    DataType dataType;
    @Schema(description = "数据接入方式")
    SourceType source;
    @Schema(description = "API查询方式")
    APIQueryWay apiQueryWay;
    @Schema(description = "交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC")
    List<DeliveryMode> deliveryModes;
    @Schema(description = "MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）")
    List<MPCPurpose> mpcPurpose;
//    @Schema(description = "资产状态")
//    DataAssetPrepareStatus status;

    @NotNull
    @Schema(description = "调试数据来源")
    DebugDataSourceEnum debugDataSource;
    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "临时调试文件id")
    String tempDebugFileId;
    @Schema(description = "调试数据分隔符")
    String separator;
    @Schema(description = "调试数据是否包含表头 1:是")
    Integer hasHeader;
    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;
    @Schema(description = "文件数据资产元数据")
    FileSourceMetadata fileSourceMetadata;
    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
    @Schema(description = "AiSort元数据")
    AiSortMetadata aiSortMetadata;

    @Schema(description = "绑定交易所插件")
    List<Long> exchangePluginIds;
    @Schema(description = "绑定数字证书插件")
    List<Long> certificatePluginIds;

    @Schema(description = "产品血缘")
    String lineage;

    @Schema(description = "平台生成(MPC) 结果集(openapiId)")
    String mpcOpenAPIId;

    @Schema(description = "是否改写响应体")
    Boolean extractResponse;
}
