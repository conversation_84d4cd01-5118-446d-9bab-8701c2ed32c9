package com.ailpha.ailand.dataroute.endpoint.third.mapper;

import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.third.request.MPCDataSchemaVO;
import com.ailpha.ailand.dataroute.endpoint.third.request.MPCDataSchemasVO;
import com.ailpha.ailand.dataroute.endpoint.third.request.MPCDataset;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/12/13
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public abstract class MPCDatasetMapper {

    @Autowired
    private FilesStorageServiceImpl filesStorageService;

    @Mapping(target = "assetId", source = "id")
    @Mapping(target = "datasetName", source = "dataProductName")
    @Mapping(target = "purposes", source = "deliveryExt.mpcPurpose")
    @Mapping(target = "datasourceSchemas", ignore = true)
    @Mapping(target = "dataSourceType", ignore = true)
    @Mapping(target = "filePath", ignore = true)
    @Mapping(target = "businessArea", source = "dataExt.industry1")
    @Mapping(target = "creatorId", source = "userId")
    @Mapping(target = "routeId", source = "dataProduct.provider.company.nodeId")
    @Mapping(target = "createIp", source = "dataExt.createIp")
    @Mapping(target = "extend", ignore = true)
    public abstract MPCDataset dataAssetToMPCDataset(DataProduct dataProduct);

    @AfterMapping
    void fillOtherInfo(DataProduct dataProduct, @MappingTarget MPCDataset mpcDataset) {
        // 交付处理文件来源：复制源文件
        if (!ObjectUtils.isEmpty(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath())) {
            Path dataAssetFilePath = Paths.get(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
            File dataAssetFile = dataAssetFilePath.toFile();
            if (dataAssetFile.exists()) {
                Path mpcFilePath = filesStorageService.getRootPath()
                        .resolve("dataAsset")
                        .resolve(dataProduct.getUserId())
                        .resolve(String.format("MPC-%s", dataAssetFile.getName()));
                mpcFilePath = filesStorageService.saveTmpData2File(dataAssetFilePath, mpcFilePath, false);
                mpcDataset.setFilePath(mpcFilePath.toFile().getAbsolutePath());
            }
        }
        // dataSchema
        List<MPCPurpose> mpcPurpose = dataProduct.getDeliveryExt().getMpcPurpose();
        List<DataSchemaBO> dataSchema = dataProduct.getDataExt().getDataSchema();
        List<MPCDataSchemasVO> datasourceSchemas = new ArrayList<>(mpcPurpose.size());
        for (MPCPurpose purpose : mpcPurpose) {
            List<MPCDataSchemaVO> datasourceSchema = new ArrayList<>(datasourceSchemas.size());
            for (DataSchemaBO dataSchemaBO : dataSchema) {
                if (dataSchemaBO.isAllowQuery()) {
                    MPCDataSchemaVO mpcDataSchemaVO = MPCDataSchemaVO.builder()
                            .isParticipateCompute(dataSchemaBO.isId()).fieldName(dataSchemaBO.getFieldName())
                            .dataType(MPCPurpose.CIPHER_TEXT_COMPUTE.equals(purpose) ? "FLOAT" : dataSchemaBO.getType())
                            .description(dataSchemaBO.getComment()).build();
                    datasourceSchema.add(mpcDataSchemaVO);
                }
            }
            MPCDataSchemasVO mpcDataSchemasVO = MPCDataSchemasVO.builder().purpose(purpose).datasourceSchema(datasourceSchema).build();
            datasourceSchemas.add(mpcDataSchemasVO);
        }
        mpcDataset.setDatasourceSchemas(datasourceSchemas);
        // 数据集数据来源类型
        mpcDataset.setDataSourceType("FILE");
        // 扩展字段
        MPCDataset.Extend extend = new MPCDataset.Extend();
        extend.setSource(dataProduct.getSourceType());
        mpcDataset.setExtendObj(extend);
    }
}
