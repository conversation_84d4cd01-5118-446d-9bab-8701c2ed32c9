package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import com.ailpha.ailand.dataroute.endpoint.connector.RouteStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "连接器激活返回信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ActivateResponse {
    @Schema(description = "连接器状态")
    RouteStatus status;
    @Schema(description = "枢纽节点")
    String routerManager;
    @Schema(description = "连接器名称")
    String routerName;
    @Schema(description = "连接器描述")
    String routerDesc;
    @Schema(description = "企业信息")
    CompanyDTO company;
}
