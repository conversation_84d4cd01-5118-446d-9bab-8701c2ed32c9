package com.ailpha.ailand.dataroute.endpoint.demand.request;


import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DemandNegotiationRecordDTO {
    @Schema(description = "提供方响应ID")
    Integer id;
    @Schema(description = "报价")
    String price;
    @Schema(description = "方案")
    String solutionFileId;
    @Schema(description = "描述")
    String description;
    @Schema(description = "关联数据资产")
    List<DataAssetDTO> dataAssets;
    @Schema(description = "最新提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date lastCommitDate;
    @Schema(description = "数源方主体信息")
    DemandSideDTO demandSide;
    @Schema(description = "状态")
    String status;
}
