package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneListResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/18 17:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderSceneRefResp {

    String sceneId;

    @Schema(description = "行业分类")
    String industry;

    @Schema(description = "交付方式")
    DeliveryType deliveryMode;

    @Schema(description = "创建时间")
    Date createTime;

    SceneListResp.DataAssetSceneRef assetSceneRef;
}
