package com.ailpha.ailand.dataroute.endpoint.hengnao.interceptor;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
public class HengNaoSignInterceptor extends BasePathMatchInterceptor {

    private final AiLandProperties aiLandProperties;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        AiLandProperties.HengNao hengNao = aiLandProperties.getHengNao();
        if (hengNao == null || !Boolean.TRUE.equals(hengNao.getEnable())) {
            return chain.proceed(chain.request());
        }
        long timestamp = System.currentTimeMillis();
        String data = String.format("%d\n%s\n%s", timestamp, hengNao.getAppSecret(), hengNao.getAppKey());
        String sign = generateSign(data, hengNao.getAppSecret(), timestamp);

        Request original = chain.request();
        Request request = original.newBuilder()
                .addHeader("appKey", hengNao.getAppKey())
                .addHeader("sign", sign)
                .build();
        return chain.proceed(request);
    }

    private String generateSign(String data, String secret, long timestamp) {
        try {
            Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            hmacSHA256.init(secretKeySpec);
            byte[] sign = hmacSHA256.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return String.format("%d%s", timestamp, new String(Base64.encodeBase64(sign), StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }
} 