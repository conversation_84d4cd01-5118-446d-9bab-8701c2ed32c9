package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2023/3/2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResponseBO implements Serializable {

    @NotEmpty
    @Schema(description = "字段名称")
    String fieldName;

    @NotNull
    @Schema(description = "数据类型")
    DataTypeEnum dataType;
}
