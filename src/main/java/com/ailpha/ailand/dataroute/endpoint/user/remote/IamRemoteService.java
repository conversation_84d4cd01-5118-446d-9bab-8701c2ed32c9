package com.ailpha.ailand.dataroute.endpoint.user.remote;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.IamRemoteInterceptor;
import com.ailpha.ailand.dataroute.endpoint.common.rest.iam.CommResult;
import com.ailpha.ailand.dataroute.endpoint.common.rest.iam.UserCommRes;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.ClientTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.CreateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.DeleteUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.GetUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam.UpdateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.ClientTokenResponse;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserInfoDTO;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserInfoResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

@RetrofitClient(baseUrl = "${ailand.iam-server.base-url}", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = IamRemoteInterceptor.class,
        exclude = {"/iam/auth/api/ext/token", "/iam/auth/oauth2/accessToken"})
public interface IamRemoteService {
    @POST("/iam/auth/api/ext/token")
    CommResult<ClientTokenResponse> appToken(@Body ClientTokenRequest request);


    @POST("/iam/auth/oauth2/accessToken")
    CommResult<String> accessToken(@Query("code") String code, @Query("client_id") String clientId,
                                 @Query("client_secret") String clientSecret, @Query("grant_type") String grantType);

    @POST("/iam/auth/oauth2/userInfo")
    Response<UserInfoResponse> userinfo(@Header("Authorization") String token);

    @POST("/iam/resource/api/userCreateService")
    Response<UserCommRes> createUser(@Body CreateUserRequest request);

    @POST("/iam/resource/api/userUpdateService")
    Response<UserCommRes> updateUser(@Body UpdateUserRequest request);

    @POST("/iam/resource/api/userDeleteService")
    Response<UserCommRes> deleteUser(@Body DeleteUserRequest request);
    @POST("/iam/resource/api/getUserInfo")
    CommResult<List<UserInfoDTO>> getUserInfo(@Body GetUserRequest request);


}
