package com.ailpha.ailand.dataroute.endpoint.order.remote;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18 12:10
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IPageDTO<T> {

    Pagination pagination;
    List<T> data;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Pagination {
        Long total;
        Long offset;
    }

}
