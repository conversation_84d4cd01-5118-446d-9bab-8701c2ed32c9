package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CreateServiceRequest {
    InvokeParam invokeParam;

    public String getInvokeParam() {
        return JacksonUtils.obj2json(invokeParam);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeParam {
        String name;

        String desc;

//        List<String> hosts;

        // "plugins\":{\"basic-auth\":{\"_meta\":{\"disable\":false}}}
        // {"key-auth":{"header":"apikey","query":"apikey","hide_credentials":true},"consumer-restriction":{"whitelist":["△"],"rejected_code":401,"type":"consumer_name","rejected_msg":"Authorization Invalid.Please Contact The Administrator To Enable Permissions "}}
        PluginWrapper plugins;

        UpStream upstream;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class UpStream {
            // 负载均衡算法
            @Builder.Default
            String type = "roundrobin";
            // 是否传递主机名
            @Builder.Default
            @JsonProperty("pass_host")
            String passHost = "pass";
            // 重试次数
            @Builder.Default
            Integer retries = 1;
            // 重试超时时间
            @Builder.Default
            @JsonProperty("retry_timeout")
            Integer retryTimeout = 5;
            // 协议
            @Builder.Default
            String scheme = "http";
            // 超时
            @Builder.Default
            Timeout timeout = new Timeout();
            // 连接池
            @Builder.Default
            @JsonProperty("keepalive_pool")
            KeepalivePool keepalivePool = new KeepalivePool();
            // 节点 ip -> port
            @Builder.Default
            Map<String, Integer> nodes = new HashMap<>();

            public void addNode(String hostPort, int weight) {
                nodes.put(hostPort, weight);
            }
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class Timeout {
            @Builder.Default
            Integer connect = 6;
            @Builder.Default
            Integer send = 6;
            @Builder.Default
            Integer read = 6;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class KeepalivePool {
            @Builder.Default
            Integer size = 320;
            @Builder.Default
            @JsonProperty("idle_timeout")
            Integer idleTimeout = 60;
            @Builder.Default
            Integer requests = 1000;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class PluginWrapper {
            @JsonProperty("key-auth")
            KeyAuth keyAuth;
            @JsonProperty("consumer-restriction")
            ConsumerRestriction consumerRestriction;
        }

        public static final PluginWrapper DEFAULT_PLUGINS = PluginWrapper.builder()
                .keyAuth(KeyAuth.builder().build())
                .consumerRestriction(ConsumerRestriction.builder().build())
                .build();

        public static final PluginWrapper EMPTY_PLUGINS = PluginWrapper.builder()
                .build();

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class KeyAuth {
            @Builder.Default
            String header = "apikey";
            @Builder.Default
            String query = "apikey";
            @Builder.Default
            @JsonProperty("hide_credentials")
            Boolean hideCredentials = true;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class BasicAuth {
            @Builder.Default
            Boolean disable = false;
        }

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @FieldDefaults(level = AccessLevel.PRIVATE)
        public static class ConsumerRestriction {
            @Builder.Default
            List<String> whitelist = Arrays.asList("~^");
            @Builder.Default
            @JsonProperty("rejected_code")
            Integer rejectedCode = 401;
            @Builder.Default
            String type = "consumer_name";
            @Builder.Default
            @JsonProperty("rejected_msg")
            String rejectedMsg = "Authorization Invalid.Please Contact The Administrator To Enable Permissions ";
        }
    }
}
