package com.ailpha.ailand.dataroute.endpoint.demand;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.demand.request.*;
import com.ailpha.ailand.dataroute.endpoint.demand.response.MarkAsDealResponse;
import com.ailpha.ailand.dataroute.endpoint.demand.response.SubmitQuotationResponse;
import com.dbapp.rest.response.SuccessResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.*;

import java.util.List;

@RetrofitClient(baseUrl = "http://127.0.0.1:8082")
@Sign(baseCapabilityType = BaseCapabilityType.TRADE_PLATFORM, tokenUrl = "/third/app/token")
public interface DemandRemoteService {

    @POST("/demand/add")
    SuccessResponse<Boolean> add(@Body AddDemandRequest request);

    @POST("/demand/list")
    SuccessResponse<List<DemandPageDTO>> listDemands(@Body DemandMapRequest request);

    @GET("demand/{id}")
    SuccessResponse<DemandDetailDTO> demandDetail(@Path("id") Integer id);

    @POST("/demand/negotiation/related")
    SuccessResponse<List<DemandNegotiationRecordDTO>> listDemandNegotiations(@Body DemandNegotiationRecordRequest request);

    @POST("/demand/cancelCollect")
    SuccessResponse<Boolean> cancelCollectDemand(@Body CollectDemandRequest request);

    @POST("/demand/collect")
    SuccessResponse<Boolean> collectDemand(@Body CollectDemandRequest request);

    @GET("/demand/map/{id}")
    SuccessResponse<DemandMapDetailDTO> demandMapDetail(@Path("id") Integer id, @Query("userId") String userId);

    @POST("/demand/map")
    SuccessResponse<List<DemandMapDTO>> demandMap(@Body DemandMapRequest request);

    @POST("/demand/checkTitle")
    SuccessResponse<Boolean> checkTitle(@Body CheckTitleRequest request);

    @POST("/demand/edit")
    SuccessResponse<Boolean> edit(@Body EditDemandRequest request);

    @POST("/demand/close")
    SuccessResponse<Boolean> close(@Body CloseDemandRequest request);

    @POST("/demand/submitQuotation")
    SuccessResponse<SubmitQuotationResponse> submitQuotation(@Body SubmitQuotationRequest request);

    @POST("/demand/negotiation/done")
    SuccessResponse<MarkAsDealResponse> markNegotiationDone(@Body MarkAsDealRequest request);

    @POST("/demand/audit")
    SuccessResponse<Boolean> audit(@Body AuditDemandRequest request);

    @POST("/demand/negotiation/page")
    SuccessResponse<List<DemandNegotiationPageDTO>> demandNegotiationPage(@Body DemandNegotiationPageRequest request);

    @POST("/demand/statistic")
    SuccessResponse<DemandStatisticDTO> statistic(@Body DemandStatisticRequest request);
}
