package com.ailpha.ailand.dataroute.endpoint.order.schedule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.ApprovalStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetDeliveryExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.OrderClassify;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.IPageDTO;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.TradingStrategyDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.BusinessNodeOrderDTO;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderResolveService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderResolveDTO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.repository.ServiceNodeRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUser;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.dbapp.rest.exception.RestfulApiException;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 订单有效期检查
 * @date 2025/2/25 15:29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderSyncSchedule {

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final CompanyRepository companyRepository;

    private final OrderResolveService orderResolveService;

    private final DataProductService dataProductService;

    private final CompanyService companyService;

    private final JPAQueryFactory queryFactory;

    private final ServiceNodeRemoteService nodeRemoteService;

    private final HubShuHanApiClient shuHanApiClient;

    private final ServiceNodeRepository serviceNodeRepository;

    @Value("${order.sync.size:50}")
    private Integer pageSize;


    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    public void syncOrder() {
        if (BUSY.get()) {
            return;
        }

        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        // saas化以企业维度同步订单数据
        companyRepository.findAll().forEach(company -> {
            if (StringUtils.isEmpty(company.getNodeId()))
                return;
            try {
                BUSY.set(true);
                log.info("sync buyer order from service business platform ....");
                CompanyDTO companyDTO = companyService.detail(company.getId());
                AsyncManager.getInstance().executeFuture(() -> {
                    TenantContext.setCurrentTenant("tenant_" + company.getId());
                    doCompanySync(companyDTO, true);
                    return true;
                });
            } finally {
                BUSY.set(false);
            }
        });

    }

    public void doCompanySync(CompanyDTO company, boolean buyerSync) {
        List<ServiceNodeInfo> serviceNodeInfoList = serviceNodeRepository.findAllByProcessStatusOrderByCreateTimeDesc(ApprovalStatus.APPROVED.name());
        if (ObjectUtils.isEmpty(serviceNodeInfoList)) {
            return;
        }
        for (ServiceNodeInfo serviceNodeInfo : serviceNodeInfoList) {
            if (serviceNodeInfo.getEntryName().startsWith("tee")
                    || serviceNodeInfo.getEntryName().startsWith("TEE")) {
                return;
            }
            int pageNum = 0;
            try {
                if (buyerSync) {
                    doSync(pageNum, company, serviceNodeInfo);
                } else {
                    SpringUtil.getBean(OrderSellerSyncSchedule.class).doSync(pageNum, company, serviceNodeInfo);
                }
            } catch (Exception e) {
                log.error("同步订单数据异常: routeId: {} company: {}", company.getNodeId(), company.getId(), e);
            }
        }
    }

    /**
     * 调用基础能力平台 获取 任务数据
     */
    private void doSync(Integer pageNum, CompanyDTO company, ServiceNodeInfo serviceNodeInfo) {

        // 企业信息
        String routeId = company.getNodeId();
        Long companyId = company.getId();

        // 国标查询
        CatalogQueryVM catalogQuery = new CatalogQueryVM();

        CatalogQueryVM.FilterVM filterVM = new CatalogQueryVM.FilterVM();
        filterVM.setFilterProperty("initiator_router_id");
        filterVM.setFilterValue(routeId);
        filterVM.setFilterOperation("eq");

        CatalogQueryVM.FilterVM filterStatusVM = new CatalogQueryVM.FilterVM();
        filterStatusVM.setFilterProperty("delivery_status");
        filterStatusVM.setFilterOperation("in");
        filterStatusVM.setFilterValue("0");

        DateTime yesterday = DateUtil.yesterday();
        DateTime begin = DateUtil.beginOfDay(yesterday);
        DateTime end = DateUtil.dateNew(new Date());

        // 合约生效时间 —— 昨天到现在当前时间
        CatalogQueryVM.FilterVM filterTimeBetween = new CatalogQueryVM.FilterVM();
        filterTimeBetween.setFilterProperty("begin_time");
        filterTimeBetween.setFilterValue(String.format("%s-%s", begin.toTimestamp().getTime(), end.toTimestamp().getTime()));
        filterTimeBetween.setFilterOperation("between");


        catalogQuery.setPage(pageNum.longValue());
        catalogQuery.setSize(pageSize.longValue());
        catalogQuery.setFilters(Arrays.asList(filterVM, filterStatusVM, filterTimeBetween));

        IPageDTO<BusinessNodeOrderDTO> orderPageData = syncOrderRecords(serviceNodeInfo.getApiUrl(), catalogQuery);
        List<BusinessNodeOrderDTO> records = orderPageData.getData();
        if (CollectionUtil.isEmpty(records)) {
            log.info("当前同步 routeId【{}】买方订单数据为空", routeId);
            return;
        }
        log.debug("获取订单数据参数: {} 任务条数: {}", JSONUtil.toJsonStr(catalogQuery), records.size());

        // step 1、查看对应租户下是否存在 拉取到的订单信息
        List<BusinessNodeOrderDTO> rows = orderResolveService.filterExistOrder(records);
        if (CollectionUtil.isEmpty(rows)) {
            return;
        }

        // step 2、存在 —— 过滤
        // step 3、解析成【连接器】交付信息——保存
        List<OrderResolveDTO> list = rows.stream().map(orderInfo -> {
            OrderResolveDTO orderResolveDTO;
            try {
                orderResolveDTO = resolveOrderRecord(orderInfo, company, serviceNodeInfo);
            } catch (Exception e) {
                log.error("解析任务订单信息异常：orderInfo: {}", orderInfo, e);
                return null;
            }
            return orderResolveDTO;
        }).filter(Objects::nonNull).toList();
        list.forEach(orderResolveService::save);

        long total = orderPageData.getPagination().getTotal();
        long pageCount = (total + pageSize - 1) / pageSize;
        if (pageCount > pageNum) {
            ++pageNum;
            doSync(pageNum, company, serviceNodeInfo);
        }
    }

    public IPageDTO<BusinessNodeOrderDTO> syncOrderRecords(String serviceUrl, CatalogQueryVM catalogQuery) {
        // 拿到平台任务数据
        IPageDTO<BusinessNodeOrderDTO> orderPageData;
        try {
            log.debug("订单数据同步 {}, {}", serviceUrl, JSONUtil.toJsonStr(catalogQuery));
            catalogQuery.setUrl(serviceUrl);
            ShuhanResponse<IPageDTO<BusinessNodeOrderDTO>> strategyPull = nodeRemoteService.tradingStrategyGrant(catalogQuery, catalogQuery.getFilters().getFirst().getFilterValue());

            log.debug("订单数据同步 {}, 结果 {}", serviceUrl, strategyPull);

            if (strategyPull != null && strategyPull.isSuccess()) {
                orderPageData = strategyPull.getData();
            } else {
                log.error("业务节点【{}】订单数据同步异常: {}", serviceUrl, strategyPull != null ? strategyPull.getMessage() : "业务节点同步订单失败");
                throw new RestfulApiException("业务节点【" + serviceUrl + "】订单数据同步异常");
            }

        } catch (Exception e) {
            log.error("订单数据同步异常: {}", catalogQuery, e);
            throw new RestfulApiException("业务节点【" + serviceUrl + "】订单数据同步异常");
        }

        return orderPageData;
    }

    /**
     * 解析订单数据信息
     */
    private OrderResolveDTO resolveOrderRecord(BusinessNodeOrderDTO businessNodeOrderDTO, CompanyDTO beneficiaryCompany, ServiceNodeInfo serviceNodeInfo) {
        // 发起方企业信息
        String routeId = beneficiaryCompany.getNodeId();
        Long companyId = beneficiaryCompany.getId();

        // 数据产品id
        BusinessNodeOrderDTO.TransactionExecutionStrategy strategy = businessNodeOrderDTO.getTransactionExecutionStrategy();
        String productPlatformId = strategy.getRegistrationId();
        long createTime = strategy.getCreateTime();
        // 查询产品详情
        DataProductVO dataProductVO;
        try {
            shuHanApiClient.setCompany(beneficiaryCompany);
            dataProductVO = dataProductService.getDataProductByDataProductPlatformIdFromRemoteNoLoginSpecial(productPlatformId, companyId);
        } catch (Exception e) {
            log.error("根据 productPlatformId【{}】获取产品数据异常：", productPlatformId, e);
            return null;
        }

        // 数由器用户id todo：买方有没有可能没有在数由器创建账号
        User userLocal = changeUserId(strategy.getInitiatorId());

        // 数据产品 企业信息
        CompanyDTO company = dataProductVO.getProvider().getCompany();

        OrderApprovalRecord record = OrderApprovalRecord.builder()
                .id(businessNodeOrderDTO.getCtrlInstructionId())
                .classify(OrderClassify.NORMAL)
                .type(AssetType.PRODUCT)
                .assetId(dataProductVO.getId())
                .assetName(dataProductVO.getDataProductName())
                .deliveryMode(strategy.getDeliveryMethod())
                // 使用连接器唯一用户id
                .beneficiaryId(userLocal.getId())
                .beneficiaryUsername(userLocal.getAccount())
                .beneficiaryRouterId(routeId)
                .beneficiaryEnterpriseName(beneficiaryCompany.getOrganizationName())
                .beneficiaryEnterpriseProperty(beneficiaryCompany.getIndustryType())
                .approverId(dataProductVO.getUserId())
                .approverUsername(dataProductVO.getProvider().getUsername())
                .approverRouterId(dataProductVO.getProvider().getRouterId())
                .approverEnterpriseName(company.getOrganizationName())
                // 访问控制配置

                // 预付费、后付费
                .chargingWay(strategy.getPaymentMethod())
                // 按需、按时间 —— 对应 MeasurementMethod
                .meteringWay(strategy.getMeasurementMethod())
                .allowance(strategy.getTotal() == null ? new BigInteger("0") : strategy.getTotal())
                .expireDate(strategy.getEndTime() == null ? null : DateUtil.date(strategy.getEndTime()))
                .successfulUsage(new BigInteger("0"))
                .status("APPROVED")
                .createTime(DateUtil.date(createTime))
                .updateTime(new Date())
                .approveTime(strategy.getStartTime() == null ? new Date() : DateUtil.date(strategy.getStartTime()))
                .pullTime(new Date())
                .build();

        BusinessNodeOrderDTO.DeliveryInfo deliveryInfo = strategy.getDeliveryInfo();
        OderRecordExtend recordExtend = OderRecordExtend.builder()
                .callPrice("面议")
                // 对应 MeasurementUint
                .cycleWay(strategy.getMeasurementUint())
                .price(strategy.getPrice())
                .beneficiaryCreditCode(beneficiaryCompany.getCreditCode())
                .approverCreditCode(company.getCreditCode())
                .beneficiaryCompanyId(String.valueOf(companyId))
                .approverCompanyId(String.valueOf(company.getId()))
                .dataProductPlatformId(dataProductVO.getDataProductPlatformId())
                // 扩展字段
                .productPrice(String.valueOf(dataProductVO.getPrice()))
                .mpcPurposes(dataProductVO.getMpcPurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .teePurposes(dataProductVO.getTeePurpose().stream().map(Enum::name).collect(Collectors.joining(",")))
                .productionType(dataProductVO.getType())
                .dataType(dataProductVO.getDataType() == null ? null : dataProductVO.getDataType().name())
                .dataType1(dataProductVO.getDataType1())
                .isLLM(dataProductVO.getIsLLM())
                .summary(dataProductVO.getDescription())
                .providerOrg(company.getOrganizationName())
                .tradingStrategyCode(businessNodeOrderDTO.getTradingStrategyCode())
                .tradingStrategyName(businessNodeOrderDTO.getTradingStrategyName())
                .tradingStrategyContent(businessNodeOrderDTO.getTradingStrategyContent())
                .tradingStrategyTime(businessNodeOrderDTO.getTradingStrategyTime())
                .serviceNodeId(serviceNodeInfo.getServiceNodeId())
                .serviceNodeUrl(serviceNodeInfo.getApiUrl())
                .serviceNodeName(serviceNodeInfo.getEntryName())
                .deliveryModeStandards(dataProductVO.getDeliveryMethod())
                .transferMode(deliveryInfo == null ? "" : deliveryInfo.getTransferMode())
                .deliveryInfo(deliveryInfo == null ? "" : deliveryInfo.getDeliveryInfo())
                .build();

        List<DeliveryMode> deliveryModeList = DataAssetDeliveryExt.deliveryModesForOrderResolve(dataProductVO.getDeliveryModes(), dataProductVO.getMpcPurpose(), dataProductVO.getTeePurpose());
        String deliveryModes = deliveryModeList.stream().map(Enum::name).collect(Collectors.joining(","));

        recordExtend.setDeliveryModes(deliveryModes);

        record.setExtend(recordExtend);

        AssetBeneficiaryRel rel = AssetBeneficiaryRel.builder()
                .id(UUID.randomUUID().toString().replace("-", ""))
                .assetId(record.getAssetId())
                .beneficiaryId(record.getBeneficiaryId())
                .orderId(record.getId())
                // 可以不填充
                .extend(new AssetBeneficiaryExtend())
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        // 解析到订单后 —— 推送交付信息 进行中
        try {
            callbackStatus(businessNodeOrderDTO.getTradingStrategyCode(), serviceNodeInfo, record.getBeneficiaryRouterId());
        } catch (Exception e) {
            log.error("回调业务节点修改订单状态异常", e);
            return null;
        }

        return new OrderResolveDTO(record, rel);
    }

    /**
     * tradingStrategyCode 交易合约标识
     */
    private void callbackStatus(String tradingStrategyCode, ServiceNodeInfo serviceNodeInfo, String beneficiaryRouteId) {

        TradingStrategyDelivery strategyDelivery = new TradingStrategyDelivery();
        strategyDelivery.setTradingStrategyCode(tradingStrategyCode);
        strategyDelivery.setDeliveryStatus(2);
        strategyDelivery.setUrl(serviceNodeInfo.getApiUrl());
        strategyDelivery.setCurrentNodeId(beneficiaryRouteId);

        log.debug("订单状态回传交付中 {}, {}", serviceNodeInfo, JSONUtil.toJsonStr(strategyDelivery));
        final ShuhanResponse<Boolean> result = nodeRemoteService.deliveryStatusReport(strategyDelivery);

        if (result == null || !result.isSuccess()) {
            log.error("业务节点【{}】订单状态回传交付中异常: {}", serviceNodeInfo.getServiceNodeId(), result != null ? result.getMessage() : "订单状态回传交付中失败");
            throw new RestfulApiException("订单状态回传交付中失败");
        }
    }


    public User changeUserId(String thirdUserId) {
        QUser user = QUser.user;
        User userLocal = queryFactory.selectFrom(user).where(user.ext.id.eq(thirdUserId)).fetchOne();

        if (Objects.isNull(userLocal)) {
            throw new RestfulApiException("根据第三方id【" + thirdUserId + "】查询本地用户为空");
        }
        log.info("第三方用户id【{}】解析成本地用户id信息【{}】", thirdUserId, userLocal.getId());
        return userLocal;
    }

}
