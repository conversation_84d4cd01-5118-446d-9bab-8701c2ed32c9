package com.ailpha.ailand.dataroute.endpoint.order.schedule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.GanZhouPage;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.AssetBeneficiaryRel;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.GanZhouOrderRemoteService;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.ContractPageQuery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderPageQuery;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.ContractInfo;
import com.ailpha.ailand.dataroute.endpoint.order.remote.response.OrderInfo;
import com.ailpha.ailand.dataroute.endpoint.order.service.GanZhouResolveService;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.AssetBeneficiaryExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderResolveDTO;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.GenerateKeyRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneAssetApiReq;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRepository;
import com.dbapp.rest.core.Pair;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @description: 订单有效期检查
 * @date 2025/2/25 15:29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderSyncSchedule {

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final CompanyRepository companyRepository;

    private final OrderManagerService orderManagerService;

    private final GanZhouOrderRemoteService ganZhouOrderRemoteService;

    private final GanZhouResolveService ganZhouResolveService;

    private final DataProductService dataProductService;

    private final UserRepository userRepository;

    private final EndpointRemote endpointRemote;

    @Value("${order.sync.size:50}")
    private Integer pageSize;

    @Value("${circulation.platform.id}")
    private String circulationPlatformId;


    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.MINUTES)
    public void syncOrder() {
        if (BUSY.get()) {
            return;
        }

        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        companyRepository.findByStatusAndDeletedFalse(CompanyStatus.REVIEW_PASS).forEach(company -> {
            try {
                BUSY.set(true);
                int pageNum = 1;
                log.info("sync order from basic capability platform ....");
                try {
                    // 查本地 public 库获取受益人 信息
                    Pair<User, Company> pair = findUserInfo(company.getId());
                    doSync(pageNum, company.getNodeId(), company.getId(), pair);
                } catch (Exception e) {
                    log.error("同步订单数据异常: platformId: {} company: {}", company.getNodeId(), company.getId(), e);
                }

            } finally {
                BUSY.set(false);
            }
        });

    }


    /**
     * 调用基础能力平台 获取 订单接口
     */
    private void doSync(Integer pageNum, String platformId, Long companyId, Pair<User, Company> pair) {
        // 指定 连接器、订单已支付 的数据
        OrderPageQuery orderPageQuery = new OrderPageQuery();
        orderPageQuery.setPageNum(pageNum);
        orderPageQuery.setPageSize(pageSize);
//        orderPageQuery.setSrcPlatformId(circulationPlatformId);
        orderPageQuery.setPlatformId(circulationPlatformId);
        orderPageQuery.setDce(platformId);
        orderPageQuery.setConsumerCode(platformId.substring(1, 19));
        orderPageQuery.setOrderStatus("1");

        // 拿到平台订单数据
        GanZhouPage<OrderInfo> orderPage = null;
        try {
            orderPage = ganZhouOrderRemoteService.orderCatalogQuery(orderPageQuery);
            Assert.isTrue(orderPage.isSuccess(), "订单数据查询失败");
        } catch (Exception e) {
            log.error("同步订单数据异常：orderPageQuery: {}  orderPage: {}", orderPageQuery, orderPage, e);
            return;
        }

        if (CollectionUtil.isEmpty(orderPage.getData())) {
            log.info("当前同步 platformId【{}】订单数据为空", circulationPlatformId);
            return;
        }

        log.debug("获取订单数据参数: {} 订单条数: {}", orderPageQuery, orderPage.getData().size());
        // 新起一个线程
        GanZhouPage<OrderInfo> finalOrderPage = orderPage;
        Boolean future = AsyncManager.getInstance().executeFuture(() -> {
            List<OrderInfo> rows = finalOrderPage.getData();
            if (CollectionUtil.isEmpty(rows)) {
                return true;
            }

            // 赣州只有一个租户
            TenantContext.setCurrentTenant("tenant_" + companyId);
            // 连接器id
            MDC.put("currentNodeId", platformId);

            NodeDTO nodeInfo = SpringUtil.getBean(CompanyService.class).getNodeInfo(companyId);
            MDC.put("hubInfo", JSONUtil.toJsonStr(nodeInfo.getHubInfo()));

            // step 1、查看对应租户下是否存在 拉取到的订单信息
            rows = ganZhouResolveService.filterExistOrder(rows);
            if (CollectionUtil.isEmpty(rows)) {
                return true;
            }

            // step 2、存在 —— 过滤，不存在 —— 解析成【连接器】订单信息——保存
            // step 3、根据订单 合同ID，查询合同信息，解析成【连接器】交付信息——保存
            List<OrderResolveDTO> list = rows.stream().map(orderInfo -> {
                OrderResolveDTO orderResolveDTO;
                try {
                    orderResolveDTO = resolveOrderRecord(orderInfo, platformId, companyId, pair);
                } catch (Exception e) {
                    log.error("解析订单信息异常：orderInfo: {}", orderInfo, e);
                    return null;
                }
                return orderResolveDTO;
            }).filter(Objects::nonNull).toList();
            list.forEach(ganZhouResolveService::save);

            return true;
        });
        log.debug("解析订单数据结果: {}", future);

        long total = orderPage.getTotal();
        long pageCount = (total + pageSize - 1) / pageSize;
        if (pageCount > pageNum) {
            ++pageNum;
            doSync(pageNum, platformId, companyId, pair);
        }
    }


    private Pair<User, Company> findUserInfo(Long companyId) {
        Company company = null;
        Optional<Company> optional = companyRepository.findById(companyId);
        if (optional.isPresent()) {
            company = optional.get();
        }
        if (company == null) {
            log.warn("根据企业ID【{}】查询企业信息为空", companyId);
            return null;
        }

        User user = userRepository.findFirstByCompanyIdAndDeletedFalse(companyId);
        if (user == null) {
            log.warn("根据企业ID【{}】查询企业信息归属下唯一用户为空", companyId);
            return null;
        } else {
            log.warn("根据企业ID【{}】查询企业信息归属下唯一用户【{}】", companyId, user.getAccount());
        }

        return new Pair<>(user, company);
    }


    /**
     * 解析订单数据信息
     */
    private OrderResolveDTO resolveOrderRecord(OrderInfo orderInfo, String platformId, Long companyId, Pair<User, Company> pair) throws Exception {

        // 查询产品详情
        DataProductVO dataProductVO = dataProductService.getDataProductByDataProductPlatformId(orderInfo.getSrcProductId());
        CompanyDTO company = dataProductVO.getProvider().getCompany();

        if (Objects.isNull(pair)) {
            log.error("根据订单 companyId【{}】未查询到相关获益人", companyId);
            return null;
        }

        User beneficiaryUser = pair.getLeft();
        Company beneficiaryCompany = pair.getRight();

        OrderApprovalRecord record = OrderApprovalRecord.builder()
                .id(orderInfo.getOrderNo())
                .type(AssetType.PRODUCT)
                .assetId(dataProductVO.getId())
                .assetName(orderInfo.getSrcProductName())
                .deliveryMode(dataProductVO.getDeliveryModes().getFirst().name())
                // 使用连接器唯一用户id
                .beneficiaryId(beneficiaryUser.getId())
                .beneficiaryUsername(beneficiaryUser.getAccount())
                .beneficiaryRouterId(platformId)
                .beneficiaryEnterpriseName(beneficiaryCompany.getOrganizationName())
                .beneficiaryEnterpriseProperty(beneficiaryCompany.getIndustryType())
                .approverId(dataProductVO.getUserId())
                .approverUsername(dataProductVO.getProvider().getUsername())
                .approverRouterId(dataProductVO.getProvider().getRouterId())
                .approverEnterpriseName(company.getOrganizationName())
                .chargingWay("预付费")
                .meteringWay("按次")
                .allowance(new BigInteger("999"))
                .successfulUsage(new BigInteger("0"))
                .status("APPROVED")
                .createTime(DateUtil.parse(orderInfo.getCreateTime()))
                .updateTime(DateUtil.parse(orderInfo.getUpdateTime()))
                .approveTime(new Date())
                .build();

        OderRecordExtend recordExtend = OderRecordExtend.builder()
                .callPrice(orderInfo.getAmount() == null ? "" : String.valueOf(orderInfo.getAmount()))
                .price(orderInfo.getAmount() == null ? null : orderInfo.getAmount().doubleValue())
                .beneficiaryCreditCode(beneficiaryCompany.getCreditCode())
                .approverCreditCode(company.getCreditCode())
                .beneficiaryCompanyId(String.valueOf(companyId))
                .approverCompanyId(String.valueOf(company.getId()))
                .dataProductPlatformId(dataProductVO.getDataProductPlatformId())
                .build();
        record.setExtend(JSONUtil.toJsonStr(recordExtend));

        // 根据订单信息，获取合约内容
        GanZhouPage<ContractInfo> contractCatalogQuery;
        if (StringUtils.isBlank(orderInfo.getContractNo())) {
            log.warn("订单【{}】关联合约编号为空", orderInfo.getOrderNo());
            return null;
        }
        try {
            contractCatalogQuery = ganZhouOrderRemoteService.contractCatalogQuery(ContractPageQuery.builder().pageNum(1).pageSize(10).srcPlatformId(circulationPlatformId).contractNo(orderInfo.getContractId()).build());
            Assert.isTrue(contractCatalogQuery.isSuccess(), "订单数据查询失败");
        } catch (Exception e) {
            log.error("根据订单合同编号【{}】获取合同数据异常：", orderInfo.getContractNo(), e);
            return null;
        }

        ContractInfo contractInfo = contractCatalogQuery.getData().stream().findFirst().orElse(null);
        if (contractInfo == null) {
            log.warn("订单【{}】根据合约编号【{}】查询合约内容为空", orderInfo.getOrderNo(), orderInfo.getContractNo());
            return null;
        }

        // 场景ID生成规则：ds + 年月日 + 13位随机数
        DeliveryType deliveryType = DeliveryType.getDeliveryTypeByName(record.getDeliveryMode());
        String deliverySceneId = contractInfo.getContractNo();
        DeliveryScene scene = DeliveryScene.builder()
                .id(deliverySceneId)
                // 目前一种资产只能有一种交付 ？
                .deliveryType(deliveryType)
                .sceneStatus("RUNNING")
                .createTime(DateUtil.parse(contractInfo.getSignTime()))
                .createUser(record.getBeneficiaryId())
                .ext(new DeliveryScene.Ext())
                .build();

        String apiKey;
        try {
            apiKey = generateApiKey(deliveryType, company.getNodeId(), dataProductVO);
        } catch (Exception e) {
            throw new RestfulApiException("获取apiKey异常", e);
        }

        // todo：如果资产获益人存在记录 更新
        // todo 连接器只有一个用户 —— 这个可能有问题
        AssetBeneficiaryExtend extend = new AssetBeneficiaryExtend();
        extend.setApiKey(apiKey);
        AssetBeneficiaryRel rel = AssetBeneficiaryRel.builder()
                .id(UUID.randomUUID().toString().replace("-", ""))
                .assetId(record.getAssetId())
                .beneficiaryId(record.getBeneficiaryId())
                .orderId(record.getId())
                // 可以不填充
                .extend(JSONUtil.toJsonStr(extend))
                .createTime(new Date())
                .updateTime(new Date())
                .build();

        SceneAssetApiReq.ExtData extData = new SceneAssetApiReq.ExtData();
        extData.setDataProductName(record.getAssetName());
        extData.setPlatformId(company.getNodeId());
        extData.setUserId(record.getApproverId());
        extData.setRouteId(company.getNodeId());
        extData.setDataProductPlatformId(dataProductVO.getDataProductPlatformId());
        extData.setBuyerCompanyId(String.valueOf(companyId));
        extData.setSellerCompanyId(String.valueOf(company.getId()));
        extData.setDeliveryType(record.getDeliveryMode());

        SceneAsset sceneAsset = SceneAsset.builder()
                .id(UUID.randomUUID().toString().replace("-", ""))
                .deliverySceneId(deliverySceneId)
                .dataAssetId(record.getAssetId())
                .apiId(IdUtil.fastSimpleUUID())
                // 接口获取授权key
                .apiKey(apiKey)
                .orderId(record.getId())
                .ext(JSONUtil.toJsonStr(extData))
                .build();

        return new OrderResolveDTO(record, rel, scene, sceneAsset);
    }


    private String generateApiKey(DeliveryType deliveryType, String platformIdAsset, DataProductVO dataProductVO) throws Exception {
        String currentNodeId = MDC.get("currentNodeId");
        if (StringUtils.equals(platformIdAsset, currentNodeId)) {
            // 购买当前节点
            switch (deliveryType) {
                case API -> {
                    String dataProductName = dataProductVO.getDataProductName();
                    String username = dataProductVO.getProvider().getUsername();
                    String gatewayServiceRouteId = dataProductVO.getGatewayServiceRouteId();
                    log.info("generate api key info dataProductName: [{}]  username: [{}] gatewayServiceRouteId: [{}]", dataProductName, username, gatewayServiceRouteId);
                    return orderManagerService.authGatewayApiKey(dataProductName, username, gatewayServiceRouteId);
                }
                case FILE_DOWNLOAD -> {
                    log.info("generate minio access key info dataProductId: [{}]  dataUserId: [{}]", dataProductVO.getId(), dataProductVO.getUserId());
                    return orderManagerService.authMinioApiKey(dataProductVO.getId(), dataProductVO.getUserId());
                }
                default -> throw new RestfulApiException("暂不支持【" + deliveryType + "】交付方式");
            }
        } else {
            // 跨节点购买
            GenerateKeyRequest request = GenerateKeyRequest.builder()
                    .productId(dataProductVO.getId())
                    .productUserId(dataProductVO.getUserId())
                    .username(dataProductVO.getProvider().getUsername())
                    .dataProductName(dataProductVO.getDataProductName())
                    .gatewayServiceRouteId(dataProductVO.getGatewayServiceRouteId())
                    .deliveryType(deliveryType)
                    .build();
            SuccessResponse<String> response = endpointRemote.generateKey(request);
            log.debug("跨节点资产key返回:{}", response);
            Assert.isTrue(response.isSuccess(), "跨节点资产key获取异常");
            return response.getData();
        }
    }

}
