package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublish {
    /**
     * 产品编号
     */
    String productId;
    /**
     * 上架业务节点名称
     */
    String platformName;
    /**
     * 上架业务节点ID
     */
    String platformId;
    /**
     * 上架业务节点位置
     */
    String platformLocation;
    /**
     * 其他
     */
    String others;
    /**
     * 计量方式
     */
    String measureMethod;
    /**
     * 计量单位
     */
    String unit;
    /**
     * 价格（单位：分）
     */
    Integer price;
    /**
     * 交付方式说明
     */
    String deliveryInfo;
    /**
     * 版本号
     */
    Integer version;
}
