package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataAssetPrepareStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticAssetOrderDeliveryVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产（资源、产品）信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductListVO {
    @Schema(description = "资产id")
    String id;
    @Schema(description = "数据资源全局（连接器空间）唯一标识")
    String dataProductPlatformId;
    @Schema(description = "资产名称")
    String productName;
    @Schema(description = "产品简介")
    String description;
    @Schema(description = "数据产品类型: \"01 数据集\", \"02 API产品\", \"03 数据应用\", \"04 数据报告\", \"05 其他\"")
    String type;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "交付场景")
    List<DeliveryMode> deliveryModes;
    @Schema(description = "交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）")
    String deliveryMethod;
    @Schema(description = "数据样例")
    String dataSampleAttach;
    @Schema(description = "上架业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价 单位为分，19.99 元表示为 1999")
    Integer price;
    @Schema(description = "登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销 item_status5 功能节点待审批 item_status6 功能节点通过 item_status7 功能节点拒绝")
    String itemStatus;

    @Schema(description = "上下架状态: 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销 5 业务节点待审批 6 业务节点通过 7 业务节点拒绝")
    String publishStatus;
    @Schema(description = "创建时间")
    String createTime;
    @Schema(description = "更新时间")
    String updateTime;
    String userId;
    String username;
    @Schema(description = "连接器ID")
    String routerId;
    @Schema(description = "数据资产预处理状态")
    DataAssetPrepareStatus prepareStatus;

    @Schema(description = "登记提交时间")
    String registrationSubmitTime;
    @Schema(description = "登记时间")
    String registrationTime;
    @Schema(description = "上架提交时间")
    String publishSubmitTime;
    @Schema(description = "上架时间")
    String publishTime;

    @Schema(description = "数据接入方式")
    SourceType accessWay;

    @Schema(description = "API网关跳转参数routeId的值")
    String gatewayServiceRouteId;

    @Schema(description = "统计信息")
    StatisticAssetOrderDeliveryVO statisticInfo;

    List<DataAssetExt.ProcessLog> processLogs;
}
