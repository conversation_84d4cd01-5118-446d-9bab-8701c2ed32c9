package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SyncDatasetResult implements Serializable {
    /**
     * 资产类型
     */
    AssetType assetType;
    /**
     * 资产ID
     */
    String assetId;
    /**
     * 同步数据集是否成功
     */
    Boolean result;
    /**
     * 失败原因
     */
    String message;
}
