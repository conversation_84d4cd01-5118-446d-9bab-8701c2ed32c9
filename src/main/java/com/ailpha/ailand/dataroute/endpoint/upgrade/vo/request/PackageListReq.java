package com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.dbapp.rest.request.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/6
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PackageListReq extends Page implements Serializable {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    private UpgradeModule module;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "上传时间-区间开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "上传时间-区间结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
