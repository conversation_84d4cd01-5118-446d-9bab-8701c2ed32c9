package com.ailpha.ailand.dataroute.endpoint.common.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Knife4jConfig {
    @Value("${ailand.application.version:0.0.1}")
    String applicationVersion;

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("连接器终端")
                        .description("连接器终端接口文档")
                        .contact(new Contact().name("安恒信息").url("https://www.dbappsecurity.com.cn/"))
                        .summary("连接器终端接口文档")
                        .termsOfService("https://www.dbappsecurity.com.cn/")
                        .version(applicationVersion)
                );
    }
}
