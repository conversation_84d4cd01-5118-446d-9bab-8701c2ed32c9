package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/3/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AiSortDataSourceResp implements Serializable {

    @Schema(description = "认证类型：NORMAL,KERBEROS,TOKEN,AKSK")
    String authType;
    @Schema(description = "分类模板id")
    Long classifyId;
    @Schema(description = "模板名称")
    String classifyName;
    @Schema(description = "分类分级状态")
    String classifyStatus;
    @Schema(description = "最近分类分级时间")
    String classifyTime;
    @Schema(description = "集群类型:APACHE_HADOOP,CDH,TDH")
    String clusterType;
    @Schema(description = "字段数量")
    Long colCount;
    @Schema(description = "减少字段")
    Long columnDecrease;
    @Schema(description = "新增字段")
    Long columnIncrease;
    @Schema(description = "conf文件名称")
    String confFile;
    @Schema(description = "conf文件id, krb5.conf")
    Long confFileId;
    @Schema(description = "创建时间")
    String createTime;
    @Schema(description = "创建人")
    String createUser;
    @Schema(description = "周期:daily0;daily4;tue0;tue4;monthly0;monthly4;once")
    String cycle;
    @Schema(description = "数据库环境类型: NATIVE=原生,SHARDING=分片")
    String dbEnvType;
    @Schema(description = "仅同步该库")
    Boolean dbFilter;
    @Schema(description = "数据库名称")
    String dbName;
    @Schema(description = "数据库类型")
    String dbType;
    @Schema(description = "数据库类型描述")
    String dbTypeDesc;
    @Schema(description = "用户名")
    String dbUser;
    @Schema(description = "数据库版本号")
    String dbVersion;
    @Schema(description = "部门")
    String department;
    @Schema(description = "华为dli账户名")
    String domainName;
    @Schema(description = "驱动配置")
    String driverConfig;
    @Schema(description = "执行次数")
    Long execFrequency;
    @Schema(description = "-")
    File file;
    @Schema(description = "过滤临时表统配符")
    String filterTemp;
    @Schema(description = "hive认证token")
    String guardianToken;
    @Schema(description = "是否关联任务")
    Boolean hasTask;
    @Schema(description = "地址")
    String host;
    @Schema(description = "id")
    Long id;
    @Schema(description = "kerberosPrincipal")
    String kerberosPrincipal;
    @Schema(description = "keytab文件名称")
    String keytabFile;
    @Schema(description = "keytab文件id")
    Long keytabFileId;
    @Schema(description = "metadata原数据库类型")
    String metadataDbType;
    @Schema(description = "源名称")
    String name;
    @Schema(description = "责任人")
    String owner;
    @Schema(description = "密码")
    String password;
    @Schema(description = "插件名")
    String pluginName;
    @Schema(description = "端口")
    Long port;
    @Schema(description = "华为dli项目id")
    String projectId;
    @Schema(description = "队列名称")
    String queueName;
    @Schema(description = "华为dli地区名称")
    String regionName;
    @Schema(description = "角色")
    String roleName;
    @Schema(description = "是否保存账号密码")
    Boolean save;
    @Schema(description = "扫描标识")
    String scanId;
    @Schema(description = "-")
    ScanResult scanResult;
    @Schema(description = "服务名(informix数据库用)")
    String serviceName;
    @Schema(description = "状态")
    String status;
    @Schema(description = "同步错误信息")
    String syncError;
    @Schema(description = "数据源同步模式")
    String syncMode;
    @Schema(description = "同步进度")
    Long syncProgress;
    @Schema(description = "完成表数")
    Long syncTableComplete;
    @Schema(description = "表总数")
    Long syncTableCount;
    @Schema(description = "失败表数")
    Long syncTableFail;
    @Schema(description = "同步时间")
    String syncTime;
    @Schema(description = "表数量")
    Long tableCount;
    @Schema(description = "是否绑定任务")
    Boolean taskBound;
    @Schema(description = "修改时间")
    String updateTime;
    @Schema(description = "修改人")
    String updateUser;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ScanResult implements Serializable {
        @Schema(description = "字段新增列表")
        Object columnAddList;
        @Schema(description = "表新增列表")
        List<String> tableAddList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class File implements Serializable {
        @Schema(description = "目录")
        String dir;
        @Schema(description = "文件编码")
        String encoding;
        @Schema(description = "列分隔符")
        String fieldSeparator;
        @Schema(description = "列分隔符是否为ascii")
        Boolean fieldSeparatorAscii;
        @Schema(description = "文件名匹配")
        String fileNamePattern;
        @Schema(description = "表头行号(解析csv文件使用)")
        Long headerRowNo;
        @Schema(description = "文件源id")
        Long id;
        @Schema(description = "本地文件关联id")
        Long localFileId;
        @Schema(description = "本地文件名称")
        String localFileName;
        @Schema(description = "私钥密码")
        String passphrase;
        @Schema(description = "私钥文件关联id")
        Long prvkeyFileId;
        @Schema(description = "列包围符")
        String quoteCharacter;
        @Schema(description = "列包围符是否为ascii")
        Boolean quoteCharacterAscii;
        @Schema(description = "sftp认证方式")
        String sftpAuthType;
    }
}
