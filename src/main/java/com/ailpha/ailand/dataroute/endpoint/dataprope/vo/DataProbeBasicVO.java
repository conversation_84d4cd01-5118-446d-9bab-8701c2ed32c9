package com.ailpha.ailand.dataroute.endpoint.dataprope.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/2/20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProbeBasicVO implements Serializable {

    @Schema(description = "ID")
    String id;

    @Schema(description = "表注释")
    String tableComment;

    @Schema(description = "表名")
    String tableName;

    @Schema(description = "数据源名称")
    String datasourceName;

    @Schema(description = "数据源类型")
    String datasourceType;

    @Schema(description = "schema")
    String schema;

    @Schema(description = "主机")
    String host;

    @Schema(description = "端口")
    String port;

    @Schema(description = "账号")
    String username;

    @Schema(description = "密码")
    String password;
}
