package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiUserVO {

    @Schema(description = "用户id")
    String userId;

    @Schema(description = "用户名")
    String name;

    @Schema(description = "api key")
    String apiKey;

    @Schema(description = "用户节点id")
    String serverId;
}
