package com.ailpha.ailand.dataroute.endpoint.user.vo;

import com.dbapp.rest.request.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "操作日志查询请求")
public class QueryLogRequest extends Page {
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    @Schema(description = "开始时间")
    Date startDate;
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm:ss")
    @Schema(description = "结束时间")
    Date endDate;
    @Schema(description = "操作类型")
    String opType;
    @Schema(description = "操作模块")
    String opModule;
    @Schema(description = "IP")
    String ip;
    @Schema(description = "用户名称")
    String username;
}
