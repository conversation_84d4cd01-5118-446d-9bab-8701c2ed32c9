package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UpdateCompanyRequest {
    @Schema(description = "企业id")
    Long id;
    // 企业管理员账户有效期
    @Schema(description = "企业管理员账户有效期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date adminExpireDays;
}
