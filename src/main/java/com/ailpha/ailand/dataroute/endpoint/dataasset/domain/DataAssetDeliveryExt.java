package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.ServiceNode;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataAssetDeliveryExt {
    /**
     * 交付方式：原数据、TEE、MPC
     */
    @Builder.Default
    List<DeliveryMode> deliveryModes = new ArrayList<>();
    /**
     * 交付方式：原数据、TEE、MPC
     */
    DeliveryMode deliveryMode;
    /**
     * MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）
     */
    @Builder.Default
    List<MPCPurpose> mpcPurpose = new ArrayList<>();
    // >>> 产品上架信息
    List<ServiceNodeApplyListVO> serviceNodes;
    /**
     * 交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）
     */
    String deliveryMethod;
    /**
     * 使用限制：数据产品在使用过程中的任何限制或条件，标注使用限制场景
     */
    String limitations;
    /**
     * 授权使用：数据产品使用时是否需要授权： 0：否 1：是
     */
    String authorize;
    /**
     * 是否允许二次加工：0：不允许 1：允许
     */
    String isSecondaryProcessed;
    /**
     * 计费方式: 01：一次性计费
     * 02：按次计费
     * 03：按时间计费
     */
    String billingMethod;
    /**
     * 购买单位: 011：一次性
     * 021：次
     * 031:天
     * 032:月
     * 033:年
     * 041:MB
     * 042:GB
     * 043:TB
     */
    String purchaseUnit;
    /**
     * 单价
     */
    String price;
    // <<< 产品上架信息
}
