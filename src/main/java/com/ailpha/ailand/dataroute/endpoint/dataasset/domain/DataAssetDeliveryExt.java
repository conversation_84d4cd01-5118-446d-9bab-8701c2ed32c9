package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.TEEPurpose;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PROTECTED)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataAssetDeliveryExt implements Serializable {
    /**
     * 交付方式：原数据、TEE、MPC
     */
    @Builder.Default
    List<DeliveryMode> deliveryModes = new ArrayList<>();
    /**
     * MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）
     */
    @Builder.Default
    List<MPCPurpose> mpcPurpose = new ArrayList<>();
    // >>> 产品上架信息
    List<ServiceNodeApplyListVO> serviceNodes;
    /**
     * 交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）
     */
    String deliveryMethod;
    /**
     * 使用限制：数据产品在使用过程中的任何限制或条件，标注使用限制场景
     */
    String limitations;
    /**
     * 授权使用：数据产品使用时是否需要授权： 0：否 1：是
     */
    String authorize;
    /**
     * 是否允许二次加工：0：不允许 1：允许
     */
    String isSecondaryProcessed;
    /**
     * 计费方式: 01：一次性计费
     * 02：按次计费
     * 03：按时间计费
     */
    String billingMethod;
    /**
     * 购买单位: 011：一次性
     * 021：次
     * 031:天
     * 032:月
     * 033:年
     * 041:MB
     * 042:GB
     * 043:TB
     */
    String purchaseUnit;
    /**
     * 单价 单位 分
     */
    Integer price;
    // <<< 产品上架信息

    /**
     * 交付方式说明 形式为文件 Base64 编码后的字符串
     */
    String deliveryInfo;
    /**
     * 交付方式说明文件名称.后缀
     */
//    String deliveryInfoFileName;

    public List<DeliveryMode> deliveryModesForPublish() {
        if (CollectionUtil.isNotEmpty(this.mpcPurpose)) {
            List<DeliveryMode> deliveryModes1 = JSONUtil.toList(JSONUtil.toJsonStr(this.getDeliveryModes()), DeliveryMode.class);
            for (MPCPurpose purpose : this.mpcPurpose) {
                switch (purpose) {
                    case CIPHER_TEXT_COMPUTE -> deliveryModes1.add(DeliveryMode.MPC_CIPHER_TEXT_COMPUTE);
                    case PRIVATE_SET_INTERSECTION -> deliveryModes1.add(DeliveryMode.MPC_PRIVATE_SET_INTERSECTION);
                    case PRIVATE_INFORMATION_RETRIEVAL ->
                            deliveryModes1.add(DeliveryMode.MPC_PRIVATE_INFORMATION_RETRIEVAL);
                }
            }
            deliveryModes1.remove(DeliveryMode.MPC);
            return deliveryModes1;
        }
        return deliveryModes;
    }

    public static List<DeliveryMode> deliveryModesForOrderResolve(List<DeliveryMode> deliveryModes, List<MPCPurpose> mpcPurpose, List<TEEPurpose> teePurpose) {
        if (CollectionUtil.isNotEmpty(deliveryModes) && deliveryModes.contains(DeliveryMode.MPC) && !mpcPurpose.isEmpty()) {
            for (MPCPurpose purpose : mpcPurpose) {
                switch (purpose) {
                    case CIPHER_TEXT_COMPUTE -> deliveryModes.add(DeliveryMode.MPC_CIPHER_TEXT_COMPUTE);
                    case PRIVATE_SET_INTERSECTION -> deliveryModes.add(DeliveryMode.MPC_PRIVATE_SET_INTERSECTION);
                    case PRIVATE_INFORMATION_RETRIEVAL ->
                            deliveryModes.add(DeliveryMode.MPC_PRIVATE_INFORMATION_RETRIEVAL);
                }
            }
            deliveryModes.remove(DeliveryMode.MPC);
        }

        if (CollectionUtil.isNotEmpty(deliveryModes) && deliveryModes.contains(DeliveryMode.TEE) && !teePurpose.isEmpty()) {
            for (TEEPurpose purpose : teePurpose) {
                switch (purpose) {
                    case TEE_ONLINE -> deliveryModes.add(DeliveryMode.TEE_ONLINE);
                    case TEE_OFFLINE -> deliveryModes.add(DeliveryMode.TEE_OFFLINE);
                    case TEE_MODEL_PREDICT -> deliveryModes.add(DeliveryMode.TEE_MODEL_PREDICT);
                    case TEE_MODEL_OPTIMIZE -> deliveryModes.add(DeliveryMode.TEE_MODEL_OPTIMIZE);
                }
            }
            deliveryModes.remove(DeliveryMode.TEE);
        }

        return deliveryModes;
    }

}
