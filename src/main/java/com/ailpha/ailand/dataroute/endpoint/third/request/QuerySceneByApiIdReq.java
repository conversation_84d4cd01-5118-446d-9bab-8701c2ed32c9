package com.ailpha.ailand.dataroute.endpoint.third.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class QuerySceneByApiIdReq {

    @Schema(description = "apiId")
    List<String> apiIdList;
    List<String> sceneIds;
}
