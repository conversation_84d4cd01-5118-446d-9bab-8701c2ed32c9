package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GanzhouPublishAttachFile {
    @Schema(description = "登记确认单")
    String safetyFile;
    @Schema(description = "其他说明")
    String protectFile;
}
