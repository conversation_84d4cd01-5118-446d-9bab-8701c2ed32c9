package com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response;

import com.ailpha.ailand.dataroute.endpoint.servicenode.vo.ServiceNodeExtend;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.RegionSyncListVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/7/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeAppliedListVO implements Serializable {

    @Schema(description = "ID（本地业务ID）")
    private String id;

    @Schema(description = "业务节点登记名称")
    String entryName;

    @Schema(description = "业务节点标识编码（业务节点ID）")
    String serviceNodeId;

    @Schema(description = "业务功能类型：1－应用侧基础设施，2－数据交易类，3－数据开发利用类，4－公共数据授权运营平台类，5－公共服务平台类")
    String type;

    @Schema(description = "业务功能类型描述")
    String typeDescription;

    @Schema(description = "业务节点IP地址")
    String ip;

    @Schema(description = "业务节点域名")
    String domainName;

    @NotEmpty(message = "业务节点接口地址不能为空")
    @Schema(description = "业务节点接口地址")
    String apiUrl;

    @Schema(description = "业务功能简介")
    String introduction;

    @Schema(description = "业务节点版本")
    String version;

    @Schema(description = "备注")
    String reserveNotes;

    @Schema(description = "所属法人或其他组织名称")
    String enterpriseName;

    @Schema(description = "所属法人或其他组织身份标识码")
    String enterpriseIdentityId;

    @Schema(description = "状态：APPLY（待审批）APPROVED（已通过）REJECTED（已拒绝）")
    String processStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processTime;

    @Schema(description = "扩展字段")
    private ServiceNodeExtend extend;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    public String getTypeDescription() {
        return RegionSyncListVO.getTypeDescription(this.type);
    }
}
