package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.ApproveStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产更新请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetUpdateRequest {

    @Schema(description = "资产id", requiredMode = Schema.RequiredMode.REQUIRED)
    String assetId;

    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;

    @Schema(description = "数据资源大小: MB")
    Long capacity;

    @Schema(description = "数据覆盖范围")
    String dataCoverage;

    @Schema(description = "数据覆盖周期开始时间")
    String dataCoverageTimeStart;

    @Schema(description = "数据覆盖周期结束时间")
    String dataCoverageTimeEnd;

    @Schema(description = "敏感等级")
    String sensitiveLevel;

    @Schema(description = "更新频率")
    String updateFrequency;

    @Schema(description = "标签")
    List<String> tag;

    @Schema(description = "资源摘要")
    String describeMessage;

    @Schema(description = "数据类型")
    DataType dataType;

    @Schema(description = "交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC")
    List<DeliveryMode> deliveryModes;
    ApproveStatus approveStatus;

    String extraData;
}
