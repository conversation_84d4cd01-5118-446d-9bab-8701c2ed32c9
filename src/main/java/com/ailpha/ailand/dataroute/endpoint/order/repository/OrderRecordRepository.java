package com.ailpha.ailand.dataroute.endpoint.order.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.OrderClassify;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface OrderRecordRepository extends JpaRepository<OrderApprovalRecord, String>, JpaSpecificationExecutor<OrderApprovalRecord> {

    @Query("select count(1) from OrderApprovalRecord or where or.classify='NORMAL'")
    Long queryCount();

    @Query("select count(1) from OrderApprovalRecord or where or.classify='NORMAL' and or.beneficiaryId=:currentUserId")
    Long queryCountByUserId(String currentUserId);

    @Query("select count(1) from OrderApprovalRecord or where or.classify='NORMAL' and or.beneficiaryRouterId=:beneficiaryRouterId and or.id=:orderId")
    Long queryCountByBeneficiaryRouterId(String orderId, String beneficiaryRouterId);

    OrderApprovalRecord findFirstByAssetIdAndBeneficiaryIdAndClassify(String assetId, String beneficiaryId, OrderClassify classify);
}
