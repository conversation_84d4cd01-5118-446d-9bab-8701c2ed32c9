package com.ailpha.ailand.dataroute.endpoint.common;

import cn.hutool.json.JSONUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Data
public class ServiceNodeMetaData {
    String url;
    String nodeId;

    public String toBase64() {
        return Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(this).getBytes(StandardCharsets.UTF_8));
    }

    public static ServiceNodeMetaData fromBase64(String base64) {
        return JSONUtil.toBean(new String(Base64.getDecoder().decode(base64), StandardCharsets.UTF_8), ServiceNodeMetaData.class);
    }
}
