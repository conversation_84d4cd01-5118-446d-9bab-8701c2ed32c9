package com.ailpha.ailand.dataroute.endpoint.connector.handler.strategy;

import com.ailpha.ailand.dataroute.endpoint.connector.RouteActivateContext;
import com.ailpha.ailand.dataroute.endpoint.connector.handler.DataRouteActivateHandler;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.LicenseDTO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserRole;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRepository;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@Order(1004)
public class SyncAdmin2IamHandler implements DataRouteActivateHandler {

    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;

    @Override
    public void handler(RouteActivateContext request) {
        // 同步至零信任
        // 保存本地
        LicenseDTO license = request.getLicense();
        User user = new User();
        user.setAccount(license.getAdmin().getAccount());
        userRepository.saveAndFlush(user);
        UserRole userRole = new UserRole();
        userRole.setUserId(user.getId());
        userRole.setRoleId(RoleEnums.SUPER_ADMIN.name());
        userRoleRepository.saveAndFlush(userRole);
    }
}
