package com.ailpha.ailand.dataroute.endpoint.order.remote.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2025/6/3 14:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessNodeOrderDTO {

    /**
     * 合同编号
     */
    private String tradingStrategyCode;

    /**
     * 合同名称—没有的话可规则生成
     */
    private String tradingStrategyName;

    /**
     * 合同描述—没有的话可传空
     */
    private String tradingStrategyContent;

    /**
     * 合同生效时间（13位时间戳）
     */
    private long tradingStrategyTime;

    /**
     * 买方连接器
     */
    private String buyerStrategyCode;

    /**
     * 卖方连接器
     */
    private String sellerStrategyCode;

    /**
     * 备注
     */
    private String transactionNotes;

    /**
     * 控制指令编号 —— 订单id
     */
    private String ctrlInstructionId;

    /**
     * 交付状态：
     * 0-未开始
     * 1-交付完成
     * 2-交付中
     */
    private String deliveryStatus;

    /**
     * 交易合约控制指令（包含访问控制）
     */
    private TransactionExecutionStrategy transactionExecutionStrategy;

    /**
     * 交易合约控制指令详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionExecutionStrategy {
        /**
         * 开始时间（13位时间戳）
         */
        private Long startTime;

        /**
         * 结束时间（13位时间戳）
         */
        private Long endTime;

        /**
         * 订单可被交付多少次
         */
        private BigInteger total;

        /**
         * 订单交付方式
         */
        private String deliveryMethod;

        /**
         * 发起方id（买方用户id）
         */
        private String initiatorId;

        /**
         * 买方企业信息
         */
        private String initiator;

        /**
         * 付款信息 —— <预付款、后付款>
         */
        private String paymentMethod;

        /**
         * 买方登录用户名
         */
        private String initiatorUsername;

        /**
         * 参与方id（卖方用户id）
         */
        private String participantId;

        /**
         * 产品编号
         */
        private String registrationId;

        /**
         * 计量方式
         */
        private String measurementMethod;

        /**
         * 计量单位
         */
        private String measurementUint;

        /**
         * 价格
         */
        private String price;

        /**
         * 订单创建时间（13位时间戳）
         */
        private long createTime;

        private DeliveryInfo deliveryInfo;

    }

    @Data
    public static class DeliveryInfo {

        /**
         * 拉—pull
         * 推—push
         * 都支持—all
         */
        private String transferMode;

        private String deliveryInfo;
    }
}