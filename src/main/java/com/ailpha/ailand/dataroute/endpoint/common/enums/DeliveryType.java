package com.ailpha.ailand.dataroute.endpoint.common.enums;

import cn.hutool.core.util.ObjectUtil;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/11/19 20:00
 */
public enum DeliveryType {

    API,

    FILE_DOWNLOAD,

    TEE_OFFLINE,
    TEE_ONLINE,
    MPC_PRIVATE_INFORMATION_RETRIEVAL,
    MPC_PRIVATE_SET_INTERSECTION,
    MPC_CIPHER_TEXT_COMPUTE,
    ;

    public static String getDeliveryMode(DeliveryType deliveryType) {
        return switch (deliveryType) {
            case API -> API.name();
            case FILE_DOWNLOAD -> FILE_DOWNLOAD.name();
            case TEE_OFFLINE -> TEE_OFFLINE.name();
            case TEE_ONLINE -> TEE_ONLINE.name();
            case MPC_PRIVATE_INFORMATION_RETRIEVAL -> MPC_PRIVATE_INFORMATION_RETRIEVAL.name();
            case MPC_PRIVATE_SET_INTERSECTION -> MPC_PRIVATE_SET_INTERSECTION.name();
            case MPC_CIPHER_TEXT_COMPUTE -> MPC_CIPHER_TEXT_COMPUTE.name();
        };
    }

    public static String getDeliveryModeByDeliveryRegister(DeliveryType deliveryType) {
        return switch (deliveryType) {
            case API -> API.name();
            case FILE_DOWNLOAD -> FILE_DOWNLOAD.name();
            case TEE_OFFLINE -> TEE_OFFLINE.name();
            case TEE_ONLINE -> TEE_ONLINE.name();
            case MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION, MPC_CIPHER_TEXT_COMPUTE -> "MPC";
        };
    }

    public static boolean needBuyerHeader(DeliveryType deliveryType) {
        return ObjectUtil.equals(API, deliveryType) || ObjectUtil.equals(deliveryType, FILE_DOWNLOAD);
    }

    public static DeliveryType getDeliveryTypeByName(String deliveryType) {
        return Arrays.stream(DeliveryType.values()).filter(type -> type.name().equals(deliveryType)).findFirst().orElse(null);
    }

}
