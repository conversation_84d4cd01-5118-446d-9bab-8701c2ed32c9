package com.ailpha.ailand.dataroute.endpoint.third.aigate;

import cn.hutool.crypto.digest.MD5;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.third.config.AIGateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import static com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig.acceptsUntrustedCertsHttpClient;

@Slf4j
@Component
public class AIGateClient {
    private final AIGateConfig aigateConfig;

    public AIGateClient(AIGateConfig aigateConfig, BaseCapabilityManager baseCapabilityManager) {
        this.aigateConfig = aigateConfig;
        aigateAssetApi = aiGateAssetApi(baseCapabilityManager);
    }

    private static AIGateAssetApi aigateAssetApi;

    private synchronized AIGateAssetApi aiGateAssetApi(BaseCapabilityManager baseCapabilityManager) {
        if (aigateAssetApi != null) {
            return aigateAssetApi;
        }
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        try {
            requestFactory.setHttpClient(acceptsUntrustedCertsHttpClient());
        } catch (Exception ignore) {
        }
        RestClientAdapter adapter = RestClientAdapter.create(RestClient.builder()
                .requestFactory(requestFactory)
                .baseUrl(baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.AI_GATE).getBaseUrl())
                .requestInterceptor((request, body, execution) -> {
                    if (log.isDebugEnabled()) {
                        log.debug("AIGate request: {}", new String(body));
                    }
                    // NOTE: 数据库网关接口签名是通过param传递的
//                    long accessTime = System.currentTimeMillis();
//                    String signValue = accessTime + "_" + aigateConfig.getAccessKeySecret();
//                    request.getHeaders().add("accessKeyId", aigateConfig.getAccessKeyId());
//                    request.getHeaders().add("accessTime", String.valueOf(accessTime));
//                    request.getHeaders().add("accessSign", MD5.create().digestHex(signValue));
                    return execution.execute(request, body);
                })
                .build());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        aigateAssetApi = factory.createClient(AIGateAssetApi.class);
        return aigateAssetApi;
    }

    public AIGateResponse<CreateAssetByRouterResponse> createAsset(CreateAssetByRouterRequest request) {
        long accessTime = System.currentTimeMillis();
        String signValue = accessTime + "_" + aigateConfig.getAccessKeySecret();
        return aigateAssetApi.createAsset(request, this.aigateConfig.getAccessKeyId(), String.valueOf(accessTime), MD5.create().digestHex(signValue));
    }

}
