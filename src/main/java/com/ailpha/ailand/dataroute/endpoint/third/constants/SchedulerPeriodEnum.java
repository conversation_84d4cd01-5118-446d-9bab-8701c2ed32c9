package com.ailpha.ailand.dataroute.endpoint.third.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

@Getter
public enum SchedulerPeriodEnum {

    DAY(0, "DAY", "每日更新", "log_date"),

    WEEK(1, "WEEK", "每周更新", "log_week"),

    MONTH(2, "MONTH", "每月更新", "log_month"),

    SINGLE(3, "SINGLE", "单次更新", null),

    HOUR(4, "HOUR", "按小时更新", "log_hour"),

    MULTI(5, "MULTI", "多次更新", null),

    ;

    private final Integer code;

    private final String type;

    private final String desc;

    private final String fieldName;

    SchedulerPeriodEnum(Integer code, String type, String desc, String fieldName) {
        this.code = code;
        this.type = type;
        this.desc = desc;
        this.fieldName = fieldName;
    }

    public static SchedulerPeriodEnum getByType(String type) {
        for (SchedulerPeriodEnum item : values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return String.format("%s(%s)", this.type, this.desc);
    }

    @JsonCreator
    public static SchedulerPeriodEnum getByCode(Integer code) {
        SchedulerPeriodEnum[] var1 = values();
        for (SchedulerPeriodEnum item : var1) {
            if (Objects.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

}
