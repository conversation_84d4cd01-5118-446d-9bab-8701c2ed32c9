package com.ailpha.ailand.dataroute.endpoint.third.input;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.GetNonceResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.TokenResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.demand.DemandService;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.OrderSuccessDeliveryRequest;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.controller.JumpToOrderRequest;
import com.ailpha.ailand.dataroute.endpoint.servicenode.controller.JumpToOrderResponse;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.CompanyInfoResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/11/18 15:19
 */
@Slf4j
@RestController
@Tag(name = "第三方接口")
@RequestMapping("/third")
@RequiredArgsConstructor
public class ThirdController {

    private final ThirdService thirdService;

    private final UserService userService;
    private final DemandService demandService;
    private final OrderManagerService orderManagerService;
    private final ServiceNodeRemoteService serviceNodeRemoteService;
    private final CompanyService companyService;
    private final DeliveryService deliveryService;

    @PostMapping("/user/preview/authorizationLetter")
    public void previewAuthorizationLetter(@RequestBody PreviewAuthorizationLetterRequest request) throws IOException {
        userService.preview(request.getFilename(), "", null);
    }

    @PostMapping("/download/solution")
    public void downloadDemandSolution(@RequestBody DownloadSolutionFileRequest request, HttpServletResponse response) {
        demandService.downloadSolutionFile(request.getFileKey(), response);
    }

    @PostMapping("/orderDelivery")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<Boolean> orderDelivery(@RequestBody OrderSuccessDeliveryRequest request) {
        log.info("收到创建场景交付关联|订单累计信息: {}", request);
        orderManagerService.orderDelivery(request);
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(Boolean.TRUE);
    }

    @PostMapping("/generateKey")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<String> generateKey(@RequestBody GenerateKeyRequest request) throws Exception {
        log.info("generateKey params：{}", JSONUtil.toJsonStr(request));
        String result;
        switch (request.getDeliveryType()) {
            case API -> {
                String dataProductName = request.getDataProductName();
                String username = request.getUsername();
                String gatewayServiceRouteId = request.getGatewayServiceRouteId();
                log.info("generate api key info dataProductName: [{}]  username: [{}] gatewayServiceRouteId: [{}]", dataProductName, username, gatewayServiceRouteId);
                result = orderManagerService.authGatewayApiKey(dataProductName, username, gatewayServiceRouteId);
            }
            case FILE_DOWNLOAD -> {
                log.info("generate minio access key info dataProductId: [{}]  dataUserId: [{}]", request.getProductId(), request.getProductUserId());
                result = orderManagerService.authMinioApiKey(request.getProductId(), request.getProductUserId());
            }
            default -> throw new RestfulApiException("暂不支持【" + request.getDeliveryType() + "】交付方式");
        }
        try {
            orderManagerService.recordKey(request, result);
        } catch (Exception e) {
            log.error("record seller key failed orderId: [{}]", request.getOrderId(), e);
        }

        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(result);
    }

    @PostMapping("/companyInfo")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<CompanyInfoResp> companyInfo(@RequestBody CompanyInfoRequest request) {
        CompanyInfoResp company = companyService.getCompanyInfoByNodeId(request.getNodeId());
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(company);
    }

    @PostMapping("/transfer/process")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<String> transferProcess(@RequestBody NegotiateDataTransferDTO request) {
        String resp = deliveryService.transferProcess(request);
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(resp);
    }


    @PostMapping("contract/execute/callback")
    public CommonResult<Boolean> contractExecuteCallback(@RequestBody ContractExecuteCallbackRequest request) {
        return thirdService.contractExecuteSuccessCallback(request);
    }


    @GetMapping("userinfo")
    public CommonResult<Userinfo> userinfo(@RequestParam("token") String code) {
        try {
            if (log.isDebugEnabled())
                log.debug("临时授权码 = {}", code);
            String userToken = userCache.get(code);
            Assert.isTrue(StringUtils.isNotEmpty(userToken), "非法的授权码，请重新获取");
            return CommonResult.SUCCESS(JSONUtil.toBean(userToken, Userinfo.class));
        } finally {
            userCache.remove(code);
        }

    }

    @GetMapping("userinfo/{id}")
    public CommonResult<Userinfo> userinfoById(@PathVariable String id) {
        UserDetailsResponse userDetail = userService.userDetailForThird(id);
        Assert.isTrue(ObjectUtil.isNotNull(userDetail), "账号信息不存在");
        return CommonResult.SUCCESS(Userinfo.builder().id(userDetail.getUserId()).account(userDetail.getAccount()).username(userDetail.getRealName())
                .phone(userDetail.getPhone()).email(userDetail.getEmail())
                .build());
    }

    private final DataProductService dataProductService;
    private final DataResourceService dataResourceService;

    @PostMapping("/data-asset/product/detail")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<DataProductVO> dataProduct(@RequestBody DataProductDetailRequest request) {
        DataProductVO dataProductByDataProductPlatformId = dataProductService.getDataProductByDataProductPlatformId(request.getDataResourcePlatformId(), request.getTargetNodeId(), request.getTargetNodeId());
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(dataProductByDataProductPlatformId);
    }

    @PostMapping("/data-asset/resource/detail")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<DataResourceVO> dataResource(@RequestBody DataResourceDetailRequest request) {
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(dataResourceService.getDataResourceByDataResourcePlatformId(request.getDataResourcePlatformId(), request.getTargetNodeId(), request.getTargetNodeId()));
    }

    @PostMapping(value = "data-asset/attach-file")
    public void fileAttachDownload(@RequestBody DownloadFileRequest request, HttpServletResponse response) throws IOException {
        dataProductService.downloadAttachFile(request.getDataProductPlatformId(), request.getAttachType(), response, request.getTargetNodeId());
    }

    @Data
    @Builder
    public static class Userinfo {
        String id;
        String account;
        String username;
        String phone;
        String email;
        String role;
    }

    private final Cache<String, String> userCache;
    private final BaseCapabilityManager baseCapabilityManager;

    public enum JumpTarget {
        API_GATE, AI_GATE, MPC_SSO, TEE_SSO, TEE_USE, MPC_USE, AI_SORT, SERVICE_NODE
    }

    @Data
    public static class JumpToServiceNodeOrderMenuRequest {
        String serviceNodeUrl;
        String clientPlatformUniqueNo;
        String productName;
    }

    @GetMapping("jump-url")
    @Operation(summary = "获取跳转url")
    public SuccessResponse<String> jumpUrl(@RequestParam JumpTarget target, @RequestParam String ext) {
        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        return switch (target) {
            case API_GATE ->
                    ApiResponse.success(baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.API_GATE).getJumpUrl()).build();
            case AI_GATE ->
                    ApiResponse.success(baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.AI_GATE).getJumpUrl()).build();
            case MPC_SSO, MPC_USE -> ApiResponse.success(mpcJumpUrl(localCompanyId)).build();
            case TEE_SSO, TEE_USE -> ApiResponse.success(teeJumpUrl(localCompanyId)).build();
            case AI_SORT -> ApiResponse.success(aiSortJumpUrl(localCompanyId)).build();
            case SERVICE_NODE -> {
                JumpToServiceNodeOrderMenuRequest jumpToServiceNodeOrderMenuRequest = JSONUtil.toBean(ext, JumpToServiceNodeOrderMenuRequest.class);
                JumpToOrderRequest jumpToOrderRequest = new JumpToOrderRequest();
                jumpToOrderRequest.setUrl(jumpToServiceNodeOrderMenuRequest.serviceNodeUrl);
                jumpToOrderRequest.setUserId(LoginContextHolder.currentUser().getIdShuhan());
                jumpToOrderRequest.setDataProductName(jumpToServiceNodeOrderMenuRequest.productName);
                jumpToOrderRequest.setDataProductId(jumpToServiceNodeOrderMenuRequest.clientPlatformUniqueNo);
                jumpToOrderRequest.setAccount(LoginContextHolder.currentUser().getUsername());
                com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<JumpToOrderResponse> jumpToOrderResponseCommonResult = serviceNodeRemoteService.jumpToOrder(jumpToOrderRequest);
                Assert.isTrue(jumpToOrderResponseCommonResult.isSuccess(), "获取业务节点订单菜单跳转地址异常：" + jumpToOrderResponseCommonResult.getMessage());
                yield SuccessResponse.success(String.format("%s&productName=%s&clientPlatformUniqueNo=%s", jumpToOrderResponseCommonResult.getData().getJumpUrl(), jumpToServiceNodeOrderMenuRequest.productName, jumpToServiceNodeOrderMenuRequest.clientPlatformUniqueNo)).build();
            }
        };
    }

    private String mpcJumpUrl(String localCompanyId) {
        Assert.isTrue(baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.MPC), "基础能力 MPC 未启用");
        BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.MPC);
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return capabilityConfig.getJumpUrl() + "?token=" + randomCode;
    }

    private String teeJumpUrl(String localCompanyId) {
        Assert.isTrue(baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TEE), "基础能力 TEE 未启用");
        BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.TEE);
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return capabilityConfig.getJumpUrl() + "/trader/#/third-login?token=" + randomCode;
    }

    private String aiSortJumpUrl(String localCompanyId) {
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.AI_SORT).getJumpUrl() + "/asset/user-service/openapi/data_router/login?token=" + randomCode;
    }

    private String getUserCache() {
        UserDTO user = LoginContextHolder.currentUser();
        Userinfo userinfo = Userinfo.builder()
                .id(user.getId())
                .username(user.getRealName())
                .role(LoginContextHolder.currentUserRole().name())
                .account(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .build();
        return JSONUtil.toJsonStr(userinfo);
    }


    @PostMapping("/ConnectorIdentityVerify")
    @Operation(summary = "生成 nonce 并验证证书")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<GetNonceResponse> generateUuidWithCertificate(@RequestBody GenerateUuidRequest request) {
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(thirdService.generateUuidWithCertificate(request.getVerify()));
    }

    // 根据随机数生成token
    @PostMapping("/ConnectorIdentityVerifyNonce")
    @Operation(summary = "生成 token")
    public com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<TokenResponse> generateToken(@RequestBody GenerateTokenRequest request) {
        return com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult.SUCCESS(thirdService.generateTokenWithNonce(request.getSignature()));
    }

}
