package com.ailpha.ailand.dataroute.endpoint.third.input;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.AttachType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.demand.DemandService;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.third.request.ContractExecuteCallbackRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.GenerateKeyRequest;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.slf4j.MDC;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2024/11/18 15:19
 */
@Slf4j
@RestController
@Tag(name = "第三方接口")
@RequestMapping("/third")
@RequiredArgsConstructor
public class ThirdController {

    private final ThirdService thirdService;

    private final UserService userService;
    private final DemandService demandService;
    private final OrderManagerService orderManagerService;

    @GetMapping("/download/solution/{fileId}")
    public void downloadDemandSolution(@PathVariable String fileId, HttpServletResponse response) {
        demandService.downloadSolutionFile(fileId, response);
    }

    @PostMapping("/generateKey")
    public SuccessResponse<String> generateKey(@RequestBody GenerateKeyRequest request) throws Exception {
        log.info("generateKey params：{}", JSONUtil.toJsonStr(request));
        String result;
        switch (request.getDeliveryType()) {
            case API -> {
                String dataProductName = request.getDataProductName();
                String username = request.getUsername();
                String gatewayServiceRouteId = request.getGatewayServiceRouteId();
                log.info("generate api key info dataProductName: [{}]  username: [{}] gatewayServiceRouteId: [{}]", dataProductName, username, gatewayServiceRouteId);
                result = orderManagerService.authGatewayApiKey(dataProductName, username, gatewayServiceRouteId);
            }
            case FILE_DOWNLOAD -> {
                log.info("generate minio access key info dataProductId: [{}]  dataUserId: [{}]", request.getProductId(), request.getProductUserId());
                result = orderManagerService.authMinioApiKey(request.getProductId(), request.getProductUserId());
            }
            default -> throw new RestfulApiException("暂不支持【" + request.getDeliveryType() + "】交付方式");
        }
        return SuccessResponse.success(result).build();
    }


    @PostMapping("contract/execute/callback")
    public CommonResult<Boolean> contractExecuteCallback(@RequestBody ContractExecuteCallbackRequest request) {
        return thirdService.contractExecuteSuccessCallback(request);
    }


    @GetMapping("userinfo")
    public CommonResult<Userinfo> userinfo(@RequestParam("token") String code) {
        try {
            if (log.isDebugEnabled())
                log.debug("临时授权码 = {}", code);
            String userToken = userCache.get(code);
            Assert.isTrue(StringUtils.isNotEmpty(userToken), "非法的授权码，请重新获取");
            return CommonResult.SUCCESS(JSONUtil.toBean(userToken, Userinfo.class));
        } finally {
            userCache.remove(code);
        }

    }

    @GetMapping("userinfo/{id}")
    public CommonResult<Userinfo> userinfoById(@PathVariable String id) {
        UserDetailsResponse userDetail = userService.userDetailForThird(id);
        Assert.isTrue(ObjectUtil.isNotNull(userDetail), "账号信息不存在");
        return CommonResult.SUCCESS(Userinfo.builder().id(userDetail.getUserId()).account(userDetail.getAccount()).username(userDetail.getRealName())
                .phone(userDetail.getPhone()).email(userDetail.getEmail())
                .build());
    }

    private final DataProductService dataProductService;
    private final DataResourceService dataResourceService;

    @GetMapping("/data-asset/product/detail")
    public SuccessResponse<DataProductVO> dataProduct(@RequestParam String dataProductPlatformId) {
        HttpServletRequest request = ServletUtils.getRequest();
        MDC.put("currentNodeId", request.getHeader("routerId"));
        MDC.put("hubInfo", new String(Base64.getDecoder().decode(request.getHeader("hubInfo"))));
        return SuccessResponse.success(dataProductService.getDataProductByDataProductPlatformId(dataProductPlatformId)).build();
    }

    @GetMapping("/data-asset/resource/detail")
    public SuccessResponse<DataResourceVO> dataResource(@RequestParam String dataResourcePlatformId) {
        HttpServletRequest request = ServletUtils.getRequest();
        MDC.put("currentNodeId", request.getHeader("routerId"));
        MDC.put("hubInfo", new String(Base64.getDecoder().decode(request.getHeader("hubInfo"))));
        return SuccessResponse.success(dataResourceService.getDataResourceByDataResourcePlatformId(dataResourcePlatformId)).build();
    }

    @GetMapping(value = "data-asset/attach-file")
    public void fileAttachDownload(@RequestParam("dataProductPlatformId") String dataProductPlatformId,
                                   @RequestParam AttachType attachType, HttpServletResponse response) throws IOException {
        HttpServletRequest request = ServletUtils.getRequest();
        MDC.put("currentNodeId", request.getHeader("routerId"));
        MDC.put("hubInfo", new String(Base64.getDecoder().decode(request.getHeader("hubInfo"))));
        dataProductService.downloadAttachFile(dataProductPlatformId, attachType, response);
    }

    @Data
    @Builder
    public static class Userinfo {
        String id;
        String account;
        String username;
        String phone;
        String email;
        String role;
    }

    private final Cache<String, String> userCache;
    private final BaseCapabilityManager baseCapabilityManager;

    public enum JumpTarget {
        API_GATE, AI_GATE, MPC_SSO, TEE_SSO, TEE_USE, MPC_USE, AI_SORT
    }

    @GetMapping("jump-url")
    @Operation(summary = "获取跳转url")
    public SuccessResponse<String> jumpUrl(@RequestParam JumpTarget target) {
        return switch (target) {
            case API_GATE ->
                    ApiResponse.success(baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.API_GATE).getJumpUrl()).build();
            case AI_GATE ->
                    ApiResponse.success(baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.AI_GATE).getJumpUrl()).build();
            case MPC_SSO, MPC_USE -> ApiResponse.success(mpcJumpUrl()).build();
            case TEE_SSO, TEE_USE -> ApiResponse.success(teeJumpUrl()).build();
            case AI_SORT -> ApiResponse.success(aiSortJumpUrl()).build();
        };
    }

    private String mpcJumpUrl() {
        Assert.isTrue(baseCapabilityManager.platformEnable(BaseCapabilityType.MPC), "基础能力 MPC 未启用");
        BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.MPC);
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return capabilityConfig.getJumpUrl() + "?token=" + randomCode;
    }

    private String teeJumpUrl() {
        Assert.isTrue(baseCapabilityManager.platformEnable(BaseCapabilityType.TEE), "基础能力 TEE 未启用");
        BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.TEE);
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return capabilityConfig.getJumpUrl() + "/trader/#/third-login?token=" + randomCode;
    }

    private String aiSortJumpUrl() {
        String userToken = getUserCache();
        String randomCode = IdUtil.fastSimpleUUID();
        userCache.put(randomCode, userToken);
        return baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.AI_SORT).getJumpUrl() + "/asset/user-service/openapi/data_router/login?token=" + randomCode;
    }

    private String getUserCache() {
        UserDTO user = LoginContextHolder.currentUser();
        Userinfo userinfo = Userinfo.builder()
                .id(user.getId())
                .username(user.getRealName())
                .role(LoginContextHolder.currentUserRole().getFirst().name())
                .account(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .build();
        return JSONUtil.toJsonStr(userinfo);
    }

}
