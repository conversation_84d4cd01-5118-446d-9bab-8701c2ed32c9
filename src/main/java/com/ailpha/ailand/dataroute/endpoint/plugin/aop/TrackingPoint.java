package com.ailpha.ailand.dataroute.endpoint.plugin.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @author: yuwenping
 * @date: 2025/5/28 15:35
 * @Description: 模块埋点注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface TrackingPoint {
    String moduleType() default "";

    String[] extraParams() default {};
}
