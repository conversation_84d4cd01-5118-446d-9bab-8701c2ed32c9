package com.ailpha.ailand.dataroute.endpoint.dataasset.request;


import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ModelMetadata implements Serializable {
    @Schema(description = "大模型类型：内容生成")
    String llmType;
    @Schema(description = "大模型参数规模")
    String paramSize;
    @Schema(description = "输入格式：文本")
    List<String> inputFormat;
    @Schema(description = "输出格式：文本、图像、视频、音频")
    List<String> outputFormat;

    @Schema(description = "(非大模型)数据集")
    String tableName;
    @Schema(description = "字段信息")
    List<DataSchemaBO> schemas;

    @Schema(description = "模型版本")
    String modelVersion;
    @Schema(description = "版本描述")
    String versionDesc;
}
