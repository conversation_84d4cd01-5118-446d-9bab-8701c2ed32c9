package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormStatusQueryResponse {
    /**
     * 业务ID
     */
    String processId;
    /**
     * 审核状态
     * 0：待审核
     * 1：通过
     * 2：拒绝
     */
    Integer processStatus;
    /**
     * 标识编码
     * 审核通过后，分配的对应类型标识编码。如不涉及新分配，则返回对应的原有标识码。
     */
    String identityId;
}
