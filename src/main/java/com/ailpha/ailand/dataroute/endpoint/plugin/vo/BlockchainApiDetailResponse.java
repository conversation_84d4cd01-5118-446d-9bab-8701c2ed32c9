package com.ailpha.ailand.dataroute.endpoint.plugin.vo;

import com.ailpha.ailand.biz.api.constants.BlockchainPluginApiTypeEnum;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.BlockchainParamsBO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BlockchainApiDetailResponse {
    private String url;
    private MethodEnum method;
    private List<BlockchainParamsBO> params;
    private String body;
    private BodyTypeEnum bodyType;
    private List<BlockchainParamsBO> headers;
    private BlockchainPluginApiTypeEnum type;
    private String upDataField;
}