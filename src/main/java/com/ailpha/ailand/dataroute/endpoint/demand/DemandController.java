package com.ailpha.ailand.dataroute.endpoint.demand;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.SSEMessageService;
import com.ailpha.ailand.dataroute.endpoint.demand.request.*;
import com.ailpha.ailand.dataroute.endpoint.demand.response.MarkAsDealResponse;
import com.ailpha.ailand.dataroute.endpoint.demand.response.SubmitQuotationResponse;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/demand")
@RequiredArgsConstructor
@Tag(name = "Demand", description = "需求管理")
public class DemandController {

    private final DemandRemoteService demandRemoteService;
    private final SSEMessageService sseMessageService;

    @PostMapping("/map")
    @Operation(summary = "需求地图")
    public SuccessResponse<List<DemandMapDTO>> map(@RequestBody DemandMapRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.demandMap(request);
    }

    @GetMapping("/map/{id}")
    @Operation(summary = "查询需求地图详情")
    public SuccessResponse<DemandMapDetailDTO> mapDetail(@PathVariable Integer id) {
        return demandRemoteService.demandMapDetail(id, LoginContextHolder.currentUser().getId());
    }

    @PostMapping("/collect")
    @Operation(summary = "关注需求")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> collectDemand(@RequestBody CollectDemandRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.collectDemand(request);
    }

    @PostMapping("/cancelCollect")
    @Operation(summary = "取消关注需求")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> cancelCollectDemand(@RequestBody CollectDemandRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.cancelCollectDemand(request);

    }

    @PostMapping("/list")
    @Operation(summary = "查询需求列表")
    public SuccessResponse<List<DemandPageDTO>> list(@RequestBody DemandMapRequest request) {
        if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.TRADER))
            request.setUserId(LoginContextHolder.currentUser().getId());
        request.setRouterId(LoginContextHolder.currentUser().getCompany().getNodeId());
        return demandRemoteService.listDemands(request);
    }

    // 查询需求详情
    @GetMapping("/{id}")
    @Operation(summary = "查询需求详情")
    public SuccessResponse<DemandDetailDTO> detail(@PathVariable Integer id) {
        return demandRemoteService.demandDetail(id);
    }

    @PostMapping("/negotiation/related")
    @Operation(summary = "查询需求关联响应列表")
    public SuccessResponse<List<DemandNegotiationRecordDTO>> listDemandNegotiations(@RequestBody DemandNegotiationRecordRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.listDemandNegotiations(request);
    }

    @PostMapping("/negotiation/page")
    @Operation(summary = "查询我的需求响应记录")
    public SuccessResponse<List<DemandNegotiationPageDTO>> demandNegotiationPage(@RequestBody DemandNegotiationPageRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.demandNegotiationPage(request);
    }

    private final RouterService routerService;

    @PostMapping("/add")
    @Operation(summary = "新增需求")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> add(@RequestBody @Valid AddDemandRequest request) {
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        UserDTO user = LoginContextHolder.currentUser();
        request.setUserId(user.getId());
        request.setRouterId(LoginContextHolder.currentUser().getCompany().getNodeId());
        request.setDemandSide(new DemandSideDTO(company.getOrganizationName(), company.getIndustryType(), company.getConnectorName(),
                company.getNodeId(), user.getUsername(), user.getPhone(), user.getEmail()));
        return demandRemoteService.add(request);
    }

    // 检查需求标题是否重复
    @PostMapping("/checkTitle")
    @Operation(summary = "检查需求标题是否重复", description = "true:重复 false:没有重复")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> checkTitle(@RequestBody CheckTitleRequest request) {
        return demandRemoteService.checkTitle(request);
    }

    // 编辑需求
    @PostMapping("/edit")
    @Operation(summary = "编辑需求")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> edit(@RequestBody EditDemandRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.edit(request);
    }

    // 关闭需求
    @PostMapping("/close")
    @Operation(summary = "关闭需求")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> close(@RequestBody CloseDemandRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        return demandRemoteService.close(request);
    }

    // 提交报价
    @PostMapping("/submitQuotation")
    @Operation(summary = "提交报价")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> submitQuotation(@RequestBody SubmitQuotationRequest request) {
        request.setUserId(LoginContextHolder.currentUser().getId());
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        UserDTO user = LoginContextHolder.currentUser();
        request.setDemandSide(new DemandSideDTO(company.getOrganizationName(), company.getIndustryType(), company.getConnectorName(),
                company.getNodeId(), user.getUsername(), user.getPhone(), user.getEmail()));
        SuccessResponse<SubmitQuotationResponse> submitQuotation = demandRemoteService.submitQuotation(request);
        if (submitQuotation.isSuccess()) {
            try {
                sseMessageService.notifyDataRouteMessage(List.of(SSEMessageRequest.builder()
                        .message(String.format("【需求被响应】 %s 被响应，请需求方前往查看\n%s; %s; 报价: %s", submitQuotation.getData().getTitle(),
                                submitQuotation.getData().getDataAssets(), submitQuotation.getData().getCompanyName(), submitQuotation.getData().getPrice()))
                        .userId(submitQuotation.getData().getUserId()).type(SSEMessageTypeEnum.SUBMIT_DEMAND_SOLUTION)
                        .dataId(String.valueOf(request.getDemandId()))
                        .build()), submitQuotation.getData().getRouterId(), null);
            } catch (Exception e) {
                log.error("消息通知异常：", e);
            }

        } else
            throw new RestfulApiException("提交报价方案异常");
        return SuccessResponse.success(submitQuotation.isSuccess()).build();
    }

    // 报价标记为成交状态
    @PostMapping("/negotiation/done")
    @Operation(summary = "标记成交")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<Boolean> markAsDeal(@RequestBody MarkAsDealRequest request) {
        SuccessResponse<MarkAsDealResponse> markNegotiationDone = demandRemoteService.markNegotiationDone(request);
        if (markNegotiationDone.isSuccess())
            try {
                markNegotiationDone.getData().getAcceptUsers().forEach(x -> sseMessageService.notifyDataRouteMessage(List.of(SSEMessageRequest.builder()
                        .message(String.format("【需求提交的报价方案被采纳】 %s 提交的报价方案被采纳，请提供方前往查看\n%s; %s",
                                markNegotiationDone.getData().getTitle(), markNegotiationDone.getData().getTitle(), markNegotiationDone.getData().getCompanyName()))
                        .userId(x.getUserId()).type(SSEMessageTypeEnum.ACCEPT_DEMAND_SOLUTION).dataId(String.valueOf(request.getDemandId()))
                        .build()), x.getRouterId(), null));
            } catch (Exception e) {
                log.error("消息通知异常：", e);
            }

        return SuccessResponse.success(true).build();
    }

    // 审核需求
    @PostMapping("/audit")
    @Operation(summary = "审核需求")
    @PreAuthorize("hasAuthority('COMPANY_ADMIN')")
    public SuccessResponse<Boolean> audit(@RequestBody AuditDemandRequest request) {
        return demandRemoteService.audit(request);
    }

    private final DemandService demandService;

    @PostMapping("/upload/solution")
    @Operation(summary = "上传需求方案")
    @PreAuthorize("hasAuthority('TRADER')")
    public SuccessResponse<String> uploadSolutionFile(@RequestPart("file") MultipartFile file) {
        return SuccessResponse.success(demandService.uploadDemandSolutionFile(file)).build();
    }

    @GetMapping("/download/solution/{fileId}")
    @Operation(summary = "下载需求方案")
    public void downloadSolutionFile(@PathVariable String fileId, HttpServletResponse response) {
        demandService.redirectToDownloadSolutionFile(URLUtil.decode(fileId), response);
    }

}
