package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.biz.api.dataset.ApiImportExtendBO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.BodyTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.MethodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.PathTypeBO;
import com.ailpha.ailand.dataroute.endpoint.third.response.ResponseBO;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class APISourceMetadata {
    /**
     * API接口配置
     */
    @NotEmpty(message = "请求地址不能为空")
    @Schema(description = "请求地址")
    String url;
    @NotNull(message = "请求方式不能为空")
    @Schema(description = "请求方式")
    MethodEnum method;
    @Valid
    @Schema(description = "Params参数")
    List<ParamsBO> params;
    @Schema(description = "Body参数")
    String body;
    @Schema(description = "Body类型")
    BodyTypeEnum bodyType;
    @Valid
    @Schema(description = "Headers参数")
    List<ParamsBO> headers;
    @NotEmpty(message = "返回响应体（后端解析用）不能为空")
    @Schema(description = "返回响应体（后端解析用）")
    List<ResponseBO> response;
    @NotEmpty(message = "返回响应体（前端回显用）不能为空")
    @Schema(description = "返回响应体（前端回显用）")
    String responseEcho;
    @NotEmpty(message = "元数据路径及类型不能为空")
    @Schema(description = "元数据路径及类型")
    List<PathTypeBO> dataPath;
    /**
     * 扩展配置
     */
    @JsonUnwrapped
    @Builder.Default
    ApiImportExtendBO extend = new ApiImportExtendBO();
    String strategy;
}
