package com.ailpha.ailand.dataroute.endpoint.node.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import com.ailpha.ailand.dataroute.endpoint.node.domain.Node;
import com.ailpha.ailand.dataroute.endpoint.node.domain.NodeStatus;

@Repository
public interface NodeRepository extends JpaRepository<Node, Long>, QuerydslPredicateExecutor<Node> {
        /**
     * 根据状态计算节点数量。
     *
     * @param status 节点状态
     * @return 对应状态的节点数量
     */
    long countByStatus(NodeStatus status);
}