package com.ailpha.ailand.dataroute.endpoint.node.repository;

import com.ailpha.ailand.dataroute.endpoint.node.domain.Node;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NodeRepository extends JpaRepository<Node, Long>, QuerydslPredicateExecutor<Node> {
}