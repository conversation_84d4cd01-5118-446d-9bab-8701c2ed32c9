package com.ailpha.ailand.dataroute.endpoint.order.vo.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/3/26
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CertificateRecordListReq extends Page implements Serializable {

    @Schema(description = "订单ID")
    String orderId;
}
