package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResolutionResponseDataResource {
    // 资源ID
    String resourceId;
    // 资源名称
    String resourceName;
    // 行业
    String industry;
    // 资源所有者
    String resourceOwner;
    // 身份ID
    String identityId;
    // 联系人
    String contacter;
    // 联系信息
    String contactInformation;
    // 资源摘要
    String resourceAbstract;
    // 资源格式
    String resourceFormat;
    // 项目列表
    List<Item> itemList;
    // 数据源
    String dataSource;
    // 个人信息
    int personalInformation;
    // 资源状态
    String resourceStatus;
    // 其他
    String others;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item {
        // 项目名称
        String name;
        // 项目类型
        String type;
    }
}
