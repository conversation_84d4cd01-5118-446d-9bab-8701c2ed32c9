package com.ailpha.ailand.dataroute.endpoint.logo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.logo.entity.CustomLogo;
import com.ailpha.ailand.dataroute.endpoint.logo.repository.LogoConfigRepository;
import com.ailpha.ailand.dataroute.endpoint.logo.request.LogoConfigRequest;
import com.ailpha.ailand.dataroute.endpoint.logo.service.LogoService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/1 13:50
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogoServiceImpl implements LogoService {

    private final LogoConfigRepository logoConfigRepository;


    @Override
    public void uploadLogoConfig(LogoConfigRequest request) {
        checkParam(request);

        CustomLogo customLogo;
        final List<CustomLogo> list = logoConfigRepository.findAll();
        if (CollectionUtil.isNotEmpty(list)) {
            customLogo = list.getFirst();
            customLogo.setConfigExt(request);
            customLogo.setUpdateTime(new Date());
        } else {
            log.debug("新增企业自定义信息：{}", JSONUtil.toJsonStr(request));
            customLogo = CustomLogo.builder().configExt(request).createTime(new Date()).build();
        }
        logoConfigRepository.saveAndFlush(customLogo);
    }

    private void checkParam(LogoConfigRequest request) {
        // todo
    }


    @Override
    public LogoConfigRequest findCustomLogo() {
        if (TenantContext.getCurrentTenant() == null || TenantContext.getCurrentTenant().equals(TenantIdentifierResolver.DEFAULT_TENANT)) {
            return null;
        } else {
            log.info("获取自定义logo当前租户信息:{}", TenantContext.getCurrentTenant());
        }
        final List<CustomLogo> list = logoConfigRepository.findAll();
        if (CollectionUtil.isNotEmpty(list)) {
            CustomLogo customLogo = list.getFirst();
            return customLogo.getConfigExt();
        } else {
            log.info("当前企业未自定义logo使用默认配置");
            return null;
        }
    }

}
