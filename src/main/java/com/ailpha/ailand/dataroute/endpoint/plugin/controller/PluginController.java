package com.ailpha.ailand.dataroute.endpoint.plugin.controller;

import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginPageRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.ailpha.ailand.dataroute.endpoint.plugin.aop.TrackingModuleType;
import com.ailpha.ailand.dataroute.endpoint.plugin.service.PluginService;
import com.ailpha.ailand.dataroute.endpoint.plugin.service.TrackingModuleService;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainDetailResponse;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainListResponse;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Tag(name = "区块链插件管理")
@RequestMapping("/plugin")
public class PluginController {

    private final PluginService pluginService;

    private final TrackingModuleService trackingModuleService;

    private final DataAssetService dataAssetService;

    @PostMapping("/upload")
    @Operation(summary = "上传插件JAR文件")
    public SuccessResponse<String> uploadPlugin(@RequestPart("file") MultipartFile file) {
        String path = pluginService.storePlugin(file);
        return SuccessResponse.success(path).build();
    }

    @PostMapping("/blockchain/save")
    @Operation(summary = "保存区块链插件配置")
    public SuccessResponse<Boolean> saveBlockchainPlugin(@RequestBody @Valid BlockchainPluginRequest request) {
        pluginService.saveBlockchainPlugin(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("/blockchain/update")
    @Operation(summary = "更新区块链插件配置")
    public SuccessResponse<Boolean> updateBlockchainPlugin(@RequestBody @Valid BlockchainPluginUpdateRequest request) {
        pluginService.updateBlockchainPlugin(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("/blockchain/updateStatus")
    @Operation(summary = "更新区块链插件状态")
    public SuccessResponse<Boolean> updateStatusBlockchainPlugin(@RequestBody @Valid BlockchainPluginUpdateRequest request) {
        pluginService.updateStatusBlockchainPlugin(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("/blockchain/list")
    @Operation(summary = "区块链插件列表")
    public SuccessResponse<List<BlockchainListResponse>> listBlockchainPlugin(@RequestBody @Valid BlockchainPluginPageRequest request) {
        return pluginService.listBlockchainPlugin(request);
    }

    @GetMapping("/blockchain/detail")
    @Operation(summary = "区块链插件详情")
    public SuccessResponse<BlockchainDetailResponse> blockchainPluginDetail(@RequestParam Long id) {
        return pluginService.blockchainPluginDetail(id);
    }

    @GetMapping("/blockchain/test")
    @Operation(summary = "区块链插件测试")
    public SuccessResponse<Boolean> blockchainPluginTest(@RequestParam String data) {
        pluginService.upBlockchain(TrackingModuleType.OPERATE_LOG, data);
        return SuccessResponse.success(true).build();
    }

    @Operation(summary = "API-连接测试")
    @PostMapping("/blockchain/api-test")
    @OpLog(message = "API接口")
    public SuccessResponse<String> add(@RequestBody ApiImportTestVO apiImportTestVO) throws Exception {

        boolean dispatch = false;
        if (apiImportTestVO.getHeaders() != null && apiImportTestVO.getHeaders().stream().anyMatch(paramsBO -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(paramsBO.getKey(), "apiKey"))) {
            // 正式使用的时候记录
            OPLogContext.putOpType(InternalOpType.API_INVOKE);
            dispatch = true;
        } else {
            // 非正式
            OPLogContext.closeSwitch();
        }

        // API接口导入url限制
        String response = dataAssetService.callApi(apiImportTestVO, dispatch);
        return ApiResponse.success(response).build();
    }

    @GetMapping("/module/list")
    @Operation(summary = "模块配置列表")
    public SuccessResponse<List<String>> moduleList() {
        return SuccessResponse.success(trackingModuleService.getModules()).build();
    }
}