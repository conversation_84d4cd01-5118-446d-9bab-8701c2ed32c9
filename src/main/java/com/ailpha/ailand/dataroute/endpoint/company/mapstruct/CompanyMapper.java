package com.ailpha.ailand.dataroute.endpoint.company.mapstruct;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.dto.CompanyApplyDTO;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyVerifyRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CompanyMapper {

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "schema", ignore = true)
    @Mapping(target = "delegateContact", ignore = true)
    CompanyDTO toDTO(Company company);

    // afterMapping
    @AfterMapping
    default void afterToDTO(Company company, @MappingTarget CompanyDTO dto) {
        dto.setSchema("tenant_" + company.getId());
        NodeService nodeService = SpringUtil.getBean(NodeService.class);
        dto.setServiceNode(nodeService.getNode(company.getServiceNodeId()));
        dto.setThirdBusinessId(company.getCompanyId());
//        dto.setDelegateContact(ObjectUtil.equals(company.getAccessType(), AccessType.LEGAL_PERSON) ?
//                SpringUtil.getBean(UserService.class).findCompanyAdminByCompanyId(company.getId()).getPhone() : company.getDelegateContact());
        try {
            JSONObject companyExt = JSONUtil.parseObj(company.getExt());
            dto.setEntityId(companyExt.getStr("entityId"));
            dto.setEntityCode(companyExt.getStr("entityCode"));
        } catch (Exception ignore) {
        }
    }

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(source = "delegatePhone", target = "delegateContact")
    @Mapping(source = "delegateAddress", target = "delegateRegistrationAddress")
    @Mapping(source = "delegateRemark", target = "delegateRemarks")
    @Mapping(source = "businessLicenseLocalUrl", target = "businessLicense")
    @Mapping(source = "authorizationLetterLocalUrl", target = "authorizationLetter")
    @Mapping(source = "legalRepresentativeIdValidityStartDate", target = "legalRepresentativeIdExpiry")
    @Mapping(source = "delegateIdValidityStartDate", target = "delegateIdExpiry")
    @Mapping(source = "registeredAddress", target = "registrationAddress")
    @Mapping(source = "authTime", target = "authDate")
    @Mapping(target = "authMethod", ignore = true)
    @Mapping(target = "companyId", ignore = true)
    @Mapping(target = "nodeId", ignore = true)
    void updateCompanyFromDTO(CompanyApplyDTO dto, @MappingTarget Company company);

    @AfterMapping
    default void afterMapping(CompanyApplyDTO dto, @MappingTarget Company company) {
        if (ObjectUtil.equals(AccessType.LEGAL_PERSON, dto.getAccessType())) {
            company.setAuthMethod(dto.getAuthType());
        } else {
            company.setDelegateAuthMethod(dto.getDelegateAuthType());
        }
        company.setRegistrationDate(DateUtil.format(dto.getRegistrationDate(), "yyyy-MM-dd"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("delegateIdEndExpiry", DateUtil.format(dto.getDelegateIdValidityEndDate(), "yyyy-MM-dd"));
        entries.set("legalRepresentativeIdValidityEndDate", DateUtil.format(dto.getLegalRepresentativeIdValidityEndDate(), "yyyy-MM-dd"));
        company.setExt(entries.toString());
    }


    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "verifyType", expression = "java(dto.getAccessType().getDesc())")
    @Mapping(target = "legalPerson", source = "dto", qualifiedByName = "toLegalPerson")
    @Mapping(target = "agentPerson", source = "dto", qualifiedByName = "toAgentPerson")
    CompanyVerifyRequest toVerifyRequest(CompanyApplyDTO dto);

    @Named("toLegalPerson")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "applyName", source = "legalRepresentativeName")
    @Mapping(target = "applyIdCard", source = "legalRepresentativeIdNumber")
    @Mapping(target = "companyName", source = "organizationName")
    @Mapping(target = "companyCode", source = "creditCode")
    @Mapping(target = "registerAddress", source = "registeredAddress")
    @Mapping(target = "registerTime", source = "registrationDate")
    @Mapping(target = "industry", source = "industryType")
    @Mapping(target = "businessLicenseUrl", ignore = true)
    @Mapping(target = "operateEndTime", source = "businessEndDate")
    @Mapping(target = "operateStartTime", source = "businessStartDate")
    @Mapping(target = "documentType", source = "legalRepresentativeIdType")
    @Mapping(target = "authenticationMode", source = "authType")
    @Mapping(target = "identityState", source = "identityStatus")
    @Mapping(target = "certificationLevel", source = "legalRepresentativeAuthLevel")
    @Mapping(target = "authenticationTime", source = "authTime")
    CompanyVerifyRequest.LegalPerson toLegalPerson(CompanyApplyDTO dto);

    @AfterMapping
    default void toLegalPersonAfter(CompanyApplyDTO dto, @MappingTarget CompanyVerifyRequest.LegalPerson legalPerson) {
        legalPerson.setBusinessLicenseUrl(dto.getBusinessLicenseRemoteUrl());
        legalPerson.setBusinessLicenseUrlName(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), "/"));
        legalPerson.setRegisterTime(DateUtil.format(DateUtil.parse(dto.getAuthTime()), "yyyy-MM-dd"));
    }

    @Named("toAgentPerson")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "applyName", source = "delegateName")
    @Mapping(target = "applyIdCard", source = "delegateIdNumber")
    @Mapping(target = "applyPhone", source = "delegatePhone")
    @Mapping(target = "companyName", source = "organizationName")
    @Mapping(target = "companyCode", source = "creditCode")
    @Mapping(target = "registerAddress", source = "delegateAddress")
    @Mapping(target = "industry", source = "delegateIndustryType")
    @Mapping(target = "businessLicenseUrl", ignore = true)
    @Mapping(target = "remark", source = "delegateRemark")
    @Mapping(target = "applyIdCardEndTime", source = "delegateIdValidityEndDate")
    @Mapping(target = "applyIdCardStartTime", source = "delegateIdValidityStartDate")
    CompanyVerifyRequest.AgentPerson toAgentPerson(CompanyApplyDTO dto);

    @AfterMapping
    default void toLegalPersonAfter(@MappingTarget CompanyVerifyRequest.AgentPerson agentPerson, CompanyApplyDTO dto) {
        agentPerson.setBusinessLicenseUrl(toBase64(dto.getBusinessLicenseRemoteUrl()));
        agentPerson.setBusinessLicenseUrlName(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), "/"));
        agentPerson.setApplyIdCardStartTime(DateUtil.format(dto.getDelegateIdValidityStartDate(), "yyyy-MM-dd"));
        agentPerson.setApplyIdCardEndTime(DateUtil.format(dto.getDelegateIdValidityEndDate(), "yyyy-MM-dd"));
    }

    private String toBase64(String businessUrl) {
        if (StringUtils.isBlank(businessUrl)) {
            return null;
        }
        String filename = StringUtils.substringAfter(businessUrl, Constants.NGINX_PUBLIC_PATH);
        String fullPath = Constants.PUBLIC_PATH + FileUtil.FILE_SEPARATOR + filename;

        try {
            byte[] fileContent = FileUtil.readBytes(fullPath);
            return cn.hutool.core.codec.Base64.encode(fileContent);
        } catch (Exception e) {
            return null;
        }
    }
}