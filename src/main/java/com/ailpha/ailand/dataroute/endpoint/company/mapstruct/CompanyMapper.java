package com.ailpha.ailand.dataroute.endpoint.company.mapstruct;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.utils.RandImageUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.dto.CompanyApplyDTO;
import com.ailpha.ailand.dataroute.endpoint.company.dto.RegisterEntityInfo;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyVerifyRequest;
import com.ailpha.ailand.dataroute.endpoint.company.remote.UpdateCompanyLegalInfoReq;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.EnterpriseInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.third.response.CompanyInfoResp;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.springframework.util.ObjectUtils;

import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CompanyMapper {

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "id", source = "company.id")
    @Mapping(target = "thirdBusinessId", source = "company.companyId")
    @Mapping(target = "nodeId", source = "company.nodeId")
    @Mapping(target = "businessLicense", source = "enterpriseInfoResponse.extendInfo.businessLicense")
    @Mapping(target = "businessLicenseType", source = "enterpriseInfoResponse.extendInfo.businessLicenseType")
    @Mapping(target = "organizationName", source = "enterpriseInfoResponse.baseInfo.enterpriseName")
    @Mapping(target = "creditCode", source = "enterpriseInfoResponse.baseInfo.enterpriseCode")
    @Mapping(target = "legalRepresentativeName", source = "enterpriseInfoResponse.baseInfo.legalPerson")
    @Mapping(target = "legalRepresentativeIdNumber", source = "enterpriseInfoResponse.baseInfo.legalPersonCertno")
    @Mapping(target = "legalRepresentativeAuthLevel", source = "enterpriseInfoResponse.baseInfo.legalPersonAuthLevel")
    @Mapping(target = "authMethod", source = "enterpriseInfoResponse.baseInfo.authType")
    @Mapping(target = "registrationAddress", source = "enterpriseInfoResponse.extendInfo.enterpriseAddress")
    @Mapping(target = "industryType", source = "enterpriseInfoResponse.extendInfo.industryCategory")
//    @Mapping(target = "businessStartDate", source = "enterpriseInfoResponse.baseInfo.operatingPeriodBegin")
//    @Mapping(target = "businessEndDate", source = "enterpriseInfoResponse.baseInfo.operatingPeriodEnd")
    @Mapping(target = "businessScope", source = "enterpriseInfoResponse.extendInfo.businessScope")
    @Mapping(target = "ext", source = "company.ext")
    CompanyDTO toDetail(Company company, EnterpriseInfoResponse enterpriseInfoResponse);

    @AfterMapping
    default void toDetail(Company company, EnterpriseInfoResponse response, @MappingTarget CompanyDTO dto) {
        dto.setSchema("tenant_" + company.getId());
        dto.setBusinessStartDate(DateUtil.parse(response.getBaseInfo().getOperatingPeriodBegin(), "yyyy-MM-dd"));
        dto.setBusinessEndDate(DateUtil.parse(response.getBaseInfo().getOperatingPeriodEnd(), "yyyy-MM-dd"));
    }

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "schema", ignore = true)
    CompanyDTO toDTO(Company company);


    CompanyInfoResp toCompanyInfoResp(Company company);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "organizationName", source = "enterpriseInfoResponse.baseInfo.enterpriseName")
    @Mapping(target = "creditCode", source = "enterpriseInfoResponse.baseInfo.enterpriseCode")
    @Mapping(target = "legalRepresentativeName", source = "enterpriseInfoResponse.baseInfo.legalPerson")
    CompanyInfoResp toCompanyInfoResp(EnterpriseInfoResponse enterpriseInfoResponse);

//    // afterMapping
//    @AfterMapping
//    default void afterToDTO(Company company, @MappingTarget CompanyDTO dto) {
//        dto.setSchema("tenant_" + company.getId());
////        NodeService nodeService = SpringUtil.getBean(NodeService.class);
////        dto.setServiceNode(nodeService.getNode(company.getServiceNodeId()));
//        dto.setThirdBusinessId(company.getCompanyId());
//        JSONObject entries = JSONUtil.parseObj(company.getExt());
//
//        // 从 ext 字段中提取银行相关信息和新增的四个字段
//        dto.setBankName(entries.getStr("bankName"));
//        dto.setBankAccount(entries.getStr("bankAccount"));
//        dto.setFax(entries.getStr("fax"));
//        dto.setPostalCode(entries.getStr("postalCode"));
//        dto.setBankAddress(entries.getStr("bankAddress"));
//        dto.setAccountName(entries.getStr("accountName"));
//        // todo 后续需要改为远端地址

    /// /        dto.setBusinessLicense(toBase64(company.getBusinessLicense()));
    /// /        if (dto.getLegalRepresentativeIdType().equals("身份证")) {
    /// /            dto.setLegalRepresentativeIdNumber(StringUtils.substring(dto.getLegalRepresentativeIdNumber(), 0, 6) + "****" + StringUtils.substring(dto.getLegalRepresentativeIdNumber(), -4));
    /// /        } else {
    /// /            dto.setLegalRepresentativeIdNumber(StringUtils.repeat("*", dto.getLegalRepresentativeIdNumber().length()));
    /// /        }
//    }

//    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
//    @Mapping(source = "businessLicenseLocalUrl", target = "businessLicense")
//    @Mapping(source = "authorizationLetterLocalUrl", target = "authorizationLetter")
//    @Mapping(source = "legalRepresentativeIdValidityStartDate", target = "legalRepresentativeIdExpiry")
//    @Mapping(source = "registeredAddress", target = "registrationAddress")
//    @Mapping(source = "authTime", target = "authDate")
//    @Mapping(target = "authMethod", source = "authType")
//    @Mapping(target = "companyId", ignore = true)
//    @Mapping(target = "nodeId", ignore = true)
//    void convertToCompany(RegisterEntityInfo entityInfo, @MappingTarget Company company);
    @AfterMapping
    default void convertToCompanyAfterMapping(RegisterEntityInfo entityInfo, @MappingTarget Company company) {
//        company.setRegistrationDate(DateUtil.format(entityInfo.getRegistrationDate(), "yyyy-MM-dd"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("authorizationLetterRemoteUrl", entityInfo.getAuthorizationLetterRemoteUrl());
        entries.set("businessLicenseRemoteUrl", URLDecoder.decode(entityInfo.getBusinessLicenseRemoteUrl(), Charset.defaultCharset()));
        entries.set("legalRepresentativeIdValidityEndDate", DateUtil.format(entityInfo.getLegalRepresentativeIdValidityEndDate(), "yyyy-MM-dd"));
        company.setExt(entries.toString());
    }


    //    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
//    @Mapping(source = "businessLicenseLocalUrl", target = "businessLicense")
//    @Mapping(source = "authorizationLetterLocalUrl", target = "authorizationLetter")
//    @Mapping(source = "legalRepresentativeIdValidityStartDate", target = "legalRepresentativeIdExpiry")
//    @Mapping(source = "delegateIdValidityStartDate", target = "delegateIdExpiry")
//    @Mapping(source = "registeredAddress", target = "registrationAddress")
//    @Mapping(source = "authTime", target = "authDate")
//    @Mapping(target = "authMethod", ignore = true)
//    @Mapping(target = "companyId", ignore = true)
//    @Mapping(target = "nodeId", ignore = true)
    void updateCompanyFromDTO(CompanyApplyDTO dto, @MappingTarget Company company);

    @AfterMapping
    default void afterMapping(CompanyApplyDTO dto, @MappingTarget Company company) {
//        if (ObjectUtil.equals(AccessType.LEGAL_PERSON, dto.getAccessType())) {
//            company.setAuthMethod(dto.getAuthType());
//        } else {
//            company.setDelegateAuthMethod(dto.getDelegateAuthType());
//        }
//        company.setRegistrationDate(DateUtil.format(dto.getRegistrationDate(), "yyyy-MM-dd"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
//        entries.set("delegateIdEndExpiry", DateUtil.format(dto.getDelegateIdValidityEndDate(), "yyyy-MM-dd"));
        entries.set("legalRepresentativeIdValidityEndDate", DateUtil.format(dto.getLegalRepresentativeIdValidityEndDate(), "yyyy-MM-dd"));
        company.setExt(entries.toString());
    }


    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Mapping(target = "verifyType", expression = "java(dto.getAccessType().getDesc())")
    @Mapping(target = "legalPerson", source = "dto", qualifiedByName = "toLegalPerson")
    @Mapping(target = "agentPerson", source = "dto", qualifiedByName = "toAgentPerson")
    CompanyVerifyRequest toVerifyRequest(CompanyApplyDTO dto);

    @Named("toLegalPerson")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "applyName", source = "legalRepresentativeName")
    @Mapping(target = "applyIdCard", source = "legalRepresentativeIdNumber")
    @Mapping(target = "companyName", source = "organizationName")
    @Mapping(target = "companyCode", source = "creditCode")
    @Mapping(target = "registerAddress", source = "registeredAddress")
    @Mapping(target = "registerTime", source = "registrationDate")
    @Mapping(target = "industry", source = "industryType")
    @Mapping(target = "businessLicenseUrl", ignore = true)
    @Mapping(target = "operateEndTime", source = "businessEndDate")
    @Mapping(target = "operateStartTime", source = "businessStartDate")
    @Mapping(target = "documentType", source = "legalRepresentativeIdType")
    @Mapping(target = "authenticationMode", source = "authType")
    @Mapping(target = "identityState", source = "identityStatus")
    @Mapping(target = "certificationLevel", source = "legalRepresentativeAuthLevel")
    @Mapping(target = "authenticationTime", source = "authTime")
    @Mapping(target = "partyFax", source = "fax")
    @Mapping(target = "partyPostalCode", source = "postalCode")
    @Mapping(target = "partyBank", source = "bankName")
    @Mapping(target = "partyBankAddress", source = "bankAddress")
    @Mapping(target = "partyAccountName", source = "bankAccount")
    @Mapping(target = "partyBankAccountNumber", source = "bankAddress")
    @Mapping(target = "partyContactInfo", source = "partyContactInfo")
    CompanyVerifyRequest.LegalPerson toLegalPerson(CompanyApplyDTO dto);

    @AfterMapping
    default void toLegalPersonAfter(CompanyApplyDTO dto, @MappingTarget CompanyVerifyRequest.LegalPerson legalPerson) {
        legalPerson.setBusinessLicenseUrl(dto.getBusinessLicenseRemoteUrl());
        legalPerson.setBusinessLicenseUrlName(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), "/"));
        legalPerson.setRegisterTime(DateUtil.format(DateUtil.parse(dto.getAuthTime()), "yyyy-MM-dd"));
        if (!ObjectUtils.isEmpty(dto.getLegalRepresentativeIdValidityStartDate()) && !ObjectUtils.isEmpty(dto.getLegalRepresentativeIdValidityEndDate())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String certificateValidityPeriodStartDate = simpleDateFormat.format(dto.getLegalRepresentativeIdValidityStartDate());
            String certificateValidityPeriodEndDate = simpleDateFormat.format(dto.getLegalRepresentativeIdValidityEndDate());
            legalPerson.setCertificateValidityPeriod(String.format("%s 至 %s", certificateValidityPeriodStartDate, certificateValidityPeriodEndDate));
        }
    }

    @Named("toAgentPerson")
    @BeanMapping(ignoreByDefault = true)
//    @Mapping(target = "applyName", source = "delegateName")
//    @Mapping(target = "applyIdCard", source = "delegateIdNumber")
//    @Mapping(target = "applyPhone", source = "delegatePhone")
//    @Mapping(target = "companyName", source = "organizationName")
//    @Mapping(target = "companyCode", source = "creditCode")
//    @Mapping(target = "registerAddress", source = "delegateAddress")
//    @Mapping(target = "industry", source = "delegateIndustryType")
//    @Mapping(target = "businessLicenseUrl", ignore = true)
//    @Mapping(target = "remark", source = "delegateRemark")
//    @Mapping(target = "applyIdCardEndTime", source = "delegateIdValidityEndDate")
//    @Mapping(target = "applyIdCardStartTime", source = "delegateIdValidityStartDate")
    CompanyVerifyRequest.AgentPerson toAgentPerson(CompanyApplyDTO dto);

    @AfterMapping
    default void toLegalPersonAfter(@MappingTarget CompanyVerifyRequest.AgentPerson agentPerson, CompanyApplyDTO dto) {
        agentPerson.setBusinessLicenseUrl(toBase64(dto.getBusinessLicenseRemoteUrl()));
        agentPerson.setBusinessLicenseUrlName(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), "/"));
//        agentPerson.setApplyIdCardStartTime(DateUtil.format(dto.getDelegateIdValidityStartDate(), "yyyy-MM-dd"));
//        agentPerson.setApplyIdCardEndTime(DateUtil.format(dto.getDelegateIdValidityEndDate(), "yyyy-MM-dd"));
    }

    private String toBase64(String businessUrl) {
        if (StringUtils.isBlank(businessUrl)) {
            return null;
        }
        if (businessUrl.contains("base64"))
            return businessUrl;

        try {
            byte[] fileContent;
            if (businessUrl.startsWith("http://") || businessUrl.startsWith("https://")) {
                // 使用HttpUtil.downloadBytes处理网络URL
                fileContent = cn.hutool.http.HttpUtil.downloadBytes(businessUrl);
            } else {
                // 处理本地文件路径
                String filename = StringUtils.contains(businessUrl, Constants.NGINX_PUBLIC_PATH) ?
                        StringUtils.substringAfter(businessUrl, Constants.NGINX_PUBLIC_PATH) : businessUrl;
                String fullPath = SpringUtil.getProperty("ailand.file-storage.base-path") +
                        FileUtil.FILE_SEPARATOR + "company" + FileUtil.FILE_SEPARATOR + filename;
                fileContent = FileUtil.readBytes(fullPath);
            }

            return RandImageUtils.BASE64_PRE + cn.hutool.core.codec.Base64.encode(fileContent);
        } catch (Exception e) {
            return null;
        }
    }

    @Mapping(target = "applyIdCard", source = "legalRepresentativeIdNumber")
    @Mapping(target = "applyName", source = "legalRepresentativeName")
//    @Mapping(target = "applyType", source = "legalRepresentativeIdType")
    @Mapping(target = "authenticationMode", source = "authType")
    @Mapping(target = "authenticationTime", source = "authTime")
    @Mapping(target = "businessLicenseUrl", source = "businessLicenseRemoteUrl")
//    @Mapping(target = "businessLicenseUrlName", source = "java(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), \"/\"))")
    @Mapping(target = "certificationLevel", source = "legalRepresentativeAuthLevel")
//    @Mapping(target = "companyCode", source = "delegateIdValidityStartDate")
    @Mapping(target = "companyName", source = "organizationName")
    @Mapping(target = "documentType", source = "legalRepresentativeIdType")
//    @Mapping(target = "examineInstitution", source = "delegateIdValidityStartDate")
    @Mapping(target = "identityState", source = "identityStatus")
    @Mapping(target = "industry", source = "industryType")
    @Mapping(target = "operateEndTime", source = "businessEndDate")
    @Mapping(target = "operateRange", source = "businessScope")
    @Mapping(target = "operateStartTime", source = "businessStartDate")
    @Mapping(target = "partyAccountName", source = "accountName")
    @Mapping(target = "partyBank", source = "bankName")
    @Mapping(target = "partyBankAccountNumber", source = "bankAccount")
    @Mapping(target = "partyBankAddress", source = "bankAddress")
    @Mapping(target = "partyFax", source = "fax")
    @Mapping(target = "partyPostalCode", source = "postalCode")
    @Mapping(target = "registerAddress", source = "registeredAddress")
    @Mapping(target = "registerAmount", source = "registeredCapital")
    @Mapping(target = "registerTime", source = "registrationDate")
    @Mapping(target = "partyContactInfo", source = "partyContactInfo")
//    @Mapping(target = "scope", source = "delegateIdValidityStartDate")
    UpdateCompanyLegalInfoReq toUpdateReq(RegisterEntityInfo dto);

    @AfterMapping
    default void toUpdateReqAfter(RegisterEntityInfo dto, @MappingTarget UpdateCompanyLegalInfoReq companyLegalInfoReq) {
        companyLegalInfoReq.setBusinessLicenseUrlName(StringUtils.substringAfterLast(dto.getBusinessLicenseRemoteUrl(), "/"));
        companyLegalInfoReq.setCompanyCode(LoginContextHolder.currentUser().getCompany().getCreditCode());
        companyLegalInfoReq.setCompanyName(LoginContextHolder.currentUser().getCompany().getOrganizationName());
        if (!ObjectUtils.isEmpty(dto.getLegalRepresentativeIdValidityStartDate()) && !ObjectUtils.isEmpty(dto.getLegalRepresentativeIdValidityEndDate())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String certificateValidityPeriodStartDate = simpleDateFormat.format(dto.getLegalRepresentativeIdValidityStartDate());
            String certificateValidityPeriodEndDate = simpleDateFormat.format(dto.getLegalRepresentativeIdValidityEndDate());
            companyLegalInfoReq.setCertificateValidityPeriod(String.format("%s 至 %s", certificateValidityPeriodStartDate, certificateValidityPeriodEndDate));
        }
    }
}