package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据资产信息")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceVO {
    @Schema(description = "资产id", requiredMode = Schema.RequiredMode.REQUIRED)
    String id;
    @Schema(description = "数据资源全局（连接器空间）唯一标识")
    String dataResourcePlatformId;

    /**
     * 用户id
     */
    String userId;

    @Schema(description = "资源提供方")
    ProviderExt provider;

    // 基本信息
    @Schema(description = "资产名称")
    String dataResourceName;
    @Schema(description = "资源格式")
    String resourceFormat;
    @Schema(description = "资产中文名称")
    String dataResourceNameCN;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是")
    String personalInformation;
    @Schema(description = "产品简介")
    String description;
    @Schema(description = "数据资源来源")
    String source;
    @Schema(description = "登记提交时间")
    Long registrationSubmitTime;
    @Schema(description = "登记时间")
    Long registrationTime;
    @Schema(description = "最近登记更新时间")
    Long registrationUpdateTime;

    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "其他")
    String other;

    @Schema(description = "一个或多个数据资源标识码，产品关联数据资源统一标识，基于那些数据资源形成的数据产品。", examples = "[{\"resourceId\":\"resourceId1\"},{\"resourceId\":\"resourceId2\"}]")
    String resourceId;

    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
}
