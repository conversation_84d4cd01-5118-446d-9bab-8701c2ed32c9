package com.ailpha.ailand.dataroute.endpoint.third.input;

import cn.hutool.core.thread.ThreadUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "Executor任务执行回调接口")
@RequiredArgsConstructor
@RequestMapping("callback/executor")
public class DataCollectorJobCallback {

    private final DataAssetPrepareSchedule dataAssetPrepareSchedule;
    private final BaseCapabilityManager baseCapabilityManager;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ExecutorJobRunResult {
        boolean success;
        String message;
    }

    @RequestMapping("{assetId}")
    public void callback(@PathVariable String assetId, @RequestBody ExecutorJobRunResult runResult) {
        ThreadUtil.execAsync(() -> {
            if (log.isDebugEnabled()) {
                log.debug("Executor任务执行回调 assetId: {}, result: {}", assetId, runResult);
            }
            dataAssetPrepareSchedule.executorCallback(assetId, runResult);
        });
    }

    @GetMapping("/getTeePlatformAppKey")
    public CommonResult<BaseCapabilityConfig> getTeePlatformAppKey() {
        BaseCapabilityConfig teePlatformAppKey = baseCapabilityManager.getTeePlatformAppKey();
        return CommonResult.SUCCESS(teePlatformAppKey);
    }
}
