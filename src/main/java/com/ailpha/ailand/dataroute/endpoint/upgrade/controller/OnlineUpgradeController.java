package com.ailpha.ailand.dataroute.endpoint.upgrade.controller;

import com.ailpha.ailand.dataroute.endpoint.upgrade.entity.PackageInfo;
import com.ailpha.ailand.dataroute.endpoint.upgrade.service.OnlineUpgradeService;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.UpgradeTaskVO;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.VersionInfo;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.PackageListReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.PackageUploadReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.UpgradeReq;
import com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request.UpgradeTaskListReq;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Slf4j
@Tag(name = "在线升级")
@RequestMapping("/online-upgrade")
@RestController
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('SUPER_ADMIN','COMPANY_ADMIN')")
public class OnlineUpgradeController {

    private final OnlineUpgradeService onlineUpgradeService;

    @Operation(summary = "私钥加密MD5")
    @Parameters({
            @Parameter(name = "md5", description = "md5", in = ParameterIn.QUERY)
    })
    @GetMapping(value = "/rsa-private-encrypt-md5")
    public SuccessResponse<String> rsaPrivateEncryptMd5(@RequestParam(value = "md5") String md5) throws Exception {
        String encryptMD5 = onlineUpgradeService.rsaPrivateEncryptMd5(md5);
        return SuccessResponse.success(encryptMD5).build();
    }

    @Operation(summary = "包管理-上传")
    @PostMapping(value = "/package/upload")
    public SuccessResponse<String> packageUpload(MultipartFile file) throws Exception {
        String id = onlineUpgradeService.packageUpload(file);
        return SuccessResponse.success(id).build();
    }

    @Operation(summary = "包管理-创建")
    @PostMapping(value = "/package/create")
    public SuccessResponse<String> packageCreate(@Valid @RequestBody PackageUploadReq packageUploadReq) throws Exception {
        String id = onlineUpgradeService.packageCreate(packageUploadReq);
        return SuccessResponse.success(id).build();
    }

    @Operation(summary = "包管理-删除")
    @Parameters({
            @Parameter(name = "id", description = "id", in = ParameterIn.QUERY)
    })
    @DeleteMapping(value = "/package/delete")
    public SuccessResponse<Boolean> packageDelete(@RequestParam(value = "id") String id) {
        onlineUpgradeService.packageDelete(id);
        return SuccessResponse.success(Boolean.TRUE).build();
    }

    @Operation(summary = "包管理-列表")
    @PostMapping(value = "/package/list")
    public SuccessResponse<List<PackageInfo>> packageList(@Valid @RequestBody PackageListReq packageListReq) {
        return onlineUpgradeService.packageList(packageListReq);
    }

    @Operation(summary = "当前节点版本信息")
    @GetMapping(value = "/version-info")
    public SuccessResponse<VersionInfo> versionInfo() {
        return SuccessResponse.success(OnlineUpgradeService.VERSION_INFO).build();
    }

    @Operation(summary = "节点升级")
    @PostMapping(value = "/upgrade")
    public SuccessResponse<String> upgrade(@Valid @RequestBody UpgradeReq upgradeReq) throws Exception {
        String id = onlineUpgradeService.upgrade(upgradeReq);
        return SuccessResponse.success(id).build();
    }

    @Operation(summary = "节点升级-列表")
    @PostMapping(value = "/upgrade/list")
    public SuccessResponse<List<UpgradeTaskVO>> upgradeList(@Valid @RequestBody UpgradeTaskListReq upgradeListReq) {
        return onlineUpgradeService.upgradeList(upgradeListReq);
    }

    @Operation(summary = "节点升级-日志")
    @Parameters({
            @Parameter(name = "id", description = "id", in = ParameterIn.QUERY)
    })
    @GetMapping(value = "/upgrade/log")
    public SuccessResponse<String> upgradeLog(@RequestParam(value = "id") String id) {
        return SuccessResponse.success(onlineUpgradeService.upgradeLog(id)).build();
    }
}
