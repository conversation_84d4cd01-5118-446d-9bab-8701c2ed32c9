package com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:23
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_delivery_scene")
@AllArgsConstructor
public class DeliveryScene implements Serializable {

    @Id
    private String id;

    /**
     * 交付方式
     */
    @Schema(description = "交付方式")
    @Column(name = "delivery_type")
    @Enumerated(EnumType.STRING)
    private DeliveryType deliveryType;

    /**
     * 状态 RUNNING（进行中）TERMINATED（已终止）COMPLETED（已完成）
     */
    @Schema(description = "状态")
    @Column(name = "scene_status")
    private String sceneStatus;

    /**
     * 数字证书 —— 合规场景id
     */
    @Schema(description = "数字证书 —— 合规场景id")
    @Column(name = "digital_scene_id")
    private String digitalSceneId;

    /**
     * 数字证书 —— 合规场景名称 冗余存储
     */
    @Schema(description = "数字证书 —— 合规场景名称 冗余存储")
    @Column(name = "digital_scene_name")
    private String digitalSceneName;

    /**
     * 扩展
     */
    @Schema(description = "扩展")
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "ext", columnDefinition = "json")
    private Ext ext;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Ext {

        /**
         * TEE、MPC 交付方式下 创建的合约id
         */
        private String contractId;

        /**
         * TEE、MPC 交付方式下 创建的合约名称
         */
        private String contractName;

    }

}
