package com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:23
 */
@Data
@Entity
@Builder
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Table(name = "dr_delivery_scene")
@AllArgsConstructor
public class DeliveryScene implements Serializable {

    @Id
    private String id;

    /**
     * 交付使用场景
     */
    @Schema(description = "交付使用场景")
    @Column(name = "delivery_type")
    @Enumerated(EnumType.STRING)
    private DeliveryType deliveryType;

    /**
     * 状态 RUNNING（进行中）TERMINATED（已终止）COMPLETED（已完成） 新增：待完善 WAIT_ENTER  磋商中 NEGOTIATE
     */
    @Schema(description = "状态")
    @Column(name = "scene_status")
    private String sceneStatus;

    /**
     * 数字证书 —— 合规场景id
     */
    @Schema(description = "数字证书 —— 合规场景id")
    @Column(name = "digital_scene_id")
    private String digitalSceneId;

    /**
     * 数字证书 —— 合规场景名称 冗余存储
     */
    @Schema(description = "数字证书 —— 合规场景名称 冗余存储")
    @Column(name = "digital_scene_name")
    private String digitalSceneName;

    /**
     * 扩展
     */
    @Schema(description = "扩展")
    @Embedded
    @Column(name = "ext")
    @JdbcTypeCode(SqlTypes.JSON)
    private Ext ext;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "create_user")
    private String createUser;

    /**
     * 是否经过传输协商
     */
    @Schema(description = "会签传输协商")
    @Column(name = "transfer_all")
    private Boolean transferAll;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Ext implements Serializable {

        /**
         * TEE、MPC 交付方式下 创建的合约id
         */
        private String contractId;

        /**
         * TEE、MPC 交付方式下 创建的合约名称
         */
        private String contractName;

        private String contract;
    }

    public Ext getExt() {
        if (ext == null) {
            return new Ext();
        }
        return ext;
    }

}
