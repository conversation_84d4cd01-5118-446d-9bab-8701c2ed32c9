package com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UploadMethod;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/6/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PackageUploadReq implements Serializable {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    private UpgradeModule module;

    @NotEmpty(message = "版本不能为空")
    @Schema(description = "版本", example = "V1.0")
    private String version;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "MD5（私钥加密）")
    private String md5;

    @NotNull(message = "上传方式不能为空")
    @Schema(description = "上传方式：WEB（页面）SERVER（服务器）", example = "WEB")
    private UploadMethod uploadMethod;

    @Schema(description = "升级包文件ID")
    private String fileId;

    @Schema(description = "服务器文件路径")
    private String serverFilePath;
}
