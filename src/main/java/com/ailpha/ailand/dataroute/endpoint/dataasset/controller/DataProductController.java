package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.common.log.InternalOpType;
import com.ailpha.ailand.dataroute.endpoint.common.log.OPLogContext;
import com.ailpha.ailand.dataroute.endpoint.common.log.OpLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.PublishProduct;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType.API;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType.FROM_MPC;

@Slf4j
@RestController
@Tag(name = "数据资产-数据产品")
@RequiredArgsConstructor
@RequestMapping("data-product")
@PreAuthorize("hasAuthority('TRADER')")
public class DataProductController {

    private final DataProductService dataProductService;

    @PostMapping()
    @Operation(summary = "数据产品列表")
    public SuccessResponse<List<DataProductListVO>> allDataAsset(@RequestBody DataProductListQuery dataProductListQuery) {
        UserDTO currentUser = LoginContextHolder.currentUser();
        return dataProductService.allDataAssets((int) dataProductListQuery.getNum(), (int) dataProductListQuery.getSize(), specification -> {
            specification = DataProduct.dataProductNameLike(specification, dataProductListQuery.getProductName());
            specification = DataProduct.dataTypeIs(specification, dataProductListQuery.getDataType());
            specification = DataProduct.accessWayIs(specification, dataProductListQuery.getAccessWay());
            specification = DataProduct.userIdIs(specification, currentUser.getId());
            specification = DataProduct.publishStatusIs(specification, dataProductListQuery.getPublishStatus());
            specification = DataProduct.itemStatusIs(specification, dataProductListQuery.getItemStatus());
            specification = DataProduct.prepareStatusIs(specification, dataProductListQuery.getDataAssetPrepareStatus());
            return specification;
        });
    }

    @GetMapping("{dataProductId}")
    @Operation(summary = "获取数据产品详情")
    @Parameters({
            @Parameter(name = "dataProductId", description = "数据产品id", in = ParameterIn.PATH)
    })
    public SuccessResponse<DataProductVO> getDataAsset(@PathVariable("dataProductId") String dataAssetId) {
        DataProductVO dataProductVO = dataProductService.getDataAssetById(dataAssetId);
        UserDTO currentUser = LoginContextHolder.currentUser();
        if (!(API.equals(dataProductVO.getAccessWay()) || FROM_MPC.equals(dataProductVO.getAccessWay())) ||
                !StringUtils.pathEquals(dataProductVO.getUserId(), currentUser.getId())) {
            // NOTE: 仅产品创建人可跳转API产品的网关高级配置页面
            dataProductVO.setGatewayServiceRouteId(null);
        }
        return SuccessResponse.success(dataProductVO).build();
    }

    @GetMapping("market/{dataProductPlatformId}")
    @Operation(summary = "获取数据产品详情(数据目录、产品地图)")
    @Parameters({
            @Parameter(name = "dataProductPlatformId", description = "数据产品全局id", in = ParameterIn.PATH)
    })
    public SuccessResponse<DataProductVO> getDataProductByDataProductPlatformId(@PathVariable("dataProductPlatformId") String dataProductPlatformId) {
        DataProductVO dataProductVO = dataProductService.getDataProductByDataProductPlatformId(dataProductPlatformId);
        UserDTO currentUser = LoginContextHolder.currentUser();
        if (!(API.equals(dataProductVO.getAccessWay()) || FROM_MPC.equals(dataProductVO.getAccessWay())) ||
                !StringUtils.pathEquals(dataProductVO.getUserId(), currentUser.getId())) {
            // NOTE: 仅产品创建人可跳转API产品的网关高级配置页面
            dataProductVO.setGatewayServiceRouteId(null);
        }
        return SuccessResponse.success(dataProductVO).build();
    }

    @PostMapping("registration/temporary-save")
    @OpLog(message = "数据产品登记暂存")
    @Operation(summary = "数据产品登记暂存")
    public SuccessResponse<Boolean> tempSaveRegistration(@RequestBody DataProductRegistRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_TEMPORARY_SAVE);
        if (StringUtils.hasText(request.getId())) {
            request.setProductName(null);
        } else {
            dataProductService.checkNameExists(null, request.getProductName());
        }
        dataProductService.temporarySave(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("registration")
    @OpLog(message = "数据产品登记")
    @Operation(summary = "数据产品登记")
    public SuccessResponse<Boolean> submitRegistration(@RequestBody @Valid DataProductRegistRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST);
        if (StringUtils.hasText(request.getId())) {
            request.setProductName(null);
        } else {
            dataProductService.checkNameExists(null, request.getProductName());
        }
        dataProductService.registration(request);
        return SuccessResponse.success(true).build();
    }

    @PutMapping("registration")
    @OpLog(message = "数据产品登记更新")
    @Operation(summary = "数据产品登记更新")
    public SuccessResponse<Boolean> updateRegistration(@RequestBody DataProductRegistUpdateRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST_UPDATE);
        dataProductService.updateRegistration(request);
        return SuccessResponse.success(true).build();
    }

    @DeleteMapping("registration/revoke/{dataProductId}")
    @OpLog(message = "数据产品登记撤销")
    @Operation(summary = "数据产品登记撤销")
    public SuccessResponse<Boolean> revokeRegistration(@PathVariable String dataProductId) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_REGIST_REVOKE);
        dataProductService.revokeRegistration(dataProductId);
        // NOTE bug-174558
//        SuccessResponse<List<OrderApprovalRecord>> orderList = hubOrderRemote.selectOrderApprovalRecordWhereAssetIdInAndBeneficiaryId(AssetBeneficiaryRelDTOListReq.builder()
//                .assetIds(List.of(dataProductId))
//                .build());
//        List<String> orderIds = null;
//        if (orderList.isSuccess() && !CollectionUtils.isEmpty(orderList.getData())) {
//            List<OrderApprovalRecord> approvalRecords = orderList.getData().stream().filter(order -> org.apache.commons.lang3.StringUtils.equals(order.getStatus(), "APPLY") || org.apache.commons.lang3.StringUtils.equals(order.getStatus(), "APPROVED"))
//                    .peek(order -> {
//                        order.setStatus(OrderStatus.TERMINATED.name());
//                        order.setChangeStatus(true);
//                    }).toList();
//            hubOrderRemote.updateOrderApprovalRecords(approvalRecords);
//            orderIds = approvalRecords.stream().map(OrderApprovalRecord::getId).collect(Collectors.toList());
//        }
//        try {
//            // TEE MPC 数据集删除、合约终止
//            orderManagerService.terminalContract(orderIds, Collections.singletonList(dataProductId));
//        } catch (Exception ignore) {
//        }
        return SuccessResponse.success(true).build();
    }

    @PostMapping("publish")
    @OpLog(message = "数据产品上架")
    @Operation(summary = "数据产品上架")
    public SuccessResponse<Boolean> publish(@RequestBody DataProductPublishRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_ONLINE);
        dataProductService.publish(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("publishProduct")
    @OpLog(message = "数据产品发布")
    @Operation(summary = "数据产品发布")
    public SuccessResponse<Boolean> publishProduct(@RequestBody PublishProduct request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_ONLINE);
        dataProductService.publishProduct(request);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("publish-to-node")
    @OpLog(message = "数据产品上架到业务节点")
    @Operation(summary = "数据产品上架到业务节点: productId -> {dataProductId}, 业务节点id -> {serviceNodeId}")
    public SuccessResponse<Map<String, String>> publishToServiceNode(@RequestBody DataProductPublishToServiceNodeRequest publishToServiceNodeRequest) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_ONLINE);
        Map<String, String> publishToNodeResult = dataProductService.publishToNode(publishToServiceNodeRequest.getDataProductId(), publishToServiceNodeRequest.getServiceNodeIds());
        return SuccessResponse.success(publishToNodeResult).build();
    }

    @PostMapping("publish/update")
    @OpLog(message = "数据产品上架更新")
    @Operation(summary = "数据产品上架更新")
    public SuccessResponse<Boolean> publishUpdate(@RequestBody DataProductPublishUpdateRequest request) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_ONLINE_UPDATE);
        dataProductService.publishUpdate(request);
        return SuccessResponse.success(true).build();
    }

    @PutMapping("unpublish")
    @OpLog(message = "数据产品下架: productId -> {dataProductId}, 业务节点id -> {serviceNodeId}")
    @Operation(summary = "数据产品下架")
    public SuccessResponse<Boolean> unpublish(@RequestBody DataProductPublishToServiceNodeRequest publishToServiceNodeRequest) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_OFFLINE);
        OPLogContext.put("dataProductId", publishToServiceNodeRequest.getDataProductId());
        OPLogContext.put("serviceNodeId", publishToServiceNodeRequest.getServiceNodeIds());
        dataProductService.unpublish(publishToServiceNodeRequest.getDataProductId(), publishToServiceNodeRequest.getServiceNodeIds());
        return SuccessResponse.success(true).build();
    }


    @DeleteMapping("delete/{dataProductId}")
    @OpLog(message = "数据产品（暂存）删除: productId -> {dataProductId}")
    @Operation(summary = "数据产品（暂存）删除")
    public SuccessResponse<Boolean> delete(@PathVariable String dataProductId) {
        OPLogContext.putOpType(InternalOpType.DATAASSET_DELETE);
        OPLogContext.put("dataProductId", dataProductId);

        dataProductService.delete(dataProductId);
        return SuccessResponse.success(true).build();
    }


}
