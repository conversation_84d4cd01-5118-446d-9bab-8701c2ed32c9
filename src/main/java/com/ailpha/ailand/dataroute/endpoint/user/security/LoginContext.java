package com.ailpha.ailand.dataroute.endpoint.user.security;

import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
public class LoginContext implements Serializable {
    UserDTO userDTO;
    RoleEnums role;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof UserDTO) {
            obj = userDTO;
            return ((UserDTO) obj).getId().equals(userDTO.getId());
        }

        return super.equals(obj);
    }
}
