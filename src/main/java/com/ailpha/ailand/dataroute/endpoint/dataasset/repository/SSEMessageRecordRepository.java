package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageReadStatus;
import com.ailpha.ailand.dataroute.endpoint.sse.entity.SSEMessageRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 11:21
 * @Description:
 */
public interface SSEMessageRecordRepository extends JpaRepository<SSEMessageRecord, Long>, QuerydslPredicateExecutor<SSEMessageRecord> {
    List<SSEMessageRecord> findAllByReadStatus(SSEMessageReadStatus readStatus);

    Long countByUserIdAndReadStatus(String userId, SSEMessageReadStatus readStatus);
}
