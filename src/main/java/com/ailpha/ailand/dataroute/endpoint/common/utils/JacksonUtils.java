package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;

@Slf4j
public class JacksonUtils {
    private final static ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        objectMapper.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {
            @Override
            public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                String fieldName = gen.getOutputContext().getCurrentName();
                try {
                    if (StringUtils.isEmpty(fieldName) || gen.currentValue() == null) {
                        return;
                    }
                    //反射获取字段类型
                    Field field = gen.currentValue().getClass().getDeclaredField(fieldName);
//                    if (Objects.equals(field.getType(), String.class)) {
//                        //字符串型空值""
//                        gen.writeString("");
//                        return;
//                    } else
                    if (Objects.equals(field.getType(), List.class)) {
                        //列表型空值返回[]
                        gen.writeStartArray();
                        gen.writeEndArray();
                        return;
                    } else if (Objects.equals(field.getType(), Map.class)) {
                        //map型空值返回{}
                        gen.writeStartObject();
                        gen.writeEndObject();
                        return;
                    }
                } catch (NoSuchFieldException ignore) {
                }
                gen.writeNull();
            }
        });

    }

    public static ObjectMapper getInstance() {
        return objectMapper;
    }

    public static String obj2json(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            return "{}";
        }
    }

    public static String obj2jsonIgnoreNull(Object obj) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            return "";
        }
    }

    public static <T> T json2pojo(String jsonString, Class<T> clazz) {
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            throw new RuntimeException("解析json失败");
        }
    }

    public static <T> Map<String, Object> json2map(String jsonString) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return mapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {
            });
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            throw new RuntimeException("解析json失败");
        }
    }

    public static Map<String, Object> json2mapDeeply(String json) {
        try {
            return json2MapRecursion(json, objectMapper);
        } catch (Exception e) {
            log.error("解析json失败:", e);
            return new HashMap<>();
        }
    }

    private static List<Object> json2ListRecursion(String json, ObjectMapper mapper) {
        if (json == null) {
            return new ArrayList<>();
        }

        try {
            List<Object> list = mapper.readValue(json, new TypeReference<List<Object>>() {
            });

            for (Object obj : list) {
                if (obj instanceof String str) {
                    if (str.startsWith("[")) {
                        obj = json2ListRecursion(str, mapper);
                    } else if (obj.toString().startsWith("{")) {
                        obj = json2MapRecursion(str, mapper);
                    }
                }
            }

            return list;
        } catch (Exception e) {
            log.error("解析json失败:", e);
            throw new RuntimeException("解析json失败");
        }

    }

    private static Map<String, Object> json2MapRecursion(String json, ObjectMapper mapper) throws Exception {
        if (json == null) {
            return null;
        }

        Map<String, Object> map = mapper.readValue(json, new TypeReference<Map<String, Object>>() {
        });

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object obj = entry.getValue();
            if (obj instanceof String str) {
                if (str.startsWith("[")) {
                    List<?> list = json2ListRecursion(str, mapper);
                    map.put(entry.getKey(), list);
                } else if (str.startsWith("{")) {
                    Map<String, Object> mapRecursion = json2MapRecursion(str, mapper);
                    map.put(entry.getKey(), mapRecursion);
                }
            }
        }
        return map;
    }

    public static <T> List<T> json2list(String jsonArrayStr, Class<T> clazz) {
        try {
            JavaType javaType = getCollectionType(ArrayList.class, clazz);
            return objectMapper.readValue(jsonArrayStr, javaType);
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            return new ArrayList<>();
        }
    }

    public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
        return objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
    }

    public static <T> T map2pojo(Map<?, ?> map, Class<T> clazz) {
        return objectMapper.convertValue(map, clazz);
    }

    public static JsonNode readTree(String jsonStr) {
        try {
            return objectMapper.readTree(jsonStr);
        } catch (JsonProcessingException e) {
            log.error("解析json失败:", e);
            throw new RuntimeException("解析json失败");
        }
    }

    public static <T> T obj2pojo(Object obj, Class<T> clazz) {
        return objectMapper.convertValue(obj, clazz);
    }

}
