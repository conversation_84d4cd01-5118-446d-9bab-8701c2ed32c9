package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
public class AddDemandRequest {
    @Schema(description = "标题")
    @Length(min = 1, max = 15, message = "标题不能超过15个字符")
    String title;

    /**
     * 描述
     */
    @Schema(description = "描述")
    String description;

    /**
     * 到期日期
     */
    @Schema(description = "需求有效期")
    String expireDate;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    String dataType;

    @Schema(description = "标签")
    List<String> tags;

    /**
     * 数据规模
     */
    @Schema(description = "数据量级")
    String dataScale;

    /**
     * 质量要求
     */
    @Schema(description = "质量要求")
    String qualityRequirements;

    /**
     * 预算范围
     */
    @Schema(description = "预算范围")
    String budgetRange;

    /**
     * 预期交付方式
     */
    @Schema(description = "预期交付方式")
    List<String> expectedDeliveryMethod;

    /**
     * 法律条款文件ID
     */
    @Schema(description = "法律条款文件ID", hidden = true)
    String legalTermsFileId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", hidden = true)
    String userId;

    /**
     * 路由ID
     */
    @Schema(description = "路由ID", hidden = true)
    String routerId;

    @Schema(description = "需求方信息", hidden = true)
    DemandSideDTO demandSide;

}
