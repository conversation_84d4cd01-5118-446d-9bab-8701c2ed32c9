package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FileSourceMetadata implements Serializable {
    @Schema(description = "数据文件（数据集、图像、模型）路径")
    String dataAssetFilePath;
    @Schema(description = "数据文件哈希")
    String dataAssetFileHash;
    @Schema(description = "数据文件来源：页面上传、服务器文件路径")
    FileSourceEnum fileSource;
}
