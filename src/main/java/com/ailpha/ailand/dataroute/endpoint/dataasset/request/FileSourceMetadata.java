package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FileSourceMetadata {
    @Schema(description = "数据资产文件临时id")
    String dataAssetFileId;
    String debugDataPath;
    String dataAssetFilePath;
    String dataAssetFileHash;
}
