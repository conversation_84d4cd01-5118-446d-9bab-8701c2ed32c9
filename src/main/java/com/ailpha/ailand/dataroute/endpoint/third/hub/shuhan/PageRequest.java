package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PageRequest<T> {
    /**
     * 当前页码
     */
    int current;
    /**
     * 排序规则, 默认asc,可用值:desc,asc
     */
    String order;
    /**
     * 页面大小
     */
    Integer size;
    /**
     * 排序,默认createTime,可用值:id,createTime,updateTime
     */
    String sort;

    /**
     * 查询参数
     */
    T model;
}
