package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataProductUnPublishVM {

    @ApiModelProperty(value = "数据产品唯一标识（下架必传）")
    @NotNull(message = "数据上架ID为空")
    private Long launchId;
}
