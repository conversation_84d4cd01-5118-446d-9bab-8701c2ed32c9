package com.ailpha.ailand.dataroute.endpoint.common.enums;

/**
 * @author: sunsas.yu
 * @date: 2024/11/28 11:07
 * @Description: 接口类型
 */
public enum PluginApiMarkTypeEnums {
    /**
     * 数商上报
     */
    business_reporting,

    /**
     * 新增产品数据
     */
    save_data_asset,

    /**
     * 编辑产品数据
     */
    update_data_asset,

    /**
     * 产品数据上下架
     */
    publish_status,

    /**
     * 交易登记
     */
    transaction_register,

    /**
     * 交付登记
     */
    delivery_register,
    ;
}
