package com.ailpha.ailand.dataroute.endpoint.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetBeneficiaryExtend implements Serializable {

    @Schema(description = "API网关appKey")
    String apiKey;
}
