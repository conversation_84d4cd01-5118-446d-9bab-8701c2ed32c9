package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetBeneficiaryExtend implements Serializable {

    @Schema(description = "API网关appKey")
    String apiKey;

    @Schema(description = "minio访问key")
    String accessKey;

}
