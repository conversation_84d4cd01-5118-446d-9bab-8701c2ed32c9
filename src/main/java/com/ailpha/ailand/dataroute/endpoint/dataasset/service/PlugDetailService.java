package com.ailpha.ailand.dataroute.endpoint.dataasset.service;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.PluginDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailPageVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PluginDetailVO;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginDetail;
import com.dbapp.rest.response.ApiResponse;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:45
 * @Description:
 */
public interface PlugDetailService {
    PluginDetailVO getById(Long id);

    ApiResponse<List<PluginDetailPageVO>> pageByParam(PluginDetailPageRequest request);

    /**
     * 功能描述: 查询列表
     *
     * @param status status 启用状态
     * @param type   type 插件类型
     * @return PluginDetail
     * <AUTHOR>
     * @date 2024/11/27 15:42
     */
    List<PluginDetail> listByParam(Boolean status, PluginApiTypeEnums type);

    boolean updateStatus(PluginDetailRequest request);

    boolean savePlug(PluginDetailRequest request);

    boolean updatePlug(PluginDetailRequest request);

    List<PluginDetail> findAllByIdIn(List<Long> ids);
    List<PluginDetail> getByDataAssertIds(List<String> dataAssertIds);

}
