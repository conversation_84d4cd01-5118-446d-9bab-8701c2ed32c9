package com.ailpha.ailand.dataroute.endpoint.third.util;

import cn.hutool.crypto.SmUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SignUtil;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/14 14:19
 */
public class Demo {

    public static void main(String[] args) {
        // 请求地址
        String url = "http://b.com/api/addMoney";
        // 请求参数
        Map<String, Object> paramMap = new LinkedHashMap<>();
        paramMap.put("accessKey", "ak123");
        paramMap.put("workOrderNo", "123");
        paramMap.put("workOrderStatus", "ORDER_SUCCESS");
        // 需要加密的字段，使用 secretKey 进行 SM4 加密
        // paramMap.put("certNo", encryptBySm4("身份证号", "sk123"));
        paramMap.put(SignUtil.TIMESTAMP, System.currentTimeMillis());
        paramMap.put(SignUtil.NONCE, SignUtil.getRandomString(32));

        // 构建签名
        // secretKey
        String sign = SignUtil.createSign(paramMap, "sk123");
        paramMap.put(SignUtil.SIGN, sign);
        // 发送请求，示例使用 hutool，非必须
        String res = HttpUtil.post(url, JSONUtil.toJsonStr(paramMap));
    }

    /*** sm4 加密
     *
     * @param data
    待加密数据
     * @param password 秘钥字符串
     * @return 加密后字符串, 采用 Base64 编码
     */
    public static String encryptBySm4(String data, String password) {
        if (StringUtils.isBlank(password)) {
            throw new IllegalArgumentException("SM4 需要传入秘钥信息");
        }

        // sm4 算法的秘钥要求是 16 位长度
        int sm4PasswordLength = 16;
        if (sm4PasswordLength != password.length()) {
            throw new IllegalArgumentException("SM4 秘钥长度要求为 16 位");
        }
        return SmUtil.sm4(password.getBytes(StandardCharsets.UTF_8)).encryptBase64(data, StandardCharsets.UTF_8);
    }

}
