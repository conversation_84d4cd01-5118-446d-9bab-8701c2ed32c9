package com.ailpha.ailand.dataroute.endpoint.user.vo.login;

import com.ailpha.ailand.dataroute.endpoint.user.enums.GrantType;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LoginRequest {
    @Schema(description = "授权码")
    String code;
    @Schema(description = "用户名")
    String username;
    @Schema(description = "密码")
    String password;
    GrantType type = GrantType.password;
    String captchaId;
    String captchaValue;
    String schema;
}
