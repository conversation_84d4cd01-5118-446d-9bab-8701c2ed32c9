package com.ailpha.ailand.dataroute.endpoint.plugin.service.impl;

import com.ailpha.ailand.biz.api.collector.BlockchainPluginPageRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginRequest;
import com.ailpha.ailand.biz.api.collector.BlockchainPluginUpdateRequest;
import com.ailpha.ailand.biz.api.constants.BlockchainPluginTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginApiDetail;
import com.ailpha.ailand.dataroute.endpoint.entity.BlockchainPluginDetail;
import com.ailpha.ailand.dataroute.endpoint.plugin.common.BlockchainStatusConstant;
import com.ailpha.ailand.dataroute.endpoint.plugin.service.PluginService;
import com.ailpha.ailand.dataroute.endpoint.plugin.strategy.BlockchainProcessingStrategy;
import com.ailpha.ailand.dataroute.endpoint.plugin.strategy.BlockchainStrategyFactory;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainApiDetailResponse;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainDetailResponse;
import com.ailpha.ailand.dataroute.endpoint.plugin.vo.BlockchainListResponse;
import com.ailpha.ailand.dataroute.endpoint.repository.BlockchainPluginApiDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.repository.BlockchainPluginDetailRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.invoke.api.CommonException;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PluginServiceImpl implements PluginService {

    @Value("${ailand.file-storage.base-path}")
    private String basePath;

    private final BlockchainPluginDetailRepository blockchainPluginDetailRepository;
    private final BlockchainPluginApiDetailRepository blockchainPluginApiDetailRepository;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Override
    public String storePlugin(MultipartFile file) {
        try {
            Path storagePath = Paths.get(basePath).resolve("plugins");
            Files.createDirectories(storagePath);
            Path targetLocation = storagePath.resolve(file.getOriginalFilename());
            Files.copy(file.getInputStream(), targetLocation);
            return targetLocation.toString();
        } catch (Exception e) {
            throw new RuntimeException("插件存储失败: " + e.getMessage(), e);
        }
    }

    @Transactional
    public void saveBlockchainPlugin(BlockchainPluginRequest request) {
        // 校验插件名称唯一性
        String pluginName = request.getName();
        if (blockchainPluginDetailRepository.existsByName(pluginName)) {
            throw new CommonException("插件名称" + pluginName + "已存在，请修改后重试");
        }

        BlockchainProcessingStrategy strategy = BlockchainStrategyFactory.getStrategy(request.getType());
        Long res = strategy.save(request);
        if (Objects.isNull(res)) {
            throw new CommonException("保存插件失败: " + request);
        }
    }

    @Override
    @Async
    public void upBlockchain(String moduleName, String data) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        List<BlockchainPluginDetail> blockchainPluginDetails = blockchainPluginDetailRepository.findALLByStatusAndModuleType(BlockchainStatusConstant.ON, moduleName);

        if (CollectionUtils.isEmpty(blockchainPluginDetails)) {
            log.debug("未配置区块链插件");
            return;
        }
        log.debug("区块链插件配置列表：{}", blockchainPluginDetails);
        List<CompletableFuture<Void>> futures = blockchainPluginDetails.stream()
            .map(detail -> CompletableFuture.runAsync(() -> {
                TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
                BlockchainProcessingStrategy strategy = BlockchainStrategyFactory.getStrategy(detail.getType());
                try {
                    if (!strategy.process(data, detail)) {
                        log.error("处理区块链类型{}时发生异常", detail.getType());
                    }
                } catch (Exception e) {
                    log.error("处理区块链类型{}时发生异常", detail.getType(), e);
                }
            }, threadPoolTaskExecutor))
            .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    @Override
    public SuccessResponse<List<BlockchainListResponse>> listBlockchainPlugin(BlockchainPluginPageRequest request) {

        Pageable pageable = PageRequest.of((int) (request.getNum() - 1), (int) request.getSize(), Sort.by(Sort.Direction.DESC, "createTime"));

        BlockchainPluginDetail probe = new BlockchainPluginDetail();
        if (StringUtils.hasText(request.getName())) {
            probe.setName(request.getName());
        }
        if (Objects.nonNull(request.getStatus())) {
            probe.setStatus(request.getStatus());
        }
        if (Objects.nonNull(request.getType())) {
            probe.setType(request.getType());
        }
        if (Objects.nonNull(request.getModuleType())) {
            probe.setModuleType(request.getModuleType());
        }

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("name", ExampleMatcher.GenericPropertyMatcher::contains)
                .withIgnoreNullValues();

        Page<BlockchainPluginDetail> pageResult = blockchainPluginDetailRepository.findAll(
                Example.of(probe, matcher),
                pageable
        );

        List<BlockchainListResponse> responses = pageResult.getContent().stream().map(detail ->
                new BlockchainListResponse(
                        detail.getId(),
                        detail.getName(),
                        detail.getModuleType(),
                        detail.getDescription(),
                        detail.getStatus(),
                        detail.getCreateTime(),
                        detail.getType(),
                        detail.doGetUrl()
                )
        ).collect(Collectors.toList());

        return SuccessResponse.success(responses)
                .total(pageResult.getTotalElements())
                .page(com.dbapp.rest.request.Page.of(request.getNum(), request.getSize()))
                .build();
    }

    @Override
    public SuccessResponse<BlockchainDetailResponse> blockchainPluginDetail(Long id) {
        BlockchainPluginDetail detail = blockchainPluginDetailRepository.findById(id)
                .orElseThrow(() -> new CommonException("插件不存在"));

        BlockchainDetailResponse response = new BlockchainDetailResponse();
        response.setId(detail.getId());
        response.setName(detail.getName());
        response.setModuleType(detail.getModuleType());
        response.setDescription(detail.getDescription());
        response.setType(detail.getType());
        response.setStatus(detail.getStatus());
        response.setCreateTime(detail.getCreateTime());

        if (BlockchainPluginTypeEnum.API.equals(detail.getType())) {
            List<BlockchainPluginApiDetail> apiDetails = detail.getBlockchainPluginApiDetails();
            response.setApiDetails(apiDetails.stream()
                    .map(this::toBlockchainApiDetailResponse)
                    .collect(Collectors.toList()));
        } else if (BlockchainPluginTypeEnum.GROOVY.equals(detail.getType())) {
            try {
                Path scriptPath = Paths.get(detail.getExt());
                String scriptContent = Files.readString(scriptPath);
                response.setScript(scriptContent);
            } catch (IOException e) {
                throw new CommonException("读取脚本文件失败", e);
            }
        }

        return SuccessResponse.success(response).build();
    }

    @Override
    @Transactional
    public void updateBlockchainPlugin(BlockchainPluginUpdateRequest request) {
        // 名称重复校验（排除自身）
        if (blockchainPluginDetailRepository.existsByNameAndIdNot(request.getName(), request.getId())) {
            throw new CommonException("插件名称" + request.getName() + "已存在，请修改后重试");
        }
        BlockchainProcessingStrategy strategy = BlockchainStrategyFactory.getStrategy(request.getType());
        Long res = strategy.update(request);
        if (Objects.isNull(res)) {
            throw new CommonException("更新插件失败: " + request);
        }
    }

    @Override
    @Transactional
    public void updateStatusBlockchainPlugin(BlockchainPluginUpdateRequest request) {
        BlockchainPluginDetail detail = blockchainPluginDetailRepository.findById(request.getId())
                .orElseThrow(() -> new CommonException("插件不存在"));
        detail.setStatus(request.getStatus());
        blockchainPluginDetailRepository.saveAndFlush(detail);
    }

    private BlockchainApiDetailResponse toBlockchainApiDetailResponse(BlockchainPluginApiDetail detail) {
        return BlockchainApiDetailResponse.builder()
                .url(detail.getUrl())
                .method(detail.getMethod())
                .params(detail.getParams())
                .body(detail.getBody())
                .bodyType(detail.getBodyType())
                .headers(detail.getHeaders())
                .type(detail.getType())
                .upDataField(detail.getUpDataField())
                .build();
    }
}
