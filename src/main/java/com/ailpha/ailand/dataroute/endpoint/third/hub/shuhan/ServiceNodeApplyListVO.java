package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/4/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeApplyListVO implements Serializable {

    @Schema(description = "业务节点id")
    String serviceNodeId;

    @Schema(description = "业务节点名称")
    String serviceNodeName;

    @Schema(description = "业务节点位置")
    String serviceNodeLocation;

    @Schema(description = "业务节点地址")
    String serviceNodeUrl;

    @Schema(description = "申请时间：YYYY-MM-DD")
    String applyTime;

    @Schema(description = "状态：APPLY（待审批）APPROVED（已通过）REJECTED（已拒绝）")
    String status;

    @Builder.Default
    @Schema(description = "上架状态")
    PushStatus pushStatus = PushStatus.OFFLINE;
}
