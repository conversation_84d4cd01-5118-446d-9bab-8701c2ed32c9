package com.ailpha.ailand.dataroute.endpoint.third.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.mapstruct.CompanyMapper;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryRegisterRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneDetailResp;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.TeeContract;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRecord;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticDeliveryRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.HubDeliverySceneRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.HubOrderRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:45
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdServiceImpl implements ThirdService {

    private final DigitalCertificateRemote digitalCertificateRemote;

    private final HubOrderRemote hubOrderRemote;

    private final HubDeliverySceneRemote hubDeliverySceneRemote;

    private final ExecutorService ASYNC_TEE_JOB_RUNNER = Executors.newFixedThreadPool(20);

    /**
     * todo：优化成异步形式
     * todo：文件下载上报
     */
    @Override
    public CommonResult<Boolean> logReport(List<LogReportReq> logReportReqs) {
        ASYNC_TEE_JOB_RUNNER.submit(() -> report(logReportReqs));
        return CommonResult.SUCCESS(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> contractExecuteSuccessCallback(ContractExecuteCallbackRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("合约执行回调，请求参数：{}", request.getCallback());
        }

        // 交付ApiId关联表
        List<SceneApiIdRelateReq> deliveryApIIdRelList;
        QuerySceneByApiIdReq req = new QuerySceneByApiIdReq();
        req.setSceneIds(request.getCallback().stream().map(ContractExecuteCallbackRequest.CallbackParams::getSceneId).collect(Collectors.toList()));
        CommonResult<List<SceneApiIdRelateReq>> result = hubDeliverySceneRemote.querySceneByApiIdList(req);
        deliveryApIIdRelList = result.getData();

        // Map：交付ID -> 交付方式
        Map<String, DeliveryType> deliveryTypeMap = new HashMap<>(request.getCallback().size());
        for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApIIdRelList) {
            deliveryTypeMap.put(deliveryApiIdRel.getSceneId(), deliveryApiIdRel.getDeliveryType());
        }

        // Map：交付ID -> 交付ApiId关联列表
        Map<String, List<SceneApiIdRelateReq>> deliveryApiRelMap = new HashMap<>(request.getCallback().size());
        for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApIIdRelList) {
            List<SceneApiIdRelateReq> deliveryApiIdRels = deliveryApiRelMap.get(deliveryApiIdRel.getSceneId());
            deliveryApiIdRels = ObjectUtils.isEmpty(deliveryApiIdRels) ? new ArrayList<>() : deliveryApiIdRels;
            deliveryApiIdRels.add(deliveryApiIdRel);
            deliveryApiRelMap.put(deliveryApiIdRel.getSceneId(), deliveryApiIdRels);
        }

        // Map：交付ID -> 订单ID列表
        Map<String, List<String>> deliveryIdOrderIdMap = new HashMap<>(deliveryApiRelMap.size());
        for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApIIdRelList) {
            List<String> orderIds = deliveryIdOrderIdMap.get(deliveryApiIdRel.getSceneId());
            orderIds = ObjectUtils.isEmpty(orderIds) ? new ArrayList<>() : orderIds;
            orderIds.add(deliveryApiIdRel.getOrderId());
            deliveryIdOrderIdMap.put(deliveryApiIdRel.getSceneId(), orderIds);
        }

        // 订单列表
        List<OrderApprovalRecord> orderList;
        List<String> orderIdList = deliveryApIIdRelList.stream().map(SceneApiIdRelateReq::getOrderId).toList();
        SuccessResponse<List<OrderApprovalRecord>> response = hubOrderRemote.selectOrderApprovalRecordWhereIdIn(orderIdList);
        orderList = response.getData();

        // Map：订单ID -> 订单详情
        Map<String, OrderApprovalRecord> orderMap = new HashMap<>(orderList.size());
        for (OrderApprovalRecord order : orderList) {
            orderMap.put(order.getId(), order);
        }

        // 首页：埋点-交付记录
        List<StatisticDeliveryRecord> statisticDeliveryRecords = new ArrayList<>(orderList.size());
        for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            if (logReport.getSuccess()) {
                String deliveryId = logReport.getSceneId();
                List<String> orderIds = deliveryIdOrderIdMap.get(deliveryId);
                for (String orderId : orderIds) {
                    OrderApprovalRecord order = orderMap.get(orderId);
                    StatisticDeliveryRecordExtend extend = StatisticDeliveryRecordExtend.builder().chargingWay(order.getChargingWay()).meteringWay(order.getMeteringWay())
                            .orderConfig(!ObjectUtils.isEmpty(order.getExtend()) ? JacksonUtils.json2pojo(order.getExtend(), OderRecordExtend.class) : new OderRecordExtend()).build();
                    StatisticDeliveryRecord statisticDeliveryRecord = StatisticDeliveryRecord.builder().deliveryId(deliveryId)
                            .deliveryMode(deliveryTypeMap.get(deliveryId).name()).assetName(order.getAssetName()).assetCreatorId(order.getApproverId())
                            .assetCreatorRouterId(order.getApproverRouterId()).orderId(orderId).extend(JacksonUtils.obj2json(extend)).build();
                    statisticDeliveryRecords.add(statisticDeliveryRecord);
                }
            }
        }
        hubOrderRemote.insertStatisticDeliveryRecords(statisticDeliveryRecords);

        // 交付登记
        try {
            for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
                if (logReport.getSuccess()) {
                    String deliveryId = logReport.getSceneId();
                    List<SceneApiIdRelateReq> deliveryApiIdRels = deliveryApiRelMap.get(deliveryId);
                    for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApiIdRels) {
                        String buyerCompanyId = deliveryApiIdRel.getJsonExt().getBuyerCompanyId();
                        Optional<Company> optional = SpringUtil.getBean(CompanyRepository.class).findById(Long.valueOf(buyerCompanyId));
                        final Company company = optional.orElseThrow(() -> new RestfulApiException("未查询到本节点【" + buyerCompanyId + "】企业信息"));
                        // 异步交付
                        ASYNC_TEE_JOB_RUNNER.submit(() -> deliveryRegister(LogReportReq.builder().assetRouteId(company.getNodeId()).build(), deliveryApiIdRel, buyerCompanyId, true));
                    }
                }
            }
        } catch (Exception e) {
            log.error("交付登记异常：", e);
        }

        for (ContractExecuteCallbackRequest.CallbackParams callback : request.getCallback()) {
            syncSceneStatus(callback);
        }

        // 修改订单调用次数
        for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            String deliveryId = logReport.getSceneId();
            List<String> orderIds = deliveryIdOrderIdMap.get(deliveryId);
            for (String orderId : orderIds) {
                OrderApprovalRecord order = orderMap.get(orderId);
                long usedSuccess = 0L;
                long usedFail = 0L;
                if (logReport.getSuccess()) {
                    usedSuccess = 1;
                } else {
                    usedFail = 1;
                }
                log.info("订单【{}】资产【{}】总调用次数【{}】本次成功调用次数【{}】失败调用次数【{}】", orderId, order.getAssetId(), order.getAllowance(), usedSuccess, usedFail);
                order.setSuccessfulUsage(order.getSuccessfulUsage().add(BigInteger.valueOf(usedSuccess)));
                order.setUnsuccessfulUsage(order.getUnsuccessfulUsage().add(BigInteger.valueOf(usedFail)));
                changeOrderStatus(orderId, order, true);
            }
        }
        orderList = orderMap.values().stream().toList();
        // 更新订单调用次数
        log.info("使用次数累计更新接口req：{}", orderList);
        SuccessResponse<Integer> recordsResponse = hubOrderRemote.updateOrderApprovalRecords(orderList);
        log.info("使用次数累计更新接口返回：{}", recordsResponse);

        // 上报交付记录
        List<DeliveryRecordRequest.DeliveryRecord> deliveryRecords = new ArrayList<>(orderList.size());
        for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            if (logReport.getSuccess()) {
                String deliveryId = logReport.getSceneId();
                List<SceneApiIdRelateReq> deliveryApiIdRels = deliveryApiRelMap.get(deliveryId);
                for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApiIdRels) {
                    DeliveryRecordRequest.DeliveryRecord deliveryRecord = new DeliveryRecordRequest.DeliveryRecord();
                    deliveryRecord.setDataAssetId(deliveryApiIdRel.getDataAssetId());
                    deliveryRecord.setDeliveryId(deliveryId);
                    deliveryRecord.setRouterId(deliveryApiIdRel.getJsonExt().getRouteId());
                    deliveryRecords.add(deliveryRecord);
                }
            }
        }
        reportDeliveryRecord(deliveryRecords);

        return CommonResult.SUCCESS(true);
    }

    /**
     * 功能描述: 同步交付场景状态,目前仅判断TEE单次合约执行成功，同步该场景状态结束
     *
     * @param callback callback
     * @return: void
     * @author: yuwenping
     * @date: 2025/3/21 17:12
     */
    private void syncSceneStatus(ContractExecuteCallbackRequest.CallbackParams callback) {
        if (!callback.getSuccess()) {
            return;
        }
        String sceneId = callback.getSceneId();
        CommonResult<SceneDetailResp> sceneDetailRespCommonResult = hubDeliverySceneRemote.sceneDetail(sceneId);
        SceneDetailResp data = sceneDetailRespCommonResult.getData();
        if (!DeliveryType.TEE_OFFLINE.toString().equals(data.getDeliveryType())) {
            return;
        }
        TeeContract teeContract = JSONUtil.toBean(data.getContract(), TeeContract.class);
        if ("once".equals(teeContract.getExecuteType())) {
            log.debug("单次Tee交付场景执行成功，更新场景状态为已完成");
            DeliverySceneUpdateRequest request = new DeliverySceneUpdateRequest();
            request.setSceneId(sceneId);
            request.setStatus("TERMINATED");
            hubDeliverySceneRemote.updateScene(request);
        }
    }

    private synchronized void report(List<LogReportReq> logReportReqs) {
        log.info("本次接收日志上报信息:{}", JSONUtil.toJsonStr(logReportReqs));

        // 交付ApiId关联表
        List<SceneApiIdRelateReq> deliveryApIIdRelList;
        QuerySceneByApiIdReq req = new QuerySceneByApiIdReq();
        req.setApiIdList(logReportReqs.stream().distinct().map(LogReportReq::getApiId).toList());
        CommonResult<List<SceneApiIdRelateReq>> result = hubDeliverySceneRemote.querySceneByApiIdList(req);
        deliveryApIIdRelList = result.getData();

        // Map：ApiId -> 交付ID
        Map<String, String> deliveryIdMap = new HashMap<>(deliveryApIIdRelList.size());
        for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApIIdRelList) {
            deliveryIdMap.put(deliveryApiIdRel.getApiId(), deliveryApiIdRel.getSceneId());
        }

        // Map：ApiId -> 交付方式
        Map<String, DeliveryType> deliveryTypeMap = new HashMap<>(deliveryApIIdRelList.size());
        for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApIIdRelList) {
            deliveryTypeMap.put(deliveryApiIdRel.getApiId(), deliveryApiIdRel.getDeliveryType());
        }

        // Map：ApiId -> 交付ApiId关联列表
        Map<String, List<SceneApiIdRelateReq>> apiIddeliveryRelMap = new HashMap<>(deliveryApIIdRelList.size());
        for (SceneApiIdRelateReq apiIdDeliveryRel : deliveryApIIdRelList) {
            List<SceneApiIdRelateReq> apiIdDeliveryRels = apiIddeliveryRelMap.get(apiIdDeliveryRel.getApiId());
            apiIdDeliveryRels = ObjectUtils.isEmpty(apiIdDeliveryRels) ? new ArrayList<>() : apiIdDeliveryRels;
            apiIdDeliveryRels.add(apiIdDeliveryRel);
            apiIddeliveryRelMap.put(apiIdDeliveryRel.getApiId(), apiIdDeliveryRels);
        }

        // Map：ApiId -> 订单ID列表
        Map<String, List<String>> apiIdOrderIdMap = new HashMap<>(apiIddeliveryRelMap.size());
        for (SceneApiIdRelateReq apiIdDeliveryRel : deliveryApIIdRelList) {
            List<String> orderIds = apiIdOrderIdMap.get(apiIdDeliveryRel.getApiId());
            orderIds = ObjectUtils.isEmpty(orderIds) ? new ArrayList<>() : orderIds;
            orderIds.add(apiIdDeliveryRel.getOrderId());
            apiIdOrderIdMap.put(apiIdDeliveryRel.getApiId(), orderIds);
        }

        // 订单列表
        List<OrderApprovalRecord> orderList;
        List<String> orderIdList = deliveryApIIdRelList.stream().map(SceneApiIdRelateReq::getOrderId).toList();
        SuccessResponse<List<OrderApprovalRecord>> response = hubOrderRemote.selectOrderApprovalRecordWhereIdIn(orderIdList);
        orderList = response.getData();

        // Map：订单ID -> 订单详情
        Map<String, OrderApprovalRecord> orderMap = new HashMap<>(orderList.size());
        for (OrderApprovalRecord order : orderList) {
            orderMap.put(order.getId(), order);
        }

        // 首页：埋点-交付记录
        List<StatisticDeliveryRecord> statisticDeliveryRecords = new ArrayList<>(orderList.size());
        for (LogReportReq logReport : logReportReqs) {
            if (1 == logReport.getStatus()) {
                String apiId = logReport.getApiId();
                List<String> orderIds = apiIdOrderIdMap.get(apiId);
                for (String orderId : orderIds) {
                    OrderApprovalRecord order = orderMap.get(orderId);
                    StatisticDeliveryRecordExtend extend = StatisticDeliveryRecordExtend.builder().chargingWay(order.getChargingWay()).meteringWay(order.getMeteringWay())
                            .orderConfig(!ObjectUtils.isEmpty(order.getExtend()) ? JacksonUtils.json2pojo(order.getExtend(), OderRecordExtend.class) : new OderRecordExtend()).build();
                    StatisticDeliveryRecord statisticDeliveryRecord = StatisticDeliveryRecord.builder().deliveryId(deliveryIdMap.get(apiId))
                            .deliveryMode(deliveryTypeMap.get(apiId).name()).assetName(order.getAssetName()).assetCreatorId(order.getApproverId())
                            .assetCreatorRouterId(order.getApproverRouterId()).orderId(orderId).extend(JacksonUtils.obj2json(extend)).build();
                    statisticDeliveryRecords.add(statisticDeliveryRecord);
                }
            }
        }
        try {
            hubOrderRemote.insertStatisticDeliveryRecords(statisticDeliveryRecords);
        } catch (Exception e) {
            log.error("埋点信息异常：", e);
        }

        // 交付登记
        for (LogReportReq logReport : logReportReqs) {
            if (1 == logReport.getStatus()) {
                String apiId = logReport.getApiId();
                List<SceneApiIdRelateReq> apiIdDeliveryRels = apiIddeliveryRelMap.get(apiId);
                for (SceneApiIdRelateReq apiIdDeliveryRel : apiIdDeliveryRels) {
                    String sellerCompanyId = apiIdDeliveryRel.getJsonExt().getSellerCompanyId();
                    deliveryRegister(logReport, apiIdDeliveryRel, sellerCompanyId, false);
                }
            }
        }

        // 修改订单调用次数
        for (LogReportReq logReport : logReportReqs) {
            String apiId = logReport.getApiId();
            List<String> orderIds = apiIdOrderIdMap.get(apiId);
            for (String orderId : orderIds) {
                OrderApprovalRecord order = orderMap.get(orderId);
                long usedSuccess = 0L;
                long usedFail = 0L;
                if (1 == logReport.getStatus()) {
                    usedSuccess = 1;
                } else {
                    usedFail = 1;
                }
                log.info("订单【{}】资产【{}】总调用次数【{}】本次成功调用次数【{}】失败调用次数【{}】", orderId, order.getAssetId(), order.getAllowance(), usedSuccess, usedFail);
                order.setSuccessfulUsage(order.getSuccessfulUsage().add(BigInteger.valueOf(usedSuccess)));
                order.setUnsuccessfulUsage(order.getUnsuccessfulUsage().add(BigInteger.valueOf(usedFail)));
                changeOrderStatus(orderId, order, false);
            }
        }
        orderList = orderMap.values().stream().toList();
        // 更新订单调用次数
        log.info("使用次数累计更新接口req：{}", orderList);
        SuccessResponse<Integer> recordsResponse = hubOrderRemote.updateOrderApprovalRecords(orderList);
        log.info("使用次数累计更新接口返回：{}", recordsResponse);

        // 上报交付记录
        try {
            List<DeliveryRecordRequest.DeliveryRecord> deliveryRecords = new ArrayList<>(orderList.size());
            for (LogReportReq logReport : logReportReqs) {
                if (1 == logReport.getStatus()) {
                    String apiId = logReport.getApiId();
                    List<SceneApiIdRelateReq> deliveryApiIdRelList = apiIddeliveryRelMap.get(apiId);
                    for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApiIdRelList) {
                        DeliveryRecordRequest.DeliveryRecord deliveryRecord = new DeliveryRecordRequest.DeliveryRecord();
                        deliveryRecord.setDataAssetId(deliveryApiIdRel.getDataAssetId());
                        deliveryRecord.setDeliveryId(deliveryApiIdRel.getSceneId());
                        deliveryRecords.add(deliveryRecord);
                    }
                }
            }
            reportDeliveryRecord(deliveryRecords);
        } catch (Exception e) {
            log.error("上报交付记录异常：", e);
        }
    }

    private void reportDeliveryRecord(List<DeliveryRecordRequest.DeliveryRecord> deliveryRecords) {
        DeliveryRecordRequest deliveryRecordRequest = new DeliveryRecordRequest();
        deliveryRecordRequest.setDeliveryRecords(deliveryRecords);
        CommonResult<Boolean> reportDeliveryRecord = hubDeliverySceneRemote.reportDeliveryRecord(deliveryRecordRequest);
        if (!reportDeliveryRecord.isSuccess())
            log.error("上报交付记录异常：" + reportDeliveryRecord.getMsg());
    }


    private final BaseCapabilityManager baseCapabilityManager;
    private final DataProductService dataProductService;

    private void deliveryRegister(LogReportReq logReportReq, SceneApiIdRelateReq relateReq, String localCompanyId, boolean isMpcTeeCallback) {
        MDC.put(SystemConstants.COMPANY_ID, String.valueOf(localCompanyId));

        if (!baseCapabilityManager.platformEnable(BaseCapabilityType.DATA_INVOICE)) {
            log.info("当前企业【{}】没有开启数字证书存证", localCompanyId);
            return;
        }

        // 查看配置的数字证书插件列表 —— 开启情况下 —— 进行推送
        DeliveryRegisterRequest request = new DeliveryRegisterRequest();
        try {
            MDC.put("currentNodeId", logReportReq.getAssetRouteId());
            try {
                Tuple2<Boolean, Company> companyTuple2 = SpringUtil.getBean(CompanyService.class).localCompany(logReportReq.getAssetRouteId());
                CompanyDTO companyDTO = SpringUtil.getBean(CompanyMapper.class).toDTO(companyTuple2.second);
                MDC.put("hubInfo", JSONUtil.toJsonStr(companyDTO.getServiceNode().getHubInfo()));
            } catch (Exception e) {
                log.error("查询本地企业信息异常：", e);
            }
            // 查资产详情
            DataProductVO dataProduct = dataProductService.getDataProductByOldAssetId(relateReq.getDataAssetId());

            String thirdBusinessId = StrUtil.join(":", relateReq.getSceneId(), relateReq.getDataAssetId());
            request.setThirdTransactionId(thirdBusinessId);
            request.setDeliveryType(DeliveryType.getDeliveryModeByDeliveryRegister(relateReq.getDeliveryType()));

            switch (relateReq.getDeliveryType()) {
                case API -> request.setDataHash(logReportReq.getDataHash());
                case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION,
                     TEE_OFFLINE, TEE_ONLINE, FILE_DOWNLOAD -> {
                    String hash = dataProduct.getFileSourceMetadata().getDataAssetFileHash();
                    request.setDataHash(hash);
                }
                default -> request.setDataHash("");
            }
            request.setThirdBusinessId(thirdBusinessId);
            if (!isMpcTeeCallback) {
                MDC.put(SystemConstants.COMPANY_ID, String.valueOf(dataProduct.getProvider().getCompany().getId()));
            }
            CommonResult<String> register = digitalCertificateRemote.deliveryRegister(request);
            log.info("交付登记返回：{}", JSONUtil.toJsonStr(register));
        } catch (Exception e) {
            log.error("交付登记异常：{}", JSONUtil.toJsonStr(request), e);
        } finally {
            MDC.remove("currentNodeId");
        }

    }


    private final OrderManagerService orderManagerService;

    private void changeOrderStatus(String orderId, OrderApprovalRecord record, boolean isContractCallback) {
        if (StringUtils.equals(record.getStatus(), "COMPLETED") || StringUtils.equals(record.getStatus(), "TERMINATED")) {
            log.info("当前订单【{}】状态【{}】已完成", record.getId(), record.getStatus());
        } else if (StringUtils.equals(record.getStatus(), "APPROVED")) {
            if (StringUtils.equals(record.getMeteringWay(), "按次") && record.getAllowance().compareTo(record.getSuccessfulUsage()) <= 0) {
                // 变更状态 允许调用次数 小于 成功调用次数
                record.setStatus("COMPLETED");
                record.setChangeStatus(true);
                log.info("当前订单【{}】资产【{}】累计达到调用次数", record.getId(), record.getAssetId());

                if (StringUtils.contains(record.getDeliveryMode(), DeliveryMode.MPC.name())
                        || StringUtils.contains(record.getDeliveryMode(), DeliveryMode.TEE_OFFLINE.name())
                        || StringUtils.contains(record.getDeliveryMode(), DeliveryMode.TEE_ONLINE.name())) {

                    try {
                        OderRecordExtend oderConfig = JacksonUtils.json2pojo(record.getExtend(), OderRecordExtend.class);
                        String companyId;
                        if (isContractCallback) {
                            companyId = oderConfig.getBeneficiaryCompanyId();
                        } else {
                            companyId = oderConfig.getApproverCompanyId();
                        }
                        MDC.put(SystemConstants.COMPANY_ID, companyId);
                        orderManagerService.terminalContract(Collections.singletonList(orderId), null);
                    } catch (Exception e) {
                        log.error("订单终止合约异常：", e);
                    }
                }

            }
        }
    }
}
