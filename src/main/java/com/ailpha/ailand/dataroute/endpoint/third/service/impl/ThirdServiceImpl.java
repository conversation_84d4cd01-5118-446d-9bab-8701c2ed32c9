package com.ailpha.ailand.dataroute.endpoint.third.service.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.*;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.IdentityProviderItem;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.IdentityProviderListResponse;
import com.ailpha.ailand.dataroute.endpoint.dataInvoiceStorage.request.DeliveryRegisterRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.DeliveryScene;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.SceneAsset;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.DeliverySceneRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.SceneAssetRepository;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.SceneAssetResp;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.TeeContract;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.DigitalCertificateRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.ContractExecuteCallbackRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.LogReportReq;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneApiIdRelateReq;
import com.ailpha.ailand.dataroute.endpoint.third.service.ThirdService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.openapi.AppToken;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:45
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThirdServiceImpl implements ThirdService {

    private final DigitalCertificateRemote digitalCertificateRemote;

    private final DeliverySceneRepository deliverySceneRepository;

    private final DeliveryService deliveryService;

    private final OrderManagerService orderManagerService;

    private final SceneAssetRepository sceneAssetRepository;

    private final UserService userService;

    @Override
    public CommonResult<Boolean> contractExecuteSuccessCallback(ContractExecuteCallbackRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("合约执行回调，请求参数：{}", request.getCallback());
        }

        // 执行成功的存证
        request.getCallback().stream().filter(ContractExecuteCallbackRequest.CallbackParams::getSuccess).forEach(callback -> AsyncManager.getInstance().execute(() -> {
            String userId = callback.getUserId();
            User user = userService.findByUserId(userId);
            if (user == null) {
                log.error("当前合约执行回调获取用户信息异常: {}", userId);
                return;
            }

            Long companyId = user.getCompanyId();
            TenantContext.setCurrentTenant("tenant_" + companyId);

            String sceneId = callback.getSceneId();
            DeliveryScene deliveryScene = deliverySceneRepository.getReferenceById(sceneId);
            List<SceneAsset> sceneAssetList = sceneAssetRepository.findAllByDeliverySceneIdIn(Collections.singletonList(sceneId));

            sceneAssetList.forEach(sceneAsset -> {
                SceneAssetResp sceneAssetResp = deliveryService.sceneAssetResp(sceneId, sceneAsset.getDataAssetId());
                orderManagerService.buyerOrderDeliverySuccess(sceneAssetResp, "", deliveryScene.getDeliveryType());
            });

            // n、TEE 单次终止场景判断
            syncSceneStatus(callback);
        }));


        // 首页：埋点-交付记录
/*        List<StatisticDeliveryRecord> statisticDeliveryRecords = new ArrayList<>(orderList.size());
        for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            if (logReport.getSuccess()) {
                String deliveryId = logReport.getSceneId();
                List<String> orderIds = deliveryIdOrderIdMap.get(deliveryId);
                for (String orderId : orderIds) {
                    OrderApprovalRecord order = orderMap.get(orderId);
                    StatisticDeliveryRecordExtend extend = StatisticDeliveryRecordExtend.builder().chargingWay(order.getChargingWay()).meteringWay(order.getMeteringWay()).allowance(order.getAllowance()).expireDate(order.getExpireDate()).approveTime(order.getApproveTime())
                            .orderConfig(!ObjectUtils.isEmpty(order.getExtend()) ? order.getExtend() : new OderRecordExtend()).build();
                    StatisticDeliveryRecord statisticDeliveryRecord = StatisticDeliveryRecord.builder().assetId(order.getAssetId()).deliveryId(deliveryId)
                            .deliveryMode(deliveryTypeMap.get(deliveryId).name()).assetName(order.getAssetName()).beneficiaryEnterpriseName(order.getBeneficiaryEnterpriseName())
                            .assetCreatorId(order.getApproverId()).assetCreatorRouterId(order.getApproverRouterId()).orderId(orderId).extend(JacksonUtils.obj2json(extend)).build();
                    statisticDeliveryRecords.add(statisticDeliveryRecord);
                }
            }
        }
        hubOrderRemote.insertStatisticDeliveryRecords(statisticDeliveryRecords);*/

        // 交付登记
/*        try {
            for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
                if (logReport.getSuccess()) {
                    String deliveryId = logReport.getSceneId();
                    List<SceneApiIdRelateReq> deliveryApiIdRels = deliveryApiRelMap.get(deliveryId);
                    for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApiIdRels) {
                        String buyerCompanyId = deliveryApiIdRel.getJsonExt().getBuyerCompanyId();
                        Optional<Company> optional = SpringUtil.getBean(CompanyRepository.class).findById(Long.valueOf(buyerCompanyId));
                        final Company company = optional.orElseThrow(() -> new RestfulApiException("未查询到本节点【" + buyerCompanyId + "】企业信息"));
                        // 异步交付
                        ASYNC_TEE_JOB_RUNNER.submit(() -> deliveryRegister(LogReportReq.builder().assetRouteId(company.getNodeId()).build(), deliveryApiIdRel, buyerCompanyId));
                    }
                }
            }
        } catch (Exception e) {
            log.error("交付登记异常：", e);
        }*/

/*        for (ContractExecuteCallbackRequest.CallbackParams callback : request.getCallback()) {
            syncSceneStatus(callback);
        }*/

        // 修改订单调用次数
        /*for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            String deliveryId = logReport.getSceneId();
            List<String> orderIds = deliveryIdOrderIdMap.get(deliveryId);
            for (String orderId : orderIds) {
                OrderApprovalRecord order = orderMap.get(orderId);
                long usedSuccess = 0L;
                long usedFail = 0L;
                if (logReport.getSuccess()) {
                    usedSuccess = 1;
                } else {
                    usedFail = 1;
                }
                log.info("订单【{}】资产【{}】总调用次数【{}】本次成功调用次数【{}】失败调用次数【{}】", orderId, order.getAssetId(), order.getAllowance(), usedSuccess, usedFail);
                order.setSuccessfulUsage(order.getSuccessfulUsage().add(BigInteger.valueOf(usedSuccess)));
                order.setUnsuccessfulUsage(order.getUnsuccessfulUsage().add(BigInteger.valueOf(usedFail)));
                changeOrderStatus(orderId, order, true);
            }
        }
        orderList = orderMap.values().stream().toList();
        // 更新订单调用次数
        log.info("使用次数累计更新接口req：{}", orderList);
        SuccessResponse<Integer> recordsResponse = hubOrderRemote.updateOrderApprovalRecords(orderList);
        log.info("使用次数累计更新接口返回：{}", recordsResponse);*/

        // 上报交付记录
/*        List<DeliveryRecordRequest.DeliveryRecord> deliveryRecords = new ArrayList<>(orderList.size());
        for (ContractExecuteCallbackRequest.CallbackParams logReport : request.getCallback()) {
            if (logReport.getSuccess()) {
                String deliveryId = logReport.getSceneId();
                List<SceneApiIdRelateReq> deliveryApiIdRels = deliveryApiRelMap.get(deliveryId);
                for (SceneApiIdRelateReq deliveryApiIdRel : deliveryApiIdRels) {
                    DeliveryRecordRequest.DeliveryRecord deliveryRecord = new DeliveryRecordRequest.DeliveryRecord();
                    deliveryRecord.setDataAssetId(deliveryApiIdRel.getDataAssetId());
                    deliveryRecord.setDeliveryId(deliveryId);
                    deliveryRecord.setRouterId(deliveryApiIdRel.getJsonExt().getRouteId());
                    deliveryRecords.add(deliveryRecord);
                }
            }
        }
        reportDeliveryRecord(deliveryRecords);*/

        return CommonResult.SUCCESS(true);
    }

    /**
     * 功能描述: 同步交付场景状态,目前仅判断TEE单次合约执行成功，同步该场景状态结束
     *
     * @param callback callback
     * @return: void
     * @author: yuwenping
     * @date: 2025/3/21 17:12
     */
    private void syncSceneStatus(ContractExecuteCallbackRequest.CallbackParams callback) {
        if (!callback.getSuccess()) {
            return;
        }
        String sceneId = callback.getSceneId();
        DeliveryScene data = deliverySceneRepository.getReferenceById(sceneId);
        if (!isTeeExecuteContract(data.getDeliveryType())) {
            return;
        }
        TeeContract teeContract = JSONUtil.toBean(data.getExt().getContract(), TeeContract.class);
        if ("once".equals(teeContract.getExecuteType())) {
            log.debug("单次Tee交付场景执行成功，更新场景状态为已完成");
            data.setSceneStatus("TERMINATED");
            deliverySceneRepository.save(data);
        }
    }


    private final BaseCapabilityManager baseCapabilityManager;
    private final DataProductService dataProductService;

    public void deliveryRegister(LogReportReq logReportReq, SceneApiIdRelateReq relateReq, String localCompanyId) {
        if (!baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.DATA_INVOICE)) {
            log.info("当前企业【{}】没有开启数字证书存证", localCompanyId);
            return;
        }

        // 查看配置的数字证书插件列表 —— 开启情况下 —— 进行推送
        DeliveryRegisterRequest request = new DeliveryRegisterRequest();
        try {
            String thirdBusinessId = StrUtil.join(":", relateReq.getSceneId(), relateReq.getDataAssetId());
            request.setThirdTransactionId(thirdBusinessId);
            request.setDeliveryType(DeliveryType.getDeliveryModeByDeliveryRegister(relateReq.getDeliveryType()));

            switch (relateReq.getDeliveryType()) {
                case API -> request.setDataHash(logReportReq.getDataHash());
                case MPC_CIPHER_TEXT_COMPUTE, MPC_PRIVATE_INFORMATION_RETRIEVAL, MPC_PRIVATE_SET_INTERSECTION,
                     TEE_OFFLINE, TEE_ONLINE, FILE_DOWNLOAD -> {
                    // String hash = dataProduct.getFileSourceMetadata().getDataAssetFileHash();
                    // todo hash 获取
                    String hash = logReportReq.getDataHash();
                    request.setDataHash(hash);
                }
                default -> request.setDataHash("");
            }
            request.setThirdBusinessId(thirdBusinessId);
            CommonResult<String> register = digitalCertificateRemote.deliveryRegister(localCompanyId, request);
            log.info("交付登记返回：{}", JSONUtil.toJsonStr(register));
        } catch (Exception e) {
            log.error("交付登记异常：{}", JSONUtil.toJsonStr(request), e);
        }
    }


    private void changeOrderStatus(String orderId, OrderApprovalRecord record, boolean isContractCallback) {
        if (StringUtils.equals(record.getStatus(), "COMPLETED") || StringUtils.equals(record.getStatus(), "TERMINATED")) {
            log.info("当前订单【{}】状态【{}】已完成", record.getId(), record.getStatus());
        } else if (StringUtils.equals(record.getStatus(), "APPROVED")) {
            if (StringUtils.equals(record.getMeteringWay(), "按次") && record.getAllowance().compareTo(record.getSuccessfulUsage()) <= 0) {
                // 变更状态 允许调用次数 小于 成功调用次数
                record.setStatus("COMPLETED");
                record.setChangeStatus(true);
                log.info("当前订单【{}】资产【{}】累计达到调用次数", record.getId(), record.getAssetId());

                if (StringUtils.contains(record.getDeliveryMode(), DeliveryMode.MPC.name())
                        || StringUtils.contains(record.getDeliveryMode(), DeliveryMode.TEE_OFFLINE.name())
                        || StringUtils.contains(record.getDeliveryMode(), DeliveryMode.TEE_ONLINE.name())) {

                    try {
                        OderRecordExtend oderConfig = record.getExtend();
                        String localCompanyId;
                        if (isContractCallback) {
                            localCompanyId = oderConfig.getBeneficiaryCompanyId();
                        } else {
                            localCompanyId = oderConfig.getApproverCompanyId();
                        }
                        orderManagerService.terminalContract(localCompanyId, Collections.singletonList(orderId), null);
                    } catch (Exception e) {
                        log.error("订单终止合约异常：", e);
                    }
                }

            }
        }
    }

    private static Boolean isTeeExecuteContract(DeliveryType deliveryType) {
        return DeliveryType.TEE_OFFLINE == deliveryType ||
                DeliveryType.TEE_MODEL_OPTIMIZE == deliveryType ||
                DeliveryType.TEE_MODEL_PREDICT == deliveryType;
    }

    private final RouterManagerRemoteService routerManagerRemoteService;
    private final LicenseRemoteService licenseRemoteService;
    private final AiLandProperties aiLandProperties;
    private final cn.hutool.cache.Cache<String, String> nonceCache = new LRUCache<>(32);

    @Override
    public GetNonceResponse generateUuidWithCertificate(String certificate) {
        HttpServletRequest request = ServletUtils.getRequest();
        String xRequestId = request.getHeader("xRequestId");
        // 校验证书是否存在
        if (StringUtils.isBlank(certificate)) {
            throw new RestfulApiException("证书不能为空");
        }

        // 可选：校验证书有效性逻辑（如调用第三方验证服务）
        checkLocalRootCertificate();
        // 读取本地根证书 todo：zhou tao
        CommonResult<Void> verifyLicense = licenseRemoteService.verifyLicense(VerifyPlatformCertRequest.builder().requestId(xRequestId).certificate(certificate).build());
        Assert.isTrue(verifyLicense.isSuccess(), "证书验证失败：" + verifyLicense.getMsg());

        // 生成 UUID
        String uuid = IdUtil.fastSimpleUUID();
        log.info("Generated UUID: {} with certificate: {}", uuid, certificate);
//        CommonResult<String> generateSign = licenseRemoteService.generateSign(uuid);
//        Assert.isTrue(generateSign.isSuccess(), "签名生成失败：" + generateSign.getMsg());
        // nonce 本地缓存 默认保留三十分钟
        nonceCache.put(xRequestId, uuid, 30 * 60 * 1000);
        return new GetNonceResponse(uuid, 30 * 60 * 1000L);
    }

    // 检查本地根证书是否存在，如果不存在则调用routerManagerRemoteService.getIdentityProviderList
    private void checkLocalRootCertificate() {
        // 本地根证书路径
        String localRootCertificatePath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "root.crt";
        if (!FileUtil.exist(localRootCertificatePath)) {
            // 获取证书列表
            GetIdentityProviderListRequest request = new GetIdentityProviderListRequest();
            request.setPage(1);
            request.setSize(100);
            com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<IdentityProviderListResponse> identityProviderListResponseCommonResult
                    = routerManagerRemoteService.getIdentityProviderList(request);
            Assert.isTrue(identityProviderListResponseCommonResult.isSuccess(), "获取证书列表失败：" + identityProviderListResponseCommonResult.getMessage());
            // 获取第一个证书
            List<IdentityProviderItem> identityProviderItems = identityProviderListResponseCommonResult.getData().getList();
            // 下载证书
            FileUtil.writeUtf8String(JSONUtil.toJsonStr(identityProviderItems), localRootCertificatePath);

            List<String> list = new ArrayList<>();
            List<String> verifyList = new java.util.ArrayList<>(identityProviderItems.stream().map(IdentityProviderItem::getVerify).map(IdentityProviderItem.GetIdentityProviderLisVerifyBO::getVerify).toList());
            List<String> VerifyRegionsList = identityProviderItems.stream().map(IdentityProviderItem::getVerify).map(IdentityProviderItem.GetIdentityProviderLisVerifyBO::getVerifyRegions).toList();
            list.addAll(VerifyRegionsList);
            list.addAll(verifyList);

            CommonResult<Void> saveRootCert = licenseRemoteService.saveRootCert(SaveRootCertRequest.builder().certificates(list).build());
            Assert.isTrue(saveRootCert.isSuccess(), "保存证书失败：" + saveRootCert.getMsg());
            log.info("保存证书成功：{}", localRootCertificatePath);
        }
    }

    private final Cache<String, String> tokenCache = CacheUtil.newLRUCache(32);

    @Override
    public TokenResponse generateTokenWithNonce(String nonce) {
        HttpServletRequest request = ServletUtils.getRequest();
        String requestId = request.getHeader("xRequestId");
        String nodeId = request.getHeader("nodeId");
        // 检查随机数是否有效
        Assert.isTrue(nonceCache.containsKey(requestId), "随机数无效");
        // 解密随机数
        CommonResult<String> decryptNonce = licenseRemoteService.decryptNonce(DecryptNonceRequest.builder()
                .message(nonceCache.get(requestId)).nodeId(nodeId).requestId(requestId).sign(nonce).build());
        Assert.isTrue(decryptNonce.isSuccess(), "验证nonce签名值失败：" + decryptNonce.getMsg());
        String decryptedNonce = decryptNonce.getData();
        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put("nonce", decryptedNonce);
        long expireTime = System.currentTimeMillis() + 30 * 60 * 1000L;
        tokenMap.put("timestamp", expireTime);
        String token = JWTUtil.createToken(tokenMap, "2wsxVFR_".getBytes(StandardCharsets.UTF_8));
        log.info("Generated token: {} with nonce: {}", token, decryptedNonce);
        tokenCache.put(token, token, 30 * 60 * 1000);
        return new TokenResponse(token, "jwt token", "", expireTime);
    }

    // 检查token是否有效
    @Override
    public Boolean checkToken(String token) {
        boolean containsKey = tokenCache.containsKey(token);
        if (containsKey) {
            JWT jwt = JWTUtil.parseToken(tokenCache.get(token));
            long expireTime = ((Number) jwt.getPayload("timestamp")).longValue();
            return System.currentTimeMillis() < expireTime;
        }
        return false;
    }

    private final EndpointRemote endpointRemote;
    private final ServiceNodeRemoteService serviceNodeRemoteService;

    @Override
    public AppToken generateToken(BaseCapabilityType type, String certificate, NodeDTO.HubInfo hubInfo, String metaData) {
        GenerateUuidRequest request = new GenerateUuidRequest();
        request.setVerify(certificate);
        String requestId = UuidUtils.uuid32();
        request.setRequestId(requestId);
        request.setHubInfo(hubInfo);
        com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<GetNonceResponse> generateNonce;
        switch (type) {
            case END_POINT -> generateNonce = endpointRemote.generateNonce(request, metaData);
            case SHU_HAN -> generateNonce = routerManagerRemoteService.getNonce(request, metaData);
            case SERVICE_NODE -> // 需要指定业务节点
                    generateNonce = serviceNodeRemoteService.getNonce(request, metaData);
            default -> {
                log.error("不支持的连接器类型：{}", type);
                throw new IllegalArgumentException("不支持的连接器类型：" + type);
            }
        }
        Assert.isTrue(generateNonce.getData() != null, "生成随机数失败：" + generateNonce);
        CommonResult<SignResponse> encryptNonce = licenseRemoteService.encryptNonce(EncryptNonceRequest.builder().message(generateNonce.getData().getNonce()).keyId(hubInfo.getKeyId()).build());
        Assert.isTrue(encryptNonce.isSuccess(), "随机数加密失败：" + encryptNonce.getMsg());
        com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult<TokenResponse> generateToken;
        GenerateTokenRequest generateTokenRequest = new GenerateTokenRequest();
        generateTokenRequest.setSignature(encryptNonce.getData().getSign());
        generateTokenRequest.setRequestId(requestId);
        generateTokenRequest.setHubInfo(hubInfo);
        switch (type) {
            case END_POINT -> generateToken = endpointRemote.generateToken(generateTokenRequest, metaData);
            case SERVICE_NODE -> generateToken = serviceNodeRemoteService.getToken(generateTokenRequest, metaData);
            case SHU_HAN -> generateToken = routerManagerRemoteService.getToken(generateTokenRequest, metaData);
            default -> {
                log.error("不支持的连接器类型：{}", type);
                throw new IllegalArgumentException("不支持的连接器类型：" + type);
            }
        }
        Assert.isTrue(generateToken.getData() != null, "[" + type.name() + "] 生成Token失败：" + generateToken);
        return new AppToken(generateToken.getData().getTokenValue(), generateToken.getData().getTokenTimeout());
    }


}
