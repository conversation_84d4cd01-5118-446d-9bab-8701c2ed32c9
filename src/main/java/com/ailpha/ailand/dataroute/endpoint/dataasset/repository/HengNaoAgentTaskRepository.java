package com.ailpha.ailand.dataroute.endpoint.dataasset.repository;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AgentTaskType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.HengNaoAgentTask;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface HengNaoAgentTaskRepository extends JpaRepository<HengNaoAgentTask, Long> {
    List<HengNaoAgentTask> findByStatusAndTaskTypeIn(Integer status, List<AgentTaskType> taskType);
}
