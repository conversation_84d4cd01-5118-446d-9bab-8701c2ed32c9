package com.ailpha.ailand.dataroute.endpoint.demand.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "编辑需求请求")
public class EditDemandRequest {
    Integer demandId;
    @Schema(hidden = true)
    String userId;
    @Schema(description = "标题")
    String title;

    /**
     * 描述
     */
    @Schema(description = "描述")
    String description;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期")
    String expireDate;

    /**
     * 数据类型
     */
    @Schema(description = "数据类型")
    String dataType;

    @Schema(description = "标签")
    List<String> tags;

    /**
     * 数据规模
     */
    @Schema(description = "数据规模")
    String dataScale;

    /**
     * 质量要求
     */
    @Schema(description = "质量要求")
    String qualityRequirements;

    /**
     * 预算范围
     */
    @Schema(description = "预算范围")
    String budgetRange;

    /**
     * 预期交付方式
     */
    @Schema(description = "预期交付方式")
    List<String> expectedDeliveryMethod;
}
