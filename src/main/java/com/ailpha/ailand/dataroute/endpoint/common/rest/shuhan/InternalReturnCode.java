package com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan;


import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.IReturnCode;
import lombok.Getter;

public enum InternalReturnCode implements IReturnCode {

    SUCCESS(200, "成功"),

    FAIL(400, "失败"),

    UNAUTHORIZED(401, "未授权"),
    ;

    @Getter
    private final Integer code;

    private final String message;

    InternalReturnCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getValue() {
        return message;
    }

}
