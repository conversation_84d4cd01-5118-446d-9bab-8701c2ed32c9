package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2022/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemConfigureInfo implements Serializable {

//    @ApiModelProperty(value = "登录安全配置")
//    private LoginSettingDTO loginSecurityInfo;
//
    @ApiModelProperty(value = "其他配置")
    private UserSettingFormDTO otherInfo;

    @ApiModelProperty(value = "部署配置")
    private DeployInfo deployInfo;

    @ApiModelProperty(value = "资源配置")
    private ResourceConfig resourceConfig;

}
