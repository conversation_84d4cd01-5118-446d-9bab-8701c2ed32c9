package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.SSEMessageReadStatus;
import com.dbapp.rest.request.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: sunsas.yu
 * @date: 2024/11/17 16:39
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SSEMessageListRequest extends Page {

    private SSEMessageReadStatus status;

    private Long id;

    private String dataId;
}
