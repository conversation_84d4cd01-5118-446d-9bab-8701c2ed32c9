package com.ailpha.ailand.dataroute.endpoint.home;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/26 19:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ContractOrderDealTop10 {

    @Schema(description = "订单编号")
    String orderId;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "使用次数")
    BigInteger used;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description = "创建时间")
    Date createTime;
}
