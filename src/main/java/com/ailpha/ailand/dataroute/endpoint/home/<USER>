package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDealStatisticDTO {
    /**
     SELECT
     count(id)
     FROM
     dr_order_approval_record
     WHERE
     beneficiary_id = '148c3ddacd2540649d74ac4415f2e3a2'
     AND status IN ( 'APPROVED', 'TERMINATED' );
     */
    @Schema(description = "累计成交次数")
    Long dealCount;

    /**
     SELECT
     *
     FROM
     dr_order_approval_record
     WHERE
     beneficiary_id = '148c3ddacd2540649d74ac4415f2e3a2'
     AND status IN ( 'APPROVED', 'TERMINATED' ) ORDER BY create_time desc limit 10;
     */
    @Schema(description = "最新成交的10个订单")
    List<OrderDealTop10> dealTop10s;

}
