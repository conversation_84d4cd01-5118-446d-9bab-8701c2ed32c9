package com.ailpha.ailand.dataroute.endpoint.home;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * <AUTHOR>
 * 2025/2/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticDeliveryRecordVO {

    @Schema(description = "ID")
    Long id;

    @Schema(description = "资产ID")
    String assetId;

    @Schema(description = "交付ID")
    String deliveryId;

    @Schema(description = "交付方式")
    String deliveryMode;

    @Schema(description = "数据资产名称")
    String assetName;

    @Schema(description = "获益人方企业名称")
    String beneficiaryEnterpriseName;

    @Schema(description = "资产创建人用户ID")
    String assetCreatorId;

    @Schema(description = "资产创建人连接器ID")
    String assetCreatorRouterId;

    @Schema(description = "订单ID")
    String orderId;

    @Schema(description = "扩展字段")
    StatisticDeliveryRecordExtend extend;

    @Schema(description = "使用时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    Date updateTime;
}
