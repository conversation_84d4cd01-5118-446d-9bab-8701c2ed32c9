package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.demand.DemandRemoteService;
import com.ailpha.ailand.dataroute.endpoint.demand.DemandStatisticDTO;
import com.ailpha.ailand.dataroute.endpoint.demand.request.DemandStatisticRequest;
import com.ailpha.ailand.dataroute.endpoint.order.mapstruct.OrderMapstruct;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OrderStatisticDTO;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderStatisticRequest;
import com.ailpha.ailand.dataroute.endpoint.third.output.HubOrderRemote;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/home")
@RequiredArgsConstructor
@Tag(name = "首页", description = "首页")
public class HomeController {

    private final DemandRemoteService demandRemoteService;
    private final RouterService routerService;
    private final HubOrderRemote hubOrderRemote;
    private final StatisticDeliveryRepository statisticDeliveryRepository;
    private final OrderMapstruct orderMapstruct;
    private final DataResourceService dataResourceService;
    private final DataProductService dataProductService;

    @GetMapping
    @Operation(summary = "首页统计数据", description = "首页统计数据")
    public SuccessResponse<HomeResponse> home() {
        String currentUserId = LoginContextHolder.currentUser().getId();

        HomeResponse.DataLifecycleDTO dataLifecycleDTO = HomeResponse.DataLifecycleDTO.builder()
                .build();
        // 交付使用分组统计
        dataLifecycleDTO.setDeliveryStatisticGroup(statisticDeliveryRepository.selectStatisticDeliveryGroupByDeliveryMode(currentUserId));

        SuccessResponse<DemandStatisticDTO> demandStatistic = demandRemoteService.statistic(DemandStatisticRequest.builder()
                .userId(currentUserId).routerId(LoginContextHolder.currentUser().getCompany().getNodeId()).build());
        if (demandStatistic.isSuccess()) {
            dataLifecycleDTO.setDemandTop10(demandStatistic.getData().getDemandTop10());
            dataLifecycleDTO.setLatestDemandNegotiations(demandStatistic.getData().getLatestDemandNegotiations());
            dataLifecycleDTO.setDemandCount(demandStatistic.getData().getDemandCount());
            dataLifecycleDTO.setDemandNegotiationCount(demandStatistic.getData().getDemandNegotiationCount());
        }

        StatisticAssetDTO approvedAssetStatistic = new StatisticAssetDTO();
        try {
            SuccessResponse<OrderStatisticDTO> response = hubOrderRemote.statistic(OrderStatisticRequest.builder()
                    .userId(currentUserId).routerId(LoginContextHolder.currentUser().getCompany().getNodeId()).build());
            if (response.isSuccess()) {
                OrderStatisticDTO orderStatisticDTO = response.getData();
                dataLifecycleDTO.setDealStatisticDTO(orderStatisticDTO.getDealStatisticDTO());
                dataLifecycleDTO.setContractOrderDealStatisticDTO(orderStatisticDTO.getContractOrderDealStatisticDTO());
                approvedAssetStatistic = orderStatisticDTO.getAssetStatistic();
            }
            int resourceCount = getResourceCount();
            int productCount = getProductCount();

            approvedAssetStatistic.setResourceCount(resourceCount);
            approvedAssetStatistic.setProductCount(productCount);
            approvedAssetStatistic.setTotalCount(resourceCount + productCount);

        } catch (Exception e) {
            log.error("获取合同订单指标数据异常：", e);
        }

        // 交付记录埋点数据
        List<StatisticDeliveryRecordVO> latestDeliveryRecords = new ArrayList<>();
        try {
            long page = 1;
            long size = 6;
            SuccessResponse<StatisticDeliveryRecordsResp> statisticDeliveryRecordsResponse = hubOrderRemote.statisticDeliveryRecordsPage(StatisticDeliveryRecordsReq.builder().assetCreatorId(currentUserId)
                    .page(page).size(size).offset((page - 1L) * size).build());
            StatisticDeliveryRecordsResp statisticDeliveryRecords = statisticDeliveryRecordsResponse.getData();
            if (!ObjectUtils.isEmpty(statisticDeliveryRecords)) {
                for (StatisticDeliveryRecord statisticDeliveryRecord : statisticDeliveryRecords.getData()) {
                    latestDeliveryRecords.add(orderMapstruct.toStatisticDeliveryRecordVO(statisticDeliveryRecord));
                }
                // 数据被使用次数
                approvedAssetStatistic.setDataBeingUsed(statisticDeliveryRecords.getTotal());
            }
        } catch (Exception e) {
            log.error("获取交付记录埋点数据异常：", e);
        }

        return SuccessResponse.success(HomeResponse.builder()
                .dataFlow(dataLifecycleDTO)
                .latestDeliveryRecords(latestDeliveryRecords)
                .routerServiceStatusInfo(routerService.getRouterServiceInfo())
                .approvedAssetStatistic(approvedAssetStatistic)
                .build()).build();
    }

    private Integer getProductCount() {
        return dataProductService.getApprovedCount().intValue();
    }

    private int getResourceCount() {
        return dataResourceService.getApprovedCount().intValue();
    }
}
