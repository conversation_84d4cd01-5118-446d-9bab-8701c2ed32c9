package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/02/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticDeliveryRecordsResp implements Serializable {

    @Schema(description = "交付记录列表")
    List<StatisticDeliveryRecord> data;

    Long total;
}
