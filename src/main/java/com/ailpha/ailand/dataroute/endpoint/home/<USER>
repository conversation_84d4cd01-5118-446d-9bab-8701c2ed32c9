package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.demand.request.LatestDemandNegotiationDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.RouterServiceStatusInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class HomeResponse {
    @Schema(description = "成交进度")
    DataLifecycleDTO dataFlow;
    @Schema(description = "终端使用情况")
    RouterServiceStatusInfo routerServiceStatusInfo;
    @Schema(description = "最近交付数据")
    List<StatisticDeliveryRecordVO> latestDeliveryRecords;
    @Schema(description = "数据资产统计")
    StatisticAssetDTO approvedAssetStatistic;

    @Data
    @Builder
    public static class DataLifecycleDTO {
        @Schema(description = "发布需求")
        Long demandCount;
        @Schema(description = "需求响应总数")
        Long demandNegotiationCount;
        @Schema(description = "发布需求")
        List<DemandNegotiationTop10DTO> demandTop10;
        @Schema(description = "交易磋商")
        List<LatestDemandNegotiationDTO> latestDemandNegotiations;
        @Schema(description = "签订合同统计")
        OrderDealStatisticDTO dealStatisticDTO;
        @Schema(description = "使用交付")
        List<DeliveryStatisticGroup> deliveryStatisticGroup;
        @Schema(description = "合同完成统计")
        ContractOrderDealStatisticDTO contractOrderDealStatisticDTO;
    }

    @Data
    public static class DemandNegotiationTop10DTO {
        @Schema(description = "需求标题")
        String title;
        @Schema(description = "需求响应统计")
        String negotiationCount;
    }
}
