package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/05/08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticAssetOrderDeliveryReq implements Serializable {

    @Schema(description = "资产ID")
    String assetId;

    @Schema(description = "获益人方企业名称")
    String beneficiaryEnterpriseName;

    @Schema(description = "订单ID")
    String orderId;

    @Schema(description = "交付方式")
    List<String> deliveryMode;

    Long page;
    Long offset;
    Long size;
}
