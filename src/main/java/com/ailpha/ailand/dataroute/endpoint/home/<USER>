package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * 2025/2/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticDeliveryRecordExtend {

    @Schema(description = "计费方式：预付费、后付费")
    String chargingWay;

    @Schema(description = "计量方式：按次、按时间")
    String meteringWay;

    @Schema(description = "订单配置")
    OderRecordExtend orderConfig;
}
