package com.ailpha.ailand.dataroute.endpoint.home;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/26 18:59
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderDealTop10 {

    @Schema(description = "订单编号")
    String orderId;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "计量方式：按次、按时间")
    String meteringWay;

    @Schema(description = "使用次数上限")
    BigInteger allowance;

    @Schema(description = "订单配置")
    String config;

    @Schema(description = "订单有效期")
    Date expireDate;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description = "创建时间")
    Date createTime;

    /**
     * 审批通过时间
     */
    @Schema(description = "审批通过时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    Date approveTime;
}