package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/02/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticDeliveryRecordsReq implements Serializable {

    @Schema(description = "资产创建人用户ID")
    String assetCreatorId;

    Long page;
    Long offset;
    Long size;
}
