package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

/**
 * @author: yuwenping
 * @date: 2025/3/4 14:00
 * @Description: 首页-登记数据资产
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class StatisticAssetDTO {
    @Schema(description = "产品数量")
    Integer productCount;
    @Schema(description = "资源数量")
    Integer resourceCount;
    @Schema(description = "总数量-创建通过总数量")
    Integer totalCount;
    @Schema(description = "数据被使用数量")
    Long dataBeingUsed;
}
