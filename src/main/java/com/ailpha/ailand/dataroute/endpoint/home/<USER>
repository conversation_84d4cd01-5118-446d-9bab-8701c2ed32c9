package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/2/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DeliveryStatisticGroupVO implements Serializable {

    @Schema(description = "API, FILE_DOWNLOAD, MPC, TEE")
    String deliveryModeGroup;

    @Schema(description = "数量")
    Long number;
}
