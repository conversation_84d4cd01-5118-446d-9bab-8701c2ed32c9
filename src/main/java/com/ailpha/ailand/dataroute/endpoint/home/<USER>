package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yuwenping
 * @date: 2025/3/4 14:00
 * @Description: 首页-登记数据资产
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@AllArgsConstructor
@NoArgsConstructor
public class StatisticAssetDTO {
    @Schema(description = "产品数据统计")
    StatisticProduct product;

    @Schema(description = "资源数据统计")
    StatisticResource resource;

    @Data
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticResource {
        @Schema(description = "资源数量")
        Integer count;
        @Schema(description = "结构化资源数量")
        Integer structuredCount;
        @Schema(description = "非结构化资源数量")
        Integer unstructuredCount;
        @Schema(description = "图片资源数量")
        Integer pictureCount;
        @Schema(description = "数据集资源数量")
        Integer dataAssetCount;
        @Schema(description = "文本资源数量")
        Integer textCount;
    }

    @Data
    @Builder
    @FieldDefaults(level = AccessLevel.PRIVATE)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StatisticProduct {
        @Schema(description = "产品数量")
        Integer count;
        @Schema(description = "结构化产品数量")
        Integer structuredCount;
        @Schema(description = "非结构化产品数量")
        Integer unstructuredCount;
        @Schema(description = "图片产品数量")
        Integer pictureCount;
        @Schema(description = "数据集产品数量")
        Integer dataAssetCount;
        @Schema(description = "文本产品数量")
        Integer textCount;
        @Schema(description = "模型产品数量")
        Integer modelCount;
        @Schema(description = "交付方式数量")
        Map<String, Integer> deliveryMethodCount = new HashMap<>();
        @Schema(description = "交付场景数量")
        Map<DeliveryMode, Integer> deliveryModeCount = new HashMap<>();
    }

}
