package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/5/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticAssetOrderDeliveryVO implements Serializable {

    @Schema(description = "成交订单个数")
    Long orderNumber;

    @Schema(description = "合作企业个数（获益方企业个数）")
    Long beneficiaryEnterpriseNumber;

    @Schema(description = "累计交付使用个数")
    Long deliveryNumber;
}
