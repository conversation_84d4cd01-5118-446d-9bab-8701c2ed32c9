package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/2/28
 */
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "statistic_delivery")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticDelivery implements Serializable {

    @Id
    @Column(updatable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    String id;

    @Schema(description = "交付ID")
    @Column(name = "delivery_id")
    String deliveryId;

    @Schema(description = "交付方式")
    @Enumerated(EnumType.STRING)
    @Column(name = "delivery_mode")
    DeliveryType deliveryMode;

    @Schema(description = "创建人用户ID")
    @Column(name = "creator_id")
    String creatorId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyyMMdd HH:mm:ss", timezone = "GMT+8")
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyyMMdd HH:mm:ss", timezone = "GMT+8")
    @LastModifiedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    Date updateTime;
}
