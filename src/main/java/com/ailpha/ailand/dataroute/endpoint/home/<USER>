package com.ailpha.ailand.dataroute.endpoint.home;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/2/28
 */
public interface StatisticDeliveryRepository extends JpaRepository<StatisticDelivery, Long>, QuerydslPredicateExecutor<StatisticDelivery> {

    @Query("SELECT new com.ailpha.ailand.dataroute.endpoint.home.DeliveryStatisticGroup(SD.deliveryMode,COUNT(SD.id)) " +
            "FROM StatisticDelivery AS SD WHERE SD.creatorId=:creatorId GROUP BY SD.deliveryMode")
    List<DeliveryStatisticGroup> selectStatisticDeliveryGroupByDeliveryMode(@Param("creatorId") String creatorId);
}
