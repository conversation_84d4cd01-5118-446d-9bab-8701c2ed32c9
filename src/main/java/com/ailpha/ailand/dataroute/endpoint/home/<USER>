package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @date 2025/2/26 19:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ContractOrderDealTop10 {

    @Schema(description = "订单编号")
    String orderId;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "使用次数")
    BigInteger used;
}
