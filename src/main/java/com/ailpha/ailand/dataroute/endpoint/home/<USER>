package com.ailpha.ailand.dataroute.endpoint.home;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ContractOrderDealStatisticDTO {
    /**
     SELECT
     count(successful_usage)
     FROM
     dr_order_approval_record
     WHERE
     approver_id = '148c3ddacd2540649d74ac4415f2e3a2'
     AND status not IN ( 'APPLY', 'REJECTED' );
     */
    @Schema(description = "累计数据使用次数")
    Long dealCount;

    /**
     SELECT
     successful_usage,"id"
     FROM
     dr_order_approval_record
     WHERE
     approver_id = '148c3ddacd2540649d74ac4415f2e3a2'
     AND status not IN ( 'APPLY', 'REJECTED' ) ORDER BY successful_usage desc;
     */
    @Schema(description = "被使用最多次数的10个订单")
    List<ContractOrderDealTop10> contractOrderDealTop10s;

}
