package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/05/08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class StatisticAssetOrderDeliveryPageReq extends Page implements Serializable {

    @Schema(description = "资产ID")
    String assetId;

    @Schema(description = "获益人方企业名称")
    String beneficiaryEnterpriseName;

    @Schema(description = "订单ID")
    String orderId;

    @Schema(description = "交付场景")
    DeliveryMode deliveryMode;
}
