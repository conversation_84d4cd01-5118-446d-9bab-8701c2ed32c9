package com.ailpha.ailand.dataroute.endpoint.home;

import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.OrderClassify;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataResourceService;
import com.ailpha.ailand.dataroute.endpoint.order.domain.QOrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/home")
@RequiredArgsConstructor
@Tag(name = "首页", description = "首页")
public class HomeController {

    private final RouterService routerService;
    private final StatisticDeliveryRepository statisticDeliveryRepository;
    private final DataResourceService dataResourceService;
    private final DataProductService dataProductService;
    private final OrderRecordRepository orderRecordRepository;
    private final JPAQueryFactory queryFactory;

    @GetMapping
    @Operation(summary = "首页统计数据", description = "首页统计数据")
    public SuccessResponse<HomeResponse> home() {
        String currentUserId = LoginContextHolder.currentUser().getId();

        HomeResponse.DataLifecycleDTO dataLifecycleDTO = HomeResponse.DataLifecycleDTO.builder()
                .build();
        // 交付使用分组统计
        dataLifecycleDTO.setDeliveryStatisticGroup(getStatisticGroupVO(currentUserId));
        // 签订合同
        dataLifecycleDTO.setDealStatisticDTO(getOrderDealStatisticDTO(currentUserId));
        // 合同完成
        dataLifecycleDTO.setContractOrderDealStatisticDTO(getContractOrderDealStatistic(currentUserId));

        StatisticAssetDTO.StatisticProduct product = new StatisticAssetDTO.StatisticProduct();
        StatisticAssetDTO.StatisticResource resource = new StatisticAssetDTO.StatisticResource();
        try {
            product = dataProductService.statistics();
            resource = dataResourceService.statistics();
        } catch (Exception e) {
            log.error("error: ", e);
        }

        return SuccessResponse.success(HomeResponse.builder()
                .dataFlow(dataLifecycleDTO)
                .routerServiceStatusInfo(routerService.getRouterServiceInfo())
                .assetStatistic(StatisticAssetDTO.builder()
                        .product(product)
                        .resource(resource)
                        .build())
                .build()).build();
    }

    public ContractOrderDealStatisticDTO getContractOrderDealStatistic(String currentUserId) {
        ContractOrderDealStatisticDTO contractOrderDealStatisticDTO = new ContractOrderDealStatisticDTO();

        QOrderApprovalRecord approvalRecord = QOrderApprovalRecord.orderApprovalRecord;

        // 查询订单总使用次数
        BigInteger bigInteger = queryFactory
                .select(approvalRecord.successfulUsage.sum())
                .from(approvalRecord)
                .where(approvalRecord.beneficiaryId.eq(currentUserId).and(approvalRecord.classify.eq(OrderClassify.NORMAL)))
                .fetchOne();
        contractOrderDealStatisticDTO.setDealCount(bigInteger == null ? 0L : bigInteger.longValue());

        List<ContractOrderDealTop10> dealTop10List = queryFactory.selectFrom(approvalRecord)
                .where(approvalRecord.beneficiaryId.eq(currentUserId))
                .orderBy(approvalRecord.successfulUsage.desc()).limit(10).fetch().stream()
                .map(orderApprovalRecord -> ContractOrderDealTop10.builder().orderId(orderApprovalRecord.getId())
                        .assetName(orderApprovalRecord.getAssetName())
                        .used(orderApprovalRecord.getSuccessfulUsage())
                        .build()).toList();

        contractOrderDealStatisticDTO.setContractOrderDealTop10s(dealTop10List);
        return contractOrderDealStatisticDTO;
    }

    public OrderDealStatisticDTO getOrderDealStatisticDTO(String currentUserId) {
        OrderDealStatisticDTO orderDealStatisticDTO = new OrderDealStatisticDTO();
        Long count = orderRecordRepository.queryCountByUserId(currentUserId);
        orderDealStatisticDTO.setDealCount(count);

        QOrderApprovalRecord approvalRecord = QOrderApprovalRecord.orderApprovalRecord;
        final List<OrderDealTop10> list = queryFactory.selectFrom(approvalRecord)
                .where(approvalRecord.beneficiaryId.eq(currentUserId).and(approvalRecord.classify.eq(OrderClassify.NORMAL)))
                .orderBy(approvalRecord.pullTime.desc()).limit(10).fetch().stream().map(orderApprovalRecord -> OrderDealTop10.builder()
                        .orderId(orderApprovalRecord.getId())
                        .assetName(orderApprovalRecord.getAssetName())
                        .meteringWay(orderApprovalRecord.getMeteringWay())
                        .allowance(orderApprovalRecord.getAllowance())
                        .expireDate(orderApprovalRecord.getExpireDate())
                        .approveTime(orderApprovalRecord.getApproveTime())
                        .createTime(orderApprovalRecord.getCreateTime())
                        .build()).toList();
        orderDealStatisticDTO.setDealTop10s(list);
        return orderDealStatisticDTO;
    }

    public List<DeliveryStatisticGroupVO> getStatisticGroupVO(String currentUserId) {
        List<DeliveryStatisticGroup> group = statisticDeliveryRepository.selectStatisticDeliveryGroupByDeliveryMode(currentUserId);

        long teeTotal = group.stream()
                .filter(item -> item.getDeliveryMode().name().startsWith("TEE"))
                .mapToLong(DeliveryStatisticGroup::getNumber)
                .sum();

        long mpcTotal = group.stream()
                .filter(item -> item.getDeliveryMode().name().startsWith("MPC"))
                .mapToLong(DeliveryStatisticGroup::getNumber)
                .sum();

        final List<DeliveryStatisticGroupVO> list = new ArrayList<>(group.stream().filter(item ->
                        !(item.getDeliveryMode().name().startsWith("TEE") || item.getDeliveryMode().name().startsWith("MPC")))
                .map(deliveryStatisticGroup -> DeliveryStatisticGroupVO.builder().number(deliveryStatisticGroup.getNumber()).deliveryModeGroup(deliveryStatisticGroup.getDeliveryMode().name()).build()).toList());

        list.add(DeliveryStatisticGroupVO.builder().number(teeTotal).deliveryModeGroup("TEE").build());
        list.add(DeliveryStatisticGroupVO.builder().number(mpcTotal).deliveryModeGroup("MPC").build());

        return list;
    }

}