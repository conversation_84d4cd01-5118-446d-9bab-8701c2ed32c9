package com.ailpha.ailand.dataroute.endpoint.demand.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Data
public class MarkAsDealResponse {
    @Schema(hidden = true)

    String title;
    @Schema(hidden = true)
    String companyName;

    @Schema(description = "被采纳的用户信息")
    List<AcceptUser> acceptUsers;

    @Getter
    @Setter
    public static class AcceptUser {
        String userId;
        String routerId;
    }
}
