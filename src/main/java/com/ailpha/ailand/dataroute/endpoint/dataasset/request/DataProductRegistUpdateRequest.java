package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品登记请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductRegistUpdateRequest {
    @NotEmpty(message = "产品ID不能为空")
    @Schema(description = "数据标识")
    String id;
    @Schema(description = "产品中文名称")
    String productNameCN;
    @Schema(description = "产品类型：01-数据集,02-API产品,03-数据应用,04-数据报告,05-其他长度不能超过2")
    String type;
    @Schema(description = "数据覆盖周期开始时间")
    String dataCoverageTimeStart;
    @Schema(description = "数据覆盖周期结束时间")
    String dataCoverageTimeEnd;
    @Schema(description = "行业分类")
    String industry;
    @Schema(description = "行业分类(前端回显用)")
    String industry1;
    @Schema(description = "地域分类")
    String region;
    @Schema(description = "地域分类(前端回显用)")
    String region1;
    @Schema(description = "是否涉及个人信息：0:否，1:是")
    String personalInformation;
    @Schema(description = "产品简介")
    String description;
    @Schema(description = "产品来源")
    String source;
    @Schema(description = "数据规模")
    Double dataSize;
    @Schema(description = "数据规模单位 MB GB TB")
    String dataSizeUnit;
    @Schema(description = "更新频率")
    Integer updateFrequency;
    String updateFrequencyUnit;
    // @Schema(description = "交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC")
    // List<DeliveryMode> deliveryModes;
    @Schema(description = "交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）")
    String deliveryMethod;
    @Schema(description = "使用限制：数据产品在使用过程中的任何限制或条件，标注使用限制场景")
    String limitations;
    @Schema(description = "授权使用：数据产品使用时是否需要授权： 0：否 1：是")
    String authorize;
    @Schema(description = "是否允许二次加工：0：不允许 1：允许")
    String isSecondaryProcessed;
    @Schema(description = "产品关联数据资源统一标识，基于那些数据资源形成的数据产品。", examples = "[{\"resourceId\":\"resourceId1\"},{\"resourceId\":\"resourceId2\"}]")
    String platformResourceId;
    @Schema(description = "产品血缘")
    String lineage;
    @Schema(description = "其他")
    String other;

    // 声明信息
    @Schema(description = "声明信息")
    QualificationDoc qualificationDoc;
}
