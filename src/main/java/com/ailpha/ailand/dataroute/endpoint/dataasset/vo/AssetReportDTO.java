package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/17 14:32
 * 数据资产上报内容
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetReportDTO {

    /**
     * 商品价格详情
     */
    String billDetail;

    /**
     * 商品计费方式按次、包年、包月、面议
     */
    String billingType;

    /**
     * 企业名称
     */
    String companyName;

    /**
     * 企业统信码
     */
    String companySucc;

    /**
     * 数商类型1普通，2卖家，3第三方服务机构
     */
    Integer dataCompanyType;

    /**
     * 登记平台企业ID,使用路由器id
     */
    String platformCompanyId;

    /**
     * 登记平台商品ID
     */
    String platformProductId;

    /**
     * 商品名称
     */
    String productName;

    /**
     * 登记平台商品详情页（外部商品详情链接地址）
     */
    String productPlatformUrl;

    /**
     * 登记平台id
     */
    String sourcePlatformId;

    /**
     * 是否结构化数据
     */
    Integer structData;

    /**
     * 联系人
     */
    List<BusinessReportDTO.CompanyLink> companyLinkParams;

    @Data
    public static class BillDetail{

        private Integer times;

        private Integer price;

        private String duration;
    }
}
