package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

/**
 * @Author: luva.hua
 * @Description: 数据类型
 * @Date: 2024-11-13
 */
public enum DataType {
    // 结构化数据、非结构化数据、模型
    STRUCTURED, UNSTRUCTURED, MODEL;

    public static DataType fromName(String name) {
        for (DataType dataType : values()) {
            if (dataType.name().equalsIgnoreCase(name)) {
                return dataType;
            }
        }
        return null;
    }
}
