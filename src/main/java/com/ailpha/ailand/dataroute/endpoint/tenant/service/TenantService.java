package com.ailpha.ailand.dataroute.endpoint.tenant.service;

import javax.sql.DataSource;

import org.flywaydb.core.Flyway;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ailpha.ailand.dataroute.endpoint.tenant.entity.Tenant;
import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUser;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.querydsl.jpa.impl.JPAQueryFactory;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class TenantService {

    private final TenantRepository tenantRepository;
    private final JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;
    private final JPAQueryFactory jpaQueryFactory;

    @Transactional(readOnly = true)
    public String getTenantSchemaByUsername(String username) {
        QUser user = QUser.user;
        User userInfo = jpaQueryFactory.selectFrom(user)
                .where(user.account.eq(username))
                .fetchOne();

        if (userInfo == null) {
            return null;
        }

        return "tenant_" + userInfo.getCompanyId();
    }

    public void addTenantSchema(String schemaName) {
        jdbcTemplate.execute("CREATE SCHEMA IF NOT EXISTS " + schemaName);

        // 创建租户记录
        Tenant tenant = Tenant.builder()
                .tenantCode(IdUtil.fastSimpleUUID())
                .tenantName("")
                .schemaName(schemaName)
                .status("ACTIVE")
                .build();

        // 保存租户信息
        tenantRepository.saveAndFlush(tenant);

        // 初始化租户schema
        initTenantSchema(schemaName);
    }

    // 在TenantService中添加初始化租户schema的方法
    public void initTenantSchema(String schema) {
        try {
            Flyway.configure()
                    .dataSource(dataSource)
                    .schemas(schema)
                    .locations("classpath:db/migration/tenant")
                    .baselineOnMigrate(true)
                    .load()
                    .migrate();
        } catch (Exception e) {
            log.error("初始化企业schema[{}]异常 ", schema, e);
            throw e;
        }
        if (log.isDebugEnabled())
            log.debug("初始化企业租户库【{}】成功", schema);
    }

    public Boolean schemaExist(String schema) {
        return tenantRepository.existsBySchemaName(schema);
    }
}