package com.ailpha.ailand.dataroute.endpoint.demand.request;

import com.dbapp.rest.request.Page;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DemandNegotiationRecordRequest extends Page {
    @Schema(description = "需求ID")
    Integer demandId;
    @Schema(hidden = true)
    String userId;
    @Schema(description = "是否隐藏所有人的报价", example = "适用场景: 需求地图 值为true")
    Boolean hideAllSolution = false;
    @Schema(description = "是否隐藏其他人的报价", example = "适用场景：我的需求响应列表 只能显示自己的 值为true")
    Boolean hideOtherSolution = false;
}
