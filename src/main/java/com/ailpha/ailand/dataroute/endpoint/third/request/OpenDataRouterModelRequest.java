package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.biz.api.model.Table;
import com.ailpha.ailand.biz.dao.constants.ModelSourceType;
import com.ailpha.ailand.biz.web.vo.dataset.CreateModelRequestVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: sunsas.yu
 * @date: 2025/05/30 16:57
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenDataRouterModelRequest {

    @NotNull
    @ApiModelProperty(value = "模型id", required = true)
    String dataRouteModelId;

    @ApiModelProperty(value = "模型名称", required = true)
    String name;

    @ApiModelProperty(value = "模型描述", required = true)
    String desc;

    @ApiModelProperty("模型来源类型")
    ModelSourceType sourceType;

    @ApiModelProperty("数据集")
    Table table;

    @ApiModelProperty("是否为大语言模型")
    Boolean isLLM;

    @ApiModelProperty("大语言模型参数")
    CreateModelRequestVO.LLM llmModel;

    @JsonFormat(pattern = "yyyy.MM.dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy.MM.dd HH:mm")
    Date expireDate;

    @ApiModelProperty(value = "数据文件路径")
    String filePath;

    @ApiModelProperty(value = "创建用户")
    String userId;

    @ApiModelProperty(value = "创建用户名")
    String userName;

    @ApiModelProperty(value = "企业id")
    String companyId;

    @ApiModelProperty(value = "平台id")
    String nodeId;

    @ApiModelProperty(value = "前置机调用url")
    String executorUrl;

    @ApiModelProperty(value = "业务领域")
    String purposeList;

    @ApiModelProperty(value = "数据后缀类型")
    private String datasetFileType;

}
