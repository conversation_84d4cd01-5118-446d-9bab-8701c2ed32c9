package com.ailpha.ailand.dataroute.endpoint.upgrade.entity;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/9
 */
@Getter
@Setter
@Entity
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "upgrade_task_info")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpgradeTask implements Serializable {

    @Id
    @Schema(description = "ID")
    @Column(name = "id", updatable = false)
    private String id;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    @Enumerated(EnumType.STRING)
    @Column(name = "module")
    private UpgradeModule module;

    @Schema(description = "升级前包ID")
    @Column(name = "before_package_id")
    private String beforePackageId;

    @Schema(description = "升级前版本")
    @Column(name = "before_version")
    private String beforeVersion;

    @Schema(description = "升级后包ID")
    @Column(name = "after_package_id")
    private String afterPackageId;

    @Schema(description = "升级后包名称")
    @Column(name = "after_package_name")
    private String afterPackageName;

    @Schema(description = "升级后版本")
    @Column(name = "after_version")
    private String afterVersion;

    @Schema(description = "MD5")
    @Column(name = "md5")
    private String md5;

    @Schema(description = "来源：DATA_ROUTE（连接器）")
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    private UpgradeSource source;

    @Schema(description = "是否立即升级")
    @Column(name = "immediately")
    private Boolean immediately;

    @Schema(description = "升级时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "upgrade_time")
    private Date upgradeTime;

    @Schema(description = "升级状态：WAIT（待升级）UPGRADING（升级中）SUCCESS（升级成功）FAILURE（升级失败）INVALID（已失效）")
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private UpgradeStatus status;

    @JsonIgnore
    @Schema(description = "日志")
    @Column(name = "log")
    private String log;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "update_time")
    private Date updateTime;
}
