package com.ailpha.ailand.dataroute.endpoint.dataasset.enums;

public enum SeparatorEnum {
    comma(1, ",", "逗号"),
    tab(2, "\t", "tab分隔符"),
    utf8_first_char(4, "\001", "utf8第一个字符"),
    ascii_first_char(5, "\01", "ascii第一个字符"),
    pound_key(6, "#", "井号"),
    semicolon(7, ";", "分号");

    private final Integer code;
    private final String fieldDelimiter;
    private final String desc;

    SeparatorEnum(Integer code, String fieldDelimiter, String desc) {
        this.code = code;
        this.fieldDelimiter = fieldDelimiter;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getFieldDelimiter() {
        return fieldDelimiter;
    }
}
