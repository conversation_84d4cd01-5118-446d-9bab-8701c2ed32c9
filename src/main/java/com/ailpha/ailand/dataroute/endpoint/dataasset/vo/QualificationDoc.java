package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QualificationDoc implements Serializable {
    @Schema(description = "数据样例")
    String dataSampleAttach;
    @Schema(description = "合法合规声明")
    String complianceAndLegalStatementAttach;
    @Schema(description = "数据来源声明")
    String dataSourceStatementAttach;
    @Schema(description = "安全分类分级")
    String safeLevelAttach;
    @Schema(description = "数据质量产品价值评估报告")
    String evaluationReportAttach;
    @Schema(description = "合规自查手册")
    String complianceSelfCheckManualAttach;
    @Schema(description = "其他")
    String otherAttach;
}
