package com.ailpha.ailand.dataroute.endpoint.user.repository;

import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

public interface UserRepository extends JpaRepository<User, String>, QuerydslPredicateExecutor<User> {
    User findFirstByAccountOrderByCreateTimeDesc(String account);

    int countByAccountAndDeletedFalse(String account);

    int countByAccountAndDeletedFalseAndIdIsNot(String account, String userId);

    User findFirstByAccount(String account);
    List<User> findByAccountLike(String account);

    List<User> findByCompanyId(Long companyId);

    List<User> findByIdIn(List<String> userIds);

    Integer countByDeleted(<PERSON><PERSON><PERSON> deleted);
}
