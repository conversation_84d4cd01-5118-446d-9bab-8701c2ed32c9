package com.ailpha.ailand.dataroute.endpoint.user.repository;

import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserRole;
import lombok.Data;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

public interface UserRepository extends JpaRepository<User, String>, QuerydslPredicateExecutor<User> {
    User findFirstByAccountOrderByCreateTimeDesc(String account);

    int countByAccountAndDeletedFalse(String account);

    int countByAccountAndDeletedFalseAndIdIsNot(String account, String userId);

    List<User> findByAccountLike(String account);

    List<User> findByCompanyId(Long companyId);

    List<User> findByIdIn(List<String> userIds);

    boolean existsUserByAccount(String account);

    User findFirstByCompanyIdAndDeletedFalse(Long companyId);

    User findByAccount(String account);
}
