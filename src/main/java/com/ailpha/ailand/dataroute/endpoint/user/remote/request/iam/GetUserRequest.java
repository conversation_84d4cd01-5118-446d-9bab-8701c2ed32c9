package com.ailpha.ailand.dataroute.endpoint.user.remote.request.iam;

import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class GetUserRequest {
    String uuid;
    String bimRequestId;

    public GetUserRequest() {
        this.bimRequestId = UuidUtils.uuid32();
    }
}
