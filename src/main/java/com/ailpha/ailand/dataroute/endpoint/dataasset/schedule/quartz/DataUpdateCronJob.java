package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

@Slf4j
public class DataUpdateCronJob implements Job {

    public static String PARAMS = "dataUpdateCronJob";

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        DataUpdateCronJobParams dataUpdateCronJobParams = (DataUpdateCronJobParams) jobExecutionContext.getMergedJobDataMap().get(PARAMS);

        if (null == dataUpdateCronJobParams) {
            log.warn("task params is null");
            return;
        }
        log.info("task info {}", JSONUtil.toJsonStr(dataUpdateCronJobParams.getDataUpdateTask()));

        try {

            DataAssetPrepareSchedule dataAssetPrepareSchedule = dataUpdateCronJobParams.getDataAssetPrepareSchedule();

            if (null == dataAssetPrepareSchedule) {
                log.warn("task params DataAssetPrepareSchedule is null");
                return;
            }

            dataAssetPrepareSchedule.executeScheduledTask(dataUpdateCronJobParams);

        } catch (Exception e) {
            log.error("submit query engine error {}", JSONUtil.toJsonStr(dataUpdateCronJobParams.getDataUpdateTask()), e);
        }

    }
}
