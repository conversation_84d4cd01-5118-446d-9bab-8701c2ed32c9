package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AgentTaskType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.HengNaoAgentTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.HengNaoAgentTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.hengnao.HengNaoAdapterService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ExploreTaskStatusScheduler {
    private final HengNaoAgentTaskRepository repository;
    private final HengNaoAdapterService hengNaoRemoteService;
    private final TenantRepository tenantRepository;

    @Scheduled(cron = "0 * * * * ?")
    public void checkTaskStatus() {
        tenantRepository.findAll().forEach(tenant -> {
            if (StringUtils.equals(tenant.getSchemaName(), TenantIdentifierResolver.DEFAULT_TENANT))
                return;
            TenantContext.clear();
            TenantContext.setCurrentTenant(tenant.getSchemaName());
            List<HengNaoAgentTask> runningTasks = repository.findByStatusAndTaskTypeIn(1, List.of(AgentTaskType.DATA_RESOURCE_EXPLORE, AgentTaskType.DATA_PRODUCT_EXPLORE));
            for (HengNaoAgentTask task : runningTasks) {
                try {
                    Integer status = hengNaoRemoteService.queryAsyncTaskStatus(task.getTaskId());
                    if (status == 2) { // 成功
                        String result = JacksonUtils.obj2json(hengNaoRemoteService.queryAsyncTaskMessages(task.getTaskId()));
                        task.setResultJson(result);
                        task.setStatus(2);
                        if (log.isDebugEnabled())
                            log.debug("智能体执行日志：\n{}", JSONUtil.parseObj(JSONUtil.parseObj(result).getByPath("messages[0].content")).getByPath("logs"));
                    } else if (status == 4) { // 失败
                        task.setStatus(4);
                        task.setFailReason("恒脑返回失败");
                    }
                    repository.saveAndFlush(task);
                } catch (Exception e) {
                    log.error("定时任务处理失败，taskId={}", task.getTaskId(), e);
                }
            }
        });

    }
}
