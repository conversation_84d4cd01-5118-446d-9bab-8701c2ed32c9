package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OpenApiListRequest {
    @Schema(description = "接口名称")
    String name;
    @Schema(description = "接口状态 NORMAL，EXPIRED，DELETED，UNAVAILABLE")
    APIStatusEnum status;
    @Schema(description = "上线状态")
    Boolean online;
    @Schema(description = "模型名称")
    String modelName;
    @Schema(description = "合约名称")
    String contractName;
    @Schema(description = "用户id")
    String userId;
    @Schema(description = "数据资产id")
    String assetId;

    @Builder.Default
    @JsonProperty("$page")
    long num = 1;
    @Builder.Default
    @JsonProperty("$size")
    long size = 10;
}
