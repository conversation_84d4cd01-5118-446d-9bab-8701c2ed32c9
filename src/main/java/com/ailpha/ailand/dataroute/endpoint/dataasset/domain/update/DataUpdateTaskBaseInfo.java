package com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update;

import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

/**
 * 数据产品接入更新任务baseinfo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataUpdateTaskBaseInfo {
    /**
     * 数据接入更新方式
     */
    UpdateWay updateWay;

    /**
     * 启动时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "GMT+8")
    Date startTime;

    /**
     * 停止时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, timezone = "GMT+8")
    Date stopTime;

    /**
     * 调度周期
     */
    SchedulerPeriodEnum schedulerPeriod;

    /**
     * 间隔小时数
     */
    Integer intervalHour;
}