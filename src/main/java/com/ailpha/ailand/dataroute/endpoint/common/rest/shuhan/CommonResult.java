package com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.IReturnCode;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonResult<T> implements IResult<T> {

    @Serial
    private static final long serialVersionUID = 1581651691719242300L;

    @Setter
    private int statusCode = InternalReturnCode.SUCCESS.getCode();

    @Setter
    @Getter
    private String message = "成功";

    @Setter
    @Getter
    private T data;

    public CommonResult() {
    }

    protected CommonResult(int code, String message, T data, boolean success) {
        this.statusCode = code;
        this.message = message;
        this.data = data;
    }

    public CommonResult(T data) {
        this.data = data;
        if (data == null) {
            statusCode = InternalReturnCode.FAIL.getCode();
            message = "失败!";
        }
    }

    public CommonResult(String message) {
        this.statusCode = InternalReturnCode.FAIL.getCode();
        this.message = message;
    }

    public CommonResult(T data, String message) {
        this.data = data;
        this.message = message;
    }

    public CommonResult(T data, IReturnCode returnCode) {
        this.data = data;
        this.statusCode = returnCode.getCode();
    }

    public CommonResult(IReturnCode returnCode, String message) {
        this.message = message;
        this.statusCode = returnCode.getCode();
    }

    public static CommonResult<Void> SUCCESS() {
        CommonResult<Void> response = new CommonResult<>();
        response.setStatusCode(InternalReturnCode.SUCCESS.getCode());
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data) {
        CommonResult<T> response = new CommonResult<>();
        response.setStatusCode(InternalReturnCode.SUCCESS.getCode());
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> SUCCESS(T data, String message) {
        CommonResult<T> response = SUCCESS(data);
        response.setMessage(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(String message, IReturnCode code) {
        CommonResult<T> response = new CommonResult<T>();
        response.setStatusCode(code.getCode());
        response.setMessage(message);
        return response;
    }

    public static <T> CommonResult<T> FAIL(IReturnCode errorCode, String message, T data) {
        return new CommonResult<T>(errorCode.getCode(), message, data, false);
    }

    public static <T> CommonResult<T> FAIL() {
        return FAIL("", InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(String message) {
        return FAIL(message, InternalReturnCode.FAIL);
    }

    public static <T> CommonResult<T> FAIL(T data) {
        CommonResult<T> response = FAIL("失败");
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(T data, String message) {
        CommonResult<T> response = FAIL(message);
        response.setData(data);
        return response;
    }

    public static <T> CommonResult<T> FAIL(Exception e) {
        return FAIL(e.getMessage());
    }

    public Integer getServiceCode() {
        return statusCode;
    }

    @Override
    public Integer getStatusCode() {
        return statusCode;
    }

    @Override
    public boolean isSuccess() {
        return InternalReturnCode.SUCCESS.getCode().equals(statusCode);
    }

    public boolean unauthorized() {
        return InternalReturnCode.UNAUTHORIZED.getCode().equals(statusCode);
    }
}
