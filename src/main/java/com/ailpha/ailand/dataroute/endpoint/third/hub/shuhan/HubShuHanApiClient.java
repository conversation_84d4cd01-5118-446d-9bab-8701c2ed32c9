package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.*;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.restclient.BufferingClientHttpResponseWrapper;
import com.ailpha.ailand.dataroute.endpoint.servicenode.mapper.ServiceNodeMapper;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.third.input.GenerateUuidRequest;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.constant.OpenApiConstant;
import com.dbapp.rest.openapi.AppToken;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.dbapp.rest.utils.DasApiUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig.acceptsUntrustedCertsHttpClient;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.deliveryModeMapping;

/**
 * 枢纽（数瀚）接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HubShuHanApiClient {
    @Value("${ailand.logging.abbreviate:true}")
    Boolean abbreviateLog;

    private final ObjectMapper objectMapper;
    private final ServiceNodeMapper serviceNodeMapper;

    private static AppToken appToken = new AppToken();

    private static final ThreadLocal<CompanyDTO> companyDTOThreadLocal = new ThreadLocal<>();

    public HubShuHanApiClient setCompany(CompanyDTO companyDTO) {
        companyDTOThreadLocal.set(companyDTO);
        return this;
    }

    private RestClient restClient() {
        URL url = URLUtil.url(getShuHanServer().getUrl());
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        try {
            requestFactory.setHttpClient(acceptsUntrustedCertsHttpClient());
        } catch (Exception ignore) {
        }
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(List.of(
                MediaType.APPLICATION_JSON, new MediaType("application", "*+json"),
                MediaType.TEXT_PLAIN,
                MediaType.ALL
        ));
        return RestClient.builder()
                .requestFactory(requestFactory)
                .messageConverters(httpMessageConverters -> httpMessageConverters.addFirst(mappingJackson2HttpMessageConverter))
                .baseUrl(String.format("%s://%s", url.getProtocol(), url.getAuthority() + url.getPath()))
                .requestInterceptor((request, body, execution) -> {
                    long timestamp = System.currentTimeMillis();
                    NodeDTO.HubInfo server = getShuHanServer();
                    String appKey = server.getAk();
                    String appSecret = server.getSk();
                    request.getHeaders().setContentType(MediaType.APPLICATION_JSON_UTF8);
                    if (LoginContextHolder.isLogin()) {
                        request.getHeaders().add("nodeId", LoginContextHolder.currentUser().getCompany().getThirdBusinessId());
                    } else {
                        CompanyDTO companyDTO = companyDTOThreadLocal.get();
                        if (companyDTO != null) {
                            request.getHeaders().add("nodeId", companyDTO.getThirdBusinessId());
                        } else {
                            request.getHeaders().add("nodeId", "");
                        }
                    }
                    UuidUtils.uuid32();
                    String requestId;
                    try {
                        requestId = JSONUtil.parseObj(new String(body, Charset.defaultCharset())).getStr("requestId");
                    } catch (Exception e) {
                        requestId = UuidUtils.uuid32();
                    }
                    request.getHeaders().add("xRequestId", requestId == null ? UuidUtils.uuid32() : requestId);
                    request.getHeaders().add("xTimestamp", String.valueOf(timestamp));
                    String sign;
                    if (!request.getURI().getPath().contains("/identityVerify")
                            && !request.getURI().getPath().contains("/identityVerifyNonce")) {
                        String nonce = DasApiUtil.generateRandomString(6);
                        String token = appToken(server.getCertificate(), server.getKeyId());
//                        request.getHeaders().add(OpenApiConstant.TOKEN, token);
                        request.getHeaders().add("Authorization", "Bearer " + token);
                        request.getHeaders().add(OpenApiConstant.NONCE, nonce);
                        sign = DasApiUtil.getSHA256Hash(
//                                (HttpMethod.GET.equals(request.getMethod()) ?
                                DasApiUtil.concatSignString(request.getURI().getQuery())
//                    :
//                                DasApiUtil.concatSignString(body2map(body))
//                        )
                                        + timestamp + token + nonce);
                    } else {
                        // 生成token需要的sign
                        sign = DasApiUtil.getSHA256Hash(timestamp + appKey + appSecret);
                    }
                    request.getHeaders().add(OpenApiConstant.SIGN, sign);
                    String responseBody = "";
                    try {
                        ClientHttpResponse response = execution.execute(request, body);
                        BufferingClientHttpResponseWrapper httpResponseWrapper = new BufferingClientHttpResponseWrapper(response);
                        responseBody = IOUtils.toString(httpResponseWrapper.getBody(), Charset.defaultCharset());
                        if (log.isDebugEnabled()) {
                            log.debug("功能节点接口 {} 请求体 {} 请求头 {} 响应 {}", request.getURI(),
                                    abbreviateLog ? org.apache.commons.lang3.StringUtils.abbreviateMiddle(new String(body, Charset.defaultCharset()), "...", 500) : new String(body, Charset.defaultCharset()),
                                    request.getHeaders(),
                                    abbreviateLog ? org.apache.commons.lang3.StringUtils.abbreviateMiddle(responseBody, "...", 500) : responseBody);
                        }
                        JsonNode jsonNode = JacksonUtils.readTree(responseBody);
                        if ((jsonNode.has("statusCode") && jsonNode.get("statusCode").asInt() == 401) || (jsonNode.has("code") && jsonNode.get("code").asInt() == 10002)) {
                            appToken = new AppToken();
                            String token = appToken(server.getCertificate(), server.getKeyId());
                            request.getHeaders().add("Authorization", "Bearer " + token);
                            return execution.execute(request, body);
                        }
                        return httpResponseWrapper;
                    } catch (Exception e) {
                        log.error("调用功能节点接口失败 url -> {}，request -> {}, headers -> {}, response -> {}", request.getURI(),
                                abbreviateLog ? org.apache.commons.lang3.StringUtils.abbreviateMiddle(new String(body, Charset.defaultCharset()), "...", 500) : new String(body, Charset.defaultCharset()),
                                request.getHeaders(),
                                abbreviateLog ? org.apache.commons.lang3.StringUtils.abbreviateMiddle(responseBody, "...", 500) : responseBody, e);
                        appToken = new AppToken();
                        throw e;
                    }
                })
                .build();
    }

    protected NodeDTO.HubInfo getShuHanServer() {
        if (LoginContextHolder.isLogin()) {
            return SpringUtil.getBean(CompanyService.class).getHubInfoForEntity(LoginContextHolder.currentUser().getCompany().getId());
        }
        if (companyDTOThreadLocal.get() != null) {
            return SpringUtil.getBean(CompanyService.class).getHubInfoForEntity(companyDTOThreadLocal.get().getId());
        }
        return SpringUtil.getBean(CompanyService.class).getHubInfoForEntity();
    }

    private final LicenseRemoteService licenseRemoteService;

    private synchronized String appToken(String certificate, String keyId) {
        //判断token是否在有效期内
        if (Objects.nonNull(appToken.getExpireTime()) && appToken.getExpireTime() - 5000 > System.currentTimeMillis()) {
            return appToken.getToken();
        }
        String requestId = UuidUtils.uuid32();
        GenerateUuidRequest request = new GenerateUuidRequest();
        request.setVerify(certificate);
        request.setRequestId(requestId);
        ShuhanResponse<GetNonceResponse> nonce = dataItemApi().nonce(request);
        Assert.isTrue(nonce.isSuccess(), nonce.getMessage());
        CommonResult<SignResponse> encryptNonce = licenseRemoteService.encryptNonce(EncryptNonceRequest.builder().message(nonce.getData().getNonce()).keyId(keyId).build());
        Assert.isTrue(encryptNonce.isSuccess(), "随机数加密失败：" + encryptNonce.getMsg());
        GenerateTokenRequest generateTokenRequest = new GenerateTokenRequest();
        generateTokenRequest.setSignature(encryptNonce.getData().getSign());
        generateTokenRequest.setRequestId(requestId);
        ShuhanResponse<TokenResponse> token = dataItemApi().token(generateTokenRequest);
        Assert.isTrue(token.isSuccess(), token.getMessage());
        appToken = new AppToken(token.getData().getTokenValue(), token.getData().getTokenTimeout());
        return appToken.getToken();
    }

    private synchronized HubShuHanDataItemApi dataItemApi() {
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        return factory.createClient(HubShuHanDataItemApi.class);
    }

    private synchronized HubShuHanDataProductApi dataProductApi() {
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        return factory.createClient(HubShuHanDataProductApi.class);
    }

    private synchronized HubShuHanDataResourceApi dataResourceApi() {
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        return factory.createClient(HubShuHanDataResourceApi.class);
    }

    public synchronized HubShuHanServiceNodeApi serviceNodeApi() {
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        return factory.createClient(HubShuHanServiceNodeApi.class);
    }

    public SuccessResponse<List<JSONObject>> allMarketDataResource(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = new ArrayList<>();
        if (StringUtils.hasText(dataAssetQuery.getAssetName())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("resourceName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getAssetName())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getProviderOrg())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("providerName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getProviderOrg())
                    .build());
        }
        if (dataAssetQuery.getSource() != null) {
            Function<SourceType, String> productTypeMapping = (sourceType) -> {
                switch (sourceType) {
                    case FILE -> {
                        return "01";
                    }
                    case API -> {
                        return "02";
                    }
                    case DATABASE -> {
                        return "03";
                    }
                    case null, default -> {
                        return "05";
                    }
                }
            };
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productType")
                    .filterOperation("=")
                    .filterValue(productTypeMapping.apply(dataAssetQuery.getSource()))
                    .build());
        }
        if (dataAssetQuery.getDeliveryModes() != null) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("deliveryMethod")
                    .filterOperation("=")
                    .filterValue(deliveryModeMapping.apply(dataAssetQuery.getDeliveryModes()))
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getIndustry())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industry")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }
        if (!CollectionUtils.isEmpty(dataAssetQuery.getIndustryClassifyList())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industryClassifyList")
                    .filterOperation("in")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }
        return dataCatalogQuery(
                CatalogQueryVM.builder()
                        .type(1)
                        .size(dataAssetQuery.getSize())
                        .page(dataAssetQuery.getNum())
                        .filters(filters).orders(List.of())
                        .build()
        );
    }

    public SuccessResponse<List<JSONObject>> allMarketDataProduct(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = catalogQueryFilter(dataAssetQuery);
        return dataCatalogQuery(
                CatalogQueryVM.builder()
                        .type(2)
                        .size(dataAssetQuery.getSize())
                        .page(dataAssetQuery.getNum())
                        .filters(filters)
                        .orders(List.of())
                        .build());
    }

    private List<CatalogQueryVM.FilterVM> catalogQueryFilter(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = new ArrayList<>();
        if (StringUtils.hasText(dataAssetQuery.getAssetName())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getAssetName())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getBusinessPlatformUniqueNo())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("businessPlatformUniqueNo")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getBusinessPlatformUniqueNo())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getProviderOrg())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("providerName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getProviderOrg())
                    .build());
        }
        if (dataAssetQuery.getSource() != null) {
            Function<SourceType, String> productTypeMapping = (sourceType) -> {
                switch (sourceType) {
                    case FILE -> {
                        return "01";
                    }
                    case API -> {
                        return "02";
                    }
                    case DATABASE -> {
                        return "03";
                    }
                    case null, default -> {
                        return "05";
                    }
                }
            };
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productType")
                    .filterOperation("=")
                    .filterValue(productTypeMapping.apply(dataAssetQuery.getSource()))
                    .build());
        }
        if (dataAssetQuery.getDeliveryModes() != null) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("deliveryMethod")
                    .filterOperation("=")
                    .filterValue(deliveryModeMapping.apply(dataAssetQuery.getDeliveryModes()))
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getIndustry())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industry")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }
        if (!CollectionUtils.isEmpty(dataAssetQuery.getIndustryClassifyList())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industryClassifyList")
                    .filterOperation("in")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }

        return filters;
    }

    public SuccessResponse<List<ServiceNodeListVO>> serviceNodeList(String entryName, List<String> serviceNodeIds, Long page, Long size) {
        List<ServiceNodeListVO> serviceNodeListVOS = new ArrayList<>();
        List<CatalogQueryVM.FilterVM> filters = new ArrayList<>();
        if (!ObjectUtils.isEmpty(serviceNodeIds)) {
            CatalogQueryVM.FilterVM filterVM = CatalogQueryVM.FilterVM.builder().filterProperty("serviceNodeId").filterOperation("in").filterValue(JacksonUtils.obj2json(serviceNodeIds)).build();
            filters.add(filterVM);
        }
        if (!ObjectUtils.isEmpty(entryName)) {
            CatalogQueryVM.FilterVM filterVM = CatalogQueryVM.FilterVM.builder().filterProperty("entryName").filterOperation("like").filterValue(entryName).build();
            filters.add(filterVM);
        }
        CatalogQueryVM catalogQueryVM = CatalogQueryVM.builder().orders(Collections.emptyList()).filters(filters).page(page).size(size).build();
        ShuhanResponse<RegionSyncListVO> shuhanResponse = serviceNodeApi().regionSyncListQuery(catalogQueryVM);
        Assert.isTrue(shuhanResponse != null && shuhanResponse.isSuccess(), shuhanResponse != null ? shuhanResponse.getMessage() : "查询业务节点列表失败");
        RegionSyncListVO regionSyncListVO = shuhanResponse.getData();
        if (!ObjectUtils.isEmpty(regionSyncListVO.getServiceNodeList())) {
            serviceNodeListVOS = new ArrayList<>(regionSyncListVO.getServiceNodeList().size());
            for (RegionSyncListVO.ServiceNode serviceNode : regionSyncListVO.getServiceNodeList()) {
                ServiceNodeListVO serviceNodeListVO = serviceNodeMapper.regionSyncListVOToServiceNodeListVO(serviceNode);
                serviceNodeListVOS.add(serviceNodeListVO);
            }
        }
        return SuccessResponse.success(serviceNodeListVOS)
                .total(regionSyncListVO.getTotal())
                .page(Page.of(page, size))
                .build();
    }

    public DataAssetSavedVO dataProductInfoRegist(DataProductSaveVM registRequest) {
        ShuhanResponse<DataAssetSavedVO> dataRegistResponse = dataProductApi().dataProductInfoRegist(registRequest);
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMessage() : "数据产品登记失败");
        return dataRegistResponse.getData();
    }

    public DataAssetUpdateVO dataProductInfoUpdate(DataProductUpdateVM updateRequest) {
        ShuhanResponse<DataAssetUpdateVO> dataProductInfoUpdate = dataProductApi().dataProductInfoUpdate(updateRequest);
        Assert.isTrue(dataProductInfoUpdate != null && dataProductInfoUpdate.isSuccess(), dataProductInfoUpdate != null ? dataProductInfoUpdate.getMessage() : "数据产品更新失败");
        return dataProductInfoUpdate.getData();
    }

    public DataAssetRevokeVO dataProductInfoRevoke(String registrationId) {
        ShuhanResponse<DataAssetRevokeVO> dataProductRevokeResponse = dataProductApi().dataProductInfoRevoke(DataAssetRevokeVM.builder().registrationId(registrationId).build());
        Assert.isTrue(dataProductRevokeResponse != null && dataProductRevokeResponse.isSuccess(), dataProductRevokeResponse != null ? dataProductRevokeResponse.getMessage() : "数据产品注销失败");
        return dataProductRevokeResponse.getData();
    }

    public DataProductPublishVO dataProductUpdate(DataProductPublishVM updateRequest) {
        ShuhanResponse<DataProductPublishVO> dataProductUpdateResponse = dataProductApi().dataProductUpdate(updateRequest);
        Assert.isTrue(dataProductUpdateResponse != null && dataProductUpdateResponse.isSuccess(), dataProductUpdateResponse != null ? dataProductUpdateResponse.getMessage() : "数据产品上架更新失败");
        return dataProductUpdateResponse.getData();
    }

    public DataAssetSavedVO dataResourceRegistry(DataResourceSaveVM registryRequest) {
        ShuhanResponse<DataAssetSavedVO> dataRegistResponse = dataResourceApi().dataResourceRegistry(registryRequest);
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMessage() : "数据资源登记失败");
        return dataRegistResponse.getData();
    }

    public DataAssetUpdateVO dataResourceRegistryUpdate(DataResourceUpdateVM updateRequest) {
        ShuhanResponse<DataAssetUpdateVO> dataRegistResponse = dataResourceApi().dataResourceRegistryUpdate(updateRequest);
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMessage() : "数据资源更新失败");
        return dataRegistResponse.getData();
    }

    public DataAssetRevokeVO dataResourceRegistryRevoke(String registrationId) {
        ShuhanResponse<DataAssetRevokeVO> dataRegistResponse = dataResourceApi().dataResourceRegistryRevoke(DataAssetRevokeVM.builder().registrationId(registrationId).build());
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMessage() : "数据资源注销失败");
        return dataRegistResponse.getData();
    }

    public SuccessResponse<List<JSONObject>> dataCatalogQuery(CatalogQueryVM catalogQuery) {
        ShuhanResponse<JSONObject> dataCatalogQueryResponse = dataItemApi().catalogQuery(catalogQuery);
        Assert.isTrue(dataCatalogQueryResponse != null && dataCatalogQueryResponse.isSuccess(), dataCatalogQueryResponse != null ? dataCatalogQueryResponse.getMessage() : "数据目录检索失败");
        return SuccessResponse.success(dataCatalogQueryResponse.getData().get("data"))
                .total(dataCatalogQueryResponse.getData().containsKey("pagination") ? dataCatalogQueryResponse.getData().getJSONObject("pagination").getLong("total") : 0)
                .page(Page.of(
                        dataCatalogQueryResponse.getData().containsKey("pagination") ? dataCatalogQueryResponse.getData().getJSONObject("pagination").getLong("page") : 0,
                        dataCatalogQueryResponse.getData().containsKey("pagination") ? dataCatalogQueryResponse.getData().getJSONObject("pagination").getLong("size") : 0
                ))
                .build();
    }

    public ResolutionResponseDataResource dataResourceDetail(String resourceId) {
        ShuhanResponse<ResolutionResponseDataResource> dataResource = dataResourceApi().regionNodeResolutionDataResource(ResolutionRequest.builder().uid(resourceId).build());
        Assert.isTrue(dataResource != null && dataResource.isSuccess(), dataResource != null ? dataResource.getMessage() : "数据资源标识解析失败");
        return dataResource.getData();
    }

    public ResolutionResponseDataProduct dataProductDetail(String productId) {
        ShuhanResponse<ResolutionResponseDataProduct> dataProduct = dataProductApi().regionNodeResolutionDataProduct(ResolutionRequest.builder().uid(productId).build());
        Assert.isTrue(dataProduct != null && dataProduct.isSuccess(), dataProduct != null ? dataProduct.getMessage() : "数据产品标识解析失败");
        return dataProduct.getData();
    }

    public FormStatusQueryResponse formStatusQuery(FormStatusQuery formStatusQuery) {
        ShuhanResponse<FormStatusQueryResponse> formStatusQueryResponse = dataItemApi().formStatusQuery(formStatusQuery);
        Assert.isTrue(formStatusQueryResponse != null && formStatusQueryResponse.isSuccess(), formStatusQueryResponse != null ? formStatusQueryResponse.getMessage() : "获取业务状态失败");
        return formStatusQueryResponse.getData();
    }
}
