package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.restclient.BufferingClientHttpResponseWrapper;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.constant.OpenApiConstant;
import com.dbapp.rest.openapi.AppToken;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.dbapp.rest.utils.DasApiUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.slf4j.MDC;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.support.RestClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.io.File;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.Function;

import static com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig.acceptsUntrustedCertsHttpClient;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.deliveryModeMapping;

/**
 * 枢纽（数瀚）接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HubShuHanApiClient {
    private final ObjectMapper objectMapper;

    private static AppToken appToken = new AppToken();

    /**
     * @see Company
     */
    private static final ThreadLocal<Company> companyThreadLocal = new ThreadLocal<>();

    public static void setCurrentCompany(Company currentCompany) {
        companyThreadLocal.set(currentCompany);
    }

    private RestClient restClient() {
        URL url = URLUtil.url(getShuHanServer().getUrl());
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        try {
            requestFactory.setHttpClient(acceptsUntrustedCertsHttpClient());
        } catch (Exception ignore) {
        }
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter(objectMapper);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(List.of(
                MediaType.APPLICATION_JSON, new MediaType("application", "*+json"),
                MediaType.TEXT_PLAIN
        ));
        return RestClient.builder()
                .requestFactory(requestFactory)
                .messageConverters(httpMessageConverters -> httpMessageConverters.addFirst(mappingJackson2HttpMessageConverter))
                .baseUrl(String.format("%s://%s", url.getProtocol(), url.getAuthority()))
                .requestInterceptor((request, body, execution) -> {
                    if (log.isDebugEnabled() && !"/gateway/file-center-service/api/pub/remote/ljqUploadFile".equals(request.getURI().getPath())) {
                        log.debug("枢纽(数瀚)接口 {} 请求体 {}", request.getURI(), new String(body, Charset.defaultCharset()));
                    }
                    if ("/gateway/file-center-service/api/pub/remote/ljqUploadFile".equals(request.getURI().getPath())) {
                        request.getHeaders().add("Authorization", "Bearer " + appToken.getToken());
                        return execution.execute(request, body);
                    }
                    long timestamp = System.currentTimeMillis();
                    NodeDTO.HubInfo server = getShuHanServer();
                    String appKey = server.getAk();
                    String appSecret = server.getSk();
                    request.getHeaders().add("routerId", getRouterId());
                    request.getHeaders().add(OpenApiConstant.APPID, appKey);
                    request.getHeaders().add("appKey", appKey);
                    request.getHeaders().add(OpenApiConstant.TIMESTAMP, String.valueOf(timestamp));
                    String sign;
                    if (!"/gateway/auth/api/route/openapi/token".equals(request.getURI().getPath())) {
                        String nonce = DasApiUtil.generateRandomString(6);
                        String token = appToken(timestamp);
                        request.getHeaders().add(OpenApiConstant.TOKEN, token);
                        request.getHeaders().add(OpenApiConstant.NONCE, nonce);
                        sign = DasApiUtil.getSHA256Hash(
//                                (HttpMethod.GET.equals(request.getMethod()) ?
                                DasApiUtil.concatSignString(request.getURI().getQuery())
//                    :
//                                DasApiUtil.concatSignString(body2map(body))
//                        )
                                        + timestamp + token + nonce);
                    } else {
                        // 生成token需要的sign
                        sign = DasApiUtil.getSHA256Hash(timestamp + appKey + appSecret);
                    }
                    request.getHeaders().add(OpenApiConstant.SIGN, sign);
                    ClientHttpResponse response = execution.execute(request, body);
                    BufferingClientHttpResponseWrapper httpResponseWrapper = new BufferingClientHttpResponseWrapper(response);
                    String responseBody = IOUtils.toString(httpResponseWrapper.getBody(), Charset.defaultCharset());
                    try {
                        JsonNode jsonNode = JacksonUtils.readTree(responseBody);
                        if (log.isDebugEnabled()) {
                            if ("/gateway/shuhan-business-service/api/inner/industry-dict/getClassifyAll".equals(request.getURI().getPath()) ||
                                    "/gateway/shuhan-business-service/api/inner/data-product/page".equals(request.getURI().getPath()) ||
                                    "/gateway/shuhan-business-service/api/inner/data-resource/page".equals(request.getURI().getPath()) ||
                                    "/gateway/shuhan-business-service/api/inner/dataResource/dataCatalogQuery".equals(request.getURI().getPath())
                            ) {
                                log.debug("数瀚接口响应 success: {}, code: {}, message: {}, dataSize: {}", jsonNode.get("success"), jsonNode.get("code"),
                                        jsonNode.get("message"), jsonNode.get("data") != null && jsonNode.get("data").get("total") != null ? jsonNode.get("data").get("total") : 0);
                            } else {
                                log.debug("数瀚接口响应 {}", jsonNode);
                            }
                        }
                        if (Boolean.FALSE.equals(jsonNode.get("success").asBoolean())) {
                            // 接口请求返回非成功时清理本地缓存的token
                            appToken = new AppToken();
                        }
                    } catch (Exception e) {
                        log.warn("数瀚响应体解析失败 {}", responseBody, e);
                        appToken = new AppToken();
                    }
                    return httpResponseWrapper;
                })
                .build();
    }

    protected NodeDTO.HubInfo getShuHanServer() {
        if (LoginContextHolder.isLogin()) {
            return LoginContextHolder.currentUser().getCompany().getServiceNode().getHubInfo();
        }
        return JSONUtil.toBean(MDC.get("hubInfo"), NodeDTO.HubInfo.class);
    }

    protected String getRouterId() {
        if (LoginContextHolder.isLogin()) {
            return LoginContextHolder.currentUser().getCompany().getNodeId();
        }
        return MDC.get("currentNodeId");
    }

    private Map<String, String> body2map(byte[] body) {
        JsonNode jsonNode = JacksonUtils.readTree(new String(body, Charset.defaultCharset()));
        Map<String, String> map = new HashMap<>();
        jsonNode.fieldNames().forEachRemaining(field -> map.put(field, jsonNode.get(field).asText()));
        return map;
    }

    private synchronized String appToken(Long timestamp) {
        //判断token是否在有效期内
        if (Objects.nonNull(appToken.getExpireTime()) && appToken.getExpireTime() - 5000 > timestamp) {
            return appToken.getToken();
        }
        ShuhanResponse<AppToken> appTokenShuhanResponse = dataItemApi().token();
        Assert.isTrue(appTokenShuhanResponse.isSuccess(), appTokenShuhanResponse.getMsg());
        System.out.println(appTokenShuhanResponse);
        appToken = appTokenShuhanResponse.getData();
        log.debug("数瀚开放接口 token 更新");
        return appToken.getToken();
    }

    private static HubShuHanDataItemApi hubShuHanDataItemApi;

    private synchronized HubShuHanDataItemApi dataItemApi() {
        if (!Objects.isNull(hubShuHanDataItemApi)) {
            return hubShuHanDataItemApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubShuHanDataItemApi = factory.createClient(HubShuHanDataItemApi.class);
        return hubShuHanDataItemApi;
    }

    private static HubShuHanDataProductApi hubShuHanDataProductApi;

    private synchronized HubShuHanDataProductApi dataProductApi() {
        if (!Objects.isNull(hubShuHanDataProductApi)) {
            return hubShuHanDataProductApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubShuHanDataProductApi = factory.createClient(HubShuHanDataProductApi.class);
        return hubShuHanDataProductApi;
    }

    private static HubShuHanDataResourceApi hubShuHanDataResourceApi;

    private synchronized HubShuHanDataResourceApi dataResourceApi() {
        if (!Objects.isNull(hubShuHanDataResourceApi)) {
            return hubShuHanDataResourceApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubShuHanDataResourceApi = factory.createClient(HubShuHanDataResourceApi.class);
        return hubShuHanDataResourceApi;
    }

    private static HubShuHanServiceNodeApi hubShuHanServiceNodeApi;

    private synchronized HubShuHanServiceNodeApi serviceNodeApi() {
        if (!Objects.isNull(hubShuHanServiceNodeApi)) {
            return hubShuHanServiceNodeApi;
        }
        RestClientAdapter adapter = RestClientAdapter.create(restClient());
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        hubShuHanServiceNodeApi = factory.createClient(HubShuHanServiceNodeApi.class);
        return hubShuHanServiceNodeApi;
    }

    public Map<String, Map<String, String>> dictionaryItem(List<String> codes) {
        ShuhanResponse<Map<String, Map<String, String>>> dictionaryItemResponse = restClient()
                .get()
                .uri(uriBuilder -> uriBuilder
                        .path("/gateway/auth/api/dictionaryItem/codes")
                        .queryParam("codes[]", codes)
                        .build()
                )
                .retrieve()
                .body(new ParameterizedTypeReference<>() {
                });
        if (dictionaryItemResponse == null || !dictionaryItemResponse.isSuccess()) {
            log.error("获取字典项: {}, {}", codes, JSONUtil.toJsonStr(dictionaryItemResponse));
        }
        Assert.isTrue(dictionaryItemResponse != null && dictionaryItemResponse.isSuccess(), dictionaryItemResponse != null ? dictionaryItemResponse.getMsg() : "获取字典项失败");
        return dictionaryItemResponse.getData();
    }

    public List<IndustryDictVO> getClassify(String id) {
        ShuhanResponse<List<IndustryDictVO>> classifyResponse = dataItemApi().getClassify(id);
        if (classifyResponse == null || !classifyResponse.isSuccess()) {
            log.error("获取行业分类: {}, {}", id, JSONUtil.toJsonStr(classifyResponse));
        }
        Assert.isTrue(classifyResponse != null && classifyResponse.isSuccess(), classifyResponse != null ? classifyResponse.getMsg() : "获取行业分类失败");
        return classifyResponse.getData();
    }

    public List<IndustryDictVO> getAllClassify() {
        ShuhanResponse<List<IndustryDictVO>> classifyResponse = dataItemApi().getClassifyAll();
        if (classifyResponse == null || !classifyResponse.isSuccess()) {
            log.error("获取行业分类: {}", JSONUtil.toJsonStr(classifyResponse));
        }
        Assert.isTrue(classifyResponse != null && classifyResponse.isSuccess(), classifyResponse != null ? classifyResponse.getMsg() : "获取所有行业分类失败");
        return classifyResponse.getData();
    }

    public SuccessResponse<List<CatalogQueryDataResource>> allMarketDataResource(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = new ArrayList<>();
        filters.add(CatalogQueryVM.FilterVM.builder()
                .filterProperty("type")
                .filterOperation("=")
                .filterValue("RESOURCE")
                .build());
        if (StringUtils.hasText(dataAssetQuery.getAssetName())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("resourceName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getAssetName())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getProviderOrg())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("providerName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getProviderOrg())
                    .build());
        }
        if (dataAssetQuery.getSource() != null) {
            Function<SourceType, String> productTypeMapping = (sourceType) -> {
                switch (sourceType) {
                    case FILE -> {
                        return "01";
                    }
                    case API -> {
                        return "02";
                    }
                    case DATABASE -> {
                        return "03";
                    }
                    case null, default -> {
                        return "05";
                    }
                }
            };
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productType")
                    .filterOperation("=")
                    .filterValue(productTypeMapping.apply(dataAssetQuery.getSource()))
                    .build());
        }
        if (dataAssetQuery.getDeliveryModes() != null) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("deliveryMethod")
                    .filterOperation("=")
                    .filterValue(deliveryModeMapping.apply(dataAssetQuery.getDeliveryModes()))
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getIndustry())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industry")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }
        if (!CollectionUtils.isEmpty(dataAssetQuery.getIndustryClassifyList())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industryClassifyList")
                    .filterOperation("in")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }

        ShuhanResponse<IPage<CatalogQueryDataResource>> dataProductPage = dataResourceApi().dataCatalogQuery(
                CatalogQueryVM.builder()
                        .limit(dataAssetQuery.getSize())
                        .offset(dataAssetQuery.getNum())
                        .filters(filters)
                        .build()
        );
        if (dataProductPage == null || !dataProductPage.isSuccess()) {
            log.error("查询数据资源列表: {}, {}", JSONUtil.toJsonStr(dataAssetQuery), JSONUtil.toJsonStr(dataProductPage));
        }
        Assert.isTrue(dataProductPage != null && dataProductPage.isSuccess(), dataProductPage != null ? dataProductPage.getMsg() : "查询数据资源列表失败");
        return SuccessResponse.success(dataProductPage.getData().getRecords())
                .total(dataProductPage.getData().getTotal())
                .page(Page.of(dataProductPage.getData().getCurrent(), dataProductPage.getData().getSize()))
                .build();
    }

    public SuccessResponse<List<CatalogQueryDataProduct>> allMarketDataProductPublished(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = allMarketDataProduct(dataAssetQuery, "PRODUCT_PUBLISH");
        ShuhanResponse<IPage<CatalogQueryDataProduct>> dataProductPage = dataProductApi().dataCatalogQuery(
                CatalogQueryVM.builder()
                        .limit(dataAssetQuery.getSize())
                        .offset(dataAssetQuery.getNum())
                        .filters(filters)
                        .build()
        );
        if (dataProductPage == null || !dataProductPage.isSuccess()) {
            log.error("查询数据产品(已上架)列表: {}, {}", JSONUtil.toJsonStr(dataAssetQuery), JSONUtil.toJsonStr(dataProductPage));
        }
        Assert.isTrue(dataProductPage != null && dataProductPage.isSuccess(), dataProductPage != null ? dataProductPage.getMsg() : "查询数据资源列表失败");
        return SuccessResponse.success(dataProductPage.getData().getRecords())
                .total(dataProductPage.getData().getTotal())
                .page(Page.of(dataProductPage.getData().getCurrent(), dataProductPage.getData().getSize()))
                .build();
    }

    public SuccessResponse<List<CatalogQueryDataProduct>> allMarketDataProduct(DataAssetQuery dataAssetQuery) {
        List<CatalogQueryVM.FilterVM> filters = allMarketDataProduct(dataAssetQuery, "PRODUCT");
        ShuhanResponse<IPage<CatalogQueryDataProduct>> dataProductPage = dataProductApi().dataCatalogQuery(
                CatalogQueryVM.builder()
                        .limit(dataAssetQuery.getSize())
                        .offset(dataAssetQuery.getNum())
                        .filters(filters)
                        .build()
        );
        if (dataProductPage == null || !dataProductPage.isSuccess()) {
            log.error("查询数据产品列表: {}, {}", JSONUtil.toJsonStr(dataAssetQuery), JSONUtil.toJsonStr(dataProductPage));
        }
        Assert.isTrue(dataProductPage != null && dataProductPage.isSuccess(), dataProductPage != null ? dataProductPage.getMsg() : "查询数据资源列表失败");
        return SuccessResponse.success(dataProductPage.getData().getRecords())
                .total(dataProductPage.getData().getTotal())
                .page(Page.of(dataProductPage.getData().getCurrent(), dataProductPage.getData().getSize()))
                .build();
    }

    private List<CatalogQueryVM.FilterVM> allMarketDataProduct(DataAssetQuery dataAssetQuery, String type) {
        List<CatalogQueryVM.FilterVM> filters = new ArrayList<>();
        filters.add(CatalogQueryVM.FilterVM.builder()
                .filterProperty("type")
                .filterOperation("=")
                .filterValue(type)
                .build());
        if (StringUtils.hasText(dataAssetQuery.getAssetName())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getAssetName())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getBusinessPlatformUniqueNo())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("businessPlatformUniqueNo")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getBusinessPlatformUniqueNo())
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getProviderOrg())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("providerName")
                    .filterOperation("like")
                    .filterValue(dataAssetQuery.getProviderOrg())
                    .build());
        }
        if (dataAssetQuery.getSource() != null) {
            Function<SourceType, String> productTypeMapping = (sourceType) -> {
                switch (sourceType) {
                    case FILE -> {
                        return "01";
                    }
                    case API -> {
                        return "02";
                    }
                    case DATABASE -> {
                        return "03";
                    }
                    case null, default -> {
                        return "05";
                    }
                }
            };
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("productType")
                    .filterOperation("=")
                    .filterValue(productTypeMapping.apply(dataAssetQuery.getSource()))
                    .build());
        }
        if (dataAssetQuery.getDeliveryModes() != null) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("deliveryMethod")
                    .filterOperation("=")
                    .filterValue(deliveryModeMapping.apply(dataAssetQuery.getDeliveryModes()))
                    .build());
        }
        if (StringUtils.hasText(dataAssetQuery.getIndustry())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industry")
                    .filterOperation("=")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }
        if (!CollectionUtils.isEmpty(dataAssetQuery.getIndustryClassifyList())) {
            filters.add(CatalogQueryVM.FilterVM.builder()
                    .filterProperty("industryClassifyList")
                    .filterOperation("in")
                    .filterValue(dataAssetQuery.getIndustry())
                    .build());
        }

        return filters;
    }

    public SuccessResponse<List<ServiceNodeListVO>> serviceNodeList(Integer page, Integer size) {
        String routerId = LoginContextHolder.currentUser().getCompany().getNodeId();
        ServiceNodeReq serviceNodeReq = ServiceNodeReq.builder().routerId(routerId).build();
        ShuhanResponse<IPage<ServiceNodeListVO>> shuhanResponse = serviceNodeApi().serviceNodeList(new PageRequest<>(page, null, size, null, serviceNodeReq));
        if (shuhanResponse == null || !shuhanResponse.isSuccess()) {
            log.error("查询业务节点列表: page:{} size:{}, {}", page, size, JSONUtil.toJsonStr(shuhanResponse));
        }
        Assert.isTrue(shuhanResponse != null && shuhanResponse.isSuccess(), shuhanResponse != null ? shuhanResponse.getMsg() : "查询业务节点列表失败");
        List<ServiceNodeListVO> serviceNodeListVOS = shuhanResponse.getData().getRecords();
        return SuccessResponse.success(serviceNodeListVOS)
                .total(shuhanResponse.getData().getTotal())
                .page(Page.of(shuhanResponse.getData().getCurrent(), shuhanResponse.getData().getSize()))
                .build();
    }

    public void serviceNodeApply(ServiceNodeApplyReq serviceNodeApplyReq) {
        String routerId = LoginContextHolder.currentUser().getCompany().getNodeId();
        serviceNodeApplyReq.setRouterId(routerId);
        serviceNodeApi().serviceNodeApply(serviceNodeApplyReq);
    }

    public SuccessResponse<List<ServiceNodeApplyListVO>> serviceNodeApplyList(ServiceNodeApplyListReq serviceNodeApplyListReq, Integer page, Integer size) {
        String routerId = LoginContextHolder.currentUser().getCompany().getNodeId();
        serviceNodeApplyListReq.setRouterId(routerId);
        ShuhanResponse<IPage<ServiceNodeApplyListVO>> shuhanResponse = serviceNodeApi().serviceNodeApplyList(new PageRequest<>(page, null, size, null, serviceNodeApplyListReq));
        if (shuhanResponse == null || !shuhanResponse.isSuccess()) {
            log.error("查询业务节点已申请列表: {} page:{} size:{}, {}", JSONUtil.toJsonStr(serviceNodeApplyListReq), page, size, JSONUtil.toJsonStr(shuhanResponse));
        }
        Assert.isTrue(shuhanResponse != null && shuhanResponse.isSuccess(), shuhanResponse != null ? shuhanResponse.getMsg() : "查询业务节点已申请列表失败");
        List<ServiceNodeApplyListVO> serviceNodeApplyListVOS = shuhanResponse.getData().getRecords();
        return SuccessResponse.success(serviceNodeApplyListVOS)
                .total(shuhanResponse.getData().getTotal())
                .page(Page.of(shuhanResponse.getData().getCurrent(), shuhanResponse.getData().getSize()))
                .build();
    }

    public DataAssetSavedVO dataProductInfoRegist(DataProductSaveVM registRequest) {
        ShuhanResponse<DataAssetSavedVO> dataRegistResponse = dataProductApi().dataProductInfoRegist(registRequest);
        if (dataRegistResponse == null || !dataRegistResponse.isSuccess()) {
            log.error("数据产品登记失败: {}, {}", JSONUtil.toJsonStr(registRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据产品登记失败");
        return dataRegistResponse.getData();
    }

    public DataAssetUpdateVO dataProductInfoUpdate(DataProductUpdateVM updateRequest) {
        ShuhanResponse<DataAssetUpdateVO> dataProductInfoUpdate = dataProductApi().dataProductInfoUpdate(updateRequest);
        if (dataProductInfoUpdate == null || !dataProductInfoUpdate.isSuccess()) {
            log.error("数据产品更新失败: {}, {}", JSONUtil.toJsonStr(updateRequest), JSONUtil.toJsonStr(dataProductInfoUpdate));
        }
        Assert.isTrue(dataProductInfoUpdate != null && dataProductInfoUpdate.isSuccess(), dataProductInfoUpdate != null ? dataProductInfoUpdate.getMsg() : "数据产品更新失败");
        return dataProductInfoUpdate.getData();
    }

    public DataAssetRevokeVO dataProductInfoRevoke(String registrationId) {
        ShuhanResponse<DataAssetRevokeVO> dataProductRevokeResponse = dataProductApi().dataProductInfoRevoke(DataAssetRevokeVM.builder().registrationId(registrationId).build());
        if (dataProductRevokeResponse == null || !dataProductRevokeResponse.isSuccess()) {
            log.error("数据产品注销失败: {}, {}", registrationId, JSONUtil.toJsonStr(dataProductRevokeResponse));
        }
        Assert.isTrue(dataProductRevokeResponse != null && dataProductRevokeResponse.isSuccess(), dataProductRevokeResponse != null ? dataProductRevokeResponse.getMsg() : "数据产品注销失败");
        return dataProductRevokeResponse.getData();
    }

    public DataProductPublishVO dataProductPublish(DataProductPublishVM publishRequest) {
        ShuhanResponse<DataProductPublishVO> dataProductPublishResponse = dataProductApi().dataProductPublish(publishRequest);
        if (dataProductPublishResponse == null || !dataProductPublishResponse.isSuccess()) {
            log.error("数据产品发布失败: {}, {}", JSONUtil.toJsonStr(publishRequest), JSONUtil.toJsonStr(dataProductPublishResponse));
        }
        Assert.isTrue(dataProductPublishResponse != null && dataProductPublishResponse.isSuccess(), dataProductPublishResponse != null ? dataProductPublishResponse.getMsg() : "数据产品发布失败");
        return dataProductPublishResponse.getData();
    }

    public DataProductPublishVO dataProductUpdate(DataProductPublishVM updateRequest) {
        ShuhanResponse<DataProductPublishVO> dataProductUpdateResponse = dataProductApi().dataProductUpdate(updateRequest);
        if (dataProductUpdateResponse == null || !dataProductUpdateResponse.isSuccess()) {
            log.error("数据产品上架更新失败: {}, {}", JSONUtil.toJsonStr(updateRequest), JSONUtil.toJsonStr(dataProductUpdateResponse));
        }
        Assert.isTrue(dataProductUpdateResponse != null && dataProductUpdateResponse.isSuccess(), dataProductUpdateResponse != null ? dataProductUpdateResponse.getMsg() : "数据产品上架更新失败");
        return dataProductUpdateResponse.getData();
    }

    public DataAssetUpdateVO dataProductUnpublish(Long launchId) {
        ShuhanResponse<DataAssetUpdateVO> dataProductUnpublishResponse = dataProductApi().dataProductUnpublish(DataProductUnPublishVM.builder().launchId(launchId).build());
        if (dataProductUnpublishResponse == null || !dataProductUnpublishResponse.isSuccess()) {
            log.error("数据产品下架失败: {}, {}", launchId, JSONUtil.toJsonStr(dataProductUnpublishResponse));
        }
        Assert.isTrue(dataProductUnpublishResponse != null && dataProductUnpublishResponse.isSuccess(), dataProductUnpublishResponse != null ? dataProductUnpublishResponse.getMsg() : "数据产品下架失败");
        return dataProductUnpublishResponse.getData();
    }

    public DataAssetSavedVO dataResourceRegistry(DataResourceSaveVM registryRequest) {
        ShuhanResponse<DataAssetSavedVO> dataRegistResponse = dataResourceApi().dataResourceRegistry(registryRequest);
        if (dataRegistResponse == null || !dataRegistResponse.isSuccess()) {
            log.error("数据资源登记失败: {}, {}", JSONUtil.toJsonStr(registryRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据资源登记失败");
        return dataRegistResponse.getData();
    }

    public DataAssetUpdateVO dataResourceRegistryUpdate(DataResourceUpdateVM updateRequest) {
        ShuhanResponse<DataAssetUpdateVO> dataRegistResponse = dataResourceApi().dataResourceRegistryUpdate(updateRequest);
        if (dataRegistResponse == null || !dataRegistResponse.isSuccess()) {
            log.error("数据资源更新失败: {}, {}", JSONUtil.toJsonStr(updateRequest), JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据资源更新失败");
        return dataRegistResponse.getData();
    }

    public DataAssetRevokeVO dataResourceRegistryRevoke(String registrationId) {
        ShuhanResponse<DataAssetRevokeVO> dataRegistResponse = dataResourceApi().dataResourceRegistryRevoke(DataAssetRevokeVM.builder().registrationId(registrationId).build());
        if (dataRegistResponse == null || !dataRegistResponse.isSuccess()) {
            log.error("数据资源注销失败: {}, {}", registrationId, JSONUtil.toJsonStr(dataRegistResponse));
        }
        Assert.isTrue(dataRegistResponse != null && dataRegistResponse.isSuccess(), dataRegistResponse != null ? dataRegistResponse.getMsg() : "数据资源注销失败");
        return dataRegistResponse.getData();
    }

    public SuccessResponse<List<JSONObject>> dataCatalogQuery(CatalogQueryVM catalogQuery) {
        ShuhanResponse<IPage<JSONObject>> dataCatalogQueryResponse = dataItemApi().catalogQuery(catalogQuery);
        if (dataCatalogQueryResponse == null || !dataCatalogQueryResponse.isSuccess()) {
            log.error("数据目录检索失败: {}, {}", JSONUtil.toJsonStr(catalogQuery), JSONUtil.toJsonStr(dataCatalogQueryResponse));
        }
        Assert.isTrue(dataCatalogQueryResponse != null && dataCatalogQueryResponse.isSuccess(), dataCatalogQueryResponse != null ? dataCatalogQueryResponse.getMsg() : "数据目录检索失败");
        return SuccessResponse.success(dataCatalogQueryResponse.getData().getRecords())
                .total(dataCatalogQueryResponse.getData().getTotal())
                .page(Page.of(dataCatalogQueryResponse.getData().getCurrent(), dataCatalogQueryResponse.getData().getSize()))
                .build();
    }

    public String uploadAttachFile(File attachFile) {
        ResponseEntity<ShuhanResponse<String>> entity;
        try (HttpEntity httpEntity = MultipartEntityBuilder.create()
                .setContentType(ContentType.MULTIPART_FORM_DATA)
                .addBinaryBody("file", attachFile, ContentType.APPLICATION_PDF, URLEncoder.encode(attachFile.getName(), StandardCharsets.UTF_8))
                .build()) {
            entity = restClient().post()
                    .uri("/gateway/file-center-service/api/pub/remote/ljqUploadFile")
                    .contentType(MediaType.valueOf(httpEntity.getContentType()))
                    .body(httpEntity::writeTo)
                    .retrieve()
                    .toEntity(new ParameterizedTypeReference<ShuhanResponse<String>>() {
                    });
            log.debug("上传附件到数瀚响应: {}", entity);
            ShuhanResponse<String> uploadAttachFile = entity.getBody();
            return uploadAttachFile.getData();
        } catch (Exception e) {
            log.warn("上传附件到数瀚失败", e);
            return null;
        }
    }

    public static void main(String[] args) {
        RestClient restClient = RestClient.builder()
                .baseUrl("http://**************:8032")
                .build();

        Path attachFile = Paths.get("C:\\Users\\<USER>\\Documents\\在线任务Flink Jar使用指南.pdf");
        ResponseEntity<ShuhanResponse<String>> entity;
        try (HttpEntity httpEntity = MultipartEntityBuilder.create()
                .setContentType(ContentType.MULTIPART_FORM_DATA)
                .addBinaryBody("file", attachFile.toFile(), ContentType.APPLICATION_PDF, URLEncoder.encode(attachFile.toFile().getName(), StandardCharsets.UTF_8))
                .build()) {
            entity = restClient.post()
                    .uri("/gateway/file-center-service/api/pub/remote/ljqUploadFile")
                    .contentType(MediaType.valueOf(httpEntity.getContentType()))
                    .header("Authorization", "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJzaHVoYW5hZG1pbjoiLCJleHAiOjE3NDEzOTU5NDN9.ckiXOWiRY7mvBm9WjzOn1YngSSUcDFUEYbcW5hfNZUoXiwmJaYWpptEDDKYxX9a3w98Se-FxjEv9m1MHxICxsg")
                    .body(httpEntity::writeTo)
                    .retrieve()
                    .toEntity(new ParameterizedTypeReference<ShuhanResponse<String>>() {
                    });
            ShuhanResponse<String> uploadAttachFile = entity.getBody();
            System.out.println(JSONUtil.toJsonStr(uploadAttachFile));
        } catch (Exception e) {
            log.warn("上传附件到数瀚失败", e);
        }
    }
}
