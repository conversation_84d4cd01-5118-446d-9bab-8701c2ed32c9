package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataAssetRevokeVO {

    @ApiModelProperty(value = "执行结果状态 0撤销成功 1执行失败")
    private String revokeFlag;
}
