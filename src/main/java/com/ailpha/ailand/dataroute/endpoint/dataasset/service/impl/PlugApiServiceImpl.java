package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import com.ailpha.ailand.dataroute.endpoint.common.enums.PluginApiTypeEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.PluginApiTypeRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.PlugApiService;
import com.ailpha.ailand.dataroute.endpoint.entity.PluginApiType;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: sunsas.yu
 * @date: 2024/11/27 10:46
 * @Description:
 */
@Service
@AllArgsConstructor
public class PlugApiServiceImpl implements PlugApiService {

    private final PluginApiTypeRepository pluginApiTypeRepository;

    @Override
    public List<PluginApiType> getPlugApiList(PluginApiTypeEnums type) {
        return pluginApiTypeRepository.findAllByType(type);
    }
}
