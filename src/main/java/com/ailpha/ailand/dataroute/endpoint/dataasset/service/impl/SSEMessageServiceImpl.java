package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.*;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SSEMessageFormatUtils;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.SSEMessageRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageListRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.SSEMessageService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.SSEMessageVO;
import com.ailpha.ailand.dataroute.endpoint.order.constants.OrderStatus;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.vo.request.OrderCreateReq;
import com.ailpha.ailand.dataroute.endpoint.sse.entity.QSSEMessageRecord;
import com.ailpha.ailand.dataroute.endpoint.sse.entity.SSEMessageRecord;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @author: yuwenping
 * @date: 2024/11/16 14:01
 * @Description:
 */
@Service
@Slf4j
@AllArgsConstructor
public class SSEMessageServiceImpl implements SSEMessageService {

    private final SSEMessageRecordRepository sseMessageRecordRepository;

    private final RouterService routerService;

    // 存储每个用户的 Sink
    private final ConcurrentHashMap<String, Tuple2<String, Sinks.Many<String>>> userSinks = new ConcurrentHashMap<>();

    private final JPAQueryFactory queryFactory;

    @Override
    public Flux<String> streamNotifications(String uid) {
        UserDTO iUserDTO = LoginContextHolder.currentUser();
        String userId = iUserDTO.getId();
        Tuple2<String, Sinks.Many<String>> existSinkTuple = userSinks.get(userId);

        if (existSinkTuple != null) {
            // 已存在连接，则关闭老连接
            log.info("用户[{}],[old uid:{}],[new uid:{}]已存在连接", userId, existSinkTuple.first, uid);
            existSinkTuple.second.tryEmitComplete();
        }

        Sinks.Many<String> sink = Sinks.many().multicast().directBestEffort();
        userSinks.put(userId, new Tuple2<>(uid, sink));
        log.info("用户[{}]:[{}]已连接: ", userId, uid);

        // 定期发送心跳
        SSEMessageVO sseMessageVO = new SSEMessageVO();
        sseMessageVO.setMessage("heartbeat");
        sseMessageVO.setType(SSEMessageTypeEnum.HEARTBEAT);
        Flux<String> heartbeatFlux = Flux.interval(Duration.ofSeconds(20))
                .filter(tick -> userSinks.get(userId) != null)
                .map(tick -> Base64.getEncoder()
                        .encodeToString(JSONUtil.toJsonStr(sseMessageVO).getBytes(StandardCharsets.UTF_8))); // 心跳消息

        // 当连接关闭时，移除对应的 Sink
        return Flux.merge(sink.asFlux(), heartbeatFlux).doFinally(signalType -> {
            Tuple2<String, Sinks.Many<String>> tuple2 = userSinks.get(userId);
            if (uid.equals(tuple2.first)) {
                sink.tryEmitError(new RuntimeException("服务已断开"));
                userSinks.remove(userId);
                log.info("用户[{}]已断开连接: ", userId);
            }
        });
    }

    @Override
    public void logSinkInfo() {
        log.info("当前连接用户: {}", userSinks.keySet());
    }

    private final EndpointRemote endpointRemote;
    private final CompanyService companyService;

    @Override
    public void notifyDataRouteMessage(List<SSEMessageRequest> sseMessageRequests, String clientNo) {
        log.debug("发送消息[{}]到对应路由器[{}]", sseMessageRequests, clientNo);
        MDC.put(SystemConstants.ROUTE_ID, clientNo);

        ApiResponse<Void> receiveDataRouteMessage = endpointRemote.receiveDataRouteMessage(sseMessageRequests);
        if (!receiveDataRouteMessage.isSuccess()) {
            log.error("发送通知到数据器失败：{}", receiveDataRouteMessage);
        }
    }

    @Override
    @Transactional
    public void receiveDataRouteMessage(List<SSEMessageRequest> sseMessageRequests) {
        log.debug("收到路由器推送消息：{}", sseMessageRequests);
        for (SSEMessageRequest sseMessageRequest : sseMessageRequests) {
            SSEMessageRecord sseMessageRecord = new SSEMessageRecord();

            BeanUtil.copyProperties(sseMessageRequest, sseMessageRecord);
            sseMessageRecord.setCreateTime(new Date());
            sseMessageRecord.setReadStatus(SSEMessageReadStatus.UNREAD);
            sseMessageRecordRepository.saveAndFlush(sseMessageRecord);

            try {
                sendNotification(sseMessageRecord);

                sseMessageRecord.setSendTime(new Date());
                sseMessageRecord.setUpdateTime(new Date());
                sseMessageRecordRepository.save(sseMessageRecord);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    @Override
    public void sendNotification(String userId, String message) {
        Tuple2<String, Sinks.Many<String>> tuple2 = userSinks.get(userId);
        if (tuple2 != null) {
            Sinks.Many<String> sink = tuple2.second;
            // 将消息编码为 Base64, 避免中文乱码出现
            String base64Message = Base64.getEncoder().encodeToString(message.getBytes(StandardCharsets.UTF_8));
            // 尝试推送消息
            sink.tryEmitNext(base64Message); // 可选择处理失败情况
        } else {
            log.error("用户[{}]未连接: ", userId);
        }
    }

    @Override
    @Transactional
    public void sendNotification(SSEMessageRecord sseMessageRecord) {
        SSEMessageVO sseMessageVO = new SSEMessageVO();
        sseMessageVO.setId(sseMessageRecord.getId());
        sseMessageVO.setMessage(sseMessageRecord.getMessage());
        sseMessageVO.setDataId(sseMessageRecord.getDataId());
        sseMessageVO.setType(sseMessageRecord.getType());
        sseMessageVO.setUserId(sseMessageVO.getUserId());
        Long unreadCount = sseMessageRecordRepository.countByUserIdAndReadStatus(sseMessageVO.getUserId(),
                SSEMessageReadStatus.UNREAD);
        sseMessageVO.setCount(unreadCount.intValue());
        sseMessageVO.setSendTime(new Date());
        // 发送
        sendNotification(sseMessageRecord.getUserId(), JSONUtil.toJsonStr(sseMessageVO));
    }

    @Override
    public void markNotice(Long id, SSEMessageReadStatus status) {
        if (SSEMessageReadStatus.UNREAD.equals(status)) {
            throw new RestfulApiException("不允许的修改状态类型：" + status);
        }

        if (id != null) {
            SSEMessageRecord sseMessageRecord = sseMessageRecordRepository.findById(id)
                    .orElseThrow(() -> new RestfulApiException("未查询到该消息记录"));
            sseMessageRecord.setReadStatus(status);
            sseMessageRecordRepository.saveAndFlush(sseMessageRecord);
            return;
        }

        List<SSEMessageRecord> all = sseMessageRecordRepository.findAllByReadStatus(SSEMessageReadStatus.UNREAD);
        all.forEach(item -> item.setReadStatus(status));
        sseMessageRecordRepository.saveAllAndFlush(all);

    }

    @Override
    @Transactional
    public void mockData(String message) {
        SSEMessageRequest sseMessageRequest = new SSEMessageRequest();
        sseMessageRequest.setType(SSEMessageTypeEnum.DATA_ASSET_APPROVAL);
        String formatMessage = SSEMessageFormatUtils.formatMessage(SSEMessageTypeEnum.DATA_ASSET_APPROVAL, "xxxx",
                TraderRoleEnums.BUYER, "某某订单", "某企业", MeteringWayEnums.PER_TIME, 1, ChargingWayEnums.PREPAID);
        sseMessageRequest.setMessage(formatMessage);
        sseMessageRequest.setUserId("test");
        sseMessageRequest.setDataId("testDataId");

        notifyDataRouteMessage(Collections.singletonList(sseMessageRequest), "test");
    }

    @Override
    public ApiResponse<List<SSEMessageRecord>> messageList(SSEMessageListRequest sseMessageListRequest) {
        UserDTO iUserDTO = LoginContextHolder.currentUser();
        String currentUserId = iUserDTO.getId();
        Page page = Page.of(sseMessageListRequest.getNum(), sseMessageListRequest.getSize());

        QSSEMessageRecord qsseMessageRecord = QSSEMessageRecord.sSEMessageRecord;
        JPAQuery<SSEMessageRecord> jpaQuery = queryFactory.selectFrom(qsseMessageRecord);
        BooleanBuilder booleanBuilder = new BooleanBuilder(qsseMessageRecord.userId.eq(currentUserId));
        if (ObjectUtil.isNotNull(sseMessageListRequest.getStatus())) {
            booleanBuilder.and(qsseMessageRecord.readStatus.eq(sseMessageListRequest.getStatus()));
        }
        if (ObjectUtil.isNotNull(sseMessageListRequest.getId())) {
            booleanBuilder.and(qsseMessageRecord.id.eq(sseMessageListRequest.getId()));
        }
        if (ObjectUtil.isNotNull(sseMessageListRequest.getDataId())) {
            booleanBuilder.and(qsseMessageRecord.dataId.eq(sseMessageListRequest.getDataId()));
        }
        Long count = jpaQuery.select(qsseMessageRecord.count()).from(qsseMessageRecord).where(booleanBuilder).fetchOne();

        List<SSEMessageRecord> resList = jpaQuery.select(qsseMessageRecord).where(booleanBuilder)
                .orderBy(qsseMessageRecord.createTime.desc())
                .offset(page.getOffset()).limit(sseMessageListRequest.getSize()).fetch();
        SuccessResponse<List<SSEMessageRecord>> response = SuccessResponse.success(resList).build();
        response.setTotal(count);
        response.setPage(sseMessageListRequest);
        return response;
    }

    @Override
    public void totalCountMessage() {
        UserDTO iUserDTO = LoginContextHolder.currentUser();
        String userId = iUserDTO.getId();
        // 新建连接时发送消息
        Long unReadCount = sseMessageRecordRepository.countByUserIdAndReadStatus(userId, SSEMessageReadStatus.UNREAD);
        if (unReadCount > 0) {
            SSEMessageVO sseMessageVOFirst = new SSEMessageVO();
            String firstMessage = SSEMessageFormatUtils.formatTotalMessage(unReadCount.intValue());
            sseMessageVOFirst.setMessage(firstMessage);
            sseMessageVOFirst.setCount(unReadCount.intValue());
            sseMessageVOFirst.setType(SSEMessageTypeEnum.TOTAL_COUNT);
            sendNotification(userId, JSONUtil.toJsonStr(sseMessageVOFirst));
        }
    }

    private final DataHubRemoteService dataHubRemoteService;

    @Override
    public void notifyOrderApplyMessage(List<OrderApprovalRecord> orderApprovalRecords, OrderCreateReq orderCreateReq) {
        log.debug("创建订单，发送消息");
        try {
            NodeInfoResponse nodeInfoResponse = routerService.currentNode();

            Map<String, List<OrderApprovalRecord>> approverClientMap = orderApprovalRecords.stream().collect(Collectors.groupingBy(OrderApprovalRecord::getApproverRouterId));
            for (Map.Entry<String, List<OrderApprovalRecord>> entry : approverClientMap.entrySet()) {
                // 相对应的 client 发送消息，该client有多个消息，将会一起发送
                String clientId = entry.getKey();
                List<OrderApprovalRecord> value = entry.getValue();
                List<SSEMessageRequest> sseMessageRequests = new ArrayList<>();
                for (OrderApprovalRecord orderApprovalRecord : value) {
                    String fmt = fmtOrder(orderApprovalRecord);
                    String assertApprovalNo = orderApprovalRecord.getId();
                    SSEMessageRequest sseMessageRequest = new SSEMessageRequest();
                    sseMessageRequest.setType(SSEMessageTypeEnum.DATA_ASSET_APPROVAL);
                    sseMessageRequest.setUserId(orderApprovalRecord.getApproverId());
                    sseMessageRequest.setTraderRoleType(TraderRoleEnums.SELLER);
                    sseMessageRequest.setMessage(SSEMessageFormatUtils.formatMessage
                            (SSEMessageTypeEnum.DATA_ASSET_APPROVAL, assertApprovalNo, TraderRoleEnums.SELLER, orderApprovalRecord.getAssetName(), LoginContextHolder.currentUser().getCompany().getOrganizationName(),
                                    orderCreateReq.getMeteringWay(), fmt, orderCreateReq.getChargingWay()));
                    sseMessageRequest.setDataId(assertApprovalNo);
                    JSONObject ext = new JSONObject();
                    DataAssetListRequest dataAssetListRequest = new DataAssetListRequest();
                    dataAssetListRequest.setDataAssetIds(List.of(orderApprovalRecord.getAssetId()));
                    CommonResult<List<DataAssetDTO>> dataAssets = dataHubRemoteService.dataAssets(dataAssetListRequest);
                    Assert.isTrue(dataAssets.isSuccess(), "查询数据资产失败");
                    ext.set("dataAssetType", dataAssets.getData().getFirst().getType());
                    sseMessageRequest.setExt(ext.toString());
                    sseMessageRequests.add(sseMessageRequest);
                }
                notifyDataRouteMessage(sseMessageRequests, clientId);
            }
        } catch (Exception e) {
            log.error("创建订单操作发送消息失败：", e);
        }
    }

    @Override
    public void notifyOrderAuditMessage(OrderApprovalRecord orderApprovalRecord, OrderStatus updateStatus) {
        log.debug("审批订单，发送消息");
        try {
            String beneficiaryId = orderApprovalRecord.getBeneficiaryId();
            List<SSEMessageRequest> sseMessageRequests = new ArrayList<>();
            String assertApprovalNo = orderApprovalRecord.getId();
            SSEMessageTypeEnum type = ofOrderStatus(updateStatus);
            SSEMessageRequest sseMessageRequest = new SSEMessageRequest();
            sseMessageRequest.setType(type);
            sseMessageRequest.setUserId(beneficiaryId);
            sseMessageRequest.setTraderRoleType(TraderRoleEnums.BUYER);

            String fmt = fmtOrder(orderApprovalRecord);
            sseMessageRequest.setMessage(SSEMessageFormatUtils.formatMessage(type, assertApprovalNo,
                    TraderRoleEnums.BUYER, orderApprovalRecord.getAssetName(), LoginContextHolder.currentUser().getCompany().getOrganizationName(),
                    orderApprovalRecord.getMeteringWay(), fmt, orderApprovalRecord.getChargingWay()));
            sseMessageRequest.setDataId(assertApprovalNo);
            sseMessageRequests.add(sseMessageRequest);
            notifyDataRouteMessage(sseMessageRequests, orderApprovalRecord.getBeneficiaryRouterId());
        } catch (Exception e) {
            log.error("审批操作发送消息失败：", e);
        }
    }

    private String fmtOrder(OrderApprovalRecord orderApprovalRecord) {
        if (orderApprovalRecord == null) {
            return "";
        }
        if (StringUtils.equals("按次", orderApprovalRecord.getMeteringWay())) {
            return "" + orderApprovalRecord.getAllowance().intValue() + "次";
        } else {
            final String formatDate = DateUtil.format(orderApprovalRecord.getExpireDate(),
                    DatePattern.NORM_DATETIME_PATTERN);
            return StringUtils.startsWith(formatDate, "9999") ? "永不过期" : formatDate;
        }
    }

    @Override
    public Object unReadCount() {
        UserDTO iUserDTO = LoginContextHolder.currentUser();
        String userId = iUserDTO.getId();
        return sseMessageRecordRepository.countByUserIdAndReadStatus(userId, SSEMessageReadStatus.UNREAD);
    }

    private SSEMessageTypeEnum ofOrderStatus(OrderStatus updateStatus) {
        if (updateStatus.equals(OrderStatus.APPROVED)) {
            return SSEMessageTypeEnum.DATA_ASSET_APPROVED;
        }
        if (updateStatus.equals(OrderStatus.REJECTED)) {
            return SSEMessageTypeEnum.DATA_ASSET_REFUSED;
        }
        if (updateStatus.equals(OrderStatus.TERMINATED)) {
            return SSEMessageTypeEnum.DATA_ASSET_TERMINATE;
        }
        return null;
    }
}
