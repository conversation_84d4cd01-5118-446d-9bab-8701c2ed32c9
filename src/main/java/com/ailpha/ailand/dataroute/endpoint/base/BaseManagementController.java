package com.ailpha.ailand.dataroute.endpoint.base;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.dataprope.service.DataProbeService;
import com.ailpha.ailand.dataroute.endpoint.dataprope.vo.DataProbeReportVO;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.StatisticRemote;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/admin/base")
@RequiredArgsConstructor
@Tag(name = "基础能力管理", description = "基础能力管理相关接口")
public class BaseManagementController {

    private final StatisticRemote statisticRemote;
    private final BaseCapabilityManager baseCapabilityManager;
    private final DataProbeService dataProbeService;

    @GetMapping("/statistic/by-delivery-mode")
    @Operation(summary = "按交付模式 统计数据资产和场景交付数据")
    public SuccessResponse<PrivatePlatformRsp> statistic1(
            @Parameter(required = true, name = "平台类型", description = "tee mpc")
            @RequestParam String type) {
        CommonResult<PrivatePlatformRsp> result = statisticRemote.privatePlatformStat(type);
        Assert.isTrue(result.isSuccess(), result.getMsg());
        return SuccessResponse.success(result.getData()).build();
    }

    @GetMapping("statistic/by-source-type")
    @Operation(summary = "按接入方式 统计数据资产数据")
    public SuccessResponse<DataAssetStatisticRsp> statistic2() {
        CommonResult<DataAssetStatisticRsp> result = statisticRemote.statisticBySourceType();
        Assert.isTrue(result.isSuccess(), result.getMsg());
        return SuccessResponse.success(result.getData()).build();
    }

    @GetMapping("configs")
    @Operation(summary = "所有基础能力平台配置")
    public SuccessResponse<Map<BaseCapabilityType, BaseCapabilityConfig>> baseCapabilityConfig() {
        return ApiResponse.success(baseCapabilityManager.getCapabilityConfig()).build();
    }

    @GetMapping("config")
    @Operation(summary = "基础能力平台配置")
    public SuccessResponse<BaseCapabilityConfig> baseCapabilityConfig(@RequestParam(required = false) BaseCapabilityType type) {
        return ApiResponse.success(baseCapabilityManager.getCapabilityConfig(type)).build();
    }

    @PostMapping("config")
    @Operation(summary = "基础能力平台配置更新")
    public SuccessResponse<BaseCapabilityConfig> baseCapabilityConfig(@RequestBody BaseCapabilityConfig config) {
        baseCapabilityManager.updateCapabilityConfig(config.getType(), config);
        return ApiResponse.success(baseCapabilityManager.getCapabilityConfig(config.getType())).build();
    }

    @GetMapping("/data-probe/report")
    @Operation(summary = "数据探查报告")
    public SuccessResponse<DataProbeReportVO> dataProbeReport() {
        DataProbeReportVO dataProbeReportVO = dataProbeService.report();
        return SuccessResponse.success(dataProbeReportVO).build();
    }

    private final GatewayWebApi gatewayWebApi;

    @GetMapping("apiGate")
    @Operation(summary = "API网关")
    public SuccessResponse<ApiGateResponse> apiGate() {
        return SuccessResponse.success(gatewayWebApi.getReportData()).build();
    }
}
