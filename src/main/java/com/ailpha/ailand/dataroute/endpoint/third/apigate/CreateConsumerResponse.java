package com.ailpha.ailand.dataroute.endpoint.third.apigate;

public class CreateConsumerResponse extends GatewayResponse<Object> {
//    @Data
//    @NoArgsConstructor
//    @FieldDefaults(level = AccessLevel.PRIVATE)
//    public static class InvokeResultWrapper {
        // NOTE {
        //    "code": "200",
        //    "message": "success",
        //    "success": true,
        //    "data": {},
        //    "requestId": "6cccfc6f-5886-4d76-a8ec-0e9488044ebc",
        //    "requestTotalCost": 74
        //}
//    }
}
