package com.ailpha.ailand.dataroute.endpoint.connector.remote.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RouterHeartBeatRequest extends BaseRemoteRequest {

    String platformId;

    String platformIp;

    BigDecimal cpu;

    BigDecimal memory;
}
