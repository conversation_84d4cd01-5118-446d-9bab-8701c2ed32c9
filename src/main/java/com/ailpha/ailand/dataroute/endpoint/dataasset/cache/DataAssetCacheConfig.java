package com.ailpha.ailand.dataroute.endpoint.dataasset.cache;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import lombok.RequiredArgsConstructor;
import org.ehcache.PersistentUserManagedCache;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.builders.UserManagedCacheBuilder;
import org.ehcache.config.units.EntryUnit;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.core.spi.service.LocalPersistenceService;
import org.ehcache.impl.config.persistence.DefaultPersistenceConfiguration;
import org.ehcache.impl.config.persistence.UserManagedPersistenceContext;
import org.ehcache.impl.persistence.DefaultLocalPersistenceService;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.File;

@Component
@RequiredArgsConstructor
public class DataAssetCacheConfig {

    private final AiLandProperties aiLandProperties;

    @Bean(destroyMethod = "close")
    public PersistentUserManagedCache<String, String> dataAssetCache() {
        LocalPersistenceService persistenceService = new DefaultLocalPersistenceService(new DefaultPersistenceConfiguration(new File(aiLandProperties.getFileStorage().getBasePath(), "data-asset-cache")));
        return UserManagedCacheBuilder.newUserManagedCacheBuilder(String.class, String.class)
                .with(new UserManagedPersistenceContext<>("data-asset-cache", persistenceService))
                .withResourcePools(ResourcePoolsBuilder.newResourcePoolsBuilder()
                        .heap(200, EntryUnit.ENTRIES)
                        .disk(30, MemoryUnit.MB, true))
                .build(true);
    }
}
