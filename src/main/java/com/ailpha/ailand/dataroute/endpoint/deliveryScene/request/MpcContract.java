package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("mpc合约信息")
public class MpcContract {
    String name;
    List<String> datasets;
    String beneficiaryId;
    @Schema(description = "申请查询数量 合约类型为匿踪查询时必填此参数 ")
    Integer applyQueryAmount = -99;
    Integer memorySize = 2;
    String priority = "MIDDLE";
    String contractType;
    String sceneId;
}
