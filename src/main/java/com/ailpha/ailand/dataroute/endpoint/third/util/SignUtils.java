package com.ailpha.ailand.dataroute.endpoint.third.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SignUtils {
    public static String sign(String payload, String secretKey) {
        // 将请求参数按照参数名的字典序进行排序
        // 在拼接好的字符串末尾加上Secret Key
        // 使用HMAC-SHA256算法对拼接好的字符串进行加密，生成签名
        byte[] hmac = hmacSha256(String.valueOf(payload).getBytes(),
                secretKey.getBytes());
        return Base64.encodeBase64String(hmac);
    }

    private static byte[] hmacSha256(byte[] data, byte[] key) {
        try {
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
            mac.init(secretKeySpec);
            return mac.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException("HmacSHA256 error: " + e.getMessage());
        }
    }

    public static String accessToken(String accessKeyId, String accessKeySecret, String apiId) {
        long accessTime = System.currentTimeMillis();
        String accessSign = sign(apiId + "_" + accessTime, accessKeySecret);
        return accessKeyId + ";" + accessTime + ";" + accessSign;
    }
}
