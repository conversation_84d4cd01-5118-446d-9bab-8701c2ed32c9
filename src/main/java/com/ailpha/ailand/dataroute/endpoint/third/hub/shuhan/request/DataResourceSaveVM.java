package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;


/**
 * 数据产品上下架
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DataResourceSaveVM {

    @ApiModelProperty(value = "资源名称")
    @NotBlank(message = "资源名称不能为空")
    @Length(max = 128, message = "资源名称长度不能超过128")
    private String resourceName;

    /**
     * 数据资源标识码（参照NDI-TR-2025-04编码规则）
     */
    @ApiModelProperty(value = "数据资源标识码（参照NDI-TR-2025-04编码规则）")
    @NotBlank(message = "数据资源标识码（参照NDI-TR-2025-04编码规则）不能为空")
    @Length(max = 255, message = "数据资源标识码（参照NDI-TR-2025-04编码规则）长度不能超过255")
    private String resourceId;

    /**
     * 行业分类（GB/T 4754-2017门类代码）
     */
    @ApiModelProperty(value = "行业分类（GB/T 4754-2017门类代码）")
    @NotBlank(message = "行业分类（GB/T 4754-2017门类代码）不能为空")
    @Length(max = 1, message = "行业分类（GB/T 4754-2017门类代码）长度不能超过1")
    private String industry;

    /**
     * 资源持有方
     */
    @ApiModelProperty(value = "资源持有方")
    @NotBlank(message = "资源持有方不能为空")
    @Length(max = 128, message = "资源持有方长度不能超过128")
    private String resourceOwner;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    @NotBlank(message = "联系人不能为空")
    @Length(max = 10, message = "联系人长度不能超过10")
    private String contacter;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "联系方式")
    @NotBlank(message = "联系方式不能为空")
    @Length(max = 11, message = "联系方式长度不能超过11")
    private String contactInformation;

    /**
     * 资源摘要
     */
    @ApiModelProperty(value = "资源摘要")
    @NotBlank(message = "资源摘要不能为空")
    @Length(max = 1024, message = "资源摘要长度不能超过1024")
    private String resourceAbstract;

    /**
     * 资源格式（如OFD、xlsx、jpg等）
     */
    @ApiModelProperty(value = "资源格式（如OFD、xlsx、jpg等）")
    @NotBlank(message = "资源格式（如OFD、xlsx、jpg等）不能为空")
    @Length(max = 24, message = "资源格式（如OFD、xlsx、jpg等）长度不能超过24")
    private String resourceFormat;

    /**
     * 信息项名称（选填）
     */
    @ApiModelProperty(value = "信息项名称（选填）")
    @Length(max = 128, message = "信息项名称（选填）长度不能超过128")
    private String itemName;

    /**
     * 信息项数据类型（选填）
     */
    @ApiModelProperty(value = "信息项数据类型（选填）")
    @Length(max = 128, message = "信息项数据类型（选填）长度不能超过128")
    private String itemType;

    /**
     * 数据来源：01-原始取得,02-收集取得,03-交易取得
     */
    @ApiModelProperty(value = "数据来源：01-原始取得,02-收集取得,03-交易取得")
    @NotBlank(message = "数据来源：01-原始取得,02-收集取得,03-交易取得不能为空")
    @Length(max = 2, message = "数据来源：01-原始取得,02-收集取得,03-交易取得长度不能超过2")
    private String dataSource;

    /**
     * 是否涉及个人信息：0-否,1-是
     */
    @ApiModelProperty(value = "是否涉及个人信息：0-否,1-是")
    @NotNull(message = "是否涉及个人信息：0-否,1-是不能为空")
    private Integer personalInformation;

    /**
     * 资源状态：01-待登记,02-已登记,03-已撤销
     */
    @ApiModelProperty(value = "资源状态：01-待登记,02-已登记,03-已撤销")
    @NotBlank(message = "资源状态：01-待登记,02-已登记,03-已撤销不能为空")
    @Length(max = 2, message = "资源状态：01-待登记,02-已登记,03-已撤销长度不能超过2")
    private String resourceStatus;

    /**
     * 其他扩展信息（JSON格式）
     */
    @ApiModelProperty(value = "其他扩展信息对象:{\"clientPlatformUniqueNo\":\"数由器唯一标识（登记的时候需要传，溯源）\"}")
    @Length(max = 1000, message = "其他扩展信息（JSON格式）长度不能超过1000")
    private JSONObject others;

    /**
     * 数据版本号，覆盖更新
     */
    @ApiModelProperty(value = "数据版本号，覆盖更新")
    @Length(max = 2, message = "数据版本号，覆盖更新长度不能超过2")
    private String dataVersion;

    @ApiModelProperty(value = "数由器唯一标识（登记的时候需要传，溯源）", hidden = true)
    private String clientPlatformUniqueNo;

}
