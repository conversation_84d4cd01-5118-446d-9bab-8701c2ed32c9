package com.ailpha.ailand.dataroute.endpoint.license;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.dbapp.licence.licsdkjava.bean.dto.ProductSnInfoDTO;
import com.dbapp.licence.licsdkjava.enums.OnlineActiveStatusEnum;
import com.dbapp.licence.licsdkjava.exceptions.LicException;
import com.dbapp.licence.licsdkjava.helper.LocalConfigBO;
import com.dbapp.licence.licsdkjava.helper.SignHandler;
import com.dbapp.licence.licsdkjava.helper.SignHelper;
import com.dbapp.rest.exception.RestfulApiException;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class DasLicenceService {

    private final SignHandler signHandler;

    private final AiLandProperties aiLandProperties;

    private final LocalConfigBO localConfigBO;


    public ProductSnInfoDTO getProductSnInfo() {
        return signHandler.readProductSn();
    }

    public LicenceInfoDTO getLicenceInfo() {
        return getNewLicenceInfo(null);
    }

    public Boolean uploadLicenceFile(InputStream inputStream, UploadLicenseFlag flag) {
        String savePath = writeIntoTempFile(inputStream);
        //先尝试用新的SDK来解释，不能成功再用老的
        LicenceInfoDTO dto = getNewLicenceInfo(savePath);

        if (dto == null && UploadLicenseFlag.NEW_LICENSE.equals(flag)) {
            // 方式一(新 License) 上传的方式二(旧 License)的证书文件
            throw new RestfulApiException("许可证验证失败，请确认您上传的是正确许可证文件");
        }
        return true;
    }

    private String writeIntoTempFile(InputStream inputStream) {
        // 先把文件写入到磁盘
        String uuid = IdUtil.fastUUID();
        String savePath = Paths.get(System.getProperty("java.io.tmpdir"), uuid).toFile().getAbsolutePath();
        FileUtil.touch(savePath);
        log.debug("上传的文件缓存的路径是: {}", savePath);
        FileUtil.writeFromStream(inputStream, savePath);
        return savePath;
    }

    private LicenceInfoDTO getNewLicenceInfo(String path) {
        try {
            String decodeData = null == path ? signHandler.licenceCheckAndParse() : licenceCheckAndParse(path);
            return change2LicenceInfoDTO(decodeData);
        } catch (LicException e) {
            log.warn("new license check and parse error, {}", e.getMessage());
        }
        return null;
    }

    public void backupLicence(HttpServletResponse response) {
        InputStream fileStream;
        String fileName = "backup.lic";
        try {
            fileStream = signHandler.getLicenceFileStream();
        } catch (LicException e) {
            log.error("读取新许可文件失败");
            throw new RestfulApiException("读取新许可文件失败 " + e.getMessage());
        }

        response.addHeader("Content-Disposition", "attachment;filename=" + fileName);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IoUtil.copy(fileStream, outputStream);
        } catch (IOException ioException) {
            log.error("备份许可证失败失败: ", ioException);
            throw new RestfulApiException("备份失败");
        }
    }

    @ToString
    @Getter
    @Setter
    public static class LicenceBaseInfoDTO extends com.dbapp.licence.licsdkjava.bean.dto.LicenceBaseInfoDTO {
        private Date productExpireTime;
    }

    private String licenceCheckAndParse(String uploadLicencePath) {
        if (!FileUtil.exist(uploadLicencePath)) {
            log.error("license path info is empty");
            throw new RestfulApiException("license 不存在");
        }
        String verify;
        try {
            verify = signHandler.licenceCheckAndParseTolerant(uploadLicencePath);
        } catch (LicException e) {
            throw new RestfulApiException("license 校验失败 " + e.getMessage());
        }
        if (StrUtil.isBlank(verify)) {
            log.error("verify info is empty");
            throw new RestfulApiException("license 校验失败");
        }
        LicenceBaseInfoDTO checkInfo = JacksonUtils.json2pojo(verify, LicenceBaseInfoDTO.class);
        String productName = checkInfo.getProductName();
        String machineCode = checkInfo.getMachineCode();
        String localCacheDir = this.localConfigBO.getLocalCacheDir();
        String diskMachineCode = SignHelper.readMachineCode(localCacheDir);

        log.info("license info is :productSn -> {}, machineCode -> {}, productName -> {}, productModel -> {}",
                checkInfo.getProductSN(), machineCode, productName, checkInfo.getProductModel());
        // 不再校验序列号，客户设备可能是烧录的序列号，导致不一致，改为校验产品名
        if (!aiLandProperties.getNewLic().getProductName().equals(productName)) {
            log.error("config productName {} is not equals license productName {}", aiLandProperties.getNewLic().getProductName(), productName);
            throw new RestfulApiException("产品名称校验异常，请联系管理员");
        }
        if (!diskMachineCode.equals(machineCode)) {
            log.error("read machineCode from disk not eq licence, disk -> {}  license -> {}", diskMachineCode, machineCode);
            throw new RestfulApiException("机器码校验异常，请联系管理员");
        }
        String configProductModel = this.localConfigBO.getProductModel();
        String defaultLicencePath;
        if (StrUtil.isNotBlank(configProductModel)) {
            defaultLicencePath = checkInfo.getProductModel();
            if (!configProductModel.equals(defaultLicencePath)) {
                log.error("config productModel {} is not equals license productModel {}", configProductModel, defaultLicencePath);
                throw new RestfulApiException("产品模块校验异常，请联系管理员");
            }
        }

        boolean licenseExpired = new Date().after(checkInfo.getProductExpireTime());
        if (licenseExpired) {
            throw new RestfulApiException("license 已过期");
        }

        defaultLicencePath = localCacheDir + "default.lic";
        if (!uploadLicencePath.equals(defaultLicencePath)) {
            File defaultFile = new File(defaultLicencePath);
            File uploadFile = new File(uploadLicencePath);
            if (!defaultFile.exists() || !SecureUtil.sha256(defaultFile).equals(SecureUtil.sha256(uploadFile))) {
                FileUtil.copy(uploadFile, defaultFile, true);
            }
        }

        return verify;
    }

    private LicenceInfoDTO change2LicenceInfoDTO(String json) {
        json = json.replace("productSN", "productSn");
        NewLicenceInfoDTO newLicenceInfoDTO = JacksonUtils.json2pojo(json, NewLicenceInfoDTO.class);
        LicenceInfoDTO res = new LicenceInfoDTO();
        BeanUtil.copyProperties(newLicenceInfoDTO, res);
        List<NewLicenceInfoDTO.UnitLicenceDTO> unitLicenseList = newLicenceInfoDTO.getUnitLicenseList();
        //找到关闭的菜单
        StringBuilder hideMenuList = new StringBuilder();
        //兼容一个单元许可配置了多个
        unitLicenseList.stream().map(NewLicenceInfoDTO.UnitLicenceDTO::getData)
                .forEach(b -> {
                    JsonNode line = JacksonUtils.readTree(b);
                    if (line.has("hideMenuIdList")) {
                        String list = line.get("hideMenuIdList").asText();
                        hideMenuList.append(list);
                    }
                });
        String hideMenu = hideMenuList.toString();
        log.debug("隐藏的菜单ID为: {}", hideMenu);
        if (StringUtils.isNotBlank(hideMenu)) {
            res.setHideMenuList(hideMenu);
        }

        //找到关闭的小菜单
        StringBuilder hidePermsList = new StringBuilder();
        List<String> listData1 = unitLicenseList.stream().map(NewLicenceInfoDTO.UnitLicenceDTO::getData).filter(data -> data.startsWith("hidePermsList")).toList();

        for (String unitLine : listData1) {
            String[] hideListArray = unitLine.split("=");
            if (hideListArray.length > 1) {
                hidePermsList.append(hideListArray[1]);
                hidePermsList.append(",");
            }
        }
        String perms = hidePermsList.toString();
        log.debug("隐藏的小菜单名字为: {}", perms);
        if (StringUtils.isNotBlank(perms)) {
            res.setHidePermsList(perms);
        }

        int maxUserCount;
        List<NewLicenceInfoDTO.UnitLicenceDTO> maxUserCountList = unitLicenseList.stream().filter(data -> data.getData().startsWith("maxUserCount")).toList();
        if (!maxUserCountList.isEmpty()) {
            maxUserCount = maxUserCountList.stream().map(NewLicenceInfoDTO.UnitLicenceDTO::getCount).mapToInt(a -> a).sum();
            res.setMaxUserCount(maxUserCount);
        }
        log.debug("新许可签发结果:{}", res);
        return res;
    }


    public Integer activeOnline(String productSn, String email) throws LicException {
        OnlineActiveStatusEnum statusEnum = signHandler.signOnlineWithProductIdentifyCodeAndEmail(productSn, email);
        return Integer.parseInt(statusEnum.getCode());
    }

    public String downloadOffLineActivationFilePreCheck(String productSn, String email) throws LicException {
        InputStream inputStream = signHandler.genSignOfflineFileWithProductIdentifyCodeStream(productSn, email);
        String tempFile = writeIntoTempFile(inputStream);
        return Paths.get(tempFile).toFile().getName();
    }

    public void downloadOffLineActivationFile(HttpServletResponse response, String fileName) {
        response.addHeader("Content-Disposition", "attachment;filename=offline_package.reqx");
        try (InputStream inputStream = Files.newInputStream(Paths.get(System.getProperty("java.io.tmpdir"), fileName).toFile().toPath());
             ServletOutputStream outputStream = response.getOutputStream()) {
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException ioException) {
            log.error("下载许可离线包失败", ioException);
        }
    }

    public void downloadArtificialPackage(String email, HttpServletResponse response) throws LicException {
        InputStream inputStream = signHandler.genManualSignFileStream(email);
        response.addHeader("Content-Disposition", "attachment;filename=artificial_package.reqx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException ioException) {
            log.error("下载许可手工包失败", ioException);
        }
    }
}
