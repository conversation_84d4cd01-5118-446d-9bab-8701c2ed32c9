package com.ailpha.ailand.dataroute.endpoint.dataasset.deliver;

import lombok.Getter;

/**
 * 用于拼接由连接器提供的接口路径
 */
@Getter
public enum InternalAPIType {
    /**
     * 连接器文件下载接口
     */
    FILE_DOWNLOAD("/data-asset/download/{dataAssetId}"),
    /**
     * 连接器文件查询接口
     */
    FILE_QUERY("/data-asset/query/file/{dataAssetId}"),
    /**
     * 连接器数据库查询接口
     */
    DATABASE_QUERY("/data-asset/query/database/{dataAssetId}");

    private final String path;

    InternalAPIType(String path) {
        this.path = path;
    }

}
