package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan;

import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IPage<T> {
    /**
     * 页面大小
     */
    Long size;
    /**
     * 总记录数
     */
    Long total;
    /**
     * 当前页
     */
    Integer current;
    /**
     * 总页数
     */
    Integer pages;
    /**
     * 记录数
     */
    List<T> records;
}