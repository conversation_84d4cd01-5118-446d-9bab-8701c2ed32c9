package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CompanyListResponse {
    Long id;
    // 组织名称
    @Schema(description = "组织名称")
    String name;
    // 统一信用代码
    @Schema(description = "统一信用代码")
    String creditCode;
    // 营业执照
    @Schema(description = "营业执照")
    String businessLicense;
    String registrationAddress;
    String loginUrl;
    // 接入主体认证状态
    @Schema(description = "接入主体认证状态")
    CompanyStatus status;
    // 法人
    @Schema(description = "法人")
    String legalPerson;
    // 经办人
    @Schema(description = "经办人")
    String agentPerson;
    // 账号状态
    @Schema(description = "账号状态")
    AccountStatus accountStatus;
    String account;
    
}
