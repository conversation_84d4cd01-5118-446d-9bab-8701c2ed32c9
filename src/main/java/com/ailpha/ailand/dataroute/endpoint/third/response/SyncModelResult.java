package com.ailpha.ailand.dataroute.endpoint.third.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 2024/12/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SyncModelResult implements Serializable {
    @ApiModelProperty(value = "模型版本id")
    String modelVersionId;

    @ApiModelProperty(value = "sdkParams")
    String uploadSdkParams;

    @ApiModelProperty(value = "filepath 实际来源就是数由器传入的路径参数")
    String filepath;
}
