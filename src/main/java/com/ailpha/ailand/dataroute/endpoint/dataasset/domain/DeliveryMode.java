package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

/**
 * @Author: luva.hua
 * @Description: 数据资产交付方式
 * @Date: 2024-11-13
 */
public enum DeliveryMode {
    // API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC
    API, FILE_DOWNLOAD, TEE_ONLINE, TEE_OFFLINE, MPC;

    public static DeliveryMode fromName(String name) {
        for (DeliveryMode deliveryMode : values()) {
            if (deliveryMode.name().equalsIgnoreCase(name)) {
                return deliveryMode;
            }
        }
        return null;
    }
}
