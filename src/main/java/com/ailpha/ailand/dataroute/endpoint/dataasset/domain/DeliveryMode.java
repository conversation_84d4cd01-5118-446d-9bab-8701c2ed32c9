package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

/**
 * @Author: luva.hua
 * @Description: 数据资产交付方式
 * @Date: 2024-11-13
 */
public enum DeliveryMode {
    // API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC
    API, FILE_DOWNLOAD,
    TEE, // NOTE: 这个值是为了迎合前端交互添加
    TEE_ONLINE, TEE_MODEL_PREDICT, TEE_MODEL_OPTIMIZE, TEE_OFFLINE,
    MPC,
    // NOTE: 以下MPC_开头仅发布到业务节点时使用
    /**
     * 匿踪查询
     */
    MPC_PRIVATE_INFORMATION_RETRIEVAL,
    /**
     * 隐私求交
     */
    MPC_PRIVATE_SET_INTERSECTION,
    /**
     * 密文计算
     */
    MPC_CIPHER_TEXT_COMPUTE;;

    public static DeliveryMode fromName(String name) {
        for (DeliveryMode deliveryMode : values()) {
            if (deliveryMode.name().equalsIgnoreCase(name)) {
                return deliveryMode;
            }
        }
        return null;
    }
}
