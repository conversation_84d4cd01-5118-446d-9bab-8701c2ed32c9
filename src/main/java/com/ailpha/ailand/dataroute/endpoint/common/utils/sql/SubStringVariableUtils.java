package com.ailpha.ailand.dataroute.endpoint.common.utils.sql;

import com.googlecode.aviator.AviatorEvaluator;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public final class SubStringVariableUtils {

    public static final String SUBSTRING_VARIABLE_PATTERN_STRING = "\\$substr\\(([^\\(\\)\\,]*),([0-9\\s\\-]*)\\)";
    public static final String SUBSTR_LEN_VARIABLE_PATTERN_STRING = "\\$substr\\(([^\\(\\)\\,]*),([0-9\\s\\-]*),([0-9\\s\\-]*)\\)";

    public static final Pattern SUBSTRING_VARIABLE_PATTERN = Pattern.compile(SUBSTRING_VARIABLE_PATTERN_STRING, Pattern.UNIX_LINES);
    public static final Pattern SUBSTR_LEN_VARIABLE_PATTERN = Pattern.compile(SUBSTR_LEN_VARIABLE_PATTERN_STRING, Pattern.UNIX_LINES);
    private static final String SUBSTRING = "string.substring";


    public static String replaceSubLenStrExpression(String sql) throws Exception {
        if (StringUtils.isBlank(sql)) {
            return sql;
        }
        Matcher matcher = SUBSTR_LEN_VARIABLE_PATTERN.matcher(sql);
        while (matcher.find()) {
            String matcherItem = matcher.group();

            String logDateString = matcher.group(1).trim();
            Integer start = Integer.valueOf(matcher.group(2).trim());
            if (logDateString.length() >= 2 &&
                    ((logDateString.charAt(0) == '\'' && logDateString.charAt(logDateString.length() - 1) == '\'')
                            || (logDateString.charAt(0) == '\"' && logDateString.charAt(logDateString.length() - 1) == '\"'))) {
                logDateString = logDateString.substring(1, logDateString.length() - 1);
            }
            if (start < 0) {
                start = start + logDateString.length();
            } else if (start > 0) {
                start--;
            }
            Integer end = start + Integer.valueOf(matcher.group(3).trim());
            if (end < 0) {
                throw new Exception("len is negative");
            }

            StringBuilder variable = new StringBuilder().append(SUBSTRING).append("('")
                    .append(logDateString)
                    .append("',").append(start).append(",").append(end)
                    .append(")");
            String format = null;
            try {
                format = AviatorEvaluator.exec(variable.toString()).toString();
            } catch (Exception e) {
                throw e;
            }
            if (format == null) {
                continue;
            }
            sql = sql.replace(matcherItem, format);
        }
        return sql;
    }


    public static String replaceSubStrExpression(String sql) throws Exception {
        if (StringUtils.isBlank(sql)) {
            return sql;
        }
        Matcher matcher = SUBSTRING_VARIABLE_PATTERN.matcher(sql);

        while (matcher.find()) {
            String matcherItem = matcher.group();

            String logDateString = matcher.group(1).trim();
            if (logDateString.length() >= 2 &&
                    ((logDateString.charAt(0) == '\'' && logDateString.charAt(logDateString.length() - 1) == '\'')
                            || (logDateString.charAt(0) == '\"' && logDateString.charAt(logDateString.length() - 1) == '\"'))) {
                logDateString = logDateString.substring(1, logDateString.length() - 1);
            }
            Integer start = Integer.valueOf(matcher.group(2).trim());
            if (start < 0) {
                start = start + logDateString.length();
            } else if (start > 0) {
                start--;
            }


            StringBuilder variable = new StringBuilder().append(SUBSTRING).append("('")
                    .append(logDateString)
                    .append("',").append(start)
                    .append(")");
            String format = null;
            try {
                format = AviatorEvaluator.exec(variable.toString()).toString();
            } catch (Exception e) {
                throw e;
            }
            if (format == null) {
                continue;
            }
            sql = sql.replace(matcherItem, format);
        }
        return sql;
    }
}
