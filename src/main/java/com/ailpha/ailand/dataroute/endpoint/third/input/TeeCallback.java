package com.ailpha.ailand.dataroute.endpoint.third.input;

import cn.hutool.core.thread.ThreadUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.LocalProductRef;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.LocalProductRefRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.response.SyncDatasetResult;
import com.ailpha.ailand.dataroute.endpoint.third.response.SyncModelResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2024/12/11
 */
@Slf4j
@RestController
@Tag(name = "TEE回调接口")
@RequiredArgsConstructor
@RequestMapping("callback/tee")
public class TeeCallback {

    private final DataAssetPrepareSchedule dataAssetPrepareSchedule;
    private final LocalProductRefRepository localProductRefRepository;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ExecutorJobRunResult {
        boolean success;
        String message;
    }

    @PostMapping("/dataset-sync-result")
    @Operation(summary = "数据集同步回调")
    public void datasetSyncCallback(@RequestBody SyncDatasetResult syncDatasetResult) {
        log.info("收到数据集同步回调: {}", syncDatasetResult);
        LocalProductRef localProductRef = localProductRefRepository.findFirstByProductPlatformId(syncDatasetResult.getAssetId());
        log.info("数据产品本地信息: {}", localProductRef);
        ThreadUtil.execAsync(() -> {
            if (log.isDebugEnabled()) {
                log.debug("TEE数据集同步回调 assetId: {}, result: {}", syncDatasetResult.getAssetId(), syncDatasetResult.getResult());
            }
            TenantContext.setCurrentTenant("tenant_" + localProductRef.getCompanyId());
            dataAssetPrepareSchedule.datasetSyncCallback(syncDatasetResult, "TEE");
        });
    }

    @PostMapping("/model-sync-result")
    @Operation(summary = "模型同步回调")
    public void modelSyncCallback(@RequestBody SyncModelResult modelResult) {
        // TEE 创建 minio 成功后触发回调
        dataAssetPrepareSchedule.runModelTask(modelResult);
    }
}
