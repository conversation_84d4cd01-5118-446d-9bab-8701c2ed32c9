package com.ailpha.ailand.dataroute.endpoint.third.input;

import cn.hutool.core.thread.ThreadUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.response.SyncDatasetResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2024/12/11
 */
@Slf4j
@RestController
@Tag(name = "TEE回调接口")
@RequiredArgsConstructor
@RequestMapping("callback/tee")
public class TeeCallback {

    private final DataAssetPrepareSchedule dataAssetPrepareSchedule;
    private final DataProductService dataProductService;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ExecutorJobRunResult {
        boolean success;
        String message;
    }

    @PostMapping("/dataset-sync-result")
    @Operation(summary = "数据集同步回调")
    public void datasetSyncCallback(@RequestBody SyncDatasetResult syncDatasetResult) {
        ThreadUtil.execAsync(() -> {
            if (log.isDebugEnabled()) {
                log.debug("TEE数据集同步回调 assetId: {}, result: {}", syncDatasetResult.getAssetId(), syncDatasetResult.getResult());
            }
            DataProductVO dataProduct = dataProductService.getDataProductByOldAssetId(syncDatasetResult.getAssetId());
            TenantContext.setCurrentTenant("tenant_" + dataProduct.getProvider().getCompany().getId());
            dataAssetPrepareSchedule.datasetSyncCallback(syncDatasetResult, "TEE");
        });
    }
}
