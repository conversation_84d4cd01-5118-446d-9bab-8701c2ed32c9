package com.ailpha.ailand.dataroute.endpoint.order.remote.request;

import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRequest;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @date 2025/5/8 10:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderConfigDTO extends ServiceNodeRequest {

   String orderIds;

}
