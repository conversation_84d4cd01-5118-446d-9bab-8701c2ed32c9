package com.ailpha.ailand.dataroute.endpoint.servicenode.remote;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.input.TokenBaseRequest;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceNodeInterceptor extends BasePathMatchInterceptor {

    private final List<String> EXCLUDE_GET_TOKEN_URLS = List.of("/ServiceIdentityVerify",
            "/ServiceIdentityVerifyNonce");
    private final List<String> TEMP_LIST = List.of("/**/ljqLogin", "/**/saveDataRouteUser", "/GetEnterpriseInfo");
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        String nodeId = "";
        ServiceNodeRequest serviceNodeRequest = transformBody(request);
        if (LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            nodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        } else {
            if (StringUtils.isEmpty(nodeId)) {
                nodeId = serviceNodeRequest.getCurrentNodeId();
            }
            //todo 优化
            if (StringUtils.isEmpty(nodeId)) {
                nodeId = request.header("nodeId") == null ? ServiceNodeMetaData.fromBase64(request.header("metaData")).getNodeId() : request.header("nodeId");
            }
        }

        NodeDTO.HubInfo hubInfo;
        if (LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().equals(RoleEnums.SUPER_ADMIN)) {
            hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo(LoginContextHolder.currentUser().getCompany().getId());
        } else {
            hubInfo = getHubInfo(request);
        }

        String serviceNodeUrl;
        if (HttpMethod.POST.name().equals(request.method())) {
            // ServiceNodeRequest serviceNodeRequest = transformBody(request);
            Assert.isTrue(ObjectUtil.isNotNull(serviceNodeRequest), "解析业务节点地址异常，请联系管理员");
            serviceNodeUrl = serviceNodeRequest.getUrl();
            if (TEMP_LIST.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()))) {
                BaseRemoteRequest baseRemoteRequest = transformBody2BaseRemoteRequest(request);
                serviceNodeUrl = baseRemoteRequest.getHubInfo().getServiceNodeUrl();
            }
        } else {
            serviceNodeUrl = request.header(SystemConstants.SERVICE_NODE_URL);
        }
        if (StringUtils.isEmpty(serviceNodeUrl) && StringUtils.isNotEmpty(request.header("metaData")))
            serviceNodeUrl = ServiceNodeMetaData.fromBase64(request.header("metaData")).getUrl();

        // TODO 临时修改
        if ("http://***************".equals(serviceNodeUrl)) {
            serviceNodeUrl = "https://***********:4037";
        }

        boolean skipGetToken = EXCLUDE_GET_TOKEN_URLS.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()));
        if (skipGetToken && !StringUtils.contains(serviceNodeUrl, "/gateway/data-route-business-service/api/openapi")) {
            serviceNodeUrl = serviceNodeUrl + "/gateway/data-route-business-service/api/openapi";
        }
        URL url = URLUtil.url(serviceNodeUrl);
        Assert.isTrue(ObjectUtil.isNotNull(url), "解析业务节点地址 " + serviceNodeUrl + " 异常，请联系管理员");
        Request.Builder newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request, BaseCapabilityType.SERVICE_NODE, nodeId, hubInfo, skipGetToken, serviceNodeUrl, "");

        HttpUrl httpUrl = request.url().newBuilder()
                .encodedPath(url.getPath() + request.url().encodedPath())
                .host(url.getHost())
                .port(url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        newRequest.url(httpUrl)
                .method(request.method(), request.body())
                .tag(request.tag());
        if (HttpMethod.GET.name().equals(request.method())) {
            // 数瀚GET请求走鉴权有问题，需要加
            newRequest.header("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        }
        Response response = chain.proceed(newRequest.build());
        return OpenApiHttpUtil.doInterceptForResp(chain, newRequest, response, skipGetToken, hubInfo.getCertificate(), BaseCapabilityType.SERVICE_NODE, BaseCapabilityType.SERVICE_NODE.name(), nodeId, serviceNodeUrl, hubInfo);
    }

    private NodeDTO.HubInfo getHubInfo(Request request) {
        if (StringUtils.equals(request.method(), "POST")) {
            if (request.body() == null)
                return null;
            try {
                Buffer buffer = new Buffer();
                request.body().writeTo(buffer);
                if (EXCLUDE_GET_TOKEN_URLS.stream().anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()))) {
                    return JSONUtil.toBean(buffer.readUtf8(), TokenBaseRequest.class).getHubInfo();
                } else
                    return JSONUtil.toBean(buffer.readUtf8(), BaseRemoteRequest.class).getHubInfo();
            } catch (IOException e) {
                return null;
            }
        } else if (StringUtils.equals(request.method(), "GET")) {
            String hubInfo = request.header("hubInfo");
            if (StringUtils.isEmpty(hubInfo)) {
                return SpringUtil.getBean(CompanyService.class).getHubInfo();
            }
            return JSONUtil.toBean(new String(Base64.getDecoder().decode(hubInfo), StandardCharsets.UTF_8), NodeDTO.HubInfo.class);
        } else
            throw new RestfulApiException("暂不支持该请求类型获取功能节点信息");

    }


    private ServiceNodeRequest transformBody(Request request) {
        if (request.body() == null)
            return null;
        try {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            return JSONUtil.toBean(buffer.readUtf8(), ServiceNodeRequest.class);
        } catch (IOException | JSONException e) {
            return null;
        }
    }

    private BaseRemoteRequest transformBody2BaseRemoteRequest(Request request) {
        if (request.body() == null)
            return null;
        try {
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            return JSONUtil.toBean(buffer.readUtf8(), BaseRemoteRequest.class);
        } catch (IOException | JSONException e) {
            return null;
        }
    }
}
