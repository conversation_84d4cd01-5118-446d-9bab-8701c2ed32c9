package com.ailpha.ailand.dataroute.endpoint.user.remote.response;

import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.enums.IdentityAuthStatus;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UserDetailsResponse {
    @JsonAlias("id")
    String userId;
    String account;
    @JsonAlias("name")
    String realName;
    @JsonAlias("mobile")
    String phone;
    String email;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @Schema(name = "身份认证状态")
    IdentityAuthStatus authStatus;
    String authTime;
    User.DelegateInfo delegateInfo;
}
