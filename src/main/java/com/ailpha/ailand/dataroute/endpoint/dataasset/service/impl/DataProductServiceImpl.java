package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.collector.DatasourceCheckQuery;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.*;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.DataAssetListRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.DataAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.ProviderExt;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.SeparatorEnum;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.GanzhouPublishAttachFile;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.DescribeRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.DescribeRoutesResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.HubGanzhouApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataProductInfoRegist;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataProductInfoUpdate;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.PublishProduct;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.DataProductInfo;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.FileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.*;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.DataCollectorApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import retrofit2.Response;

import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.deliveryModeMapping;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType.STRUCTURED;
import static com.ailpha.ailand.dataroute.endpoint.third.minio.MinioConfig.customHttpClient;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataProductServiceImpl implements DataProductService {

    private final HubGanzhouApiClient hubGanzhouApiClient;

    private final DataAssetMapper dataAssetMapper;

    private final RouterService routerService;

    @Lazy
    private final TraderService traderService;

    private final DataHubRemoteService dataHubRemote;

    private final DataProductRepository dataProductRepository;

    private final FilesStorageServiceImpl filesStorageService;

    private final GatewayWebApi gatewayWebApi;

    private final DataCollectorApi dataCollectorApi;

    @Override
    public DataProductVO getDataAssetById(String dataAssetId) {
        DataProductVO dataAssetVO = getDataProduct(dataAssetId);
        // TODO quick fix
        if (dataAssetVO == null) {
            return DataProductVO.builder().build();
        }
        List<List<String>> dataList = new ArrayList<>();
        try {
            if (!DebugDataSourceEnum.NONE.equals(dataAssetVO.getDebugDataSource()) && StringUtils.isNotBlank(dataAssetVO.getDebugDataPath())) {
                Path debugFilePath = Paths.get(dataAssetVO.getDebugDataPath());
                int hasHeader = 1; // 是否包含表头， 跳过第一行
                try (RandomAccessFile raf = new RandomAccessFile(debugFilePath.toFile(), "r")) {
                    for (int i = 0; i < 10; i++) {
                        //解决中文乱码
                        String rawStr = raf.readLine();
                        if (StringUtils.isBlank(rawStr) || (hasHeader == 1 && i == 0)) {
                            continue;
                        }
                        String line = new String(rawStr.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                        dataList.add(Arrays.asList(line.split(SeparatorEnum.comma.getFieldDelimiter())));
                    }
                }
            }
        } catch (Exception ignore) {
        }
        dataAssetVO.setDataList(dataList);
        return dataAssetVO;
    }

    private final EndpointRemote endpointRemote;
    private final org.ehcache.Cache<String, String> dataAssetCache;
    private final static String DATA_PRODUCT_ROUTER_KEY = "data_product_router_%s";

    @Override
    public DataProductVO getDataProduct(String dataAssetId) {
        Optional<DataProduct> dataProduct = dataProductRepository.findById(dataAssetId);
        Assert.isTrue(dataProduct.isPresent(), "未找到 id 为 " + dataAssetId + " 的数据产品");
        return dataAssetMapper.dataProductToDataProductVO(dataProduct.get());
    }

    @Override
    public DataProductVO getDataProductByOldAssetId(String dataAssetId) {
        DataAssetListRequest request = new DataAssetListRequest();
        request.setDataAssetIds(Collections.singletonList(dataAssetId));
        CommonResult<List<DataAssetDTO>> commonResultAsset = dataHubRemote.dataAssets(request);
        List<DataAssetDTO> list = commonResultAsset.getData();
        if (CollectionUtil.isEmpty(list)) {
            throw new RestfulApiException("未查到资产信息");
        }
        DataAssetDTO assetDTO = list.getFirst();
        if (null == MDC.get("currentNodeId")) {
            MDC.put("currentNodeId", assetDTO.getRouterId());
        }
        return this.getDataProductByDataProductPlatformId(assetDTO.getDataProductPlatformId());
    }

    @Override
    public String getCompanyIdByAssetId(String dataAssetId) {
        DataAssetListRequest request = new DataAssetListRequest();
        request.setDataAssetIds(Collections.singletonList(dataAssetId));
        CommonResult<List<DataAssetDTO>> commonResultAsset = dataHubRemote.dataAssets(request);
        List<DataAssetDTO> list = commonResultAsset.getData();
        if (CollectionUtil.isEmpty(list)) {
            throw new RestfulApiException("未查到资产信息");
        }
        DataAssetDTO assetDTO = list.getFirst();
        String companyId = String.valueOf(assetDTO.getExtraData().getCompanyId());
        if (ObjectUtils.isEmpty(companyId)) {
            log.warn("dataAssetId:{} 未查到companyId!!", dataAssetId);
        }
        return companyId;
    }

    @Override
    public DataProductVO getDataProductByDataProductPlatformId(String dataProductPlatformId) {
        String currentNodeId;
        if (LoginContextHolder.isLogin()) {
            currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        } else {
            currentNodeId = MDC.get("currentNodeId");
            if (StringUtils.isBlank(currentNodeId)) {
                log.error("查询资产详情未设置节点ID：");
                Arrays.stream(ThreadUtil.getStackTrace()).forEach(stackTraceElement -> log.error(stackTraceElement.toString()));
            }
        }
        String targetNodeId;
        String companyId;
        final String tmpCache = dataAssetCache.get(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId));
        if (StringUtils.isEmpty(tmpCache) || tmpCache.contains("null")) {
            DataProductInfo dataResources = hubGanzhouApiClient.getProductInfo(dataProductPlatformId);
            JSONObject others = JSONUtil.parseObj(dataResources.getOthers());
            targetNodeId = others.getStr("routerId");

            companyId = others.getStr("companyId");
            dataAssetCache.put(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId), String.format("%s&%s", targetNodeId, companyId));
        } else {
            targetNodeId = StringUtils.substringBefore(tmpCache, "&");
            companyId = StringUtils.substringAfterLast(tmpCache, "&");
        }
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            // 兼容日志回调情况
            TenantContext.setCurrentTenant("tenant_" + companyId);
            DataProduct dataResource = dataProductRepository.findByDataProductPlatformId(dataProductPlatformId);
            Assert.notNull(dataResource, "未找到 dataProductPlatformId 为 " + dataProductPlatformId + " 的数据产品");
            return dataAssetMapper.dataProductToDataProductVO(dataResource);
        } else {
            MDC.put(SystemConstants.ROUTE_ID, targetNodeId);
            MDC.put("X-Tenant-Schema", "tenant_" + companyId);
            SuccessResponse<DataProductVO> dataAssetSuccessResponse = endpointRemote.dataProductDetail(dataProductPlatformId);
            Assert.isTrue(dataAssetSuccessResponse.isSuccess(), "查询目标连接器详情异常");
            return dataAssetSuccessResponse.getData();
        }
    }

    @Override
    public void downloadAttachFile(String dataProductPlatformId, AttachType attachType, HttpServletResponse response) throws IOException {
        String currentNodeId;
        if (LoginContextHolder.isLogin()) {
            currentNodeId = LoginContextHolder.currentUser().getCompany().getNodeId();
        } else {
            currentNodeId = MDC.get("currentNodeId");
            if (StringUtils.isBlank(currentNodeId)) {
                log.error("查询资产详情未设置节点ID：");
                Arrays.stream(ThreadUtil.getStackTrace()).forEach(stackTraceElement -> log.error(stackTraceElement.toString()));
            }
        }
        String targetNodeId;
        String companyId;
        if (StringUtils.isEmpty(dataAssetCache.get(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId)))) {
            SuccessResponse<List<DataProductInfo>> dataResources = hubGanzhouApiClient.allMarketDataProduct(DataAssetQuery.builder()
                    .assetIds(List.of(dataProductPlatformId))
                    .size(100L).num(1L)
                    .build());
            Assert.isTrue(dataResources.isSuccess() && !CollectionUtils.isEmpty(dataResources.getData()), "未查询到id为 " + dataProductPlatformId + " 的数据产品目录数据");
            JSONObject others = JSONUtil.parseObj(dataResources.getData().getFirst().getOthers());
            targetNodeId = others.getStr("routerId");

            companyId = others.getStr("companyId");
            dataAssetCache.put(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId), String.format("%s&%s", targetNodeId, companyId));
        } else {
            targetNodeId = StringUtils.substringBefore(dataAssetCache.get(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId)), "&");
            companyId = StringUtils.substringAfterLast(dataAssetCache.get(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId)), "&");
        }
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            // 兼容日志回调情况
            TenantContext.setCurrentTenant("tenant_" + companyId);
            DataProduct dataProduct = dataProductRepository.findByDataProductPlatformId(dataProductPlatformId);
            Assert.notNull(dataProduct, "未找到 dataProductPlatformId 为 " + dataProductPlatformId + " 的数据产品");
            downloadAttachFile(dataProduct, attachType, response);
        } else {
            MDC.put(SystemConstants.ROUTE_ID, targetNodeId);
            MDC.put("X-Tenant-Schema", "tenant_" + companyId);
            Response<ResponseBody> attachFileDownload = endpointRemote.dataProductAttachFile(dataProductPlatformId, attachType);
            try (ResponseBody responseBody = attachFileDownload.body()) {
                Assert.isTrue(responseBody != null, "下载附件文件失败");
                ServletOutputStream outputStream = response.getOutputStream();
                response.reset();
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                response.addHeader("Content-Disposition", attachFileDownload.headers().get("Content-Disposition"));
                InputStream inputStream = responseBody.byteStream();
                byte[] bytes = new byte[2048];
                int len;
                while ((len = inputStream.read(bytes)) > 0) {
                    outputStream.write(bytes, 0, len);
                }
                outputStream.close();
            }
        }
    }

    public void downloadAttachFile(DataProduct dataProduct, AttachType attachType, HttpServletResponse response) throws IOException {
        Assert.notNull(dataProduct.getDataExt().getQualificationDoc(), "未找到附件信息");
        BiFunction<QualificationDoc, AttachType, String> fileName = (qualificationDoc, _attachType) -> switch (_attachType) {
            case dataSample -> qualificationDoc.getDataSampleAttach();
            case dataSourceStatement -> qualificationDoc.getDataSourceStatementAttach();
            case safeLevel -> qualificationDoc.getSafeLevelAttach();
            case evaluationReport -> qualificationDoc.getEvaluationReportAttach();
            case complianceAndLegalStatement -> qualificationDoc.getComplianceAndLegalStatementAttach();
            case complianceSelfCheckManual -> qualificationDoc.getComplianceSelfCheckManualAttach();
            case other -> qualificationDoc.getOtherAttach();
        };
        String attachFileName = fileName.apply(dataProduct.getDataExt().getQualificationDoc(), attachType);
        Assert.notNull(attachFileName, "未找到附件信息");
        Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(dataProduct.getUserId())
                .resolve(attachFileName);
        try (InputStream inputStream = Files.newInputStream(attachFilePath)) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.reset();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(attachFileName, StandardCharsets.UTF_8));
            byte[] bytes = new byte[2048];
            int len;
            while ((len = inputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        }
    }

    @Override
    public Long getApprovedCount() {
        return dataProductRepository.count(Example.of(DataProduct.builder()
                .itemStatus("item_status2")
                .userId(LoginContextHolder.currentUser().getId()).build()));
    }

    @Override
    public SuccessResponse<List<DataProductListVO>> allDataAssets(int page, int size, Function<Specification<DataProduct>, Specification<DataProduct>> specificationFunction) {
        Specification<DataProduct> dataProductSpecification = (root, query, cb) -> cb.notEqual(root.get("isDelete"), true);
        if (specificationFunction != null) {
            dataProductSpecification = specificationFunction.apply(dataProductSpecification);
        }
        org.springframework.data.domain.Page<DataProduct> dataProducts;
        PageRequest pageRequest = PageRequest.of((int) page - 1, (int) size, Sort.by(Sort.Direction.DESC, "createTime"));
        dataProducts = dataProductRepository.findAll(dataProductSpecification, pageRequest);
        return SuccessResponse.success(
                        dataProducts.getContent().stream()
                                .peek(dataAsset -> {
                                    String routerName = getRouterName(dataAsset.getProvider().getRouterId());
                                    if (!StringUtils.isEmpty(routerName)) {
                                        dataAsset.getProvider().setRouterName(routerName);
                                    }
                                })
                                .map(dataAssetMapper::dataProductToDataProductListVO)
                                .toList()
                )
                .total(dataProducts.getTotalElements())
                .page(Page.of(page, size))
                .build();
    }

    static final Cache<Object, Object> ROUTER_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .maximumSize(500)
            .initialCapacity(50).build();

    private String getRouterName(String routerId) {
        try {
            String routerName = (String) ROUTER_CACHE.getIfPresent(routerId);
            if (StringUtils.isEmpty(routerName)) {
                CommonResult<List<DrClientInfoVO>> routers = dataHubRemote.routers(RouterListRequest.builder().build());
                for (DrClientInfoVO drClientInfoVO : routers.getData()) {
                    if (StringUtils.isNotBlank(drClientInfoVO.getClientName())) {
                        ROUTER_CACHE.put(drClientInfoVO.getClientNo(), drClientInfoVO.getClientName());
                    }
                }
            }
            return (String) ROUTER_CACHE.getIfPresent(routerId);
        } catch (Exception ignore) {
            return null;
        }
    }

    @Override
    public void download(String dataAssetId, String accessKey, String secretKey, HttpServletResponse response) {
        DataProductVO dataProduct = getDataProduct(dataAssetId);
        DrClientInfoVO router = routerService.getByClientNo(dataProduct.getProvider().getRouterId());
        Assert.notNull(router, "未找到数据资产所在的连接器节点信息");

        String extension = "csv";
        try {
            extension = FileUtil.extName(dataProduct.getFileSourceMetadata().getDataAssetFilePath());
        } catch (Exception ignore) {
        }
        String fileName = dataProduct.getDataProductName() + "." + extension;
        response.reset();
        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(new URI("http://" + router.getClientIp()).getHost(), 9000, true)
                .credentials(accessKey, secretKey)
                .httpClient(customHttpClient())
                .build();
             InputStream inputStream = minioClient.getObject(GetObjectArgs.builder()
                     .bucket(dataProduct.getUserId())
                     .object(Paths.get(dataProduct.getId(), fileName).toString())
                     .build())) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            byte[] bytes = new byte[1024];
            int len;
            while ((len = inputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        } catch (Exception e) {
            throw new RestfulApiException("数据资产文件下载出错", e);
        }
    }

    @Override
    public void updateDataExt(String dataAssetId, Function<DataProduct.DataProductExt, DataProduct.DataProductExt> dataExtUpdate) {
        dataProductRepository.updateDataExt(dataAssetId, dataExtUpdate.apply(dataProductRepository.getReferenceById(dataAssetId).getDataExt()));
    }

    @Override
    public void checkNameExists(String dataProductId, String dataProductName) {
        boolean exists = dataProductRepository.exists(Example.of(DataProduct.builder().dataProductName(dataProductName).isDelete(false).build()));
        Assert.isTrue(!exists, "数据产品名称重复");
        SuccessResponse<List<DataProductInfo>> dataProductByName = hubGanzhouApiClient.allMarketDataProduct(
                DataAssetQuery.builder().assetName(dataProductName).build());
        for (DataProductInfo mayDuplicateName : dataProductByName.getData()) {
            if (dataProductName.equals(mayDuplicateName.getProductName())) {
                throw new RestfulApiException("数据产品名称重复");
            }
        }
    }

    @Override
    public void temporarySave(DataProductRegistRequest request) {
        DataProduct product;
        if (StringUtils.isNotBlank(request.getId())) {
            product = dataProductRepository.getReferenceById(request.getId());
        } else {
            product = dataAssetMapper.dataProductRegistRequestToDataProduct(request);
            product.setProvider(new ProviderExt());
            product.setCreateTime(new Date());
            product.setPushStatus(PushStatus.OFFLINE.getCode());
            product.getDataExt().setPublishStatus("0");
            product.setItemStatus("item_status0");
            UserDTO userDTO = LoginContextHolder.currentUser();
            NodeInfoResponse currentNode = routerService.currentNode();
            fillProvider(product, userDTO, currentNode);
            product.setUserId(userDTO.getId());
            product.setUsername(userDTO.getUsername());
            product.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
        }
        product = product.updateProductNameCNTo(request.getProductNameCN())
                .updateTypeTo(request.getType())
                .updateDataCoverageTime(request.getDataCoverageTimeStart(), request.getDataCoverageTimeEnd())
                .updateIndustryTo(request.getIndustry(), request.getIndustry1())
                .updateRegionTo(request.getRegion(), request.getRegion1())
                .updatePersonalInformationTo(request.getPersonalInformation())
                .updateDescriptionTo(request.getDescription())
                .updateSourceTo(request.getSource())
                .updateScaleTo(request.getDataSize())
                .updateFrequencyTo(request.getUpdateFrequency())
                .updateDeliveryModeTo(CollectionUtils.isEmpty(request.getDeliveryModes()) ? null : request.getDeliveryModes().getFirst())
                .updateDeliveryModesTo(request.getDeliveryModes())
                .updateLimitationsTo(request.getLimitations())
                .updateAuthorizeTo(request.getAuthorize())
                .updateIsSecondaryProcessedTo(request.getIsSecondaryProcessed())
                .updateResourceIdTo(request.getResourceId())
                .updateLineageTo(request.getLineage())
                .updateOtherTo(request.getOther())
                .updateDataSampleAttach(request.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(request.getQualificationDoc())
                .updateDataSourceStatementAttach(request.getQualificationDoc())
                .updateSafeLevelAttach(request.getQualificationDoc())
                .updateEvaluationReportAttach(request.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(request.getQualificationDoc())
                .updateOtherAttach(request.getQualificationDoc())
                .updateDataTypeTo(request.getDataType())
                .updateDataType1To(request.getDataType1())
                .updateAccessWayTo(request.getAccessWay())
                .updateAPIQueryWayTo(request.getApiQueryWay())
                .updateDebugDataSourceTo(request.getDebugDataSource())
                .updateDataSchemaTo(request.getDataSchema())
                .updateSeparatorTo(request.getSeparator())
                .updateCompanyIdTo(String.valueOf(product.getProvider().getCompany().getId()))
                .updateMpcOpenAPIIdIdTo(product.getDataExt().getMpcOpenAPIId())
                .updateHasHeaderTo(request.getHasHeader())
                .updateAPISourceMetadataTo(request.getApiSourceMetadata())
                .updateFileSourceMetadataTo(request.getFileSourceMetadata())
                .updateDatabaseSourceMetadataTo(request.getDatabaseSourceMetadata())
                .updateAiSortMetadataTo(request.getAiSortMetadata())
                .updateExchangePluginIdsTo(request.getExchangePluginIds())
                .updateCertificatePluginIdsTo(request.getCertificatePluginIds())
                .updateMPCOpenAPIIdTo(request.getMpcOpenAPIId())
                .updateMPCPurposeTo(request.getMpcPurpose())
                .updateExtractResponseTo(request.getExtractResponse());
        product.setUpdateTime(new Date());
        dataProductRepository.save(product);
    }

    @Override
    public void registration(DataProductRegistRequest dataProductCreateRequest) {
        UserDTO userDTO = LoginContextHolder.currentUser();
        DataProduct product;
        if (StringUtils.isNotBlank(dataProductCreateRequest.getId())) {
            product = dataProductRepository.getReferenceById(dataProductCreateRequest.getId());
//            Assert.isTrue("item_status0".equals(product.getItemStatus()), "非可登记状态");
        } else {
            product = dataAssetMapper.dataProductRegistRequestToDataProduct(dataProductCreateRequest);
            product.setCreateTime(new Date());
            product.getDataExt().setPublishStatus("0");
            NodeInfoResponse currentNode = routerService.currentNode();
            fillProvider(product, userDTO, currentNode);
            product.setUserId(userDTO.getId());
            product.setUsername(userDTO.getUsername());
            product.setUpdateTime(new Date());
            product.setPushStatus(PushStatus.OFFLINE.getCode());
            product.getDataExt().setCreateIp(IpUtils.getClientIp(ServletUtils.getRequest()));
            product.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
        }
        product.setItemStatus("item_status1");
        product = product.updateProductNameCNTo(dataProductCreateRequest.getProductNameCN())
                .updateTypeTo(dataProductCreateRequest.getType())
                .updateDataCoverageTime(dataProductCreateRequest.getDataCoverageTimeStart(), dataProductCreateRequest.getDataCoverageTimeEnd())
                .updateIndustryTo(dataProductCreateRequest.getIndustry(), dataProductCreateRequest.getIndustry1())
                .updateRegionTo(dataProductCreateRequest.getRegion(), dataProductCreateRequest.getRegion1())
                .updatePersonalInformationTo(dataProductCreateRequest.getPersonalInformation())
                .updateDescriptionTo(dataProductCreateRequest.getDescription())
                .updateSourceTo(dataProductCreateRequest.getSource())
                .updateScaleTo(dataProductCreateRequest.getDataSize())
                .updateFrequencyTo(dataProductCreateRequest.getUpdateFrequency())
                .updateDeliveryModesTo(dataProductCreateRequest.getDeliveryModes())
                .updateDeliveryModeTo(CollectionUtils.isEmpty(dataProductCreateRequest.getDeliveryModes()) ? null : dataProductCreateRequest.getDeliveryModes().getFirst())
                .updateLimitationsTo(dataProductCreateRequest.getLimitations())
                .updateAuthorizeTo(dataProductCreateRequest.getAuthorize())
                .updateIsSecondaryProcessedTo(dataProductCreateRequest.getIsSecondaryProcessed())
                .updateResourceIdTo(dataProductCreateRequest.getResourceId())
                .updateLineageTo(dataProductCreateRequest.getLineage())
                .updateOtherTo(dataProductCreateRequest.getOther())
                .updateDataSampleAttach(dataProductCreateRequest.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(dataProductCreateRequest.getQualificationDoc())
                .updateDataSourceStatementAttach(dataProductCreateRequest.getQualificationDoc())
                .updateSafeLevelAttach(dataProductCreateRequest.getQualificationDoc())
                .updateEvaluationReportAttach(dataProductCreateRequest.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(dataProductCreateRequest.getQualificationDoc())
                .updateOtherAttach(dataProductCreateRequest.getQualificationDoc())
                .updateDataTypeTo(dataProductCreateRequest.getDataType())
                .updateDataType1To(dataProductCreateRequest.getDataType1())
                .updateAccessWayTo(dataProductCreateRequest.getAccessWay())
                .updateAPIQueryWayTo(dataProductCreateRequest.getApiQueryWay())
                .updateDebugDataSourceTo(dataProductCreateRequest.getDebugDataSource())
                .updateDataSchemaTo(dataProductCreateRequest.getDataSchema())
                .updateSeparatorTo(dataProductCreateRequest.getSeparator())
                .updateCompanyIdTo(String.valueOf(product.getProvider().getCompany().getId()))
                .updateMpcOpenAPIIdIdTo(product.getDataExt().getMpcOpenAPIId())
                .updateHasHeaderTo(dataProductCreateRequest.getHasHeader())
                .updateAPISourceMetadataTo(dataProductCreateRequest.getApiSourceMetadata())
                .updateFileSourceMetadataTo(dataProductCreateRequest.getFileSourceMetadata())
                .updateDatabaseSourceMetadataTo(dataProductCreateRequest.getDatabaseSourceMetadata())
                .updateAiSortMetadataTo(dataProductCreateRequest.getAiSortMetadata())
                .updateExchangePluginIdsTo(dataProductCreateRequest.getExchangePluginIds())
                .updateCertificatePluginIdsTo(dataProductCreateRequest.getCertificatePluginIds())
                .updateMPCOpenAPIIdTo(dataProductCreateRequest.getMpcOpenAPIId())
                .updateMPCPurposeTo(dataProductCreateRequest.getMpcPurpose())
                .updateExtractResponseTo(dataProductCreateRequest.getExtractResponse());
        product.getDataExt().setDataVersion(product.getDataExt().getDataVersion() + 1);
        product.getDataExt().setRegistrationSubmitTime(System.currentTimeMillis());

        if (SourceType.API.equals(dataProductCreateRequest.getAccessWay()) || SourceType.FROM_MPC.equals(dataProductCreateRequest.getAccessWay())) {
            product.getDataExt().setApiQueryWay(dataProductCreateRequest.getApiQueryWay());
            if (!ObjectUtils.isEmpty(dataProductCreateRequest.getApiSourceMetadata()) && !ObjectUtils.isEmpty(dataProductCreateRequest.getApiSourceMetadata().getExtend())
                    && dataProductCreateRequest.getApiSourceMetadata().getExtend().getIsBatchParams()) {
                Assert.isTrue(!ObjectUtils.isEmpty(dataProductCreateRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId()), "批量参数文件不存在，请重新上传");
                String batchParamsFilePath = dataAssetCache.get(dataProductCreateRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId());
                Assert.isTrue(batchParamsFilePath != null, "批量参数文件不存在，请重新上传");
                product.getDataExt().getApiSourceMetadata().getExtend().setLocalBatchParamsPath(batchParamsFilePath);
            }
        } else if (SourceType.FILE.equals(dataProductCreateRequest.getAccessWay())) {
            String dataAssetFilePath = dataAssetCache.get(dataProductCreateRequest.getFileSourceMetadata().getDataAssetFileId());
            Assert.isTrue(dataAssetFilePath != null, "数据资产文件不存在，请重新上传");
            Path dataAssetFile = Paths.get(dataAssetFilePath);
            Assert.isTrue(dataAssetFile.toFile().exists(), "数据资产文件不存在，请重新上传");
            if (STRUCTURED.equals(product.getDataExt().getDataType())) {
                String lineValue;
                try {
                    lineValue = FileUtils.readFirstLine(dataAssetFile.toFile());
                } catch (IOException e) {
                    log.warn("读取文件第一行尝试获取文件列数失败", e);
                    throw new RestfulApiException("读取文件第一行尝试获取文件列数失败", e);
                }
                List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
                Assert.isTrue(dataProductCreateRequest.getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
                ).count() == dataSchema.size(), "原始文件列数与数据集描述不一致，请检查是否正确配置");
            }
            product.getDataExt().getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath);
            product.getDataExt().getFileSourceMetadata().setDataAssetFileHash(SM3DigestUtil.getHash(dataAssetFile.toFile()));
        } else if (SourceType.DATABASE.equals(dataProductCreateRequest.getAccessWay())) {
            if (dataProductCreateRequest.getAiSortMetadata() != null) {
                product.getDataExt().getAiSortMetadata().setSourceId(dataProductCreateRequest.getAiSortMetadata().getSourceId());
                product.getDataExt().getAiSortMetadata().setId(dataProductCreateRequest.getAiSortMetadata().getId());
                product.getDataExt().getAiSortMetadata().setTableName(dataProductCreateRequest.getAiSortMetadata().getTableName());
                product.getDataExt().getAiSortMetadata().setTableDesc(dataProductCreateRequest.getAiSortMetadata().getTableDesc());
                product.getDataExt().getAiSortMetadata().setSchemaName(dataProductCreateRequest.getAiSortMetadata().getSchemaName());
            }
            DatasourceCheckQuery query = new DatasourceCheckQuery();
            BeanUtils.copyProperties(dataProductCreateRequest.getDatabaseSourceMetadata(), query);
            query.setTable(dataProductCreateRequest.getDatabaseSourceMetadata().getTableName());
            dataCollectorApi.checkConn(query);
        }
        if (DebugDataSourceEnum.EXTRA_UPLOAD.equals(dataProductCreateRequest.getDebugDataSource())) {
            Assert.isTrue(StringUtils.isNotBlank(dataProductCreateRequest.getTempDebugFileId()), "请上传数据资产调试文件");
            String dataAssetDebugFilePath = dataAssetCache.get(dataProductCreateRequest.getTempDebugFileId());
            Assert.isTrue(dataAssetDebugFilePath != null, "数据资产调试文件不存在，请重新上传");
            Path dataAssetDebugFile = Paths.get(dataAssetDebugFilePath);
            Assert.isTrue(dataAssetDebugFile.toFile().exists(), "数据资产调试文件不存在，请重新上传");
            String lineValue;
            try {
                lineValue = FileUtils.readFirstLine(dataAssetDebugFile.toFile());
            } catch (IOException e) {
                log.warn("读取调试文件第一行尝试获取文件列数失败", e);
                throw new RestfulApiException("读取调试文件第一行尝试获取文件列数失败", e);
            }
            List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
            Assert.isTrue(dataProductCreateRequest.getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
            ).count() == dataSchema.size(), "调试数据文件列数与数据集描述不一致，请检查是否正确配置");
            product.getDataExt().setDebugDataPath(dataAssetDebugFilePath);
            product.getDataExt().setSeparator(ObjectUtils.isEmpty(dataProductCreateRequest.getSeparator()) ? "," : dataProductCreateRequest.getSeparator());
            product.getDataExt().setHasHeader(ObjectUtils.isEmpty(dataProductCreateRequest.getHasHeader()) ? 1 : dataProductCreateRequest.getHasHeader());
        }
        product.setUpdateTime(new Date());
//        product.getDataExt().setPublishStatus("1");
        product.getDataExt().setRegistrationTime(System.currentTimeMillis());
        product.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        product.getDataExt().setRegistrationSubmitTime(System.currentTimeMillis());
        try {
            product = dataProductRepository.save(product);
            if (SourceType.API.equals(dataProductCreateRequest.getAccessWay()) || SourceType.FROM_MPC.equals(dataProductCreateRequest.getAccessWay())) {
                try {
                    // 对接方式：API，创建网关代理
                    CreateRouteResponse serviceAndRouteForExternalAPI = gatewayWebApi.createServiceAndRouteForExternalAPI(product.getId(),
                            product.getDataExt().getApiSourceMetadata().getDataPath(), product.getDataExt().getApiSourceMetadata().getResponse(),
                            product.getDataExt().getApiSourceMetadata().getResponseEcho(),
                            String.format("%s_%s", product.getId(), product.getDataProductName()),
                            product.getId(), product.getDataExt().getApiSourceMetadata().getUrl(), Boolean.TRUE.equals(product.getDataExt().getExtractResponse()));
                    this.updateDataExt(product.getId(), dataProductExt -> {
                        dataProductExt.setGatewayServiceRouteId(serviceAndRouteForExternalAPI.getData().getInvokeResult().getId());
                        return dataProductExt;
                    });
                } catch (Exception e) {
                    if (e.getMessage() != null && e.getMessage().contains("route name exists")) {
                        DescribeRoutesResponse describeRoutesResponse = gatewayWebApi.describeRoutes(product.getId());
                        DataProduct finalDataProduct = product;
                        Optional<DescribeRouteResponse.InvokeResult> route = describeRoutesResponse.getData().getInvokeResult().getRows().stream().filter(_route -> finalDataProduct.getId().equals(_route.getName())).findFirst();
                        if (route.isEmpty()) {
                            return;
                        }
                        this.updateDataExt(product.getId(), dataProductExt -> {
                            dataProductExt.setGatewayServiceRouteId(route.get().getId());
                            return dataProductExt;
                        });
                    } else {
                        log.error("createServiceAndRouteForExternalAPI 失败:", e);
                    }
                }
            }

            JSONObject others = new JSONObject();
            others.set("assetId", product.getId());
            others.set("productNameCN", product.getDataExt().getAssetNameCN());
            others.set("industry1", product.getDataExt().getIndustry1());
            others.set("region1", product.getDataExt().getRegion1());
            others.set("deliveryModes", product.getDeliveryExt().getDeliveryModes());
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            others.set("clientPlatformUniqueNo", company.getNodeId());
            others.set("userId", product.getUserId());
            others.set("username", product.getUsername());
            others.set("organizationName", company.getOrganizationName());
            others.set("routerId", company.getNodeId());
            others.set("platformId", product.getPlatformId());
            others.set("companyId", company.getId());
            others.set("deliveryModes", product.getDeliveryExt().getDeliveryModes());
            others.set("billingMethod", product.getDeliveryExt().getBillingMethod());
            others.set("purchaseUnit", product.getDeliveryExt().getPurchaseUnit());
            others.set("price", product.getDeliveryExt().getPrice());
            product.getDataExt().setQualificationDocShuhan(new QualificationDoc());
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getDataSampleAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getDataSampleAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setDataSampleAttach(shuhanFile.getUrl());
            }
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setComplianceAndLegalStatementAttach(shuhanFile.getUrl());
            }
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setDataSourceStatementAttach(shuhanFile.getUrl());
            }
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getSafeLevelAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getSafeLevelAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setSafeLevelAttach(shuhanFile.getUrl());
            }
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getEvaluationReportAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setEvaluationReportAttach(shuhanFile.getUrl());
            }
            if (StringUtils.isNotBlank(product.getDataExt().getQualificationDoc().getComplianceSelfCheckManualAttach())) {
                Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(product.getUserId())
                        .resolve(product.getDataExt().getQualificationDoc().getComplianceSelfCheckManualAttach());
                FileUploadResponse shuhanFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
                product.getDataExt().getQualificationDocShuhan().setComplianceSelfCheckManualAttach(shuhanFile.getUrl());
            }
//        if (StringUtils.isNotBlank(dataProduct.getDataExt().getQualificationDoc().getOtherAttach() != null) {
//            Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(dataProduct.getUserId())
//                    .resolve(dataProduct.getDataExt().getQualificationDoc().getOtherAttach());
//            String shuhanFile = shuHanApiClient.uploadAttachFile(attachFilePath.toFile());
//            dataProduct.getDataExt().getQualificationDocShuhan().setOtherAttach(shuhanFile);
//        }
            String productCode = hubGanzhouApiClient.dataProductInfoRegist(DataProductInfoRegist.builder()
                    .outerProductId(product.getId())
                    .productType(product.getDataExt().getType())
                    .productName(product.getDataProductName())
                    .validStartTime(product.getDataExt().getDataCoverageTimeStart())
                    .validEndTime(product.getDataExt().getDataCoverageTimeEnd())
                    .industry(product.getIndustry())
                    .productRegion(product.getDataExt().getRegion())
                    .personalInformation(product.getDataExt().getPersonalInformation())
                    .description(product.getDescription())
                    .deliveryMethod(product.getDeliveryExt().getDeliveryMethod() != null ? product.getDeliveryExt().getDeliveryMethod() : (
                            !CollectionUtils.isEmpty(product.getDeliveryExt().getDeliveryModes()) ? deliveryModeMapping.apply(product.getDeliveryExt().getDeliveryModes().getFirst()) : null
                    ))
                    .limitations(product.getDeliveryExt().getLimitations())
                    .authorize(product.getDeliveryExt().getAuthorize())
                    .dataSubject(product.getDataExt().getDataSubject() == null ? "01" : product.getDataExt().getDataSubject())
                    .dataSize(product.getCapacity() == null ? null : String.valueOf(product.getCapacity()))
                    .updateFrequency(product.getUpdateFrequency())
                    .others(JSONUtil.toJsonStr(others))
                    .providerName(company.getOrganizationName())
                    .providerType(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "02" : "03")
                    .entityInformation(JSONUtil.toJsonStr(company))
                    .identityId(userDTO.getIdentityId())
                    .providerDesc(company.getAccessType().getDesc())
                    .operatorName(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeName() : company.getDelegateName())
                    .operatorTelephone(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "" : company.getDelegateContact())
                    .operatorIdCard(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeIdNumber() : company.getDelegateIdNumber())
                    .commission(company.getAuthorizationLetter())
                    .dataSample(product.getDataExt().getQualificationDocShuhan().getDataSampleAttach())
                    .complianceAndLegalStatement(product.getDataExt().getQualificationDocShuhan().getComplianceAndLegalStatementAttach())
                    .dataSourceStatement(product.getDataExt().getQualificationDocShuhan().getDataSourceStatementAttach())
                    .safeLevel(product.getDataExt().getQualificationDocShuhan().getSafeLevelAttach())
                    .evaluationReport(product.getDataExt().getQualificationDocShuhan().getEvaluationReportAttach())
                    .entityId(product.getProvider().getCompany().getEntityId())
//                    .entityId(company.getNodeId().substring(1, 19))
//                    .entityCode(product.getProvider().getCompany().getEntityCode())
                    .entityCode(company.getNodeId().substring(1, 19))
                    .platformId(product.getProvider().getCompany().getNodeId())
                    .resourceCodes(JSONUtil.toJsonStr(List.of(product.getId())))
                    .dpe(product.getProvider().getCompany().getNodeId())
                    .build());
            product.setDataProductPlatformId(productCode);
            dataProductRepository.save(product);
//        if (StringUtils.isNotBlank(dataProductCreateRequest.getId())) {
//            // 登记更新 —— 同步交易中心
//            // 简介、产品类型、行业分类
//            if (baseCapabilityManager.platformEnable(BaseCapabilityType.TRADE_PLATFORM)) {
//                dataHubRemote.updateDataAsset(DataAssetUpdateRequest.builder()
//                        .assetId(product.getId())
//                        .describeMessage(product.getDescription())
//                        .industry(product.getIndustry())
//                        .industry1(product.getDataExt().getIndustry1())
//                        .extraData(JSONUtil.toJsonStr(product.getDeliveryExt()))
//                        .build());
//            }
//        }
        } catch (Exception e) {
            dataProductRepository.deleteById(product.getId());
            throw e;
        }
    }

    @Override
    public void updateRegistration(DataProductRegistUpdateRequest dataProductRegistUpdateRequest) {
        Optional<DataProduct> optionalDataProduct = dataProductRepository.findById(dataProductRegistUpdateRequest.getId());
        Assert.isTrue(optionalDataProduct.isPresent(), "未找到id为 " + dataProductRegistUpdateRequest.getId() + " 的数据产品");
        DataProduct product = optionalDataProduct.get();
//        Assert.isTrue(COULD_UPDATE_REGISTRATION_STATUS.contains(product.getItemStatus()), "非可登记更新状态");
        product = product.updateProductNameCNTo(dataProductRegistUpdateRequest.getProductNameCN())
                .updateTypeTo(dataProductRegistUpdateRequest.getType())
                .updateDataCoverageTime(dataProductRegistUpdateRequest.getDataCoverageTimeStart(), dataProductRegistUpdateRequest.getDataCoverageTimeEnd())
                .updateIndustryTo(dataProductRegistUpdateRequest.getIndustry(), dataProductRegistUpdateRequest.getIndustry1())
                .updateRegionTo(dataProductRegistUpdateRequest.getRegion(), dataProductRegistUpdateRequest.getRegion1())
                .updatePersonalInformationTo(dataProductRegistUpdateRequest.getPersonalInformation())
                .updateDescriptionTo(dataProductRegistUpdateRequest.getDescription())
                .updateSourceTo(dataProductRegistUpdateRequest.getSource())
                .updateScaleTo(dataProductRegistUpdateRequest.getDataSize())
                .updateFrequencyTo(dataProductRegistUpdateRequest.getUpdateFrequency())
                .updateDeliveryModesTo(dataProductRegistUpdateRequest.getDeliveryModes())
                .updateDeliveryModeTo(CollectionUtils.isEmpty(dataProductRegistUpdateRequest.getDeliveryModes()) ? null : dataProductRegistUpdateRequest.getDeliveryModes().getFirst())
                .updateLimitationsTo(dataProductRegistUpdateRequest.getLimitations())
                .updateAuthorizeTo(dataProductRegistUpdateRequest.getAuthorize())
                .updateIsSecondaryProcessedTo(dataProductRegistUpdateRequest.getIsSecondaryProcessed())
                .updateResourceIdTo(dataProductRegistUpdateRequest.getPlatformResourceId())
                .updateLineageTo(dataProductRegistUpdateRequest.getLineage())
                .updateOtherTo(dataProductRegistUpdateRequest.getOther())
                .updateDataSampleAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateDataSourceStatementAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateSafeLevelAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateEvaluationReportAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateOtherAttach(dataProductRegistUpdateRequest.getQualificationDoc());
        product.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        product.setUpdateTime(new Date());
        if ("item_status3".equals(product.getItemStatus())) {
            // 审批拒绝后更新重置状态待审批
            product.setItemStatus("item_status1");
        }
        DataProduct dataProduct = dataProductRepository.save(product);

        if ("item_status2".equals(dataProduct.getItemStatus())) {
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            JSONObject others = new JSONObject();
            others.set("registrationId", dataProduct.getDataProductPlatformId());
            others.set("assetId", dataProduct.getId());
            others.set("productNameCN", dataProduct.getDataExt().getAssetNameCN());
            others.set("industry1", dataProduct.getDataExt().getIndustry1());
            others.set("region1", dataProduct.getDataExt().getRegion1());
            others.set("deliveryModes", dataProduct.getDeliveryExt().getDeliveryModes());
            others.set("clientPlatformUniqueNo", company.getNodeId());
            others.set("userId", product.getUserId());
            others.set("username", product.getUsername());
            others.set("organizationName", company.getOrganizationName());
            others.set("routerId", company.getNodeId());
            others.set("platformId", dataProduct.getPlatformId());
            others.set("companyId", company.getId());
            DataAssetUpdateVO dataAssetUpdateVO = hubGanzhouApiClient.dataProductInfoUpdate(DataProductInfoUpdate.builder()
                    .productCode(dataProduct.getDataProductPlatformId())
                    .outerProductId(dataProduct.getId())
                    .productType(product.getDataExt().getType())
                    .productName(product.getDataProductName())
                    .validStartTime(product.getDataExt().getDataCoverageTimeStart())
                    .validEndTime(product.getDataExt().getDataCoverageTimeEnd())
                    .industry(product.getIndustry())
                    .productRegion(product.getDataExt().getRegion())
                    .personalInformation(product.getDataExt().getPersonalInformation())
                    .description(product.getDescription())
                    .deliveryMethod(product.getDeliveryExt().getDeliveryMethod() != null ? product.getDeliveryExt().getDeliveryMethod() : (
                            !CollectionUtils.isEmpty(product.getDeliveryExt().getDeliveryModes()) ? deliveryModeMapping.apply(product.getDeliveryExt().getDeliveryModes().getFirst()) : null
                    ))
                    .limitations(product.getDeliveryExt().getLimitations())
                    .authorize(product.getDeliveryExt().getAuthorize())
                    .dataSubject(product.getDataExt().getDataSubject() == null ? "01" : product.getDataExt().getDataSubject())
                    .dataSize(product.getCapacity() == null ? null : String.valueOf(product.getCapacity()))
                    .updateFrequency(product.getUpdateFrequency())
                    .others(others.toString())
                    .providerName(company.getOrganizationName())
                    .providerType(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "02" : "03")
                    .entityInformation(JSONUtil.toJsonStr(company))
                    .identityId(company.getCreditCode())
                    .providerDesc(company.getAccessType().getDesc())
                    .operatorName(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeName() : company.getDelegateName())
                    .operatorTelephone(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? null : company.getDelegateContact())
                    .operatorIdCard(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeIdNumber() : company.getDelegateIdNumber())
                    .commission(company.getAuthorizationLetter())
                    .dataSample(product.getDataExt().getQualificationDoc().getDataSampleAttach())
                    .complianceAndLegalStatement(product.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                    .dataSourceStatement(product.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                    .safeLevel(product.getDataExt().getQualificationDoc().getSafeLevelAttach())
                    .evaluationReport(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                    .entityId(company.getNodeId().substring(1, 19)) // TODO
                    .platformId(company.getNodeId()) // TODO
                    .resourceCodes(JSONUtil.toJsonStr(List.of(dataProduct.getDataProductPlatformId()))) // TODO
                    .dpe(company.getThirdBusinessId()) // TODO
                    .build());
            Assert.isTrue("0".equals(dataAssetUpdateVO.getUpdateFlag()), "数据产品登记更新失败");
        }
    }

    @Override
    @Transactional
    public void revokeRegistration(String productId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(productId);
        Assert.notNull(dataProduct, "未找到要撤销登记的数据资产");
        UserDTO userDTO = LoginContextHolder.currentUser();
        Assert.isTrue(userDTO.getId().equals(dataProduct.getUserId()), "无权操作");
        traderService.deleteFileSource(dataProduct.getUserId(), dataProduct.getDataExt().getFileSourceMetadata());
        dataProduct.setItemStatus("item_status4");
        dataProduct.getDataExt().setPublishStatus("4");
        dataProductRepository.saveAndFlush(dataProduct);
        if (StringUtils.isNotBlank(dataProduct.getDataProductPlatformId())) {
            DataAssetRevokeVO revokeFlag = hubGanzhouApiClient.dataProductInfoRevoke(dataProduct.getDataProductPlatformId());
            log.debug("撤销登记数据产品 {}({}) , {}", productId, dataProduct.getDataProductName(), revokeFlag.getRevokeFlag());
        }
        if ("4".equals(dataProduct.getDataExt().getPublishStatus()) && !CollectionUtils.isEmpty(dataProduct.getDataExt().getServiceNodeLaunchIds())) {
            this.unpublish(productId, new ArrayList<>(dataProduct.getDataExt().getServiceNodeLaunchIds().keySet()));
        }
        // NOTE: bug-174558
//        try {
//            DescribeRouteResponse describeRouteResponse = gatewayWebApi.describeRoute(DescribeRouteRequest.builder().id(dataProduct.getDataExt().getGatewayServiceRouteId()).build());
//            DescribeRouteResponse.InvokeResult routeResponseData = describeRouteResponse.getData().getInvokeResult();
//            gatewayWebApi.deleteRoute(DeleteRouteRequest.builder().ids(routeResponseData.getId()).build());
//            gatewayWebApi.deleteService(DeleteServiceRequest.builder().id(routeResponseData.getServiceId()).build());
//            FileUtil.del(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
//        } catch (Exception e) {
//            log.warn("清理数据产品资源出错 {}", e.getMessage());
//        }
    }

    @Override
    public void publishProduct(PublishProduct publishProduct) {
        String productCode = publishProduct.getBizCode();
        DataProduct byDataProductCode = dataProductRepository.findByDataProductPlatformId(productCode);
        publishProduct.setEntityCode(byDataProductCode.getProvider().getCompany().getNodeId().substring(1, 19));
        byDataProductCode.getDataExt().setGanzhouPublishAttachFile(new GanzhouPublishAttachFile());
        if (StringUtils.isNotBlank(publishProduct.getSafetyFile())) {
            Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(byDataProductCode.getUserId())
                    .resolve(publishProduct.getSafetyFile());
            FileUploadResponse ossFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
            byDataProductCode.getDataExt().getGanzhouPublishAttachFile().setSafetyFile(ossFile.getUrl());
            publishProduct.setSafetyFile(ossFile.getOssId());
        }
        if (StringUtils.isNotBlank(publishProduct.getProtectFile())) {
            Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(byDataProductCode.getUserId())
                    .resolve(publishProduct.getProtectFile());
            FileUploadResponse ossFile = hubGanzhouApiClient.fileUpload(attachFilePath.toFile());
            byDataProductCode.getDataExt().getGanzhouPublishAttachFile().setProtectFile(ossFile.getUrl());
            publishProduct.setProtectFile(ossFile.getOssId());
        }
        hubGanzhouApiClient.publishProduct(publishProduct);
        byDataProductCode.getDataExt().setPublishStatus("1");
        byDataProductCode.getDataExt().setPublishTime(System.currentTimeMillis());
        byDataProductCode.getDataExt().setPublishSubmitTime(System.currentTimeMillis());
        byDataProductCode.getDataExt().setPublishUpdateTime(System.currentTimeMillis());
        dataProductRepository.saveAndFlush(byDataProductCode);
    }

    @Override
    public void publish(DataProductPublishRequest dataProductPublishRequest) {
        throw new RestfulApiException("[赣州]连接器无此功能");
    }

    @Override
    public void publishUpdate(DataProductPublishUpdateRequest request) {
        throw new RestfulApiException("[赣州]连接器无此功能");
    }

    @Override
    public void unpublish(String dataProductId, List<String> serviceNodeId) {
        throw new RestfulApiException("[赣州]连接器无此功能");
    }

    @Override
    public Map<String, String> publishToNode(String dataProductId, List<String> serviceNodeId) {
        throw new RestfulApiException("[赣州]连接器无此功能");
    }

    @Override
    public void delete(String dataProductId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
        Assert.isTrue("item_status0".equals(dataProduct.getItemStatus()), "只可删除暂存状态的数据产品");
        dataProductRepository.deleteById(dataProductId);
    }

    private void fillProvider(DataProduct product, UserDTO userDTO, NodeInfoResponse currentNode) {
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        String nodeName = currentNode.getName();
        nodeName = StringUtils.isBlank(nodeName) ? "node_" + company.getOrganizationName() : nodeName;
        product.setPlatformId(currentNode.getPlatformId());
        product.setPlatformType(0);
        if (product.getProvider() == null) {
            product.setProvider(new ProviderExt());
        }
        product.getProvider().setRouterId(company.getNodeId());
        product.getProvider().setRouterName(nodeName);
        product.getProvider().setUsername(userDTO.getUsername());
        product.getProvider().setPhone(userDTO.getPhone());
        product.getProvider().setEmail(userDTO.getEmail());
        product.getProvider().setCompany(company);
    }

    public List<DataSchemaBO> parseDataSchema(String separator, String firstLine) {
        String bom = String.valueOf('\ufeff');
        if (firstLine.startsWith(bom)) {
            firstLine = firstLine.substring(bom.length());
        }
        return Arrays.stream(firstLine.split(separator, -1)).map(
                a -> new DataSchemaBO(a.toLowerCase(), "STRING", null, "safeLevel1", true, false, false, false, a, "STRING")
        ).collect(Collectors.toList());
    }
}
