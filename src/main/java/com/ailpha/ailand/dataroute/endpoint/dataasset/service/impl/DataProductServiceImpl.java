package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.collector.DatasourceCheckQuery;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.utils.*;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorRegionNodeResolutionResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.DatasourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.SeparatorEnum;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataUpdateTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.LocalProductRefRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz.QuartzSchedulerManager;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataUpdateTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.*;
import com.ailpha.ailand.dataroute.endpoint.home.StatisticAssetDTO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.AIGateClient;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.AIGateResponse;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.CreateAssetByRouterRequest;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.CreateAssetByRouterResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.DescribeRouteResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.DescribeRoutesResponse;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.config.AIGateConfig;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DbType;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.FileTypeConstants;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.CatalogQueryVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductPublishVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductUpdateVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetRevokeVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataAssetUpdateVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataProductPublishVO;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.DataAssetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.DataCollectorApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataProductDetailRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.DownloadFileRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.UpdateRouteAssetRequest;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.request.Page;
import com.dbapp.rest.response.SuccessResponse;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import retrofit2.Response;

import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.COULD_UPDATE_REGISTRATION_STATUS;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType.STRUCTURED;
import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType.UNSTRUCTURED;
import static com.ailpha.ailand.dataroute.endpoint.third.minio.MinioConfig.customHttpClient;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataProductServiceImpl implements DataProductService {

    private final HubShuHanApiClient shuHanApiClient;

    private final DataAssetMapper dataAssetMapper;

    private final RouterService routerService;

    @Lazy
    private final TraderService traderService;

    private final AIGateClient aiGateClient;

    private final AIGateConfig aigateConfig;

    private final BaseCapabilityManager baseCapabilityManager;

    private final DataCollectorApi dataCollectorApi;

    private final MPCRemote mpcRemote;

    private final TeeRemote teeRemote;

    private final DataProductRepository dataProductRepository;

    private final GatewayWebApi gatewayWebApi;

    private final FilesStorageServiceImpl filesStorageService;

    private final LocalProductRefRepository localProductRefRepository;

    private final CompanyService companyService;

    private final ServiceNodeRemoteService serviceNodeRemote;

    private final DataUpdateTaskRepository taskRepository;

    private static final Map<String, DbType> DB_TYPE_MAP = new HashMap<>() {
        {
            put("mysql", DbType.MYSQL);
            put("oracle", DbType.ORACLE);
            put("postgresql", DbType.POSTGRESQL);
            put("sqlserver", DbType.SQLSERVER);
            put("hive", DbType.HIVE);
            put("dm", DbType.DM);
            put("odps", DbType.ODPS);
            put("gauss_v5", DbType.GAUSSDB);
        }
    };

    @Override
    public DataProductVO getDataAssetById(String dataAssetId) {
        DataProductVO dataAssetVO = getDataProduct(dataAssetId);
        // TODO quick fix
        if (dataAssetVO == null) {
            return DataProductVO.builder().build();
        }
        List<List<String>> dataList = new ArrayList<>();
        try {
            if (!DebugDataSourceEnum.NONE.equals(dataAssetVO.getDebugDataSource()) && StringUtils.isNotBlank(dataAssetVO.getDebugDataPath())) {
                Path debugFilePath = Paths.get(dataAssetVO.getDebugDataPath());
                int hasHeader = 1; // 是否包含表头， 跳过第一行
                try (RandomAccessFile raf = new RandomAccessFile(debugFilePath.toFile(), "r")) {
                    for (int i = 0; i < 10; i++) {
                        //解决中文乱码
                        String rawStr = raf.readLine();
                        if (StringUtils.isBlank(rawStr) || (hasHeader == 1 && i == 0)) {
                            continue;
                        }
                        String line = new String(rawStr.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
                        dataList.add(Arrays.asList(line.split(SeparatorEnum.comma.getFieldDelimiter())));
                    }
                }
            }
        } catch (Exception ignore) {
        }
        dataAssetVO.setDataList(dataList);
        return dataAssetVO;
    }

    private final EndpointRemote endpointRemote;
    private final org.ehcache.Cache<String, String> dataAssetCache;

    @Override
    public DataProductVO getDataProduct(String dataAssetId) {
        Optional<DataProduct> dataProduct = dataProductRepository.findById(dataAssetId);
        Assert.isTrue(dataProduct.isPresent(), "未找到 id 为 " + dataAssetId + " 的数据产品");
        DataProductVO dataProductVO = dataAssetMapper.dataProductToDataProductVO(dataProduct.get());
        // TODO 待改造
//        SuccessResponse<Map<String, StatisticAssetOrderDeliveryVO>> assetOrderDeliveryResponse = hubOrderRemote.statisticAssetOrderDelivery(Collections.singletonList(dataProductVO.getId()));
//        Map<String, StatisticAssetOrderDeliveryVO> assetOrderDeliveryMap = assetOrderDeliveryResponse.getData();
//        dataProductVO.setStatisticInfo(assetOrderDeliveryMap.get(dataProductVO.getId()));
        return dataProductVO;
    }

    private final static String DATA_PRODUCT_KEY = "data_product_%s";

    /**
     * 根据dataProductPlatformId查询数据产品详情
     * 前提条件：租户过滤器已经完成切换
     * todo 缓存
     *
     * @param dataProductPlatformId 数据产品ID
     * @return
     * @see com.ailpha.ailand.dataroute.endpoint.tenant.interceptor.TenantFilter 租户过滤器
     */

    public DataProductVO getDataProductByDataProductPlatformIdLocal(String dataProductPlatformId) {
//        String key = String.format(DATA_PRODUCT_KEY, dataProductPlatformId);
//        String cache = dataAssetCache.get(key);
//        if (StringUtils.isEmpty(cache)) {
        DataProduct dataProduct = dataProductRepository.findByDataProductPlatformId(dataProductPlatformId);
        Assert.notNull(dataProduct, "未找到 dataProductPlatformId 为 " + dataProductPlatformId + " 的数据产品");
        DataProductVO dataProductVO = dataAssetMapper.dataProductToDataProductVO(dataProduct);
//            dataAssetCache.put(key, JacksonUtils.obj2json(dataProductVO));
        return dataProductVO;
//        }
//        return JacksonUtils.json2pojo(cache, DataProductVO.class);
    }

    /**
     * 根据dataProductPlatformId远程调用查询数据产品详情
     *
     * @param dataProductPlatformId 数据产品ID
     * @return
     */
    @Override
    public DataProductVO getDataProductByDataProductPlatformIdFromRemote(String dataProductPlatformId, String targetNodeId) {
        // JSONObject others = detail(dataProductPlatformId).getFirst().getJSONObject("others");
        Tuple2<String, Long> dataProductTuple2 = findDataProductNodeIdAndTargetCompanyId(dataProductPlatformId);
        DataProductDetailRequest detailRequest = DataProductDetailRequest.builder().dataResourcePlatformId(dataProductPlatformId).build();
        detailRequest.setTargetNodeId(targetNodeId);
        // detailRequest.setTargetCompanyId(others.getLong("companyId"));
        detailRequest.setTargetCompanyId(dataProductTuple2.second);
        CommonResult<DataProductVO> dataAssetSuccessResponse = endpointRemote.dataProductDetail(detailRequest);
        Assert.isTrue(dataAssetSuccessResponse.isSuccess(), "查询目标连接器数据产品详情异常：" + dataAssetSuccessResponse.getMessage());
        return dataAssetSuccessResponse.getData();
    }


    @Override
    public DataProductVO getDataProductByDataProductPlatformIdFromRemoteNoLoginSpecial(String dataProductPlatformId, Long currentCompanyId) {
        String key = String.format(DATA_PRODUCT_KEY, dataProductPlatformId);
        Tuple2<String, Long> dataProductTuple2 = findDataProductNodeIdAndTargetCompanyId(dataProductPlatformId);

        // 判断该企业是否在本节点， 是————直接切库查询  否————走远端
        Tuple2<Boolean, Company> tuple2 = companyService.localCompanyByNodeId(dataProductTuple2.first);
        if (tuple2.first) {
            Company company = tuple2.second;
            return AsyncManager.getInstance().executeFuture(() -> {
                TenantContext.setCurrentTenant("tenant_" + company.getId());
                return this.getDataProductByDataProductPlatformIdLocal(dataProductPlatformId);
            });
        }

        String cache = dataAssetCache.get(key);
        if (StringUtils.isEmpty(cache)) {
            DataProductDetailRequest detailRequest = DataProductDetailRequest.builder().dataResourcePlatformId(dataProductPlatformId).build();
            detailRequest.setTargetNodeId(dataProductTuple2.first);
            detailRequest.setTargetCompanyId(dataProductTuple2.second);


            if (!LoginContextHolder.isLogin()) {
                // 没有登录
                detailRequest.setHubInfo(companyService.getHubInfo(currentCompanyId));
            }

            CommonResult<DataProductVO> dataAssetSuccessResponse = endpointRemote.dataProductDetail(detailRequest);
            Assert.isTrue(dataAssetSuccessResponse.isSuccess(), "查询目标连接器数据产品详情异常：" + dataAssetSuccessResponse.getMessage());
            DataProductVO data = dataAssetSuccessResponse.getData();
            dataAssetCache.put(key, JacksonUtils.obj2json(data));
            return data;
        }
        return JacksonUtils.json2pojo(cache, DataProductVO.class);

    }

    @Override
    public DataProductVO getDataProductByDataProductPlatformId(String dataProductPlatformId, String currentNodeId, String targetNodeId) {
        // 如果目标连接器ID和当前连接器ID相同则本地查询，前置租户过滤器已经完成租户切换 否则调用远程接口
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            return getDataProductByDataProductPlatformIdLocal(dataProductPlatformId);
        } else {
            return getDataProductByDataProductPlatformIdFromRemote(dataProductPlatformId, targetNodeId);
        }
    }

    private final static String DATA_PRODUCT_ROUTER_KEY = "data_product_router_%s";
    private final static String DATA_PRODUCT_COMPANY_KEY = "data_product_company_%s";

    @Override
    public DataProductVO getDataProductByDataProductPlatformId(String dataProductPlatformId, String currentNodeId) {
        String targetNodeId = findDataProductNodeIdAndTargetCompanyId(dataProductPlatformId).first;
        return getDataProductByDataProductPlatformId(dataProductPlatformId, currentNodeId, targetNodeId);
    }

    private Tuple2<String, Long> findDataProductNodeIdAndTargetCompanyId(String dataProductPlatformId) {
        String targetNodeId;
        Long targetCompanyId;
        final String tmpCache = dataAssetCache.get(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId));
        final String tmpCompanyCache = dataAssetCache.get(String.format(DATA_PRODUCT_COMPANY_KEY, dataProductPlatformId));
        if (StringUtils.isEmpty(tmpCache) || StringUtils.isEmpty(tmpCompanyCache) || tmpCache.contains("null") || tmpCompanyCache.contains("null")) {
            JSONObject others = detail(dataProductPlatformId).getFirst().getJSONObject("others");
            targetNodeId = others.getStr("routerId");
            targetCompanyId = others.getLong("companyId");

            // todo ChenHQ
            // targetNodeId = others.getStr("routerId", "591330108MA2H2DAT3J0000UJ251V3U1");

            dataAssetCache.put(String.format(DATA_PRODUCT_ROUTER_KEY, dataProductPlatformId), targetNodeId);
            dataAssetCache.put(String.format(DATA_PRODUCT_COMPANY_KEY, dataProductPlatformId), String.valueOf(targetCompanyId));
        } else {
            targetNodeId = tmpCache;
            targetCompanyId = Long.valueOf(tmpCompanyCache);
        }
        log.info("根据 dataProductPlatformId:[{}] 查询枢纽 targetNodeId:[{}]", dataProductPlatformId, targetNodeId);
        return new Tuple2<>(targetNodeId, targetCompanyId);
    }

    @Override
    public DataProductVO getDataProductByDataProductPlatformId(String dataProductPlatformId) {
        String targetNodeId = findDataProductNodeIdAndTargetCompanyId(dataProductPlatformId).first;
        return getDataProductByDataProductPlatformIdFromRemote(dataProductPlatformId, targetNodeId);
    }

    @Override
    public void downloadAttachFile(String dataProductPlatformId, AttachType attachType, HttpServletResponse response, String currentNodeId) throws IOException {
        Tuple2<String, Long> dataProductTuple2 = findDataProductNodeIdAndTargetCompanyId(dataProductPlatformId);
        String targetNodeId = dataProductTuple2.first;
        if (StringUtils.equals(targetNodeId, currentNodeId)) {
            DataProduct dataProduct = dataProductRepository.findByDataProductPlatformId(dataProductPlatformId);
            downloadAttachFile(dataProduct, attachType, response);
        } else {
            // List<JSONObject> detail = detail(dataProductPlatformId);
            DownloadFileRequest request = DownloadFileRequest.builder().attachType(attachType).dataProductPlatformId(dataProductPlatformId).build();
            request.setTargetNodeId(targetNodeId);
            request.setTargetCompanyId(dataProductTuple2.second);
            Response<ResponseBody> attachFileDownload = endpointRemote.dataProductAttachFile(request);
            try (ResponseBody responseBody = attachFileDownload.body()) {
                Assert.isTrue(responseBody != null, "下载附件文件失败");
                ServletOutputStream outputStream = response.getOutputStream();
                response.reset();
                response.setContentType("application/octet-stream");
                response.setCharacterEncoding("utf-8");
                response.addHeader("Content-Disposition", attachFileDownload.headers().get("Content-Disposition"));
                InputStream inputStream = responseBody.byteStream();
                byte[] bytes = new byte[2048];
                int len;
                while ((len = inputStream.read(bytes)) > 0) {
                    outputStream.write(bytes, 0, len);
                }
                outputStream.close();
            }
        }
    }


    public List<JSONObject> detail(String dataProductPlatformId) {
        SuccessResponse<List<JSONObject>> dataCatalogQuery = shuHanApiClient.dataCatalogQuery(CatalogQueryVM.builder()
                .type(2)
                .filters(List.of(
                        CatalogQueryVM.FilterVM.builder().filterProperty("productId").filterOperation("=").filterValue(dataProductPlatformId).build()
                ))
                .orders(List.of())
                .size(100L)
                .page(1L)
                .build());
        Assert.isTrue(dataCatalogQuery.isSuccess() && !CollectionUtils.isEmpty(dataCatalogQuery.getData()), "未查询到id为 " + dataProductPlatformId + " 的数据产品目录数据");
        return dataCatalogQuery.getData();
    }

    public void downloadAttachFile(DataProduct dataProduct, AttachType attachType, HttpServletResponse response) throws IOException {
        Assert.notNull(dataProduct.getDataExt().getQualificationDoc(), "未找到附件信息");
        BiFunction<QualificationDoc, AttachType, String> fileName = (qualificationDoc, _attachType) -> switch (_attachType) {
            case dataSample -> qualificationDoc.getDataSampleAttach();
            case dataSourceStatement -> qualificationDoc.getDataSourceStatementAttach();
            case safeLevel -> qualificationDoc.getSafeLevelAttach();
            case evaluationReport -> qualificationDoc.getEvaluationReportAttach();
            case complianceAndLegalStatement -> qualificationDoc.getComplianceAndLegalStatementAttach();
            case complianceSelfCheckManual -> qualificationDoc.getComplianceSelfCheckManualAttach();
            case other -> qualificationDoc.getOtherAttach();
        };
        String attachFileName = fileName.apply(dataProduct.getDataExt().getQualificationDoc(), attachType);
        Assert.notNull(attachFileName, "未找到附件信息");
        Path attachFilePath = filesStorageService.getRootPath().resolve("attach").resolve(dataProduct.getUserId())
                .resolve(attachFileName);
        try (InputStream inputStream = Files.newInputStream(attachFilePath)) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.reset();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(attachFileName, StandardCharsets.UTF_8));
            byte[] bytes = new byte[2048];
            int len;
            while ((len = inputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        }
    }


    @Override
    public SuccessResponse<List<DataProductListVO>> allDataAssets(int page, int size, Function<Specification<DataProduct>, Specification<DataProduct>> specificationFunction) {
        Specification<DataProduct> dataProductSpecification = (root, query, cb) -> cb.notEqual(root.get("isDelete"), true);
        if (specificationFunction != null) {
            dataProductSpecification = specificationFunction.apply(dataProductSpecification);
        }
        org.springframework.data.domain.Page<DataProduct> dataProducts;
        PageRequest pageRequest = PageRequest.of((int) page - 1, (int) size, Sort.by(Sort.Direction.DESC, "createTime"));
        dataProducts = dataProductRepository.findAll(dataProductSpecification, pageRequest);
        List<String> assetIds = dataProducts.getContent().stream().map(DataProduct::getId).toList();
        if (dataProducts.isEmpty()) {
            List<DataProductListVO> empty = new ArrayList<>();
            return SuccessResponse.success(empty).total(0L).build();
        }
        // TODO 待改造
//        SuccessResponse<Map<String, StatisticAssetOrderDeliveryVO>> assetOrderDeliveryResponse = hubOrderRemote.statisticAssetOrderDelivery(assetIds);
//        Map<String, StatisticAssetOrderDeliveryVO> assetOrderDeliveryMap = assetOrderDeliveryResponse.getData();
        return SuccessResponse.success(
                        dataProducts.getContent().stream()
                                .peek(dataAsset -> {
                                    String routerName = getRouterName(dataAsset.getProvider().getRouterId());
                                    if (!StringUtils.isEmpty(routerName)) {
                                        dataAsset.getProvider().setRouterName(routerName);
                                    }
                                })
                                .map(dataProduct -> {
                                    DataProductListVO dataProductListVO = dataAssetMapper.dataProductToDataProductListVO(dataProduct);
//                                    dataProductListVO.setStatisticInfo(assetOrderDeliveryMap.get(dataProduct.getId()));
                                    return dataProductListVO;
                                })
                                .toList()
                )
                .total(dataProducts.getTotalElements())
                .page(Page.of(page, size))
                .build();
    }

    static final Cache<Object, Object> ROUTER_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofSeconds(30))
            .maximumSize(500)
            .initialCapacity(50).build();

    private String getRouterName(String routerId) {
        try {
            String routerName = (String) ROUTER_CACHE.getIfPresent(routerId);
            if (StringUtils.isEmpty(routerName)) {
                ConnectorRegionNodeResolutionResponse connectorRegionNodeResolutionResponse = routerService.regionNodeResolution(routerId, companyService.getHubInfo(LoginContextHolder.currentUser().getCompany().getId()));
                if (StringUtils.isNotBlank(connectorRegionNodeResolutionResponse.getConnectorName())) {
                    ROUTER_CACHE.put(routerId, connectorRegionNodeResolutionResponse.getConnectorName());
                }

            }
            return (String) ROUTER_CACHE.getIfPresent(routerId);
        } catch (Exception ignore) {
            return null;
        }
    }

    @Override
    public void download(String dataAssetId, String accessKey, String secretKey, HttpServletResponse response) {
        DataProductVO dataProduct = getDataProduct(dataAssetId);
        DrClientInfoVO router = routerService.getByClientNo(dataProduct.getProvider().getRouterId(), null);
        Assert.notNull(router, "未找到数据资产所在的连接器节点信息");

        String extension = "csv";
        try {
            extension = FileUtil.extName(dataProduct.getFileSourceMetadata().getDataAssetFilePath());
        } catch (Exception ignore) {
        }
        String fileName = dataProduct.getDataProductName() + "." + extension;
        response.reset();
        try (MinioClient minioClient = MinioClient.builder()
                .endpoint(new URI("http://" + router.getClientIp()).getHost(), 9000, true)
                .credentials(accessKey, secretKey)
                .httpClient(customHttpClient())
                .build();
             InputStream inputStream = minioClient.getObject(GetObjectArgs.builder()
                     .bucket(dataProduct.getUserId())
                     .object(Paths.get(dataProduct.getId(), fileName).toString())
                     .build())) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            byte[] bytes = new byte[1024];
            int len;
            while ((len = inputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        } catch (Exception e) {
            throw new RestfulApiException("数据资产文件下载出错", e);
        }
    }

    @Override
    public void updateDataExt(String dataAssetId, Function<DataAssetExt, DataAssetExt> dataExtUpdate) {
        dataProductRepository.updateDataExt(dataAssetId, dataExtUpdate.apply(dataProductRepository.getReferenceById(dataAssetId).getDataExt()));
    }

    @Override
    public void checkNameExists(String dataProductId, String dataProductName) {
        List<DataProduct> dataProductByName = dataProductRepository.findAll(Example.of(DataProduct.builder().dataProductName(dataProductName).isDelete(false).build()));
        Assert.isTrue((dataProductId == null && CollectionUtils.isEmpty(dataProductByName)) ||
                dataProductByName.stream().allMatch(dataProduct -> dataProduct.getId().equals(dataProductId)), "数据产品名称重复");
        SuccessResponse<List<JSONObject>> dataProductByName1 = shuHanApiClient.allMarketDataProduct(
                DataAssetQuery.builder().assetName(dataProductName).build());
        for (JSONObject mayDuplicateName : dataProductByName1.getData()) {
            if (dataProductName.equals(mayDuplicateName.getStr("productName"))) {
                throw new RestfulApiException("数据产品名称重复");
            }
        }
    }

    @Override
    public void temporarySave(DataProductRegistRequest request) {
        DataProduct product;
        if (StringUtils.isNotBlank(request.getId())) {
            product = dataProductRepository.getReferenceById(request.getId());
        } else {
            product = dataAssetMapper.dataProductRegistRequestToDataProduct(request);
            product.setProvider(new ProviderExt());
            product.setCreateTime(new Date());
            product.setPushStatus(PushStatus.OFFLINE.getCode());
            product.getDataExt().setPublishStatus("0");
            product.setItemStatus("item_status0");
            UserDTO userDTO = LoginContextHolder.currentUser();
            fillProvider(product, userDTO);
            product.setUserId(userDTO.getId());
            product.setUsername(userDTO.getUsername());
            product.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
        }
        product = product.updateProductNameCNTo(request.getProductNameCN())
                .updateTypeTo(request.getType())
                .updateDataCoverageTime(request.getDataCoverageTimeStart(), request.getDataCoverageTimeEnd())
                .updateIndustryTo(request.getIndustry(), request.getIndustry1())
                .updateRegionTo(request.getRegion(), request.getRegion1())
                .updatePersonalInformationTo(request.getPersonalInformation())
                .updateDescriptionTo(request.getDescription())
                .updateSourceTo(request.getSource())
                .updateScaleTo(request.getDataSize(), request.getDataSizeUnit())
                .updateFrequencyTo(request.getUpdateFrequency(), request.getUpdateFrequencyUnit())
                .updateDeliveryMethodTo(request.getDeliveryMethod())
                .updateLimitationsTo(request.getLimitations())
                .updateAuthorizeTo(request.getAuthorize())
                .updateIsSecondaryProcessedTo(request.getIsSecondaryProcessed())
                .updateResourceIdTo(request.getResourceId())
                .updateLineageTo(request.getLineage())
                .updateOtherTo(request.getOther())
                .updateDataSampleAttach(request.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(request.getQualificationDoc())
                .updateDataSourceStatementAttach(request.getQualificationDoc())
                .updateSafeLevelAttach(request.getQualificationDoc())
                .updateEvaluationReportAttach(request.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(request.getQualificationDoc())
                .updateOtherAttach(request.getQualificationDoc());
        product.setUpdateTime(new Date());
        dataProductRepository.save(product);
    }

    @Override
    public void registration(DataProductRegistRequest dataProductCreateRequest) {
        DataProduct product;
        if (StringUtils.isNotBlank(dataProductCreateRequest.getId())) {
            product = dataProductRepository.getReferenceById(dataProductCreateRequest.getId());
            Assert.isTrue("item_status0".equals(product.getItemStatus()), "非可登记状态");
        } else {
            product = dataAssetMapper.dataProductRegistRequestToDataProduct(dataProductCreateRequest);
            product.setCreateTime(new Date());
            product.getDataExt().setPublishStatus("0");
            UserDTO userDTO = LoginContextHolder.currentUser();
            fillProvider(product, userDTO);
            product.setUserId(userDTO.getId());
            product.setUsername(userDTO.getUsername());
            product.setUpdateTime(new Date());
            product.setPushStatus(PushStatus.OFFLINE.getCode());
            product.getDataExt().setCreateIp(IpUtils.getClientIp(ServletUtils.getRequest()));
            product.getProvider().setUserIdShuhan(userDTO.getIdShuhan());
        }
        product.setItemStatus("item_status1");
        product.getDataExt().addProcessLog("提交连接器主体登记审批", System.currentTimeMillis(), null, product.getUsername());
        product = product.updateProductNameCNTo(dataProductCreateRequest.getProductNameCN())
                .updateTypeTo(dataProductCreateRequest.getType())
                .updateDataCoverageTime(dataProductCreateRequest.getDataCoverageTimeStart(), dataProductCreateRequest.getDataCoverageTimeEnd())
                .updateIndustryTo(dataProductCreateRequest.getIndustry(), dataProductCreateRequest.getIndustry1())
                .updateRegionTo(dataProductCreateRequest.getRegion(), dataProductCreateRequest.getRegion1())
                .updatePersonalInformationTo(dataProductCreateRequest.getPersonalInformation())
                .updateDescriptionTo(dataProductCreateRequest.getDescription())
                .updateSourceTo(dataProductCreateRequest.getSource())
                .updateScaleTo(dataProductCreateRequest.getDataSize(), dataProductCreateRequest.getDataSizeUnit())
                .updateFrequencyTo(dataProductCreateRequest.getUpdateFrequency(), dataProductCreateRequest.getUpdateFrequencyUnit())
                .updateDeliveryMethodTo(dataProductCreateRequest.getDeliveryMethod())
                .updateLimitationsTo(dataProductCreateRequest.getLimitations())
                .updateAuthorizeTo(dataProductCreateRequest.getAuthorize())
                .updateIsSecondaryProcessedTo(dataProductCreateRequest.getIsSecondaryProcessed())
                .updateResourceIdTo(dataProductCreateRequest.getResourceId())
                .updateLineageTo(dataProductCreateRequest.getLineage())
                .updateOtherTo(dataProductCreateRequest.getOther())
                .updateDataSampleAttach(dataProductCreateRequest.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(dataProductCreateRequest.getQualificationDoc())
                .updateDataSourceStatementAttach(dataProductCreateRequest.getQualificationDoc())
                .updateSafeLevelAttach(dataProductCreateRequest.getQualificationDoc())
                .updateEvaluationReportAttach(dataProductCreateRequest.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(dataProductCreateRequest.getQualificationDoc())
                .updateOtherAttach(dataProductCreateRequest.getQualificationDoc());
        product.getDataExt().setDataVersion(product.getDataExt().getDataVersion() + 1);
        product.getDataExt().setRegistrationSubmitTime(System.currentTimeMillis());

        dataProductRepository.saveAndFlush(product);


/*        if (StringUtils.isNotBlank(dataProductCreateRequest.getId())) {
            // 登记更新 —— 同步交易中心
            // 简介、产品类型、行业分类
            String localCompanyId = null;
            if (LoginContextHolder.isLogin()) {
                UserDTO currentUser = LoginContextHolder.currentUser();
                localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
            }
            if (baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TRADE_PLATFORM)) {
                dataHubRemote.updateDataAsset(DataAssetUpdateRequest.builder()
                        .assetId(product.getId())
                        .describeMessage(product.getDescription())
                        .industry(product.getIndustry())
                        .industry1(product.getDataExt().getIndustry1())
                        .extraData(JSONUtil.toJsonStr(product.getDeliveryExt()))
                        .build());
            }
        }*/
    }

    private final UserService userService;

    @Override
    public void updateRegistration(DataProductRegistUpdateRequest dataProductRegistUpdateRequest) {
        Optional<DataProduct> optionalDataProduct = dataProductRepository.findById(dataProductRegistUpdateRequest.getId());
        Assert.isTrue(optionalDataProduct.isPresent(), "未找到id为 " + dataProductRegistUpdateRequest.getId() + " 的数据产品");
        DataProduct product = optionalDataProduct.get();
        Assert.isTrue(COULD_UPDATE_REGISTRATION_STATUS.contains(product.getItemStatus()), "非可登记更新状态");
        product = product.updateProductNameCNTo(dataProductRegistUpdateRequest.getProductNameCN())
                .updateTypeTo(dataProductRegistUpdateRequest.getType())
                .updateDataCoverageTime(dataProductRegistUpdateRequest.getDataCoverageTimeStart(), dataProductRegistUpdateRequest.getDataCoverageTimeEnd())
                .updateIndustryTo(dataProductRegistUpdateRequest.getIndustry(), dataProductRegistUpdateRequest.getIndustry1())
                .updateRegionTo(dataProductRegistUpdateRequest.getRegion(), dataProductRegistUpdateRequest.getRegion1())
                .updatePersonalInformationTo(dataProductRegistUpdateRequest.getPersonalInformation())
                .updateDescriptionTo(dataProductRegistUpdateRequest.getDescription())
                .updateSourceTo(dataProductRegistUpdateRequest.getSource())
                .updateScaleTo(dataProductRegistUpdateRequest.getDataSize(), dataProductRegistUpdateRequest.getDataSizeUnit())
                .updateFrequencyTo(dataProductRegistUpdateRequest.getUpdateFrequency(), dataProductRegistUpdateRequest.getUpdateFrequencyUnit())
                .updateLimitationsTo(dataProductRegistUpdateRequest.getLimitations())
                .updateAuthorizeTo(dataProductRegistUpdateRequest.getAuthorize())
                .updateIsSecondaryProcessedTo(dataProductRegistUpdateRequest.getIsSecondaryProcessed())
                .updateResourceIdTo(dataProductRegistUpdateRequest.getPlatformResourceId())
                .updateLineageTo(dataProductRegistUpdateRequest.getLineage())
                .updateOtherTo(dataProductRegistUpdateRequest.getOther())
                .updateDataSampleAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateComplianceAndLegalStatementAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateDataSourceStatementAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateSafeLevelAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateEvaluationReportAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateComplianceSelfCheckManualAttach(dataProductRegistUpdateRequest.getQualificationDoc())
                .updateOtherAttach(dataProductRegistUpdateRequest.getQualificationDoc());
        product.getDataExt().setRegistrationUpdateTime(System.currentTimeMillis());
        product.getDataExt().setDataVersion(product.getDataExt().getDataVersion() + 1);
        product.setUpdateTime(new Date());
        if ("item_status3".equals(product.getItemStatus()) || "item_status7".equals(product.getItemStatus())) {
            // 审批拒绝后更新重置状态待审批
            product.setItemStatus("item_status1");
            product.getDataExt().addProcessLog("重新提交连接器主体登记审批", System.currentTimeMillis(), null, product.getUsername());
        }
        DataProduct dataProduct = dataProductRepository.save(product);

        if ("item_status2".equals(dataProduct.getItemStatus())) {
            CompanyDTO company = LoginContextHolder.currentUser().getCompany();
            JSONObject others = new JSONObject();
            others.set("routerId", company.getNodeId());
            others.set("companyId", company.getId());
            others.set("dataType", product.getDataExt().getDataType());
            UserDetailsResponse userDetail = userService.userDetail(dataProduct.getUserId());
            DataAssetUpdateVO dataAssetUpdateVO = shuHanApiClient.dataProductInfoUpdate(DataProductUpdateVM.builder()
                    .registrationId(dataProduct.getDataProductPlatformId())
                    .productName(product.getDataProductName())
                    .productType(product.getDataExt().getType())
                    .timeRange(product.getDataExt().getDataCoverageTimeStart() == null || product.getDataExt().getDataCoverageTimeEnd() == null ? null :
                            String.format("%s 至 %s", product.getDataExt().getDataCoverageTimeStart(), product.getDataExt().getDataCoverageTimeEnd()))
                    .industry(product.getIndustry().substring(0, 1))
                    .productRegion(product.getDataExt().getRegion())
                    .personalInformation("1".equals(product.getDataExt().getPersonalInformation()) ? 1 : 0)
                    .description(product.getDescription())
                    .deliveryMethod(product.getDeliveryExt().getDeliveryMethod())
                    .limitations(product.getDeliveryExt().getLimitations())
                    .authorize("1".equals(product.getDeliveryExt().getAuthorize()) ? 1 : 0)
                    .dataSubject(product.getDataExt().getDataSubject() == null ? "01" : product.getDataExt().getDataSubject())
                    .dataSize(dataProduct.getDataExt().getDataSize())
                    .dataSizeUnit(dataProduct.getDataExt().getDataSizeUnit())
                    .updateFrequency(product.getDataExt().getUpdateFrequency() == null ? 0 : product.getDataExt().getUpdateFrequency())
                    .updateFrequencyUnit(product.getDataExt().getUpdateFrequencyUnit() == null ? "次/天" : product.getDataExt().getUpdateFrequencyUnit())
// TODO                    .resourceId(List.of(dataProduct.getDataExt().getResourceId() == null ? dataProduct.getId() : dataProduct.getDataExt().getResourceId()))
                    .resourceId(Collections.emptyList())
                    .others(JSONUtil.toJsonStr(others))
                    .providerName(company.getOrganizationName())
                    .providerType("02")
                    .entityInformation(DataProductSaveVM.entityInformation(company))
                    .identityId(company.getCreditCode())
                    .providerDesc(company.getAccessType() == null ? "" : company.getAccessType().getDesc())
                    .operatorName(userDetail.getDelegateInfo().getDelegateName())
                    .operatorTelephone(userDetail.getDelegateInfo().getDelegatePhone())
                    .operatorIdCard(userDetail.getDelegateInfo().getDelegateIdNumber())
                    .commission(company.getAuthorizationLetter())
                    .commissionFileName(company.getAuthorizationLetter())
                    .dataSample(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())))
                    .dataSampleFileName(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())
                    .complianceAndLegalStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())))
                    .complianceAndLegalStatementFileName(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                    .dataSourceStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())))
                    .dataSourceStatementFileName(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                    .safeLevel(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())))
                    .safeLevelFileName(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())
                    .evaluationReport(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDataExt().getQualificationDoc().getEvaluationReportAttach())))
                    .evaluationReportFileName(product.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                    .dataVersion(String.valueOf(dataProduct.getDataExt().getDataVersion()))
                    .build());
        }
    }

    @Override
    @Transactional
    public void revokeRegistration(String productId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(productId);
        Assert.notNull(dataProduct, "未找到要撤销登记的数据资产");
        UserDTO userDTO = LoginContextHolder.currentUser();
        Assert.isTrue(userDTO.getId().equals(dataProduct.getUserId()), "无权操作");
        traderService.deleteFileSource(dataProduct.getUserId(), dataProduct.getDataExt().getFileSourceMetadata());
        dataProduct.setItemStatus("item_status4");
        dataProduct.getDataExt().setPublishStatus("4");
        dataProduct.getDataExt().setHasNonSyncedPublishStatus("0");
        dataProductRepository.saveAndFlush(dataProduct);
        if (StringUtils.isNotBlank(dataProduct.getDataProductPlatformId())) {
            DataAssetRevokeVO revokeFlag = shuHanApiClient.dataProductInfoRevoke(dataProduct.getDataProductPlatformId());
            log.debug("撤销登记数据产品 {}({}) , {}", productId, dataProduct.getDataProductName(), revokeFlag.getRevokeFlag());
        }
//        if ("4".equals(dataProduct.getDataExt().getPublishStatus()) && !CollectionUtils.isEmpty(dataProduct.getDataExt().getServiceNodeLaunchIds())) {
//            this.unpublish(productId, new ArrayList<>(dataProduct.getDataExt().getServiceNodeLaunchIds().keySet()));
//        }
        // NOTE: bug-174558
//        try {
//            DescribeRouteResponse describeRouteResponse = gatewayWebApi.describeRoute(DescribeRouteRequest.builder().id(dataProduct.getDataExt().getGatewayServiceRouteId()).build());
//            DescribeRouteResponse.InvokeResult routeResponseData = describeRouteResponse.getData().getInvokeResult();
//            gatewayWebApi.deleteRoute(DeleteRouteRequest.builder().ids(routeResponseData.getId()).build());
//            gatewayWebApi.deleteService(DeleteServiceRequest.builder().id(routeResponseData.getServiceId()).build());
//            FileUtil.del(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
//        } catch (Exception e) {
//            log.warn("清理数据产品资源出错 {}", e.getMessage());
//        }
    }

    private final AiLandProperties aiLandProperties;

    @Override
    public void publish(DataProductPublishRequest dataProductPublishRequest) {
        if (ObjectUtils.isEmpty(dataProductPublishRequest.getUpdateWay())) {
            dataProductPublishRequest.setUpdateWay(aiLandProperties.getUpdateWay());
        }

        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductPublishRequest.getId());
        Assert.isTrue("item_status6".equals(dataProduct.getItemStatus()), "非可上架状态: 未登记审批通过");
        Assert.isTrue("0".equals(dataProduct.getDataExt().getPublishStatus()), "非可上架状态: 已上架");
        Assert.isTrue(!CollectionUtils.isEmpty(dataProductPublishRequest.getServiceNodes()), "业务节点不能为空");
        for (ServiceNodeApplyListVO serviceNode : dataProductPublishRequest.getServiceNodes()) {
            Assert.isTrue(serviceNode.getServiceNodeUrl() != null, "业务节点 " + serviceNode.getServiceNodeName() + " url为空");
        }
        dataProduct = dataProduct.updateServiceNodesTo(dataProductPublishRequest.getServiceNodes())
                .updateDeliveryInfoTo(dataProductPublishRequest.getDeliveryInfo())
                .updateBillingMethodTo(dataProductPublishRequest.getBillingMethod())
                .updatePurchaseUnitTo(dataProductPublishRequest.getPurchaseUnit())
                .updatePriceTo(dataProductPublishRequest.getPrice())
                .updateDeliveryModesTo(dataProductPublishRequest.getDeliveryModes(), dataProductPublishRequest.getTeePurpose())
                .updateDataTypeTo(dataProductPublishRequest.getDataType())
                .updateDataType1To(dataProductPublishRequest.getDataType1())
                .updateDatasetFileTypeTo(dataProductPublishRequest.getDatasetFileType())
                .updateAccessWayTo(dataProductPublishRequest.getAccessWay())
                .updateAPIQueryWayTo(dataProductPublishRequest.getApiQueryWay())
                .updateDebugDataSourceTo(dataProductPublishRequest.getDebugDataSource())
                .updateDataSchemaTo(dataProductPublishRequest.getDataSchema())
                .updateSeparatorTo(dataProductPublishRequest.getSeparator())
                .updateCompanyIdTo(String.valueOf(dataProduct.getProvider().getCompany().getId()))
                .updateMpcOpenAPIIdIdTo(dataProduct.getDataExt().getMpcOpenAPIId())
                .updateHasHeaderTo(dataProductPublishRequest.getHasHeader())
                .updateAPISourceMetadataTo(dataProductPublishRequest.getApiSourceMetadata())
                .updateFileSourceMetadataTo(dataProductPublishRequest.getFileSourceMetadata())
                .updateDatabaseSourceMetadataTo(dataProductPublishRequest.getDatabaseSourceMetadata())
                .updateAiSortMetadataTo(dataProductPublishRequest.getAiSortMetadata())
                .updateExchangePluginIdsTo(dataProductPublishRequest.getExchangePluginIds())
                .updateCertificatePluginIdsTo(dataProductPublishRequest.getCertificatePluginIds())
                .updateMPCOpenAPIIdTo(dataProductPublishRequest.getMpcOpenAPIId())
                .updateMPCPurposeTo(dataProductPublishRequest.getMpcPurpose())
                .updateExtractResponseTo(dataProductPublishRequest.getExtractResponse())
                .updateUpdateWayTo(dataProductPublishRequest.getUpdateWay());
        if (UpdateWay.SCHEDULE.equals(dataProductPublishRequest.getUpdateWay())) {
            dataProduct.getDataExt().setUpdateFreq(dataProductPublishRequest.getUpdateFreq());
            dataProduct.getDataExt().setSelectDate(dataProductPublishRequest.getSelectDate());
            dataProduct.getDataExt().setSelectHour(dataProductPublishRequest.getSelectHour());
        }

        if (SourceType.API.equals(dataProductPublishRequest.getAccessWay()) || SourceType.FROM_MPC.equals(dataProductPublishRequest.getAccessWay())) {
            dataProduct.getDataExt().setApiQueryWay(dataProductPublishRequest.getApiQueryWay());
            if (!ObjectUtils.isEmpty(dataProductPublishRequest.getApiSourceMetadata()) && !ObjectUtils.isEmpty(dataProductPublishRequest.getApiSourceMetadata().getExtend())
                    && dataProductPublishRequest.getApiSourceMetadata().getExtend().getIsBatchParams()) {
                Assert.isTrue(!ObjectUtils.isEmpty(dataProductPublishRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId()), "批量参数文件不存在，请重新上传");
                String batchParamsFilePath = dataAssetCache.get(dataProductPublishRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId());
                Assert.isTrue(batchParamsFilePath != null, "批量参数文件不存在，请重新上传");
                dataProduct.getDataExt().getApiSourceMetadata().getExtend().setLocalBatchParamsPath(batchParamsFilePath);
            }
        } else if (SourceType.FILE.equals(dataProductPublishRequest.getAccessWay())) {
            String dataAssetFilePath = dataAssetCache.get(dataProductPublishRequest.getFileId());
            if ("模型".equals(dataProduct.getDataExt().getDataType1())) {
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), aiLandProperties.getModel().getSupportedFileSuffix()),
                        "支持的模型文件后缀名: " + aiLandProperties.getModel().getSupportedFileSuffix());
            } else if ("图像".equals(dataProduct.getDataExt().getDataType1())) {
                List<String> supportedSuffix = List.of(FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), supportedSuffix), "支持的文件格式为：" + supportedSuffix);
            } else if ("文件".equals(dataProduct.getDataExt().getDataType1())) {
                List<String> supportedSuffix = List.of(FileTypeConstants.PDF, FileTypeConstants.DOC, FileTypeConstants.DOCX, FileTypeConstants.XLS, FileTypeConstants.XLSX, FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                if (UNSTRUCTURED.equals(dataProduct.getDataExt().getDataType()) && !CollectionUtils.isEmpty(dataProductPublishRequest.getTeePurpose())) {
                    supportedSuffix = List.of(FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                }
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), supportedSuffix), "支持的文件格式为：" + supportedSuffix);
            }
            Assert.isTrue(dataAssetFilePath != null, "模型".equals(dataProduct.getDataExt().getDataType1()) ? "模型文件不存在，请重新上传" : "数据资产文件不存在，请重新上传");
            Path dataAssetFile = Paths.get(dataAssetFilePath);
            Assert.isTrue(dataAssetFile.toFile().exists(), "数据资产文件不存在，请重新上传");
            if (STRUCTURED.equals(dataProduct.getDataExt().getDataType())) {
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), List.of(FileTypeConstants.CSV)), "支持的文件格式为：" + FileTypeConstants.CSV);
                String lineValue;
                try {
                    lineValue = FileUtils.readFirstLine(dataAssetFile.toFile());
                } catch (IOException e) {
                    log.warn("读取文件第一行尝试获取文件列数失败", e);
                    throw new RestfulApiException("读取文件第一行尝试获取文件列数失败", e);
                }
                List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
                Assert.isTrue(dataProductPublishRequest.getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
                ).count() == dataSchema.size(), "原始文件列数与数据集描述不一致，请检查是否正确配置");
            } else if ("模型".equals(dataProduct.getDataExt().getDataType1())) {
                dataProduct.getDataExt().setIsLLM(dataProductPublishRequest.getIsLLM());
                dataProduct.getDataExt().setModelMetadata(dataProductPublishRequest.getModelMetadata());
            }
            dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath);
            dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFileHash(SM3DigestUtil.getHash(dataAssetFile.toFile()));
            dataProduct.getDataExt().getFileSourceMetadata().setFileSource(dataProductPublishRequest.getFileSource());
        } else if (SourceType.DATABASE.equals(dataProductPublishRequest.getAccessWay())) {
            if (this.aigateConfig.getEnabled() && DB_TYPE_MAP.containsKey(dataProductPublishRequest.getDatabaseSourceMetadata().getDatasourceType())) {
                String jdbcUrl = dataProductPublishRequest.getDatabaseSourceMetadata().getJdbcUrl();
                String ip;
                String port;
                if (DatasourceType.oracle.name().equals(dataProductPublishRequest.getDatabaseSourceMetadata().getDatasourceType())) {
                    String ipPort = jdbcUrl.substring(jdbcUrl.indexOf("@") + 1).replace("//", "");
                    String[] split = ipPort.split(":");
                    Assert.isTrue(split.length > 1, "jdbc url中请提供端口号");
                    ip = split[0];
                    port = split[1];
                } else {
                    String cleanURI = jdbcUrl.substring(5);
                    URI uri = URI.create(cleanURI);
                    ip = uri.getHost();
                    port = String.valueOf(uri.getPort());
                }
                AIGateResponse<CreateAssetByRouterResponse> aiGateAsset = aiGateClient.createAsset(CreateAssetByRouterRequest.builder()
                        .dbType(DB_TYPE_MAP.get(dataProductPublishRequest.getDatabaseSourceMetadata().getDatasourceType()))
                        .ip(ip)
                        .port(port)
                        .build());
                Assert.isTrue(aiGateAsset.isSuccess(), "数据库网关错误: " + aiGateAsset.getMessage());
                CreateAssetByRouterResponse data = aiGateAsset.getData();
                jdbcUrl = jdbcUrl.replace(ip, data.getGateIp())
                        .replace(port, data.getProxyPort());
                dataProductPublishRequest.getDatabaseSourceMetadata().setJdbcUrl(jdbcUrl);
            }
            if (dataProductPublishRequest.getAiSortMetadata() != null) {
                dataProduct.getDataExt().getAiSortMetadata().setSourceId(dataProductPublishRequest.getAiSortMetadata().getSourceId());
                dataProduct.getDataExt().getAiSortMetadata().setId(dataProductPublishRequest.getAiSortMetadata().getId());
                dataProduct.getDataExt().getAiSortMetadata().setTableName(dataProductPublishRequest.getAiSortMetadata().getTableName());
                dataProduct.getDataExt().getAiSortMetadata().setTableDesc(dataProductPublishRequest.getAiSortMetadata().getTableDesc());
                dataProduct.getDataExt().getAiSortMetadata().setSchemaName(dataProductPublishRequest.getAiSortMetadata().getSchemaName());
            }
            DatasourceCheckQuery query = new DatasourceCheckQuery();
            BeanUtils.copyProperties(dataProductPublishRequest.getDatabaseSourceMetadata(), query);
            query.setTable(dataProductPublishRequest.getDatabaseSourceMetadata().getTableName());
            dataCollectorApi.checkConn(query);
        }
        if (DebugDataSourceEnum.EXTRA_UPLOAD.equals(dataProductPublishRequest.getDebugDataSource())) {
            Assert.isTrue(StringUtils.isNotBlank(dataProductPublishRequest.getTempDebugFileId()), "请上传数据资产调试文件");
            String dataAssetDebugFilePath = dataAssetCache.get(dataProductPublishRequest.getTempDebugFileId());
            Assert.isTrue(dataAssetDebugFilePath != null, "数据资产调试文件不存在，请重新上传");
            Path dataAssetDebugFile = Paths.get(dataAssetDebugFilePath);
            Assert.isTrue(dataAssetDebugFile.toFile().exists(), "数据资产调试文件不存在，请重新上传");
            if (STRUCTURED.equals(dataProduct.getDataExt().getDataType())) {
                String lineValue;
                try {
                    lineValue = FileUtils.readFirstLine(dataAssetDebugFile.toFile());
                } catch (IOException e) {
                    log.warn("读取调试文件第一行尝试获取文件列数失败", e);
                    throw new RestfulApiException("读取调试文件第一行尝试获取文件列数失败", e);
                }
                List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
                Assert.isTrue(dataProductPublishRequest.getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
                ).count() == dataSchema.size(), "调试数据文件列数与数据集描述不一致，请检查是否正确配置");
            }
            dataProduct.getDataExt().setDebugDataPath(dataAssetDebugFilePath);
            dataProduct.getDataExt().setSeparator(ObjectUtils.isEmpty(dataProductPublishRequest.getSeparator()) ? "," : dataProductPublishRequest.getSeparator());
            dataProduct.getDataExt().setHasHeader(ObjectUtils.isEmpty(dataProductPublishRequest.getHasHeader()) ? 1 : dataProductPublishRequest.getHasHeader());
        }
        dataProduct.setUpdateTime(new Date());
        dataProduct.getDataExt().setPublishStatus("1");
        dataProduct.getDataExt().setPublishSubmitTime(System.currentTimeMillis());
        dataProduct.getDataExt().addProcessLog("提交连接器主体上架审批", System.currentTimeMillis(), "", dataProduct.getProvider().getUsername());
        dataProduct = dataProductRepository.save(dataProduct);
        if (SourceType.API.equals(dataProductPublishRequest.getAccessWay()) || SourceType.FROM_MPC.equals(dataProductPublishRequest.getAccessWay())) {
            try {
                // 对接方式：API，创建网关代理
                CreateRouteResponse serviceAndRouteForExternalAPI = gatewayWebApi.createServiceAndRouteForExternalAPI(dataProduct.getId(),
                        dataProduct.getDataExt().getApiSourceMetadata().getDataPath(), dataProduct.getDataExt().getApiSourceMetadata().getResponse(),
                        dataProduct.getDataExt().getApiSourceMetadata().getResponseEcho(),
                        String.format("%s_%s", dataProduct.getId(), dataProduct.getDataProductName()),
                        dataProduct.getId(), dataProduct.getDataExt().getApiSourceMetadata().getUrl(), Boolean.TRUE.equals(dataProduct.getDataExt().getExtractResponse()));
                this.updateDataExt(dataProduct.getId(), dataProductExt -> {
                    dataProductExt.setGatewayServiceRouteId(serviceAndRouteForExternalAPI.getData().getInvokeResult().getId());
                    return dataProductExt;
                });
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("route name exists")) {
                    DescribeRoutesResponse describeRoutesResponse = gatewayWebApi.describeRoutes(dataProduct.getId());
                    DataProduct finalDataProduct = dataProduct;
                    Optional<DescribeRouteResponse.InvokeResult> route = describeRoutesResponse.getData().getInvokeResult().getRows().stream().filter(_route -> finalDataProduct.getId().equals(_route.getName())).findFirst();
                    if (route.isEmpty()) {
                        return;
                    }
                    this.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.setGatewayServiceRouteId(route.get().getId());
                        return dataProductExt;
                    });
                } else {
                    log.error("createServiceAndRouteForExternalAPI 失败:", e);
                }
            }
        }

        DataProduct finalProduct = dataProduct;
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        AsyncManager.getInstance().executeFuture(() -> {
            TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
            LocalProductRef localProductRef = LocalProductRef.builder()
                    .productPlatformId(finalProduct.getDataProductPlatformId())
                    .assetId(finalProduct.getId())
                    .companyId(String.valueOf(company.getId()))
                    .createTime(new Date())
                    .updateTime(new Date())
                    .extend("{}")
                    .build();
            localProductRefRepository.saveAndFlush(localProductRef);
            return true;
        });

        // 创建更新任务,都准备好以后，才能创建更新任务，不然会造成冗余
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);

        DataUpdateTask dataUpdateTask = new DataUpdateTask();
        dataUpdateTask.setDataProductId(dataProduct.getId());
        log.debug("update way: {}", dataProduct.getDataExt().getUpdateWay());
        dataUpdateTask.setDataUpdateType(dataProduct.getDataExt().getUpdateWay());

        if (UpdateWay.SCHEDULE.equals(dataProduct.getDataExt().getUpdateWay())) {
            dataUpdateTask.setBaseInfo(dataUpdateTaskService.buildDataUpdateBaseInfo(
                    dataProduct.getDataExt().getUpdateWay(),
                    dataProduct.getDataExt().getUpdateFreq(),
                    dataProduct.getDataExt().getSelectDate(),
                    dataProduct.getDataExt().getSelectHour()
            ));
        }

        dataUpdateTaskService.createTask(dataUpdateTask);
    }

    @Override
    public void publishUpdate(DataProductPublishUpdateRequest request) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(request.getProductId());
        Assert.isTrue("item_status6".equals(dataProduct.getItemStatus()), "非可上架更新状态: 未登记审批通过");
        Assert.isTrue(!"0".equals(dataProduct.getDataExt().getPublishStatus()), "非可上架更新状态");
        dataProduct = dataProduct.updateServiceNodesTo(request.getServiceNodes())
                .updateBillingMethodTo(request.getBillingMethod())
                .updatePurchaseUnitTo(request.getPurchaseUnit())
                .updatePriceTo(request.getPrice())
                .updateDeliveryInfoTo(request.getDeliveryInfo());
        if (UpdateWay.SCHEDULE.equals(request.getUpdateWay())) {
            dataProduct.updateUpdateWayTo(request.getUpdateWay())
                    .updateUpdateFreqTo(request.getUpdateFreq())
                    .updateSelectDateTo(request.getSelectDate())
                    .updateSelectHourTo(request.getSelectHour());

//            log.debug("data product dataExt: {}", JSONUtil.toJsonStr(dataProduct.getDataExt()));
            // 更新任务信息和quartz job信息
            DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
            DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());
            dataUpdateTask.setBaseInfo(dataUpdateTaskService.buildDataUpdateBaseInfo(
                    dataProduct.getDataExt().getUpdateWay(),
                    dataProduct.getDataExt().getUpdateFreq(),
                    dataProduct.getDataExt().getSelectDate(),
                    dataProduct.getDataExt().getSelectHour()
            ));
            dataUpdateTask.setCronExpression(dataUpdateTaskService.dataUpdateBaseInfo2CronExpression(dataUpdateTask.getBaseInfo()));
            dataUpdateTask.setUpdateTime(new Date());
            log.debug("数据产品上架更新：dataUpdateTask save before {}", JSONUtil.toJsonStr(dataUpdateTask));
            taskRepository.saveAndFlush(dataUpdateTask);

            // 上架审批通过之后，才可以更新定时任务信息
            if ("2".equals(dataProduct.getDataExt().getPublishStatus())) {
                SpringUtil.getBean(QuartzSchedulerManager.class).updateJob(dataUpdateTask.getId(), dataUpdateTask);
            }
        }
        if ("3".equals(dataProduct.getDataExt().getPublishStatus())) {
            // 上架审批拒绝的在上架更新时重置上架状态未待审批
            dataProduct.getDataExt().setPublishStatus("1");
        }
        dataProduct.getDataExt().setPublishUpdateTime(System.currentTimeMillis());
        dataProduct.setUpdateTime(new Date());
        dataProduct = dataProductRepository.saveAndFlush(dataProduct);
        if ("2".equals(dataProduct.getDataExt().getPublishStatus())) { // 上架审批通过的
            CompanyDTO company = dataProduct.getProvider().getCompany();
            JSONObject others = new JSONObject();
            others.set("routerId", company.getNodeId());
            others.set("companyId", company.getId());
            others.set("dataType", dataProduct.getDataExt().getDataType());
            UserDetailsResponse userDetail = userService.userDetail(dataProduct.getUserId());

            DataProductPublishVM dataProductPublishVM = DataProductPublishVM.builder()
                    .productId(dataProduct.getDataProductPlatformId())
                    .productName(dataProduct.getDataProductName())
                    .productType(dataProduct.getDataExt().getType())
                    .timeRange(dataProduct.getDataExt().getDataCoverageTimeStart() == null || dataProduct.getDataExt().getDataCoverageTimeEnd() == null ? null :
                            String.format("%s 至 %s", dataProduct.getDataExt().getDataCoverageTimeStart(), dataProduct.getDataExt().getDataCoverageTimeEnd()))
                    .industry(dataProduct.getIndustry().substring(0, 1))
                    .productRegion(dataProduct.getDataExt().getRegion())
                    .personalInformation("1".equals(dataProduct.getDataExt().getPersonalInformation()) ? 1 : 0)
                    .description(dataProduct.getDescription())
                    .deliveryMethod(dataProduct.getDeliveryExt().getDeliveryMethod())
                    .deliveryModes(dataProduct.getDeliveryExt().deliveryModesForPublish())
                    .limitations(dataProduct.getDeliveryExt().getLimitations())
                    .authorize("1".equals(dataProduct.getDeliveryExt().getAuthorize()) ? 1 : 0)
                    .dataSubject(dataProduct.getDataExt().getDataSubject() == null ? "01" : dataProduct.getDataExt().getDataSubject())
                    .dataSize(dataProduct.getDataExt().getDataSize())
                    .dataSizeUnit(dataProduct.getDataExt().getDataSizeUnit())
                    .updateFrequency(dataProduct.getDataExt().getUpdateFrequency())
                    .updateFrequencyUnit(dataProduct.getDataExt().getUpdateFrequencyUnit())
// TODO                   .resourceId(List.of(dataProduct.getDataExt().getResourceId() == null ? dataProduct.getId() : dataProduct.getDataExt().getResourceId()))
                    .resourceId(Collections.emptyList())
                    .others(others)
                    .providerName(company.getOrganizationName())
                    .providerType("02")
                    .entityInformation(DataProductSaveVM.entityInformation(company))
                    .identityId(company.getCreditCode())
                    .providerDesc(company.getAccessType() == null ? "" : company.getAccessType().getDesc())
                    .operatorName(userDetail.getDelegateInfo().getDelegateName())
                    .operatorTelephone(userDetail.getDelegateInfo().getDelegatePhone())
                    .operatorIdCard(userDetail.getDelegateInfo().getDelegateIdNumber())
                    .dataVersion(String.valueOf(dataProduct.getDataExt().getDataVersion()))
                    .dataType(dataProduct.getDataExt().getDataType())
                    .deliveryInfo(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getDeliveryExt().getDeliveryInfo())))
                    .deliveryInfoFileName(dataProduct.getDeliveryExt().getDeliveryInfo())
                    .build();

            List<ServiceNodeApplyListVO> serviceNodes = dataProduct.getDeliveryExt().getServiceNodes();
            if (DataAssetPrepareStatus.AVAILABLE.equals(dataProduct.getDataExt().getDataAssetPrepareStatus()) && !CollectionUtils.isEmpty(serviceNodes)) {
                for (ServiceNodeApplyListVO serviceNode : serviceNodes) {
                    try {
                        if (serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() == 1) {
//                            dataProductPublishVM.setLaunchId(serviceNode.getProcessId());
                            dataProductPublishVM.setPlatformId(serviceNode.getServiceNodeId());
                            dataProductPublishVM.setPlatformName(serviceNode.getServiceNodeName());
                            dataProductPublishVM.setPlatformLocation(serviceNode.getServiceNodeLocation() == null ? "空的Location" : serviceNode.getServiceNodeLocation());
                            DataProductPublishVO dataProductPublishVO = shuHanApiClient.dataProductUpdate(dataProductPublishVM);
                            log.debug("数据产品上架更新结果 {}", dataProductPublishVO);
                            serviceNode.setProcessId(dataProductPublishVO.getProcessId());
                            serviceNode.setProcessStatus(0);
                        }
                    } catch (Exception e) {
                        log.warn("数据产品上架更新到 {} 失败", serviceNode, e);
                    }
                }
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            }
            traderService.dataAssetUpdateTrader(false, false, dataProduct.getId());
            String localCompanyId = null;
            if (LoginContextHolder.isLogin()) {
                UserDTO currentUser = LoginContextHolder.currentUser();
                localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
            }
/*            if (baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TRADE_PLATFORM)) {
                dataHubRemote.updateDataAsset(DataAssetUpdateRequest.builder()
                        .assetId(dataProduct.getId())
                        .industry(dataProduct.getIndustry())
                        .industry1(dataProduct.getDataExt().getIndustry1())
                        .extraData(JSONUtil.toJsonStr(dataProduct.getDeliveryExt()))
                        .build());
            }*/
            if (baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.MPC) && dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.MPC)) {
                mpcRemote.updateAsset(UpdateRouteAssetRequest.builder().assetId(dataProduct.getId()).businessArea(dataProduct.getDataExt().getIndustry1()).build());
            }
            if (baseCapabilityManager.platformEnable(localCompanyId, BaseCapabilityType.TEE) && (dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.TEE_OFFLINE) ||
                    dataProduct.getDeliveryExt().getDeliveryModes().contains(DeliveryMode.TEE_ONLINE))) {
                JSONObject object = JSONUtil.parseObj(dataProduct.getDataExt().getIndustry1());
                teeRemote.updateAsset(UpdateRouteAssetRequest.builder().assetId(dataProduct.getId()).businessArea(object.getStr("industryName")).build());
            }
        }
    }

    private final ServiceNodeRemoteService serviceNodeRemoteService;

    @Override
    public void unpublish(String dataProductId, List<String> serviceNodeId) {
//        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
//        List<ServiceNodeApplyListVO> serviceNodes = dataProduct.getDeliveryExt().getServiceNodes();
//        Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", companyService.getHubInfo(dataProduct.getProvider().getCompany().getId()).getUrl(), "/gateway/auth/api/route/openapi/token"), null,
//                companyService.getHubInfo(dataProduct.getProvider().getCompany().getId()).getAk(), companyService.getHubInfo(dataProduct.getProvider().getCompany().getId()).getSk());
//        for (ServiceNodeApplyListVO serviceNode : serviceNodes) {
//            if (serviceNode.getProcessStatus() == null || serviceNode.getProcessStatus() != 1) {
//                continue;
//            }
//            try {
//                if (serviceNode.getServiceNodeName().startsWith("tee") || serviceNode.getServiceNodeName().startsWith("TEE")) {
//                    DataAssetUpdateVO dataAssetUpdateVO = shuHanApiClient.dataProductUnpublish(serviceNode.getProcessId());
//                    log.debug("数据产品下架结果 {}", dataAssetUpdateVO);
//                    // TODO 业务节点（TEE）
//                } else {
//                    HttpHeaders httpHeaders = new HttpHeaders();
//                    for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
//                        httpHeaders.set(stringStringEntry.getKey(), stringStringEntry.getValue());
//                    }
////                    RequestEntity<DataProductUnPublishVM> request = RequestEntity
////                            .post(new URI(String.format("%s/%s",
////                                    serviceNode.getServiceNodeUrl().endsWith("/") ?
////                                            serviceNode.getServiceNodeUrl().substring(0, serviceNode.getServiceNodeUrl().length() - 1) :
////                                            serviceNode.getServiceNodeUrl(),
////                                    "dataProductUnPublish")))
////                            .headers(httpHeaders)
////                            .accept(MediaType.APPLICATION_JSON)
////                            .body(DataProductUnPublishVM.builder().launchId(serviceNodeLaunchIds.get(serviceNode.getServiceNodeId())).build());
////                    ResponseEntity<ShuhanResponse<DataAssetUpdateVO>> result = RestTemplateUtil.getRestTemplate().exchange(request, new ParameterizedTypeReference<ShuhanResponse<DataAssetUpdateVO>>() {
////                    });
//                    ShuhanResponse<DataAssetUpdateVO> result = serviceNodeRemoteService.dataProductUnPublish(DataProductUnPublishVM.builder().launchId(serviceNodeLaunchIds.get(serviceNode.getServiceNodeId())).build(), serviceNode.getServiceNodeUrl());
//                    log.debug("数据产品下架 {} 结果 {}", serviceNode, result);
//                    Assert.isTrue(result != null && result.isSuccess(), result != null ? result.getMessage() : "下架失败");
//                }
//            } catch (Exception e) {
//                log.error("数据产品从业务节点下架失败 {}", serviceNode, e);
//            }
//        }
//        dataProductRepository.updateDataExt(dataProductId, dataProduct.getDataExt());
//
//        traderService.dataAssetUpdateTrader(true, false, dataProductId);
    }

    @Override
    public List<ContractTemplate> contractTemplate(String serviceNodeId, String serviceNodeName, String serviceNodeUrl) {
        List<ContractTemplate> contractTemplates = new ArrayList<>();
        UserDTO currentUser = LoginContextHolder.currentUser();
        CompanyDTO company = currentUser.getCompany();
        log.debug("业务节点[{}]-[{}]-[{}] 获取合同模板", serviceNodeId, serviceNodeName, serviceNodeUrl);
        try {
//            Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", company.getServiceNode().getHubInfo().getUrl(), "/gateway/auth/api/route/openapi/token"), null, company.getServiceNode().getHubInfo().getAk(), company.getServiceNode().getHubInfo().getSk());
//            HttpHeaders httpHeaders = new HttpHeaders();
//            for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
//                httpHeaders.set(stringStringEntry.getKey(), stringStringEntry.getValue());
//            }
//            httpHeaders.setContentType(MediaType.APPLICATION_JSON); // 数瀚GET请求走鉴权有问题，需要加
//            HttpEntity<Void> requestEntity = new HttpEntity<>(httpHeaders);
//            URI uri = new URI(String.format("%s/%s", serviceNodeUrl.endsWith("/") ?
//                            serviceNodeUrl.substring(0, serviceNodeUrl.length() - 1) : serviceNodeUrl,
//                    "contract/template/list"));
//            ResponseEntity<ShuhanResponse<List<ContractTemplateDTO>>> responseEntity = RestTemplateUtil.getRestTemplate().exchange(uri, HttpMethod.GET, requestEntity, new ParameterizedTypeReference<ShuhanResponse<List<ContractTemplateDTO>>>() {
//            });
//            log.debug("业务节点[{}]-[{}] 获取合同模板 ->>> 结果:{}", serviceNodeId, serviceNodeName, responseEntity);
//            if (responseEntity.getBody() != null && responseEntity.getBody().isSuccess()) {
//                List<ContractTemplateDTO> contractTemplateDTOS = responseEntity.getBody().getData();
//                contractTemplates = contractTemplateDTOS.stream().map(contractTemplateDTO -> ContractTemplate.builder().templateId(contractTemplateDTO.getId()).templateName(contractTemplateDTO.getName()).build()).collect(Collectors.toList());
//            } else {
//                log.error("业务节点[{}]-[{}] 获取合同模板失败 ->>> 结果:{}", serviceNodeId, serviceNodeName, responseEntity);
//                throw new IllegalArgumentException(responseEntity.getBody() != null ? responseEntity.getBody().getMsg() : "状态码：" + responseEntity.getStatusCode());
//            }

            ServiceNodeMetaData metaData = new ServiceNodeMetaData();
            metaData.setNodeId(company.getNodeId());
            metaData.setUrl(serviceNodeUrl);
            ShuhanResponse<List<ContractTemplateDTO>> response = serviceNodeRemote.contractTemplateList(metaData.toBase64());
            if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getData())) {
                List<ContractTemplateDTO> contractTemplateDTOS = response.getData();
                contractTemplates = contractTemplateDTOS.stream().map(contractTemplateDTO -> ContractTemplate.builder().templateId(contractTemplateDTO.getId()).templateName(contractTemplateDTO.getName()).build()).collect(Collectors.toList());
            } else {
                log.error("业务节点[{}]-[{}] 获取合同模板失败 ->>> 结果:{}", serviceNodeId, serviceNodeName, response);
                throw new IllegalArgumentException("结果：" + response);
            }
        } catch (Exception e) {
            log.error("业务节点[{}]-[{}] 获取合同模板失败 error:", serviceNodeId, serviceNodeName, e);
            throw new IllegalArgumentException("获取合同模板失败：" + e.getMessage());
        }
        return contractTemplates;
    }

    @Override
    public ResponseEntity<?> contractTemplateDownload(String serviceNodeId, String serviceNodeName, String serviceNodeUrl, String templateId, String templateName) {
        UserDTO currentUser = LoginContextHolder.currentUser();
        CompanyDTO company = currentUser.getCompany();
        log.debug("业务节点[{}]-[{}]-[{}] 合同模板[{}]-[{}] 获取下载接口地址", serviceNodeId, serviceNodeName, serviceNodeUrl, templateId, templateName);
        InputStream fileStream = null;
        try {
//            Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", company.getServiceNode().getHubInfo().getUrl(), "/gateway/auth/api/route/openapi/token"), null, company.getServiceNode().getHubInfo().getAk(), company.getServiceNode().getHubInfo().getSk());
//            HttpHeaders httpHeaders = new HttpHeaders();
//            for (Map.Entry<String, String> stringStringEntry : headers.entrySet()) {
//                httpHeaders.set(stringStringEntry.getKey(), stringStringEntry.getValue());
//            }
//            httpHeaders.setContentType(MediaType.APPLICATION_JSON); // 数瀚GET请求走鉴权有问题，需要加
//            HttpEntity<Void> requestEntity = new HttpEntity<>(httpHeaders);
//            URI uri = new URI(String.format("%s/%s/%s", serviceNodeUrl.endsWith("/") ?
//                            serviceNodeUrl.substring(0, serviceNodeUrl.length() - 1) : serviceNodeUrl,
//                    "contract/getTemplate", templateId));
//            ResponseEntity<ShuhanResponse<String>> responseEntity = RestTemplateUtil.getRestTemplate().exchange(uri, HttpMethod.GET, requestEntity, new ParameterizedTypeReference<ShuhanResponse<String>>() {
//            });
//            log.debug("业务节点[{}]-[{}] 合同模板[{}]-[{}] 获取下载接口地址 ->>> 结果:{}", serviceNodeId, serviceNodeName, templateId, templateName, responseEntity);
//            if (responseEntity.getBody() != null && responseEntity.getBody().isSuccess()) {
//                String downloadUrl = responseEntity.getBody().getData();

            ServiceNodeMetaData metaData = new ServiceNodeMetaData();
            metaData.setNodeId(company.getNodeId());
            metaData.setUrl(serviceNodeUrl);
            ShuhanResponse<String> response = serviceNodeRemote.contractGetTemplate(metaData.toBase64(), templateId);
            if (!ObjectUtils.isEmpty(response) && !ObjectUtils.isEmpty(response.getData())) {
                String downloadUrl = response.getData();
                // 打开HTTP连接
                URL url = new URL(downloadUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod(HttpMethod.GET.name());
                // 检查响应状态
                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    log.error("业务节点[{}]-[{}] 合同模板[{}]-[{}] 下载地址[{}] 下载合同模板失败 HTTP状态码:{}", serviceNodeId, serviceNodeName, templateId, templateName, downloadUrl, responseCode);
                    return ResponseEntity.status(responseCode).body("合同模板下载失败: HTTP状态码 " + responseCode);
                }
                // 获取文件流
                fileStream = connection.getInputStream();
                // 赋值文件名
                String regex = "\\.([^/?]+)(?=\\?|$)";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(downloadUrl);
                String downloadFileName = String.format("ContractTemplate-%s%s",
                        templateId, String.format(".%s", matcher.find() ? matcher.group(1) : "docx"));
                // 设置响应头
                HttpHeaders downloadHeaders = new HttpHeaders();
                downloadHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
                downloadHeaders.set("Content-Disposition", "attachment; filename=" + new String(downloadFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
                return ResponseEntity.ok().headers(downloadHeaders).body(new InputStreamResource(fileStream));
            } else {
                log.error("业务节点[{}]-[{}] 合同模板[{}]-[{}] 获取下载接口地址失败 ->>> 结果:{}", serviceNodeId, serviceNodeName, templateId, templateName, response);
                return ResponseEntity.status(500).body("下载合同模板失败 结果: " + response);
            }
        } catch (Exception e) {
            if (fileStream != null) {
                try {
                    fileStream.close();
                } catch (IOException ignore) {
                }
            }
            log.error("业务节点[{}]-[{}] 合同模板[{}]-[{}] 下载合同模板失败 error:", serviceNodeId, serviceNodeName, templateId, templateName, e);
            return ResponseEntity.status(500).body("合同模板下载失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, String> publishToNode(String dataProductId, List<String> serviceNodeId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
        Assert.isTrue("2".equals(dataProduct.getDataExt().getPublishStatus()), "未通过发布审批");
        List<ServiceNodeApplyListVO> serviceNodes = dataProduct.getDeliveryExt().getServiceNodes();
        Assert.isTrue(!CollectionUtils.isEmpty(serviceNodes), "未找到要发布的业务节点");
        Assert.isTrue(DataAssetPrepareStatus.AVAILABLE.equals(dataProduct.getDataExt().getDataAssetPrepareStatus()), "数据预处理中");
        CompanyDTO company = dataProduct.getProvider().getCompany();
        JSONObject others = new JSONObject();
        others.set("routerId", company.getNodeId());
        others.set("companyId", company.getId());
        others.set("dataType", dataProduct.getDataExt().getDataType());
        UserDetailsResponse userDetail = userService.userDetail(dataProduct.getUserId());

        DataProductPublishVM dataProductPublishVM = DataProductPublishVM.builder()
                .productId(dataProduct.getDataProductPlatformId())
                .productName(dataProduct.getDataProductName())
                .productType(dataProduct.getDataExt().getType())
                .timeRange(dataProduct.getDataExt().getDataCoverageTimeStart() == null || dataProduct.getDataExt().getDataCoverageTimeEnd() == null ? null :
                        String.format("%s 至 %s", dataProduct.getDataExt().getDataCoverageTimeStart(), dataProduct.getDataExt().getDataCoverageTimeEnd()))
                .industry(dataProduct.getIndustry().substring(0, 1))
                .productRegion(dataProduct.getDataExt().getRegion())
                .personalInformation("1".equals(dataProduct.getDataExt().getPersonalInformation()) ? 1 : 0)
                .description(dataProduct.getDescription())
                .deliveryMethod(dataProduct.getDeliveryExt().getDeliveryMethod())
                .deliveryModes(dataProduct.getDeliveryExt().deliveryModesForPublish())
                .limitations(dataProduct.getDeliveryExt().getLimitations())
                .authorize("1".equals(dataProduct.getDeliveryExt().getAuthorize()) ? 1 : 0)
                .dataSubject(dataProduct.getDataExt().getDataSubject() == null ? "01" : dataProduct.getDataExt().getDataSubject())
                .dataSize(dataProduct.getDataExt().getDataSize())
                .dataSizeUnit(dataProduct.getDataExt().getDataSizeUnit())
                .updateFrequency(dataProduct.getDataExt().getUpdateFrequency() == null ? 0 : dataProduct.getDataExt().getUpdateFrequency())
                .updateFrequencyUnit(dataProduct.getDataExt().getUpdateFrequencyUnit() == null ? "次/天" : dataProduct.getDataExt().getUpdateFrequencyUnit())
// TODO                .resourceId(List.of(dataProduct.getDataExt().getResourceId() == null ? dataProduct.getId() : dataProduct.getDataExt().getResourceId()))
                .resourceId(Collections.emptyList())
                .others(others)
                .providerName(company.getOrganizationName())
                .providerType("02")
                .entityInformation(DataProductSaveVM.entityInformation(company))
                .identityId(company.getCreditCode())
                .providerDesc(company.getAccessType() == null ? "" : company.getAccessType().getDesc())
                .operatorName(userDetail.getDelegateInfo().getDelegateName())
                .operatorTelephone(userDetail.getDelegateInfo().getDelegatePhone())
                .operatorIdCard(userDetail.getDelegateInfo().getDelegateIdNumber())
                .commission(company.getAuthorizationLetter())
                .commissionFileName(company.getAuthorizationLetter())
                .dataSample(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())))
                .dataSampleFileName(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())
                .complianceAndLegalStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())))
                .complianceAndLegalStatementFileName(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                .dataSourceStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())))
                .dataSourceStatementFileName(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                .safeLevel(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())))
                .safeLevelFileName(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())
                .evaluationReport(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getEvaluationReportAttach())))
                .evaluationReportFileName(dataProduct.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                .dataVersion(String.valueOf(dataProduct.getDataExt().getDataVersion()))
                .partyACompanyId(company.getThirdBusinessId())
                .partyAUserId(dataProduct.getProvider().getUserIdShuhan())
                .measureMethod(dataProduct.getDeliveryExt().getBillingMethod())
                .unit(dataProduct.getDeliveryExt().getPurchaseUnit())
                .price(dataProduct.getDeliveryExt().getPrice())
                .dataType(dataProduct.getDataExt().getDataType())
                .deliveryInfo(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDeliveryExt().getDeliveryInfo())))
                .deliveryInfoFileName(dataProduct.getDeliveryExt().getDeliveryInfo())
                .build();

        Map<String, String> publishResult = new HashMap<>();
        for (ServiceNodeApplyListVO serviceNode : serviceNodes) {
            if (serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() != 0) {
                continue;
            }
            dataProductPublishVM.setBusinessPlatformUniqueNo(serviceNode.getServiceNodeId());
            dataProductPublishVM.setOthers(others);
            dataProductPublishVM.setContractTemplateId(!ObjectUtils.isEmpty(serviceNode.getContractTemplate()) ? serviceNode.getContractTemplate().getTemplateId() : null);
            try {
                publishResult.put(serviceNode.getServiceNodeId(), "success");
                log.debug("数据产品上架 {}, {}", serviceNode, JSONUtil.toJsonStr(dataProductPublishVM));
                dataProductPublishVM.setPlatformId(serviceNode.getServiceNodeId());
                dataProductPublishVM.setPlatformName(serviceNode.getServiceNodeName());
                dataProductPublishVM.setPlatformLocation(serviceNode.getServiceNodeLocation() == null ? "空的Location" : serviceNode.getServiceNodeLocation());
                ServiceNodeMetaData metaData = new ServiceNodeMetaData();
                metaData.setNodeId(company.getNodeId());
                metaData.setUrl(serviceNode.getServiceNodeUrl());
                ShuhanResponse<DataProductPublishVO> result = serviceNodeRemoteService.dataProductPublish(dataProductPublishVM, metaData.toBase64());
                log.debug("数据产品上架 {}, 结果 {}", serviceNode, result);
                if (result != null && result.isSuccess()) {
                    DataProductPublishVO dataProductPublishVO = result.getData();
                    serviceNode.setProcessId(dataProductPublishVO.getProcessId());
                    serviceNode.setProcessStatus(0);
                    dataProduct.getDataExt().addProcessLog("提交业务节点[" + serviceNode.getServiceNodeName() + "]上架审批", System.currentTimeMillis(), null, null);
                } else {
                    publishResult.put(serviceNode.getServiceNodeId(), result != null ? result.getMessage() : "上架失败");
                }
            } catch (Exception e) {
                publishResult.put(serviceNode.getServiceNodeId(), "数据产品上架到业务节点失败：" + e.getMessage());
                log.error("数据产品上架到业务节点失败 {}", serviceNode, e);
            }
        }
        dataProduct.getDeliveryExt().setServiceNodes(serviceNodes);
        dataProduct.getDataExt().setHasNonSyncedPublishStatus("1");
        dataProduct.getDataExt().setPublishUpdateTime(System.currentTimeMillis());
        dataProductRepository.updateDataExt(dataProductId, dataProduct.getDataExt());
        dataProductRepository.updateDeliveryExt(dataProductId, dataProduct.getDeliveryExt());
        return publishResult;
    }

    @Override
    public void delete(String dataProductId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
        Assert.isTrue("item_status0".equals(dataProduct.getItemStatus()), "只可删除暂存状态的数据产品");
        dataProductRepository.deleteById(dataProductId);
    }

    private void fillProvider(DataProduct product, UserDTO userDTO) {
        CompanyDTO company = LoginContextHolder.currentUser().getCompany();
        String nodeName = company.getConnectorName();
        nodeName = StringUtils.isBlank(nodeName) ? "node_" + company.getOrganizationName() : nodeName;
        product.setPlatformId(company.getNodeId());
        product.setPlatformType(0);
        if (product.getProvider() == null) {
            product.setProvider(new ProviderExt());
        }
        product.getProvider().setRouterId(company.getNodeId());
        product.getProvider().setRouterName(nodeName);
        product.getProvider().setUsername(userDTO.getUsername());
        product.getProvider().setPhone(userDTO.getPhone());
        product.getProvider().setEmail(userDTO.getEmail());
        product.getProvider().setCompany(company);
    }

    public List<DataSchemaBO> parseDataSchema(String separator, String firstLine) {
        String bom = String.valueOf('\ufeff');
        if (firstLine.startsWith(bom)) {
            firstLine = firstLine.substring(bom.length());
        }
        return Arrays.stream(firstLine.split(separator, -1)).map(
                a -> new DataSchemaBO(a.toLowerCase(), "STRING", null, "safeLevel1", true, false, false, false, a, "STRING")
        ).collect(Collectors.toList());
    }

    @Override
    public void accessConfigUpdate(DataProductAccessConfigUpdateRequest dataProductAccessConfigUpdateRequest) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductAccessConfigUpdateRequest.getId());
        Assert.isTrue("item_status6".equals(dataProduct.getItemStatus()), "非可上架状态: 未登记审批通过");
        Assert.isTrue("2".equals(dataProduct.getDataExt().getPublishStatus()), "未通过发布审批");

        SourceType accessWay = dataProduct.getSourceType();

//        dataProduct = dataProduct.updateDebugDataSourceTo(dataProductAccessConfigUpdateRequest.getDebugDataSource())
//                .updateAPISourceMetadataTo(dataProductAccessConfigUpdateRequest.getApiSourceMetadata());
        if (dataProductAccessConfigUpdateRequest.getApiSourceMetadata() != null) {
            if (dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getHeaders() != null) {
                dataProduct.getDataExt().getApiSourceMetadata().setHeaders(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getHeaders());
            }

            if (dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getBody() != null) {
                dataProduct.getDataExt().getApiSourceMetadata().setBody(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getBody());
            }

            if (dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getBodyType() != null) {
                dataProduct.getDataExt().getApiSourceMetadata().setBodyType(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getBodyType());
            }

            if (dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getParams() != null) {
                dataProduct.getDataExt().getApiSourceMetadata().setParams(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getParams());
            }
        }

        if (dataProductAccessConfigUpdateRequest.getDatabaseSourceMetadata() != null) {
            if (dataProductAccessConfigUpdateRequest.getDatabaseSourceMetadata().getTableName() != null) {
                dataProduct.getDataExt().getDatabaseSourceMetadata().setTableName(dataProductAccessConfigUpdateRequest.getDatabaseSourceMetadata().getTableName());
            }

            if (dataProductAccessConfigUpdateRequest.getDatabaseSourceMetadata().getName() != null) {
                dataProduct.getDataExt().getDatabaseSourceMetadata().setName(dataProductAccessConfigUpdateRequest.getDatabaseSourceMetadata().getName());
            }
        }


        if (SourceType.API.equals(accessWay) || SourceType.FROM_MPC.equals(accessWay)) {
//            dataProduct.getDataExt().setApiQueryWay(dataProductAccessConfigUpdateRequest.getApiQueryWay());
//            if (!ObjectUtils.isEmpty(dataProductAccessConfigUpdateRequest.getApiSourceMetadata()) && !ObjectUtils.isEmpty(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getExtend())
//                    && dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getExtend().getIsBatchParams()) {
//                Assert.isTrue(!ObjectUtils.isEmpty(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId()), "批量参数文件不存在，请重新上传");
//                String batchParamsFilePath = dataAssetCache.get(dataProductAccessConfigUpdateRequest.getApiSourceMetadata().getExtend().getBatchParamsFileId());
//                Assert.isTrue(batchParamsFilePath != null, "批量参数文件不存在，请重新上传");
//                dataProduct.getDataExt().getApiSourceMetadata().getExtend().setLocalBatchParamsPath(batchParamsFilePath);
//            }
        } else if (SourceType.FILE.equals(accessWay)) {
            String dataAssetFilePath = dataAssetCache.get(dataProductAccessConfigUpdateRequest.getFileId());
            if ("模型".equals(dataProduct.getDataExt().getDataType1())) {
                throw new RestfulApiException("模型 不支持手动更新");
            } else if ("图像".equals(dataProduct.getDataExt().getDataType1())) {
                List<String> supportedSuffix = List.of(FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), supportedSuffix), "支持的文件格式为：" + supportedSuffix);
            } else if ("文件".equals(dataProduct.getDataExt().getDataType1())) {
                List<String> supportedSuffix = List.of(FileTypeConstants.PDF, FileTypeConstants.DOC, FileTypeConstants.DOCX, FileTypeConstants.XLS, FileTypeConstants.XLSX, FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                boolean hasContainsTEE = dataProduct.getDeliveryExt().getDeliveryModes().stream()
                        .anyMatch(str -> str.toString().contains("TEE"));
                if (UNSTRUCTURED.equals(dataProduct.getDataExt().getDataType()) && hasContainsTEE) {
                    supportedSuffix = List.of(FileTypeConstants.ZIP, FileTypeConstants.TAR_GZ);
                }
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), supportedSuffix), "支持的文件格式为：" + supportedSuffix);
            }
            Assert.isTrue(dataAssetFilePath != null, "模型".equals(dataProduct.getDataExt().getDataType1()) ? "模型文件不存在，请重新上传" : "数据资产文件不存在，请重新上传");
            Path dataAssetFile = Paths.get(dataAssetFilePath);
            Assert.isTrue(dataAssetFile.toFile().exists(), "数据资产文件不存在，请重新上传");
            if (STRUCTURED.equals(dataProduct.getDataExt().getDataType())) {
                Assert.isTrue(DataAsset.validateFilenameUsingRegex(dataAssetFilePath.toLowerCase(), List.of(FileTypeConstants.CSV)), "支持的文件格式为：" + FileTypeConstants.CSV);
                String lineValue;
                try {
                    lineValue = FileUtils.readFirstLine(dataAssetFile.toFile());
                } catch (IOException e) {
                    log.warn("读取文件第一行尝试获取文件列数失败", e);
                    throw new RestfulApiException("读取文件第一行尝试获取文件列数失败", e);
                }
                List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
                Assert.isTrue(dataProduct.getDataExt().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
                ).count() == dataSchema.size(), "原始文件列数与数据集描述不一致，请检查是否正确配置");
            }

            dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath);
            dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFileHash(SM3DigestUtil.getHash(dataAssetFile.toFile()));
        }

//        else if (SourceType.DATABASE.equals(accessWay)) {
//
//        }
        // 调试文件需要确认是否需要处理？
//        if (DebugDataSourceEnum.EXTRA_UPLOAD.equals(dataProductAccessConfigUpdateRequest.getDebugDataSource())) {
//            Assert.isTrue(StringUtils.isNotBlank(dataProductAccessConfigUpdateRequest.getTempDebugFileId()), "请上传数据资产调试文件");
//            String dataAssetDebugFilePath = dataAssetCache.get(dataProductAccessConfigUpdateRequest.getTempDebugFileId());
//            Assert.isTrue(dataAssetDebugFilePath != null, "数据资产调试文件不存在，请重新上传");
//            Path dataAssetDebugFile = Paths.get(dataAssetDebugFilePath);
//            Assert.isTrue(dataAssetDebugFile.toFile().exists(), "数据资产调试文件不存在，请重新上传");
//            String lineValue;
//            try {
//                lineValue = FileUtils.readFirstLine(dataAssetDebugFile.toFile());
//            } catch (IOException e) {
//                log.warn("读取调试文件第一行尝试获取文件列数失败", e);
//                throw new RestfulApiException("读取调试文件第一行尝试获取文件列数失败", e);
//            }
//            List<DataSchemaBO> dataSchema = parseDataSchema(SeparatorEnum.comma.getFieldDelimiter(), lineValue);
//            Assert.isTrue(dataProductAccessConfigUpdateRequest.getDataSchema().stream().filter(DataSchemaBO::isAllowQuery
//            ).count() == dataSchema.size(), "调试数据文件列数与数据集描述不一致，请检查是否正确配置");
//            dataProduct.getDataExt().setDebugDataPath(dataAssetDebugFilePath);
//            dataProduct.getDataExt().setSeparator(ObjectUtils.isEmpty(dataProductAccessConfigUpdateRequest.getSeparator()) ? "," : dataProductAccessConfigUpdateRequest.getSeparator());
//            dataProduct.getDataExt().setHasHeader(ObjectUtils.isEmpty(dataProductAccessConfigUpdateRequest.getHasHeader()) ? 1 : dataProductAccessConfigUpdateRequest.getHasHeader());
//        }
        dataProduct.setUpdateTime(new Date());
        dataProduct = dataProductRepository.saveAndFlush(dataProduct);
        if (SourceType.API.equals(accessWay) || SourceType.FROM_MPC.equals(accessWay)) {
            try {
                // 对接方式：API，创建网关代理
                CreateRouteResponse serviceAndRouteForExternalAPI = gatewayWebApi.createServiceAndRouteForExternalAPI(dataProduct.getId(),
                        dataProduct.getDataExt().getApiSourceMetadata().getDataPath(), dataProduct.getDataExt().getApiSourceMetadata().getResponse(),
                        dataProduct.getDataExt().getApiSourceMetadata().getResponseEcho(),
                        String.format("%s_%s", dataProduct.getId(), dataProduct.getDataProductName()),
                        dataProduct.getId(), dataProduct.getDataExt().getApiSourceMetadata().getUrl(), Boolean.TRUE.equals(dataProduct.getDataExt().getExtractResponse()));
                this.updateDataExt(dataProduct.getId(), dataProductExt -> {
                    dataProductExt.setGatewayServiceRouteId(serviceAndRouteForExternalAPI.getData().getInvokeResult().getId());
                    return dataProductExt;
                });
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("route name exists")) {
                    DescribeRoutesResponse describeRoutesResponse = gatewayWebApi.describeRoutes(dataProduct.getId());
                    DataProduct finalDataProduct = dataProduct;
                    Optional<DescribeRouteResponse.InvokeResult> route = describeRoutesResponse.getData().getInvokeResult().getRows().stream().filter(_route -> finalDataProduct.getId().equals(_route.getName())).findFirst();
                    if (route.isEmpty()) {
                        return;
                    }
                    this.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.setGatewayServiceRouteId(route.get().getId());
                        return dataProductExt;
                    });
                } else {
                    log.error("createServiceAndRouteForExternalAPI 失败:", e);
                }
            }
        }
    }

    @Override
    public StatisticAssetDTO.StatisticProduct statistics() {
        Specification<DataProduct> dataProductSpecification = DataProduct.userIdIs(null, LoginContextHolder.currentUser().getId());
        Map<DeliveryMode, Integer> deliveryModeCounts = new HashMap<>();
        for (DeliveryMode deliveryMode : DeliveryMode.values()) {
            if (deliveryMode.name().startsWith("TEE") && deliveryModeCounts.containsKey(DeliveryMode.TEE)) {
                continue;
            }
            if (deliveryMode.name().startsWith("MPC_")) {
                continue;
            }
            deliveryMode = deliveryMode.name().startsWith("TEE") ? DeliveryMode.TEE : deliveryMode;
            deliveryModeCounts.put(deliveryMode, (int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.itemStatusIs(DataProduct.deliveryModeIs(dataProductSpecification, deliveryMode), "item_status2"), "2")));
        }
        List<String> deliveryMethods = List.of("01", "02", "03");
        Map<String, Integer> deliveryMethodCounts = new HashMap<>();
        for (String deliveryMethod : deliveryMethods) {
            deliveryMethodCounts.put(deliveryMethod, (int) dataProductRepository.count(DataProduct.itemStatusIs(DataProduct.deliveryMethodIs(dataProductSpecification, deliveryMethod), "item_status2")));
        }
        return StatisticAssetDTO.StatisticProduct.builder()
                .count((int) dataProductRepository.count(DataProduct.itemStatusIs(dataProductSpecification, "item_status2")))
                .structuredCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.dataTypeIs(dataProductSpecification, STRUCTURED), "2")))
                .unstructuredCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.dataTypeIs(dataProductSpecification, UNSTRUCTURED), "2")))
                .dataAssetCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.itemStatusIs(DataProduct.dataType1Is(dataProductSpecification, "数据集"), "item_status2"), "2")))
                .pictureCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.itemStatusIs(DataProduct.dataType1Is(dataProductSpecification, "图像"), "item_status2"), "2")))
                .textCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.itemStatusIs(DataProduct.dataType1Is(dataProductSpecification, "文件"), "item_status2"), "2")))
                .modelCount((int) dataProductRepository.count(DataProduct.publishStatusIs(DataProduct.itemStatusIs(DataProduct.dataType1Is(dataProductSpecification, "模型"), "item_status2"), "2")))
                .deliveryModeCount(deliveryModeCounts)
                .deliveryMethodCount(deliveryMethodCounts)
                .build();
    }
}
