package com.ailpha.ailand.dataroute.endpoint.node.controller;

import com.ailpha.ailand.dataroute.endpoint.node.dto.*;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.dbapp.rest.response.SuccessResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "功能节点管理")
@RestController
@RequestMapping("/node")
@RequiredArgsConstructor
@Deprecated
public class NodeController {
    private final NodeService nodeService;

    @PostMapping
    @Operation(summary = "创建节点")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<NodeDTO> createNode(@RequestBody NodeRequest request) {
        return SuccessResponse.success(nodeService.createNode(request)).build();
    }

    /**
     * 更新节点信息，包括节点名称和节点类型等。
     *
     * @param id                节点ID
     * @param updateNodeRequest 更新节点请求参数，包含节点名称、节点类型等
     * @return 更新后的节点信息
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新节点")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<NodeDTO> updateNode(@PathVariable Long id, @RequestBody UpdateNodeRequest updateNodeRequest) {
        // 注意：您需要确保 nodeService.updateNode 方法能够接收 UpdateNodeRequest类型的参数
        // 或者在 service 层进行相应的适配处理
        return SuccessResponse.success(nodeService.updateNode(id, updateNodeRequest)).build();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除节点")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<Boolean> deleteNode(@PathVariable Long id) {
        nodeService.deleteNode(id);
        return SuccessResponse.success(true).build();
    }

    @PostMapping("/{id}/activate")
    @Operation(summary = "激活节点")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<NodeDTO> activateNode(@PathVariable Long id) {
        return SuccessResponse.success(nodeService.activateNode(id)).build();
    }

    @PostMapping("/{id}/deactivate")
    @Operation(summary = "停用节点")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<NodeDTO> deactivateNode(@PathVariable Long id) {
        return SuccessResponse.success(nodeService.deactivateNode(id)).build();
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取节点详情")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<NodeDTO> getNode(@PathVariable Long id) {
        return SuccessResponse.success(nodeService.getNode(id)).build();
    }

    @PostMapping("/list")
    @Operation(summary = "查询节点列表")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public SuccessResponse<List<NodeDTO>> listNodes(@RequestBody NodePageRequest request) {
        return nodeService.listNodes(request);
    }

    @PostMapping("/simple/page")
    @Operation(summary = "分页查询节点列表(简化版)")
    public SuccessResponse<List<NodeSimpleDTO>> pageNodesSimple(@RequestBody NodeSimplePageRequest request) {
        return nodeService.pageNodesSimple(request);
    }
}