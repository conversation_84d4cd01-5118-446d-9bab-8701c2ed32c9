package com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpgradeReq implements Serializable {

    @NotEmpty(message = "升级包ID不能为空")
    @Schema(description = "升级包ID")
    private String packageId;

    @NotNull(message = "是否立即升级不能为空")
    @Schema(description = "是否立即升级")
    private Boolean immediately;

    @Schema(description = "升级时间：yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upgradeTime;
}
