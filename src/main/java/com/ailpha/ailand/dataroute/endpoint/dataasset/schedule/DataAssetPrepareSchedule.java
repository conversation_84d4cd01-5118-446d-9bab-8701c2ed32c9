package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SM3DigestUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.FileEncryptKeyHeader;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.DatasourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductListVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PartitionQueryConditionDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.QualificationDoc;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.config.GatewayConfig;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.MethodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.HubGanzhouApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.response.FileUploadResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.HubShuHanApiClient;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductPublishVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataProductPublishVO;
import com.ailpha.ailand.dataroute.endpoint.third.input.DataCollectorJobCallback;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.MPCDatasetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.TEEDatasetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.DataCollectorApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataCollectorJobParam;
import com.ailpha.ailand.dataroute.endpoint.third.request.MPCDataset;
import com.ailpha.ailand.dataroute.endpoint.third.request.TEEDataset;
import com.ailpha.ailand.dataroute.endpoint.third.response.*;
import com.ailpha.ailand.jni.SecIslandTEE;
import com.ailpha.ailand.plugin.reader.httpreader.RestTemplateUtil;
import com.ailpha.ailand.utils.Bean2HeaderUtils;
import com.ailpha.ailand.utils.file.GzipUtils;
import com.ailpha.ailand.utils.safe.RSAUtil;
import com.dbapp.rest.response.SuccessResponse;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.UploadObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class DataAssetPrepareSchedule {

    private final RouterService routerService;

    private final FilesStorageServiceImpl filesStorageService;

    private final DataCollectorApi dataCollectorApi;

    private final MinioClient minioClient;

    private final KeyPair keyPair;

    @Value("${server.port}")
    Integer serverPort;

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final GatewayWebApi gatewayWebApi;

    private final GatewayConfig gatewayConfig;

    private final TEEDatasetMapper teeDatasetMapper;

    private final MPCDatasetMapper mpcDatasetMapper;

    private final MPCRemote mpcRemote;

    private final BaseCapabilityManager baseCapabilityManager;

    @Lazy
    private final TraderService traderService;

    private static final ConcurrentHashMap<String, DataProduct> WAIT_EXECUTOR_CALLBACK = new ConcurrentHashMap<>();

    private final TenantRepository tenantRepository;
    private final CompanyRepository companyRepository;

    private final DataProductService dataProductService;

    private final DataProductRepository dataProductRepository;

    private final HubGanzhouApiClient hubGanzhouApiClient;

    private final ApplicationContext context;

    @Scheduled(fixedDelay = 30, initialDelay = 5, timeUnit = TimeUnit.SECONDS)
    public void prepareDataProduct() {
        Set<Company> companySet = tenantRepository.findAll().stream().map(t -> {
                    try {
                        Optional<Company> companyReference = companyRepository.findById(Long.valueOf(StringUtils.substringAfter(t.getSchemaName(), "_")));
                        return companyReference.orElse(null);
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        companySet.forEach(c -> {
            try {
                HubShuHanApiClient.setCurrentCompany(c);
                TenantContext.setCurrentTenant("tenant_" + c.getId());
                doPrepareDataAsset();
                TenantContext.clear();
            } catch (Exception e) {
                log.warn("处理企业 {} 预处理任务失败", c, e);
            }
        });

    }

    private void doPrepareDataAsset() {
        if (BUSY.get()) {
            return;
        }
        try {
            BUSY.set(true);
            SuccessResponse<List<DataProductListVO>> dataItemList =
                    dataProductService.allDataAssets(1, 10, specification -> {
//                        specification = DataProduct.itemStatusIs(specification, "item_status2");// 登记审批通过的
                        specification = DataProduct.itemStatusIs(specification, "item_status1");// NOTE 赣州没登记审批
//                        specification = DataProduct.publishStatusIs(specification, "2");// 上架审批通过的
                        specification = DataProduct.prepareStatusIs(specification, DataAssetPrepareStatus.CREATED);
                        return specification;
                    });
            for (DataProductListVO dataProductListVO : dataItemList.getData()) {
                DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductListVO.getId());
                if (WAIT_EXECUTOR_CALLBACK.containsKey(dataProduct.getId())) {
                    JobInfo jobInfo = dataCollectorApi.queryJobStatus(dataProduct.getId());
                    if (jobInfo != null && jobInfo.getState().isRunning()) {
                        continue;
                    }
                }
                prepareDataProduct(dataProduct);
            }
        } finally {
            BUSY.set(false);
        }
    }

    private void prepareDataProduct(DataProduct dataProduct) {
        try {
            dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.SYNC);
            dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            if (SourceType.API.equals(dataProduct.getSourceType()) || SourceType.FROM_MPC.equals(dataProduct.getSourceType())) {
                if (APIQueryWay.REALTIME.equals(dataProduct.getDataExt().getApiQueryWay())) {
                    // 在线交付，对接方式：API接口
                    onlineDelivery(dataProduct);
                } else if (APIQueryWay.OFFLINE.equals(dataProduct.getDataExt().getApiQueryWay())) {
                    // 离线交付，对接方式：API接口，前置机任务回调后继续处理
                    Path dataAssetFilePath = filesStorageService.getRootPath()
                            .resolve("dataProduct")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getId() + ".csv");
                    try {
                        String apiKey = gatewayWebApi.generateAPIKeyForOrder(dataProduct.getUserId(), dataProduct.getDataExt().getGatewayServiceRouteId());
                        ParamsBO apikey = ParamsBO.builder()
                                .dataType(DataTypeEnum.STRING)
                                .key("apikey")
                                .value(apiKey)
                                .build();
                        if (CollectionUtils.isEmpty(dataProduct.getDataExt().getApiSourceMetadata().getHeaders())) {
                            dataProduct.getDataExt().getApiSourceMetadata().setHeaders(new ArrayList<>());
                        }
                        if (MethodEnum.GET.equals(dataProduct.getDataExt().getApiSourceMetadata().getMethod())) {
                            dataProduct.getDataExt().getApiSourceMetadata().getParams().add(apikey);
                        } else {
                            dataProduct.getDataExt().getApiSourceMetadata().getHeaders().add(apikey);
                        }
                        dataProduct.getDataExt().getApiSourceMetadata().getHeaders().add(ParamsBO.builder()
                                .dataType(DataTypeEnum.STRING)
                                .key("API_TYPE")
                                .value("OTHER")
                                .build());
                        dataProduct.getDataExt().getApiSourceMetadata().setDataPath(List.of(PathTypeBO.builder()
                                .dataPath("根节点")
                                .dataType(DataTypeEnum.ARRAY)
                                .build()));
                        dataProduct.getDataExt().getApiSourceMetadata().setUrl(String.format("%s/data_%s", gatewayConfig.getServerBaseUrl(), dataProduct.getId()));
                        boolean collectorJobResult = dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
                                .dataAssetId(dataProduct.getId())
                                .datasourceType(DatasourceType.http)
                                .filepath(dataAssetFilePath.toFile().getAbsolutePath())
                                .apiParams(dataProduct.getDataExt().getApiSourceMetadata())
                                .callbackUrl(String.format("http://%s:%s%s/%s", routerService.currentRouteVirtualIp(), serverPort, "/callback/executor", dataProduct.getId()))
                                .build());
                        Assert.isTrue(collectorJobResult, "异步提交Executor任务失败");

                        dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());
                        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                        WAIT_EXECUTOR_CALLBACK.put(dataProduct.getId(), dataProduct);
                    } catch (Exception e) {
                        log.error("异步提交Executor任务失败:", e);
                        dataProduct.getDataExt().setProcessResultMessage("异步提交Executor任务失败，error:" + e.getMessage());
                        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                    }
                }
            } else if (SourceType.FILE.equals(dataProduct.getSourceType())) {
                // 离线交付，对接方式：文件
                offlineDelivery(dataProduct);
            } else if (SourceType.DATABASE.equals(dataProduct.getSourceType())) {
                // 对接方式：数据库，前置机任务回调后继续处理
                Path dataAssetFilePath = filesStorageService.getRootPath()
                        .resolve("dataProduct")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getId() + ".csv");
                try {
                    boolean collectorJobResult = dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
                            .dataAssetId(dataProduct.getId())
                            .datasourceType(DatasourceType.valueOf(dataProduct.getDataExt().getDatabaseSourceMetadata().getDatasourceType()))
                            .jdbcUrl(dataProduct.getDataExt().getDatabaseSourceMetadata().getJdbcUrl())
                            .username(dataProduct.getDataExt().getDatabaseSourceMetadata().getUsername())
                            .password(dataProduct.getDataExt().getDatabaseSourceMetadata().getPassword())
                            .columns(dataProduct.getDataExt().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery).map(DataSchemaBO::getOriginalColumnName).toList())
                            .columnAlias(dataProduct.getDataExt().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery)
                                    .map(dataSchemaBO -> StringUtils.isNotBlank(dataSchemaBO.getName()) ? dataSchemaBO.getName() : dataSchemaBO.getFieldName()).toList())
                            .tableName(dataProduct.getDataExt().getDatabaseSourceMetadata().getTableName())
                            .filepath(dataAssetFilePath.toFile().getAbsolutePath())
                            .conditions(
                                    dataProduct.getDataExt().getDatabaseSourceMetadata().getPartitionColumns() != null ?
                                            dataProduct.getDataExt().getDatabaseSourceMetadata().getPartitionColumns().stream()
                                                    .filter(partitionColumnBO -> Boolean.TRUE.equals(partitionColumnBO.getSelected()))
                                                    .map(partitionColumnBO -> PartitionQueryConditionDTO.builder()
                                                            .colName(partitionColumnBO.getColumn())
                                                            .value(partitionColumnBO.getColumnValue())
                                                            .build())
                                                    .toList()
                                            : null
                            )
                            .syncRule(dataProduct.getDataExt().getDatabaseSourceMetadata().getSyncRule())
                            .callbackUrl(String.format("http://%s:%s%s/%s", routerService.currentRouteVirtualIp(), serverPort, "/callback/executor", dataProduct.getId()))
                            .build());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());
                        return dataProductExt;
                    });
                    dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());
                    Assert.isTrue(collectorJobResult, "异步提交Executor任务失败");
                    WAIT_EXECUTOR_CALLBACK.put(dataProduct.getId(), dataProduct);
                } catch (Exception e) {
                    log.error("异步提交Executor任务失败:", e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.setProcessResultMessage("异步提交Executor任务失败，error:" + e.getMessage());
                        return dataProductExt;
                    });
                }
            }
        } catch (Exception e) {
            log.warn("数据资产数据预处理失败: ", e);
        }
    }

    /**
     * 在线交付
     */
    private void onlineDelivery(DataProduct dataProduct) {
        DataAssetPrepareStatus dataAssetPrepareStatus = dataProduct.getDataExt().getDataAssetPrepareStatus();
        if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(dataAssetPrepareStatus) || DataAssetPrepareStatus.SYNC_FAILURE.equals(dataAssetPrepareStatus)) {
            return;
        }
        for (DeliveryMode deliveryMode : dataProduct.getDeliveryExt().getDeliveryModes()) {
            if (DeliveryMode.API.equals(deliveryMode)) {
                // 交付方式：API及时查询
                dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                    dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder().dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE).build());
                    return dataProductExt;
                });
            } else if (DeliveryMode.TEE_ONLINE.equals(deliveryMode)) {
                // 交付方式：TEE在线数据集
                try {
                    TEEDataset teeDataset = teeDatasetMapper.dataAssetToTeeDataset(dataProduct, DeliveryMode.TEE_ONLINE);
                    // NOTE 调用名称
                    teeDataset.setName("P_" + teeDataset.getName());
                    log.debug("同步TEE在线数据集:{}", teeDataset);
                    MDC.put(SystemConstants.COMPANY_ID, String.valueOf(dataProduct.getProvider().getCompany().getId()));
                    BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.TEE);
                    Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/openApiKey/token"), null, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());
                    String response = RestTemplateUtil.postFormData(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/dataRouter/createDataset"), headers, teeDataset.toFormDataBody());
                    Map<String, Object> responseObject = JacksonUtils.json2map(response);
                    Assert.isTrue(Integer.parseInt(responseObject.get("code").toString()) == 0, responseObject.get("message").toString());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder().dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE).build());
                        return dataProductExt;
                    });
                } catch (Exception e) {
                    log.error("同步TEE在线数据集，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE)
                                .processResultMessage(e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                }
            }
        }
        // 会签预处理状态
        mergeProcessStatus(dataProduct.getId());
    }

    /**
     * 离线交付
     */
    private void offlineDelivery(DataProduct dataProduct) {
        for (DeliveryMode deliveryMode : dataProduct.getDeliveryExt().getDeliveryModes()) {
            if (DeliveryMode.FILE_DOWNLOAD.equals(deliveryMode)) {
                // 交付方式：文件下载
                fileDelivery(dataProduct);
            } else if (DeliveryMode.MPC.equals(deliveryMode)) {
                // 交付方式：MPC
                try {
                    MPCDataset mpcDataset = mpcDatasetMapper.dataAssetToMPCDataset(dataProduct);
                    // NOTE 调用名称
                    mpcDataset.setDatasetName("P_" + mpcDataset.getDatasetName());
                    log.debug("同步MPC数据集:{}", mpcDataset);
                    MDC.put(SystemConstants.COMPANY_ID, String.valueOf(dataProduct.getProvider().getCompany().getId()));
                    CommonResult<Void> response = mpcRemote.syncDataset(mpcDataset);
                    Assert.isTrue(response.getCode() == 1, response.getMessage());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.MPC, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                                .build());
                        return dataProductExt;
                    });
                } catch (Exception e) {
                    log.error("同步MPC数据集失败，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.MPC, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE).processResultMessage("同步MPC数据集失败，error:" + e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                }
            } else if (DeliveryMode.TEE_OFFLINE.equals(deliveryMode)) {
                // 交付方式：TEE离线数据集
                try {
                    TEEDataset teeDataset = teeDatasetMapper.dataAssetToTeeDataset(dataProduct, DeliveryMode.TEE_OFFLINE);
                    // NOTE 调用名称
                    teeDataset.setName("P_" + teeDataset.getName());
                    log.debug("同步TEE离线数据集:{}", teeDataset);
                    MDC.put(SystemConstants.COMPANY_ID, String.valueOf(dataProduct.getProvider().getCompany().getId()));
                    BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(BaseCapabilityType.TEE);
                    Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/openApiKey/token"), null, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());
                    String response = RestTemplateUtil.postFormData(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/dataRouter/createDataset"), headers, teeDataset.toFormDataBody());
                    Map<String, Object> responseObject = JacksonUtils.json2map(response);
                    Assert.isTrue(Integer.parseInt(responseObject.get("code").toString()) == 0, responseObject.get("message").toString());

                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.TEE_OFFLINE, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                                .build());
                        return dataProductExt;
                    });
                } catch (Exception e) {
                    log.error("同步TEE离线数据集失败，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.TEE_OFFLINE, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE).processResultMessage("同步TEE离线数据集失败，error:" + e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                }
            }
        }
        // 会签预处理状态
        mergeProcessStatus(dataProduct.getId());
    }

    /**
     * 会签预处理状态
     */
    private void mergeProcessStatus(String dataAssetId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataAssetId);
        DataAssetPrepareStatus dataAssetPrepareStatus = dataProduct.getDataExt().getDataAssetPrepareStatus();
        if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(dataAssetPrepareStatus) || DataAssetPrepareStatus.SYNC_FAILURE.equals(dataAssetPrepareStatus)) {
            return;
        }
        Map<DeliveryMode, DataAssetExt.ProcessResult> processResult = dataProduct.getDataExt().getProcessResult();
        // 资产不可用
        for (Map.Entry<DeliveryMode, DataAssetExt.ProcessResult> deliveryModeProcessResultEntry : processResult.entrySet()) {
            DataAssetExt.ProcessResult result = deliveryModeProcessResultEntry.getValue();
            if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(result.getDataAssetPrepareStatus())
                    || DataAssetPrepareStatus.SYNC_FAILURE.equals(result.getDataAssetPrepareStatus())) {
                dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE);
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                // 清理明文文件
                traderService.deleteFileSource(dataProduct.getUserId(), dataProduct.getDataExt().getFileSourceMetadata());
                return;
            }
        }
        // 资产可用
        if (dataProduct.getDeliveryExt().getDeliveryModes().size() == processResult.size()) {
            for (DataAssetExt.ProcessResult result : processResult.values()) {
                if (!DataAssetPrepareStatus.AVAILABLE.equals(result.getDataAssetPrepareStatus())) {
                    return;
                }
            }

            dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE);
            dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            // 清理明文文件
            traderService.deleteFileSource(dataProduct.getUserId(), dataProduct.getDataExt().getFileSourceMetadata());
        }
    }

    /**
     * 交付方式：文件下载
     */
    private void fileDelivery(DataProduct dataProduct) {
        try {
            String extension = "csv";
            try {
                extension = FileUtil.extName(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
            } catch (Exception ignore) {
            }
            String fileName = dataProduct.getDataProductName() + "." + extension;
            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(dataProduct.getUserId()).build());
            if (log.isDebugEnabled()) {
                log.debug("check bucket {} Exists {}", dataProduct.getUserId(), bucketExists);
            }
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(dataProduct.getUserId()).build());
            }
            File tarFile = new File(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath() + ".tar.gz");
            if (log.isDebugEnabled()) {
                log.debug("创建压缩文件 {}", tarFile.getAbsolutePath());
            }
            GzipUtils.createTarFile(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath(), tarFile.getAbsolutePath());
            String encryptKey = UuidUtils.uuid32();
            if (log.isDebugEnabled()) {
                log.debug("对文件 {}.encrypted 进行加密", tarFile.getAbsolutePath());
            }
            SecIslandTEE.tee_file_Encrypt(encryptKey.getBytes(StandardCharsets.UTF_8), tarFile.getAbsolutePath(), tarFile.getAbsolutePath() + ".encrypted");
            InputStream inputStream = Bean2HeaderUtils.addHeaderAheadStream(Files.newInputStream(Paths.get(tarFile.getAbsolutePath() + ".encrypted")), FileEncryptKeyHeader.builder()
                    .encryptKey(RSAUtil.encodeToString(RSAUtil.encryptByPublicKey(encryptKey.getBytes(StandardCharsets.UTF_8), (RSAPublicKey) keyPair.getPublic())))
                    .build());
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            Files.copy(inputStream, Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            inputStream.close();
            minioClient.uploadObject(UploadObjectArgs.builder()
                    .bucket(dataProduct.getUserId())
                    .object(Paths.get(dataProduct.getId(), fileName).toString())
                    .filename(tarFile.getAbsolutePath() + ".encrypted1")
                    .build());
            dataProduct.getDataExt().getProcessResult().put(DeliveryMode.FILE_DOWNLOAD, DataAssetExt.ProcessResult.builder()
                    .dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE)
                    .build());
            dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted"));
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            Files.deleteIfExists(tarFile.toPath());
        } catch (Exception e) {
            log.warn("上传文件到minIO失败", e);
        }
    }

    /**
     * 前置机任务回调
     */
    public void executorCallback(String assetId, DataCollectorJobCallback.ExecutorJobRunResult executorJobRunResult) {
        try {
            DataProduct dataProduct = WAIT_EXECUTOR_CALLBACK.get(assetId);
            if (dataProduct == null) {
                // TODO 无法确定 TenantContext.setCurrentTenant("tenant_" + c.getId());
//                dataProduct = dataProductRepository.getReferenceById(assetId);
                log.warn("上下文丢失，无法确定 tenant id");
                return;
            }
            TenantContext.setCurrentTenant("tenant_" + dataProduct.getProvider().getCompany().getId());
            if (executorJobRunResult.isSuccess()) {
                // 更新文件数据hash
                dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFileHash(SM3DigestUtil.getHash(new File(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath())));
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                // 离线交付
                offlineDelivery(dataProduct);
            } else {
                log.error("前置机任务执行失败:{}", executorJobRunResult.getMessage());
                dataProduct.getDataExt().setProcessResultMessage("前置机任务执行失败，error:" + executorJobRunResult.getMessage());
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            }
        } finally {
            WAIT_EXECUTOR_CALLBACK.remove(assetId);
            TenantContext.clear();
        }
    }

    /**
     * 数据集同步回调
     */
    public void datasetSyncCallback(SyncDatasetResult syncDatasetResult, String platform) {
        String assetId = syncDatasetResult.getAssetId();
        DataProduct dataProduct = dataProductRepository.getReferenceById(assetId);
        DeliveryMode deliveryMode;
        if (platform.equals("TEE")) {
            deliveryMode = SourceType.API.equals(dataProduct.getSourceType()) && APIQueryWay.REALTIME.equals(dataProduct.getDataExt().getApiQueryWay()) ?
                    DeliveryMode.TEE_ONLINE : DeliveryMode.TEE_OFFLINE;
        } else if (platform.equals("MPC")) {
            deliveryMode = DeliveryMode.MPC;
        } else {
            deliveryMode = dataProduct.getDeliveryExt().getDeliveryModes().getFirst();
        }
        if (Boolean.TRUE.equals(syncDatasetResult.getResult())) {
            dataProduct.getDataExt().getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder().dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE).build());
        } else {
            dataProduct.getDataExt().getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder().dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE)
                    .processResultMessage(deliveryMode + "数据集同步失败：" + syncDatasetResult.getMessage())
                    .build());
        }
        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
        MDC.put("hubInfo", JSONUtil.toJsonStr(dataProduct.getProvider().getCompany().getServiceNode().getHubInfo()));
        // 会签预处理状态
        mergeProcessStatus(dataProduct.getId());
    }

}
