package com.ailpha.ailand.dataroute.endpoint.dataasset.schedule;

import cn.hutool.core.io.FileUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.model.Table;
import com.ailpha.ailand.biz.dao.constants.ModelSourceType;
import com.ailpha.ailand.biz.web.vo.dataset.CreateModelRequestVO;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.SM3DigestUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.FileEncryptKeyHeader;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateStatus;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTask;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.DataUpdateTaskLog;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.DatasourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataUpdateTaskLogRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataUpdateTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.LocalProductRefRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz.DataUpdateCronJobParams;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataUpdateTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.TraderService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.PartitionQueryConditionDTO;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.third.apigate.GatewayWebApi;
import com.ailpha.ailand.dataroute.endpoint.third.config.GatewayConfig;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DataTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.MethodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ShuhanResponse;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductPublishVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request.DataProductSaveVM;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.response.DataProductPublishVO;
import com.ailpha.ailand.dataroute.endpoint.third.input.DataCollectorJobCallback;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.MPCDatasetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.mapper.TEEDatasetMapper;
import com.ailpha.ailand.dataroute.endpoint.third.output.DataCollectorApi;
import com.ailpha.ailand.dataroute.endpoint.third.output.MPCRemote;
import com.ailpha.ailand.dataroute.endpoint.third.output.TeeRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.DataCollectorJobParam;
import com.ailpha.ailand.dataroute.endpoint.third.request.MPCDataset;
import com.ailpha.ailand.dataroute.endpoint.third.request.OpenDataRouterModelRequest;
import com.ailpha.ailand.dataroute.endpoint.third.request.TEEDataset;
import com.ailpha.ailand.dataroute.endpoint.third.response.*;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserDetailsResponse;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.jni.SecIslandTEE;
import com.ailpha.ailand.plugin.reader.httpreader.RestTemplateUtil;
import com.ailpha.ailand.utils.Bean2HeaderUtils;
import com.ailpha.ailand.utils.file.GzipUtils;
import com.ailpha.ailand.utils.safe.RSAUtil;
import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.UploadObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class DataAssetPrepareSchedule {

    private final RouterService routerService;

    private final FilesStorageServiceImpl filesStorageService;

    private final DataCollectorApi dataCollectorApi;

    private final MinioClient minioClient;

    private final KeyPair keyPair;

    @Value("${server.port}")
    Integer serverPort;

    private final GatewayWebApi gatewayWebApi;

    private final GatewayConfig gatewayConfig;

    private final TEEDatasetMapper teeDatasetMapper;

    private final MPCDatasetMapper mpcDatasetMapper;

    private final MPCRemote mpcRemote;

    private final TeeRemote teeRemote;

    private final BaseCapabilityManager baseCapabilityManager;

    @Lazy
    private final TraderService traderService;

    private static final ConcurrentHashMap<String, DataProduct> WAIT_EXECUTOR_CALLBACK = new ConcurrentHashMap<>();

    @Lazy
    private final DataProductService dataProductService;

    private final DataProductRepository dataProductRepository;

    private final ApplicationContext context;

    private final AiLandProperties aiLandProperties;
    private final ServiceNodeRemoteService serviceNodeRemoteService;

    private final DataUpdateTaskLogRepository dataUpdateTaskLogRepository;
    private final DataUpdateTaskRepository dataUpdateTaskRepository;

//    private final Object lock = new Object();

    public void prepareDataProduct(DataProduct dataProduct) {
        // 清理数据产品预处理结果，防止出现任务失败干扰后面的任务
        dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
            if (!dataProductExt.getProcessResult().isEmpty()) {
                dataProductExt.getProcessResult().clear();
            }

            return dataProductExt;
        });
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());
        List<DataUpdateStatus> historyStatus = dataUpdateTaskService.getDataUpdateTaskStatus(dataUpdateTask.getId());
        dataUpdateTaskLogRepository.updateStartTime(dataUpdateTask.getLatestTaskLogId(), new Date());
        try {
            // 只有历史任务没成功过，才会更新数据产品的预处理状态
            if (historyStatus.isEmpty() || !historyStatus.contains(DataUpdateStatus.SUCCESS)) {
                dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.SYNC);
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            }
            dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.SYNCING);
            if (SourceType.API.equals(dataProduct.getSourceType())
                    || SourceType.FROM_MPC.equals(dataProduct.getSourceType())) {
                if (APIQueryWay.REALTIME.equals(dataProduct.getDataExt().getApiQueryWay())) {
                    // 在线交付，对接方式：API接口
                    onlineDelivery(dataProduct);
                } else if (APIQueryWay.OFFLINE.equals(dataProduct.getDataExt().getApiQueryWay())) {
                    // 离线交付，对接方式：API接口，前置机任务回调后继续处理
                    Path dataAssetFilePath = filesStorageService.getRootPath()
                            .resolve("dataProduct")
                            .resolve(dataProduct.getUserId())
                            .resolve(dataProduct.getId() + ".csv");
                    try {
                        if (CollectionUtils.isEmpty(dataProduct.getDataExt().getApiSourceMetadata().getHeaders())) {
                            dataProduct.getDataExt().getApiSourceMetadata().setHeaders(new ArrayList<>());
                        }
                        // 只有第一次预处理时，add apikey, API_TYPE
                        if (dataUpdateTask.getTaskLogNum() == 1) {
                            String apiKey = gatewayWebApi.generateAPIKeyForOrder(dataProduct.getUserId(),
                                    dataProduct.getDataExt().getGatewayServiceRouteId());
                            ParamsBO apikey = ParamsBO.builder()
                                    .dataType(DataTypeEnum.STRING)
                                    .key("apikey")
                                    .value(apiKey)
                                    .build();
                            if (MethodEnum.GET.equals(dataProduct.getDataExt().getApiSourceMetadata().getMethod())) {
                                dataProduct.getDataExt().getApiSourceMetadata().getParams().add(apikey);
                            } else {
                                dataProduct.getDataExt().getApiSourceMetadata().getHeaders().add(apikey);
                            }
                            dataProduct.getDataExt().getApiSourceMetadata().getHeaders().add(ParamsBO.builder()
                                    .dataType(DataTypeEnum.STRING)
                                    .key("API_TYPE")
                                    .value("OTHER")
                                    .build());
                        }
                        dataProduct.getDataExt().getApiSourceMetadata().setDataPath(List.of(PathTypeBO.builder()
                                .dataPath("根节点")
                                .dataType(DataTypeEnum.ARRAY)
                                .build()));
                        dataProduct.getDataExt().getApiSourceMetadata().setUrl(
                                String.format("%s/data_%s", gatewayConfig.getServerBaseUrl(), dataProduct.getId()));
                        boolean collectorJobResult = dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
                                .jobId(dataUpdateTask.getLatestTaskLogId())
                                .dataAssetId(dataProduct.getId())
                                .datasourceType(DatasourceType.http)
                                .filepath(dataAssetFilePath.toFile().getAbsolutePath())
                                .apiParams(dataProduct.getDataExt().getApiSourceMetadata())
                                .callbackUrl(String.format("http://%s:%s%s/%s", routerService.currentRouteVirtualIp(),
                                        serverPort, "/callback/executor", dataProduct.getId()))
                                .build());

                        Assert.isTrue(collectorJobResult, "异步提交Executor任务失败");
                        dataProduct.getDataExt().getFileSourceMetadata()
                                .setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());
                        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                        WAIT_EXECUTOR_CALLBACK.put(dataProduct.getId(), dataProduct);
                        log.info("Async submission of API OFFLINE executor tasks success [{}]",
                                dataUpdateTask.getLatestTaskLogId());
                    } catch (Exception e) {
                        log.error("异步提交Executor任务失败:", e);
                        dataProduct.getDataExt().setProcessResultMessage("异步提交Executor任务失败，error:\n" + e.getMessage());
                        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                        log.error("Async submission of executor API OFFLINE tasks failed, reason: {} [{}]",
                                e.getMessage(), dataUpdateTask.getLatestTaskLogId());
                        // throw new Exception(String.format("Async submission of executor tasks failed,
                        // reason(%s)", e.getMessage()));
                        dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
                        dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.FAILED);
                        dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
                        dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));
                        dataUpdateTaskLogRepository.updateExt(dataUpdateTask.getLatestTaskLogId(), DataUpdateTaskLog.DataUpdateTaskLogExt.builder()
                                .processResultMessage("异步提交Executor任务失败，error:" + e.getMessage()).build());
                    }
                }
            } else if (SourceType.FILE.equals(dataProduct.getSourceType())) {
                // 离线交付，对接方式：文件
                offlineDelivery(dataProduct);
            } else if (SourceType.DATABASE.equals(dataProduct.getSourceType())) {
                // 对接方式：数据库，前置机任务回调后继续处理
                Path dataAssetFilePath = filesStorageService.getRootPath()
                        .resolve("dataProduct")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getId() + ".csv");
                try {
                    boolean collectorJobResult = dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
                            .jobId(dataUpdateTask.getLatestTaskLogId())
                            .dataAssetId(dataProduct.getId())
                            .datasourceType(DatasourceType
                                    .valueOf(dataProduct.getDataExt().getDatabaseSourceMetadata().getDatasourceType()))
                            .jdbcUrl(dataProduct.getDataExt().getDatabaseSourceMetadata().getJdbcUrl())
                            .username(dataProduct.getDataExt().getDatabaseSourceMetadata().getUsername())
                            .password(dataProduct.getDataExt().getDatabaseSourceMetadata().getPassword())
                            .columns(
                                    dataProduct.getDataExt().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery)
                                            .map(DataSchemaBO::getOriginalColumnName).toList())
                            .columnAlias(
                                    dataProduct.getDataExt().getDataSchema().stream().filter(DataSchemaBO::isAllowQuery)
                                            .map(dataSchemaBO -> StringUtils.isNotBlank(dataSchemaBO.getName())
                                                    ? dataSchemaBO.getName()
                                                    : dataSchemaBO.getFieldName())
                                            .toList())
                            .tableName(dataProduct.getDataExt().getDatabaseSourceMetadata().getTableName())
                            .filepath(dataAssetFilePath.toFile().getAbsolutePath())
                            .conditions(
                                    dataProduct.getDataExt().getDatabaseSourceMetadata().getPartitionColumns() != null
                                            ? dataProduct.getDataExt().getDatabaseSourceMetadata().getPartitionColumns()
                                            .stream()
                                            .filter(partitionColumnBO -> Boolean.TRUE
                                                    .equals(partitionColumnBO.getSelected()))
                                            .map(partitionColumnBO -> PartitionQueryConditionDTO.builder()
                                                    .colName(partitionColumnBO.getColumn())
                                                    .value(partitionColumnBO.getColumnValue())
                                                    .build())
                                            .toList()
                                            : null)
                            .syncRule(dataProduct.getDataExt().getDatabaseSourceMetadata().getSyncRule())
                            .callbackUrl(String.format("http://%s:%s%s/%s", routerService.currentRouteVirtualIp(),
                                    serverPort, "/callback/executor", dataProduct.getId()))
                            .build());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getFileSourceMetadata()
                                .setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());
                        return dataProductExt;
                    });
                    dataProduct.getDataExt().getFileSourceMetadata()
                            .setDataAssetFilePath(dataAssetFilePath.toFile().getAbsolutePath());

                    Assert.isTrue(collectorJobResult, "异步提交Executor任务失败");
                    WAIT_EXECUTOR_CALLBACK.put(dataProduct.getId(), dataProduct);
                    log.info("Async submission of executor DATABASE tasks success [{}]", dataUpdateTask.getLatestTaskLogId());
                } catch (Exception e) {
                    log.error("异步提交Executor任务失败:", e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.setProcessResultMessage("异步提交Executor任务失败，error:" + e.getMessage());
                        return dataProductExt;
                    });

                    log.error("Async submission of executor DATABASE tasks failed, reason: {} [{}]", e.getMessage(),
                            dataUpdateTask.getLatestTaskLogId());

                    dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
                    dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.FAILED);
                    dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
                    dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));
                    dataUpdateTaskLogRepository.updateExt(dataUpdateTask.getLatestTaskLogId(), DataUpdateTaskLog.DataUpdateTaskLogExt.builder()
                            .processResultMessage("异步提交Executor任务失败，error:" + e.getMessage()).build());
                }

            }
        } catch (Exception e) {
            log.warn("数据资产数据预处理失败: ", e);
            log.error("Data asset preprocessing failed [{}]", dataUpdateTask.getLatestTaskLogId());
            dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
            dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.FAILED);
            dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
            dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));
            dataUpdateTaskLogRepository.updateExt(dataUpdateTask.getLatestTaskLogId(), DataUpdateTaskLog.DataUpdateTaskLogExt.builder()
                    .processResultMessage("数据资产数据预处理失败，error:" + e.getMessage()).build());
        }
    }

    /**
     * 在线交付
     */
    private void onlineDelivery(DataProduct dataProduct) {
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());

        DataAssetPrepareStatus dataAssetPrepareStatus = dataProduct.getDataExt().getDataAssetPrepareStatus();
        if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(dataAssetPrepareStatus)
                || DataAssetPrepareStatus.SYNC_FAILURE.equals(dataAssetPrepareStatus)) {
            return;
        }
        for (DeliveryMode deliveryMode : dataProduct.getDeliveryExt().getDeliveryModes()) {
            if (DeliveryMode.API.equals(deliveryMode)) {
                // 交付方式：API及时查询
                dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                    dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                            .dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE).build());
                    return dataProductExt;
                });
                log.info("API instant query delivery method is ready [{}]", dataUpdateTask.getLatestTaskLogId());
            } else if (DeliveryMode.TEE_ONLINE.equals(deliveryMode)) {
                // 交付方式：TEE在线数据集
                try {
                    TEEDataset teeDataset = teeDatasetMapper.dataAssetToTeeDataset(dataProduct, DeliveryMode.TEE_ONLINE);
                    // NOTE 调用名称
                    teeDataset.setName("P_" + teeDataset.getName());
                    log.debug("同步TEE在线数据集:{}", teeDataset);
                    String localCompanyId = String.valueOf(dataProduct.getProvider().getCompany().getId());
                    BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(localCompanyId,
                            BaseCapabilityType.TEE);
                    Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(
                            String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/openApiKey/token"),
                            null, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());
                    String response;
                    if (dataUpdateTask.getTaskLogNum() > 1) {
                        // 更新接口
                        response = RestTemplateUtil.postFormData(
                                String.format("%s/%s", capabilityConfig.getBaseUrl(),
                                        "_ailand/external/dataRouter/updateDataset"),
                                headers, teeDataset.toFormDataBody());
                    } else {
                        // 创建接口
                        response = RestTemplateUtil.postFormData(
                                String.format("%s/%s", capabilityConfig.getBaseUrl(),
                                        "_ailand/external/dataRouter/createDataset"),
                                headers, teeDataset.toFormDataBody());
                    }
                    // String response = RestTemplateUtil.postFormData(String.format("%s/%s",
                    // capabilityConfig.getBaseUrl(), "_ailand/external/dataRouter/createDataset"),
                    // headers, teeDataset.toFormDataBody());
                    Map<String, Object> responseObject = JacksonUtils.json2map(response);
                    Assert.isTrue(Integer.parseInt(responseObject.get("code").toString()) == 0,
                            responseObject.get("message").toString());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE).build());
                        return dataProductExt;
                    });
                    log.info("TEE online delivery method is ready [{}]", dataUpdateTask.getLatestTaskLogId());
                } catch (Exception e) {
                    log.error("同步TEE在线数据集，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE)
                                .processResultMessage(e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                    log.error("Failed to synchronize TEE online dataset, reason: {}, [{}]", e.getMessage(),
                            dataUpdateTask.getLatestTaskLogId());
                    // throw new Exception(String.format("Failed to synchronize TEE online dataset,
                    // reason(%s)", e.getMessage()));
                    // dataUpdateTaskLog.setLog(appendTaskLog(dataUpdateTaskLog.getLog(),
                    // "同步TEE在线数据集失败：" + e.getMessage()));
                    // dataUpdateTaskService.saveAndFlushDataUpdateTaskLog(dataUpdateTaskLog);
                }
            }
        }
        mergeProcessStatus(dataProduct.getId());
    }

    /**
     * 离线交付
     */
    private void offlineDelivery(DataProduct dataProduct) {
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());
        String localCompanyId = String.valueOf(dataProduct.getProvider().getCompany().getId());

        for (DeliveryMode deliveryMode : dataProduct.getDeliveryExt().getDeliveryModes()) {
            if (DeliveryMode.FILE_DOWNLOAD.equals(deliveryMode)) {
                // 交付方式：文件下载
                fileDelivery(dataProduct);
            } else if (DeliveryMode.MPC.equals(deliveryMode)) {
                // 交付方式：MPC
                try {
                    MPCDataset mpcDataset = mpcDatasetMapper.dataAssetToMPCDataset(dataProduct);
                    // NOTE 调用名称
                    mpcDataset.setDatasetName("P_" + mpcDataset.getDatasetName());
                    log.debug("同步MPC数据集:{}", mpcDataset);
                    CommonResult<Void> response;
                    if (dataUpdateTask.getTaskLogNum() > 1) {
                        response = mpcRemote.updateDataset(localCompanyId, mpcDataset);
                    } else {
                        response = mpcRemote.syncDataset(localCompanyId, mpcDataset);
                    }

                    Assert.isTrue(response.getCode() == 1, response.getMessage());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.MPC, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                                .build());
                        return dataProductExt;
                    });
                    log.info("MPC dataset is syncing [{}]", dataUpdateTask.getLatestTaskLogId());
                } catch (Exception e) {
                    log.error("同步MPC数据集失败，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.MPC, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE).processResultMessage("同步MPC数据集失败，error:" + e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                    log.error("Failed to synchronize MPC dataset, reason: {} [{}]", e.getMessage(), dataUpdateTask.getLatestTaskLogId());
//                    throw new Exception(String.format("Failed to synchronize MPC dataset, reason(%s)", e.getMessage()));
//                    dataUpdateTaskLog.setLog(appendTaskLog(dataUpdateTaskLog.getLog(), "同步MPC数据集失败, error:\n" + e.getMessage()));
//                    dataUpdateTaskService.saveAndFlushDataUpdateTaskLog(dataUpdateTaskLog);
                }
            } else if (DeliveryMode.TEE_OFFLINE.equals(deliveryMode)) {
                // 交付方式：TEE离线数据集
                try {
                    TEEDataset teeDataset = teeDatasetMapper.dataAssetToTeeDataset(dataProduct, DeliveryMode.TEE_OFFLINE);
                    // NOTE 调用名称
                    teeDataset.setName("P_" + teeDataset.getName());
                    log.debug("同步TEE离线数据集:{}", teeDataset);
                    BaseCapabilityConfig capabilityConfig = baseCapabilityManager.getCapabilityConfig(localCompanyId, BaseCapabilityType.TEE);
                    Map<String, String> headers = OpenApiHttpUtil.generateSignHeaders(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/openApiKey/token"), null, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());

                    String response;
                    if (dataUpdateTask.getTaskLogNum() > 1) {
                        // 更新接口
                        response = RestTemplateUtil.postFormData(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/dataRouter/updateDataset"), headers, teeDataset.toFormDataBody());
                    } else {
                        // 创建接口
                        response = RestTemplateUtil.postFormData(String.format("%s/%s", capabilityConfig.getBaseUrl(), "_ailand/external/dataRouter/createDataset"), headers, teeDataset.toFormDataBody());
                    }
                    log.debug("同步TEE离线数据集response:{}", response);
                    Map<String, Object> responseObject = JacksonUtils.json2map(response);
                    Assert.isTrue(Integer.parseInt(responseObject.get("code").toString()) == 0, responseObject.get("message").toString());

                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                                .build());
                        return dataProductExt;
                    });
                    log.info("TEE offline dataset syncing [{}]", dataUpdateTask.getLatestTaskLogId());
                } catch (Exception e) {
                    log.error("同步TEE离线数据集失败，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE).processResultMessage("同步TEE离线数据集失败，error:" + e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                    log.error("Failed to synchronize TEE offline dataset, reason: {} [{}]", e.getMessage(), dataUpdateTask.getLatestTaskLogId());
//                    throw new Exception(String.format("Failed to synchronize TEE offline dataset, reason(%s)", e.getMessage()));
//                    dataUpdateTaskLog.setLog(appendTaskLog(dataUpdateTaskLog.getLog(), "同步TEE离线数据集失败, error: \n" + e.getMessage()));
//                    dataUpdateTaskService.saveAndFlushDataUpdateTaskLog(dataUpdateTaskLog);
                }
            } else if ("模型".equals(dataProduct.getDataExt().getDataType1()) && (DeliveryMode.TEE_MODEL_PREDICT.equals(deliveryMode) || DeliveryMode.TEE_MODEL_OPTIMIZE.equals(deliveryMode))) {
                // 交付方式：TEE模型预测和模型精调
                try {
                    if (dataProduct.getDataExt().getProcessResult().containsKey(deliveryMode)) {
                        continue;
                    }
                    // 业务领域
                    JSONObject industry = JSONUtil.parseObj(dataProduct.getDataExt().getIndustry1());
                    OpenDataRouterModelRequest routerModelRequest = OpenDataRouterModelRequest.builder()
                            .dataRouteModelId(dataProduct.getDataProductPlatformId())
                            .name(dataProduct.getDataProductName())
                            .desc(dataProduct.getDescription())
                            .sourceType(ModelSourceType.LOCAL)
                            .table(new Table(dataProduct.getDataExt().getModelMetadata().getTableName(), dataProduct.getDataExt().getModelMetadata().getSchemas()))
                            .isLLM(dataProduct.getDataExt().getIsLLM())
                            .llmModel(CreateModelRequestVO.LLM.builder()
                                    .input(dataProduct.getDataExt().getModelMetadata().getInputFormat())
                                    .output(dataProduct.getDataExt().getModelMetadata().getOutputFormat())
                                    .type(dataProduct.getDataExt().getModelMetadata().getLlmType())
                                    .paramSize(dataProduct.getDataExt().getModelMetadata().getParamSize())
                                    .build())
//                                    .expireDate(dataProduct)
                            .filePath(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath())
                            .userId(dataProduct.getUserId())
                            .userName(dataProduct.getUsername())
                            .companyId(String.valueOf(dataProduct.getProvider().getCompany().getId()))
                            .nodeId(dataProduct.getProvider().getCompany().getNodeId())
                            .executorUrl(String.format("%s:%s", routerService.currentRouteVirtualIp(), aiLandProperties.getExecutorServer().getPort()))
                            .purposeList(industry.getStr("industryName"))
                            .datasetFileType(FileUtil.getSuffix(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath()))
                            .build();
                    com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult<Void> teeCreateModelResult = teeRemote.createModel(localCompanyId, routerModelRequest);
                    log.debug("创建TEE模型:{}, response:{}", routerModelRequest, teeCreateModelResult);
                    Assert.isTrue(teeCreateModelResult.isSuccess(), teeCreateModelResult.getMessage());

                    dataProduct.getDataExt().getProcessResult().put(DeliveryMode.TEE_MODEL_PREDICT, DataAssetExt.ProcessResult.builder()
                            .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                            .build());
                    dataProduct.getDataExt().getProcessResult().put(DeliveryMode.TEE_MODEL_OPTIMIZE, DataAssetExt.ProcessResult.builder()
                            .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC)
                            .build());
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(DeliveryMode.TEE_MODEL_PREDICT, dataProduct.getDataExt().getProcessResult().get(DeliveryMode.TEE_MODEL_PREDICT));
                        dataProductExt.getProcessResult().put(DeliveryMode.TEE_MODEL_OPTIMIZE, dataProduct.getDataExt().getProcessResult().get(DeliveryMode.TEE_MODEL_OPTIMIZE));
                        return dataProductExt;
                    });
                    log.info("TEE MODEL syncing [{}]", dataUpdateTask.getLatestTaskLogId());
                } catch (Exception e) {
                    log.error("创建TEE模型失败，资产ID:{} error:", dataProduct.getId(), e);
                    dataProductService.updateDataExt(dataProduct.getId(), dataProductExt -> {
                        dataProductExt.getProcessResult().put(deliveryMode, DataAssetExt.ProcessResult.builder()
                                .dataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE).processResultMessage("创建TEE模型失败，error:" + e.getMessage())
                                .build());
                        return dataProductExt;
                    });
                    log.error("Failed to synchronize TEE MODEL, reason: {} [{}]", e.getMessage(), dataUpdateTask.getLatestTaskLogId());
                }
            }
        }
        // 会签预处理状态
        mergeProcessStatus(dataProduct.getId());
    }

    /**
     * 会签预处理状态
     */
    private void mergeProcessStatus(String dataAssetId) {
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataAssetId);
        List<DataUpdateStatus> historyStatus = dataUpdateTaskService.getDataUpdateTaskStatus(dataUpdateTask.getId());
        DataUpdateTaskLog dataUpdateTaskLog = dataUpdateTaskService.getTaskLog(dataUpdateTask.getLatestTaskLogId());

        DataProduct dataProduct = dataProductRepository.getReferenceById(dataAssetId);
        DataAssetPrepareStatus dataAssetPrepareStatus = dataProduct.getDataExt().getDataAssetPrepareStatus();
        if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(dataAssetPrepareStatus) ||
                DataAssetPrepareStatus.SYNC_FAILURE.equals(dataAssetPrepareStatus) ||
                DataUpdateStatus.FAILED.equals(dataUpdateTaskLog.getTaskStatus())) {
            return;
        }

        Map<DeliveryMode, DataAssetExt.ProcessResult> processResult = dataProduct.getDataExt().getProcessResult();
        // 资产不可用
        for (Map.Entry<DeliveryMode, DataAssetExt.ProcessResult> deliveryModeProcessResultEntry : processResult
                .entrySet()) {
            DataAssetExt.ProcessResult result = deliveryModeProcessResultEntry.getValue();
            if (DataAssetPrepareStatus.HANDLE_FAILURE.equals(result.getDataAssetPrepareStatus())
                    || DataAssetPrepareStatus.SYNC_FAILURE.equals(result.getDataAssetPrepareStatus())) {
                if (historyStatus.isEmpty() || !historyStatus.contains(DataUpdateStatus.SUCCESS)) {
                    dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.SYNC_FAILURE);
                    dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                }

                // 会签更新任务状态
                log.error("会签更新任务状态：任务失败, 数据产品预处理状态：{}, 更新任务：{}", JSONUtil.toJsonStr(processResult), JSONUtil.toJsonStr(dataUpdateTask));
                dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
                dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.FAILED);
                dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
                dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));
                dataUpdateTaskLogRepository.updateExt(dataUpdateTask.getLatestTaskLogId(), DataUpdateTaskLog.DataUpdateTaskLogExt.builder()
                        .processResultMessage("会签更新任务状态, 任务失败, processResult:" + JSONUtil.toJsonStr(processResult)).build());
                // 清理明文文件
                traderService.deleteFileSource(dataProduct.getUserId(),
                        dataProduct.getDataExt().getFileSourceMetadata());
                return;
            }
        }
        // 资产可用
        if (dataProduct.getDeliveryExt().getDeliveryModes().size() == processResult.size()) {
            for (DataAssetExt.ProcessResult result : processResult.values()) {
                if (!DataAssetPrepareStatus.AVAILABLE.equals(result.getDataAssetPrepareStatus())) {
                    return;
                }
            }

            if (historyStatus.isEmpty() || !historyStatus.contains(DataUpdateStatus.SUCCESS)) {
                dataProduct.getDataExt().setDataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE);
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            }

            log.info("data product update task is successfully end. [{}]", dataUpdateTask.getLatestTaskLogId());

            // 会签更新任务状态
            dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
            dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.SUCCESS);
            dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
            dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));

            // 清理明文文件
            traderService.deleteFileSource(dataProduct.getUserId(), dataProduct.getDataExt().getFileSourceMetadata());

            // 第一次任务成功执行时，上报业务节点
            if (historyStatus.isEmpty() || !historyStatus.contains(DataUpdateStatus.SUCCESS)) {
                DataAssetPrepareSchedule DataAssetPrepareSchedule = context.getBean(DataAssetPrepareSchedule.class);
                DataAssetPrepareSchedule.dataProductPublish(dataAssetId);
            }
        }
    }

    private final UserService userService;

    @Transactional
    public void dataProductPublish(String dataProductId) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(dataProductId);
        CompanyDTO company = dataProduct.getProvider().getCompany();
        JSONObject others = new JSONObject();
        others.set("routerId", company.getNodeId());
        others.set("companyId", company.getId());
        others.set("dataType", dataProduct.getDataExt().getDataType());

        UserDetailsResponse userDetail = userService.userDetailForThird(dataProduct.getUserId());
        DataProductPublishVM dataProductPublishVM = DataProductPublishVM.builder()
                .productId(dataProduct.getDataProductPlatformId())
                .productName(dataProduct.getDataProductName())
                .productType(dataProduct.getDataExt().getType())
                .timeRange(dataProduct.getDataExt().getDataCoverageTimeStart() == null
                        || dataProduct.getDataExt().getDataCoverageTimeEnd() == null
                        ? null
                        : String.format("%s 至 %s", dataProduct.getDataExt().getDataCoverageTimeStart(),
                        dataProduct.getDataExt().getDataCoverageTimeEnd()))
                .industry(dataProduct.getIndustry().substring(0, 1))
                .productRegion(dataProduct.getDataExt().getRegion())
                .personalInformation("1".equals(dataProduct.getDataExt().getPersonalInformation()) ? 1 : 0)
                .description(dataProduct.getDescription())
                .deliveryMethod(dataProduct.getDeliveryExt().getDeliveryMethod())
                .deliveryModes(dataProduct.getDeliveryExt().deliveryModesForPublish())
                .limitations(dataProduct.getDeliveryExt().getLimitations())
                .authorize("1".equals(dataProduct.getDeliveryExt().getAuthorize()) ? 1 : 0)
                .dataSubject(dataProduct.getDataExt().getDataSubject() == null ? "01"
                        : dataProduct.getDataExt().getDataSubject())
                .dataSize(dataProduct.getDataExt().getDataSize())
                .dataSizeUnit(dataProduct.getDataExt().getDataSizeUnit())
                .updateFrequency(dataProduct.getDataExt().getUpdateFrequency() == null ? 0 : dataProduct.getDataExt().getUpdateFrequency())
                .updateFrequencyUnit(dataProduct.getDataExt().getUpdateFrequencyUnit() == null ? "次/天" : dataProduct.getDataExt().getUpdateFrequencyUnit())
// TODO                .resourceId(List.of(dataProduct.getDataExt().getResourceId() == null ? dataProductId : dataProduct.getDataExt().getResourceId()))
                .resourceId(Collections.emptyList())
                .others(others)
                .providerName(company.getOrganizationName())
                .providerType("02")
                .entityInformation(DataProductSaveVM.entityInformation(company))
                .identityId(company.getCreditCode())
                .providerDesc(company.getAccessType() == null ? "" : company.getAccessType().getDesc())
                .operatorName(userDetail.getDelegateInfo().getDelegateName())
                .operatorTelephone(userDetail.getDelegateInfo().getDelegatePhone())
                .operatorIdCard(userDetail.getDelegateInfo().getDelegateIdNumber())
                .commission(company.getAuthorizationLetter())
                .commissionFileName(company.getAuthorizationLetter())
                .dataSample(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())))
                .dataSampleFileName(dataProduct.getDataExt().getQualificationDoc().getDataSampleAttach())
                .complianceAndLegalStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())))
                .complianceAndLegalStatementFileName(dataProduct.getDataExt().getQualificationDoc().getComplianceAndLegalStatementAttach())
                .dataSourceStatement(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())))
                .dataSourceStatementFileName(dataProduct.getDataExt().getQualificationDoc().getDataSourceStatementAttach())
                .safeLevel(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())))
                .safeLevelFileName(dataProduct.getDataExt().getQualificationDoc().getSafeLevelAttach())
                .evaluationReport(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDataExt().getQualificationDoc().getEvaluationReportAttach())))
                .evaluationReportFileName(dataProduct.getDataExt().getQualificationDoc().getEvaluationReportAttach())
                .dataVersion(String.valueOf(dataProduct.getDataExt().getDataVersion()))
                .partyACompanyId(company.getThirdBusinessId())
                .partyAUserId(dataProduct.getProvider().getUserIdShuhan())
                .measureMethod(dataProduct.getDeliveryExt().getBillingMethod())
                .unit(dataProduct.getDeliveryExt().getPurchaseUnit())
                .price(dataProduct.getDeliveryExt().getPrice())
                .dataType(dataProduct.getDataExt().getDataType())
                .deliveryInfo(DataAssetExt.fileContentBase64(filesStorageService.getRootPath().resolve("attach")
                        .resolve(dataProduct.getUserId())
                        .resolve(dataProduct.getDeliveryExt().getDeliveryInfo() == null ? "" : dataProduct.getDeliveryExt().getDeliveryInfo())))
                .deliveryInfoFileName(dataProduct.getDeliveryExt().getDeliveryInfo())
                .build();
        List<ServiceNodeApplyListVO> serviceNodes = dataProduct.getDeliveryExt().getServiceNodes();
        for (ServiceNodeApplyListVO serviceNode : serviceNodes) {
            if (serviceNode.getProcessStatus() != null && serviceNode.getProcessStatus() == 1) {
                continue;
            }
            dataProductPublishVM.setBusinessPlatformUniqueNo(serviceNode.getServiceNodeId());
            others.set("businessPlatformUniqueNo", serviceNode.getServiceNodeId());
            dataProductPublishVM.setOthers(others);
            dataProductPublishVM.setContractTemplateId(!ObjectUtils.isEmpty(serviceNode.getContractTemplate())
                    ? serviceNode.getContractTemplate().getTemplateId()
                    : null);
            try {
                log.debug("数据产品上架 {}, {}", serviceNode, JSONUtil.toJsonStr(dataProductPublishVM));
                dataProductPublishVM.setPlatformId(serviceNode.getServiceNodeId());
                dataProductPublishVM.setPlatformName(serviceNode.getServiceNodeName());
                dataProductPublishVM.setPlatformLocation(serviceNode.getServiceNodeLocation() == null ? "空的Location" : serviceNode.getServiceNodeLocation());
                ServiceNodeMetaData metaData = new ServiceNodeMetaData();
                metaData.setNodeId(company.getNodeId());
                metaData.setUrl(serviceNode.getServiceNodeUrl());
                ShuhanResponse<DataProductPublishVO> result = serviceNodeRemoteService.dataProductPublish(dataProductPublishVM, metaData.toBase64());
                log.debug("数据产品上架 {} 结果 {}", serviceNode, result);
                Assert.isTrue(result.isSuccess(), "数据产品上架失败：" + result.getMessage());
                DataProductPublishVO dataProductPublishVO = result.getData();
                dataProduct.getDataExt().addProcessLog("提交业务节点[" + serviceNode.getServiceNodeName() + "]上架审批", System.currentTimeMillis(), null, null);
                serviceNode.setProcessId(dataProductPublishVO.getProcessId());
                serviceNode.setProcessStatus(0);
            } catch (Exception e) {
                log.error("数据产品上架到业务节点失败 {}", serviceNode, e);
            }
        }
        dataProduct.getDeliveryExt().setServiceNodes(serviceNodes);
        dataProduct.getDataExt().setHasNonSyncedPublishStatus("1");
        dataProduct.getDataExt().setPublishTime(System.currentTimeMillis());
        dataProductRepository.updateDataExt(dataProductId, dataProduct.getDataExt());
        dataProductRepository.updateDeliveryExt(dataProductId, dataProduct.getDeliveryExt());
        traderService.dataAssetUpdateTrader(true, true, dataProductId);
    }

    /**
     * 交付方式：文件下载
     */
    private void fileDelivery(DataProduct dataProduct) {
        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());
        try {
            String extension = "csv";
            try {
                extension = FileUtil.extName(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath());
            } catch (Exception ignore) {
            }
            String fileName = dataProduct.getDataProductName() + "." + extension;
            boolean bucketExists = minioClient
                    .bucketExists(BucketExistsArgs.builder().bucket(dataProduct.getUserId()).build());
            if (log.isDebugEnabled()) {
                log.debug("check bucket {} Exists {}", dataProduct.getUserId(), bucketExists);
            }
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(dataProduct.getUserId()).build());
            }
            File tarFile = new File(
                    dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath() + ".tar.gz");
            if (log.isDebugEnabled()) {
                log.debug("创建压缩文件 {}", tarFile.getAbsolutePath());
            }
            GzipUtils.createTarFile(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath(),
                    tarFile.getAbsolutePath());
            String encryptKey = UuidUtils.uuid32();
            if (log.isDebugEnabled()) {
                log.debug("对文件 {}.encrypted 进行加密", tarFile.getAbsolutePath());
            }
            SecIslandTEE.tee_file_Encrypt(encryptKey.getBytes(StandardCharsets.UTF_8), tarFile.getAbsolutePath(),
                    tarFile.getAbsolutePath() + ".encrypted");
            InputStream inputStream = Bean2HeaderUtils.addHeaderAheadStream(
                    Files.newInputStream(Paths.get(tarFile.getAbsolutePath() + ".encrypted")),
                    FileEncryptKeyHeader.builder()
                            .encryptKey(RSAUtil.encodeToString(RSAUtil.encryptByPublicKey(
                                    encryptKey.getBytes(StandardCharsets.UTF_8), (RSAPublicKey) keyPair.getPublic())))
                            .build());
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            Files.copy(inputStream, Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            inputStream.close();
            minioClient.uploadObject(UploadObjectArgs.builder()
                    .bucket(dataProduct.getUserId())
                    .object(Paths.get(dataProduct.getId(), fileName).toString())
                    .filename(tarFile.getAbsolutePath() + ".encrypted1")
                    .build());
            dataProduct.getDataExt().getProcessResult().put(DeliveryMode.FILE_DOWNLOAD,
                    DataAssetExt.ProcessResult.builder()
                            .dataAssetPrepareStatus(DataAssetPrepareStatus.AVAILABLE)
                            .build());
            dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted"));
            Files.deleteIfExists(Paths.get(tarFile.getAbsolutePath() + ".encrypted1"));
            Files.deleteIfExists(tarFile.toPath());
            log.info("File download delivery method is ready [{}]", dataUpdateTask.getLatestTaskLogId());
        } catch (Exception e) {
            dataProduct.getDataExt().getProcessResult().put(DeliveryMode.FILE_DOWNLOAD,
                    DataAssetExt.ProcessResult.builder()
                            .dataAssetPrepareStatus(DataAssetPrepareStatus.HANDLE_FAILURE)
                            .build());
            dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());

            // dataUpdateTaskLog.setLog(appendTaskLog(dataUpdateTaskLog.getLog(),
            // "上传文件到minIO失败, error: " + e.getMessage()));
            // dataUpdateTaskService.saveAndFlushDataUpdateTaskLog(dataUpdateTaskLog);
            log.warn("上传文件到minIO失败", e);
            log.error("File download delivery method error: {} [{}]", e.getMessage(), dataUpdateTask.getLatestTaskLogId());
        }
    }

    private final LocalProductRefRepository localProductRefRepository;

    /**
     * 前置机任务回调
     */
    public void executorCallback(String assetId, DataCollectorJobCallback.ExecutorJobRunResult executorJobRunResult) {
        try {
            DataProduct dataProduct = WAIT_EXECUTOR_CALLBACK.get(assetId);
            if (dataProduct == null) {
                LocalProductRef localProductRef = localProductRefRepository.findFirstByAssetId(assetId);
                log.info("数据产品本地信息: {}", localProductRef);
                TenantContext.setCurrentTenant("tenant_" + localProductRef.getCompanyId());
                dataProduct = dataProductRepository.getReferenceById(assetId);
            } else {
                TenantContext.setCurrentTenant("tenant_" + dataProduct.getProvider().getCompany().getId());
            }
            DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
            DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(assetId);

            if (executorJobRunResult.isSuccess()) {
                // 更新文件数据hash
                log.info("Ailand-data-collector task processing success. [{}]", dataUpdateTask.getLatestTaskLogId());
                dataProduct.getDataExt().getFileSourceMetadata().setDataAssetFileHash(SM3DigestUtil
                        .getHash(new File(dataProduct.getDataExt().getFileSourceMetadata().getDataAssetFilePath())));
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
                // 离线交付
                offlineDelivery(dataProduct);

            } else {
                log.error("前置机任务执行失败:{}", executorJobRunResult.getMessage());
                log.error("Ailand-data-collector task processing failed [{}]", dataUpdateTask.getLatestTaskLogId());
                dataProduct.getDataExt()
                        .setProcessResultMessage("前置机任务执行失败，error:" + executorJobRunResult.getMessage());
                dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());

                dataUpdateTaskLogRepository.updateEndTime(dataUpdateTask.getLatestTaskLogId(), new Date());
                dataUpdateTaskLogRepository.updateTaskStatus(dataUpdateTask.getLatestTaskLogId(), DataUpdateStatus.FAILED);
                dataUpdateTaskService.taskLogStatistics(dataUpdateTask.getLatestTaskLogId(), dataUpdateTask.getTaskLogNum());
                dataUpdateTaskLogRepository.updateLog(dataUpdateTask.getLatestTaskLogId(), dataUpdateTaskService.grepTaskLog(dataUpdateTask.getLatestTaskLogId()));
                dataUpdateTaskLogRepository.updateExt(dataUpdateTask.getLatestTaskLogId(), DataUpdateTaskLog.DataUpdateTaskLogExt.builder()
                        .processResultMessage("前置机任务执行失败，error:" + executorJobRunResult.getMessage()).build());
            }
        } finally {
            WAIT_EXECUTOR_CALLBACK.remove(assetId);
            TenantContext.clear();
        }
    }

    /**
     * 数据集同步回调
     */
    public void datasetSyncCallback(SyncDatasetResult syncDatasetResult, String platform) {
        log.debug("datasetSyncCallback: platform:{}, result:{}", platform, syncDatasetResult);
        String assetId = syncDatasetResult.getAssetId();
        DataProduct dataProduct = dataProductRepository.findByDataProductPlatformId(assetId);

        DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
        DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTaskByDataProductId(dataProduct.getId());

        DataAssetExt.ProcessResult processResult = DataAssetExt.ProcessResult.builder()
                .dataAssetPrepareStatus(Boolean.TRUE.equals(syncDatasetResult.getResult()) ? DataAssetPrepareStatus.AVAILABLE : DataAssetPrepareStatus.SYNC_FAILURE)
                .processResultMessage(syncDatasetResult.getMessage()).build();
        if (platform.equals("TEE")) {
            for (DeliveryMode deliveryMode : dataProduct.getDeliveryExt().getDeliveryModes()) {
                if (deliveryMode.name().startsWith("TEE")) {
                    dataProduct.getDataExt().getProcessResult().put(deliveryMode, processResult);
                }
            }
        } else if (platform.equals("MPC")) {
            dataProduct.getDataExt().getProcessResult().put(DeliveryMode.MPC, processResult);
        }
//        else {
        // NOTE: 暂无其他平台回调
//        }
        dataProductRepository.updateDataExt(dataProduct.getId(), dataProduct.getDataExt());
        if (Boolean.TRUE.equals(syncDatasetResult.getResult())) {
            log.info("Dataset sync callback success, platform {} [{}]", platform, dataUpdateTask.getLatestTaskLogId());
        } else {
            log.error("Dataset sync callback failed, platform {} [{}]", platform, dataUpdateTask.getLatestTaskLogId());
        }
        mergeProcessStatus(dataProduct.getId());
    }

    private static final ConcurrentHashMap<String, Object> TASK_LOCKS = new ConcurrentHashMap<>();

    /**
     * 实现一个定时任务执行的方法，需要切换数据库schema，并且判断是否存在正在执行的任务，如果存在则跳过本次执行
     */
    public void executeScheduledTask(DataUpdateCronJobParams dataUpdateCronJobParams) {
        try {
            log.debug("dataUpdateCronJobParams: {}", JSONUtil.toJsonStr(dataUpdateCronJobParams));
            DataUpdateTaskService dataUpdateTaskService = SpringUtil.getBean(DataUpdateTaskService.class);
            // 切换数据库schema
            TenantContext.setCurrentTenant("tenant_" + dataUpdateCronJobParams.getCompanyId());

            String taskId = dataUpdateCronJobParams.getDataUpdateTask().getId();

            Object lock = TASK_LOCKS.computeIfAbsent(taskId, k -> new Object());
            // 使用任务ID作为锁对象，确保同一任务的状态检查和更新是原子操作
            synchronized (lock) {
                DataProduct dataProduct = dataProductRepository
                        .getReferenceById(dataUpdateCronJobParams.getDataUpdateTask().getDataProductId());

                // 判断上一次任务的状态是否正在执行中,任务处于执行中，直接返回，跳过本次执行
                DataUpdateTask dataUpdateTask = dataUpdateTaskService.getTask(taskId);

                if (!dataUpdateTaskService.checkLatestTaskLogStatus(dataUpdateTask.getLatestTaskLogId())) {
                    log.info("更新任务: {}, 正在执行中, 跳过本次定时执行...", taskId);
                    return;
                }

                // 更新任务状态
                // 存储最新的执行记录ID
                DataUpdateTaskLog taskLog = new DataUpdateTaskLog();
                taskLog.setId(UUID.randomUUID().toString());
                taskLog.setTaskId(dataUpdateTask.getId());
                taskLog.setTaskStatus(DataUpdateStatus.CREATED);
                taskLog.setCreateTime(new Date());
                taskLog.setDataUpdateType(dataUpdateTask.getDataUpdateType());
                dataUpdateTaskService.saveAndFlushDataUpdateTaskLog(taskLog);

                dataUpdateTask.setLatestTaskLogId(taskLog.getId());
                // 任务执行次数+1
                int taskNums;
                if (Objects.isNull(dataUpdateTask.getTaskLogNum())) {
                    taskNums = 0;
                } else {
                    taskNums = dataUpdateTask.getTaskLogNum();
                }
                dataUpdateTask.setTaskLogNum(taskNums + 1);
                dataUpdateTask.setUpdateTime(new Date());
                dataUpdateTaskRepository.saveAndFlush(dataUpdateTask);

                log.info("Start executing data product update task [{}]", taskLog.getId());
                log.info("Update task type: {} [{}]", dataUpdateTask.getDataUpdateType(), taskLog.getId());
                if (UpdateWay.SCHEDULE.equals(dataUpdateTask.getDataUpdateType())) {
                    log.info("Schedule cron expression: {} [{}]", dataUpdateTask.getCronExpression(), taskLog.getId());
                }
                // 执行定时任务
                prepareDataProduct(dataProduct);
            }
        } finally {
            TASK_LOCKS.remove(dataUpdateCronJobParams.getDataUpdateTask().getId());
            TenantContext.clear();
        }
    }

    public void runModelTask(SyncModelResult result) {
        log.info("TEE 创建 minio 完成，通知前置机处理文件");
        boolean collectorJobResult = dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
                .datasourceType(DatasourceType.serverfile)
                .filepath(result.getFilepath())
                .uploadSdkParams(result.getUploadSdkParams())
                .dataAssetId(result.getModelVersionId())
                .jobId(result.getModelVersionId())
                .build());
    }
}
