package com.ailpha.ailand.dataroute.endpoint.deliveryScene.request;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.third.request.SceneAssetApiReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 10:06
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneListResp {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "交付方式")
    String deliveryType;

    @Schema(description = "数字证书 —— 合规场景名称")
    String digitalSceneName;

    @Schema(description = "状态")
    String sceneStatus;

    /**
     * todo：去使用 需要的信息？
     */
    @Schema(description = "关联数据资产id")
    List<DataAssetSceneRef> dataAssetSceneRefList;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    Date createTime;
    @Schema(description = "合约名称")
    String contractName;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DataAssetSceneRef {


        AssetType assetType;

        /**
         * 资产ID
         */
        String assetId;

        /**
         * 数据资源全局（连接器空间）唯一标识
         */
        String dataProductPlatformId;

        String routerId;

        /**
         * 资产名称
         */
        String assetName;

        /**
         * apIkey
         */
        String apiKey;

        String orderId;

        @Schema(description = "去使用接口地址")
        String url;

        String clientIp;

        String apiId;

        String ext;

        SceneAssetApiReq.ExtData jsonExt;

        public SceneAssetApiReq.ExtData getJsonExt() {
            return StringUtils.isBlank(ext) ? null : JSONUtil.toBean(ext, SceneAssetApiReq.ExtData.class);
        }
    }

}
