package com.ailpha.ailand.dataroute.endpoint.company.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CompanyDetailResponse {

    CompanyInfo companyInfo;
    UserInfo userInfo;
    RegisterConnectorToHubRequest registerConnectorInfo;

    @Data
    public static class CompanyInfo {


        String businessLicenseLocalUrl;
        String businessLicenseRemoteUrl;
        @Schema(description = "企业ID")
        private Long id;

        @Schema(description = "企业名称")
        private String organizationName;

        @Schema(description = "统一社会信用代码")
        private String creditCode;

        @Schema(description = "企业状态")
        private CompanyStatus status = CompanyStatus.NOT_REVIEW;

        String companyId;

        @Schema(description = "节点ID")
        String nodeId;

        @Schema(description = "接入主体类型")
        private AccessType accessType = AccessType.LEGAL_PERSON;

        @Schema(description = "法定代表人姓名")
        private String legalRepresentativeName;

        @Schema(description = "法定代表人证件类型")
        private String legalRepresentativeIdType;

        @Schema(description = "法定代表人证件号码")
        private String legalRepresentativeIdNumber;

        @Schema(description = "法定代表人证件有效期 开始")
        private String legalRepresentativeIdStartExpiry;
        @Schema(description = "法定代表人证件有效期 结束")
        private String legalRepresentativeIdEndExpiry;

        @Schema(description = "法定代表人实名认证等级")
        private String legalRepresentativeAuthLevel;

        @Schema(description = "认证方式")
        private String authMethod;

        // 认证时间
        @Schema(description = "认证时间")
        String authDate;
        // 身份状态
        @Schema(description = "身份状态")
        String identityStatus;

        @Schema(description = "注册地址")
        private String registrationAddress;

        @Schema(description = "行业类型")
        private String industryType;

        @Schema(description = "经营期限起始日期")
        private LocalDate businessStartDate;

        @Schema(description = "经营期限截止日期")
        private LocalDate businessEndDate;
        // 注册日期
        @Schema(description = "注册日期")
        String registrationDate;
        // 注册金额
        @Schema(description = "注册金额")
        String registeredCapital;
        // 经营范围 
        @Schema(description = "经营范围")
        String businessScope;
        // 授权书
        @Schema(description = "授权书")
        String authorizationLetterLocalUrl;
        String authorizationLetterRemoteUrl;

        @Schema(description = "经办人姓名")
        private String delegateName;

        @Schema(description = "经办人证件类型")
        private String delegateIdType;

        @Schema(description = "经办人证件号码")
        private String delegateIdNumber;

        @Schema(description = "经办人证件有效期 开始")
        private String delegateIdStartExpiry;
        @Schema(description = "经办人证件有效期 结束")
        private String delegateIdEndExpiry;

        @Schema(description = "经办人所属机构")
        private String delegateInstitution;

        @Schema(description = "经办人所属机构统一社会信用代码")
        private String delegateInstitutionCode;

        @Schema(description = "经办人联系方式")
        private String delegateContact;

        @Schema(description = "经办人电子邮箱")
        private String delegateEmail;

        @Schema(description = "经办人实名认证等级")
        private String delegateAuthLevel;

        @Schema(description = "经办人认证方式")
        private String delegateAuthMethod;

        @Schema(description = "经办人注册地址")
        private String delegateRegistrationAddress;

        @Schema(description = "经办人行业类型")
        private String delegateIndustryType;

        @Schema(description = "经办人任务范围")
        private String delegateTaskScope;

        @Schema(description = "授权开始日期")
        private LocalDate delegateAuthorizationStart;

        @Schema(description = "授权结束日期")
        private LocalDate delegateAuthorizationEnd;

        @Schema(description = "经办人备注")
        private String delegateRemarks;

        @Schema(description = "是否永久有效")
        private Boolean isPermanent = false;

        @Schema(description = "有效期截止日期")
        private Date validityEndDate;

        @Schema(description = "审核人ID")
        private String reviewUserId;

        @Schema(description = "审核时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime reviewTime;

        @Schema(description = "审核备注")
        private String reviewRemarks;

        @Schema(description = "拒绝原因")
        private String refuseReason;


        @Schema(description = "创建人")
        private String createdBy;

        @Schema(description = "创建时间")
        private LocalDateTime createdTime;

        @Schema(description = "更新人")
        private String updatedBy;


        @Schema(description = "更新时间")
        private LocalDateTime updatedTime;

        @Schema(description = "是否删除")
        private Boolean deleted = false;

        Long serviceNodeId;
        String ext;
    }

    @Data
    public static class UserInfo {
        // 替换原有的用户信息字段，改为更详细的字段
        @Schema(description = "提供方", example = "普通用户用户名称")
        private String providerName;

        @Schema(description = "手机号")
        private String phoneNumber;

        @Schema(description = "数由器ID")
        private String routerId;

        @Schema(description = "数由器名称")
        private String routerName;

        @Schema(description = "账号状态")
        private AccountStatus accountStatus;

        @Schema(description = "有效期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date validityPeriod;
    }

    public static CompanyDetailResponse of(Company company, User user) {
        CompanyDetailResponse response = new CompanyDetailResponse();
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        // 设置公司信息
        CompanyInfo companyInfo = BeanUtil.copyProperties(company, CompanyInfo.class);
        companyInfo.setAuthorizationLetterLocalUrl(company.getAuthorizationLetter());
        companyInfo.setAuthorizationLetterRemoteUrl(JSONUtil.parseObj(company.getExt()).getStr("authorizationLetterRemoteUrl"));
        companyInfo.setBusinessLicenseLocalUrl(company.getBusinessLicense());
        companyInfo.setBusinessLicenseRemoteUrl(JSONUtil.parseObj(company.getExt()).getStr("businessLicenseRemoteUrl"));
        // 其他字段根据实际情况从company对象或其ext字段中获取
        companyInfo.setDelegateIdStartExpiry(DateUtil.format(company.getDelegateIdExpiry(), "yyyy-MM-dd"));
        companyInfo.setDelegateIdEndExpiry(entries.getStr("delegateIdEndExpiry"));
        companyInfo.setLegalRepresentativeIdStartExpiry(DateUtil.format(company.getLegalRepresentativeIdExpiry(), "yyyy-MM-dd"));
        companyInfo.setLegalRepresentativeIdEndExpiry(entries.getStr("legalRepresentativeIdValidityEndDate"));
        response.setCompanyInfo(companyInfo);

        // 用户信息设置保持不变
        UserInfo userInfo = new UserInfo();
        // 设置新增的字段
        userInfo.setProviderName(user.getAccount());
        userInfo.setPhoneNumber(user.getPhone());
        userInfo.setAccountStatus(AccountStatus.getStatus(user));
        userInfo.setValidityPeriod(user.getExt().getExpireDate());
        // 其他字段需要根据实际情况从相应的对象中获取
        // 这里仅作示例，实际实现可能需要从其他服务或对象中获取数据

        RegisterConnectorToHubRequest registerConnectorToHubRequest = entries.get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        if (ObjectUtil.isNotNull(registerConnectorToHubRequest)) {
            RegisterConnectorToHubRequest.BaseInfo baseInfo = registerConnectorToHubRequest.getBaseInfo();
            baseInfo.setIdentifyID(company.getNodeId());
            baseInfo.setIdentityStatus(CompanyStatus.REVIEW_PASS.equals(company.getStatus()) ? "接入成功" : "");
            if (ObjectUtil.isNotNull(registerConnectorToHubRequest)) {
                userInfo.setRouterId(company.getNodeId());
                userInfo.setRouterName(registerConnectorToHubRequest.getBaseInfo().getConnectorName());
            }
            response.setRegisterConnectorInfo(registerConnectorToHubRequest);
        }

        response.setUserInfo(userInfo);

        return response;
    }
}