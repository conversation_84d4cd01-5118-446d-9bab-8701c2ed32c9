package com.ailpha.ailand.dataroute.endpoint.company.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CompanyDetailResponse {

    CompanyInfo companyInfo;
    UserInfo userInfo;
    RegisterConnectorToHubRequest registerConnectorInfo;

    @Data
    public static class CompanyInfo {


        String businessLicenseLocalUrl;
        String businessLicenseType;
        String businessLicenseRemoteUrl;
        @Schema(description = "企业ID")
        private Long id;

        @Schema(description = "企业名称")
        private String organizationName;

        @Schema(description = "统一社会信用代码")
        private String creditCode;

        @Schema(description = "企业状态")
        private CompanyStatus status = CompanyStatus.REVIEW_PASS;

        String companyId;

        @Schema(description = "节点ID")
        String nodeId;

        @Schema(description = "接入主体类型")
        private AccessType accessType = AccessType.LEGAL_PERSON;

        @Schema(description = "法定代表人姓名")
        private String legalRepresentativeName;
        @Schema(description = "法定联系方式")
        String partyContactInfo;

        @Schema(description = "法定代表人证件类型")
        private String legalRepresentativeIdType;

        @Schema(description = "法定代表人证件号码")
        private String legalRepresentativeIdNumber;
        private String legalRepresentativeIdNumberMasked;

        @Schema(description = "法定代表人证件有效期 开始")
        private String legalRepresentativeIdStartExpiry;
        @Schema(description = "法定代表人证件有效期 结束")
        private String legalRepresentativeIdEndExpiry;

        @Schema(description = "法定代表人实名认证等级")
        private String legalRepresentativeAuthLevel;

        @Schema(description = "认证方式")
        private String authMethod;

        // 认证时间
        @Schema(description = "认证时间")
        String authDate;
        // 身份状态
        @Schema(description = "身份状态")
        String identityStatus;

        @Schema(description = "注册地址")
        private String registrationAddress;

        @Schema(description = "行业类型")
        private String industryType;

        @Schema(description = "经营期限起始日期")
        private LocalDate businessStartDate;

        @Schema(description = "经营期限截止日期")
        private LocalDate businessEndDate;
        // 注册日期
        @Schema(description = "注册日期")
        String registrationDate;
        // 注册金额
        @Schema(description = "注册金额")
        String registeredCapital;
        // 经营范围 
        @Schema(description = "经营范围")
        String businessScope;
        // 授权书
        @Schema(description = "授权书")
        String authorizationLetterLocalUrl;
        String authorizationLetterRemoteUrl;

        @Schema(description = "经办人备注")
        private String delegateRemarks;

        @Schema(description = "是否永久有效")
        private Boolean isPermanent = false;

        @Schema(description = "有效期截止日期")
        private Date validityEndDate;


        @Schema(description = "创建人")
        private String createdBy;

        @Schema(description = "创建时间")
        private LocalDateTime createdTime;

        @Schema(description = "更新人")
        private String updatedBy;


        @Schema(description = "更新时间")
        private LocalDateTime updatedTime;

        @Schema(description = "是否删除")
        private Boolean deleted = false;

        Long serviceNodeId;
        String ext;

        // 开户行
        @Schema(description = "开户行", example = "中国银行")
        private String bankName;

        // 银行卡号
        @Schema(description = "银行卡号", example = "6225********9012")
        private String bankAccount;

        // 传真
        @Schema(description = "传真", example = "010-********")
        private String fax;

        // 邮编
        @Schema(description = "邮编", example = "100000")
        private String postalCode;

        // 银行地址
        @Schema(description = "银行地址", example = "北京市朝阳区XX路XX号")
        private String bankAddress;

        // 户名
        @Schema(description = "户名", example = "XX有限公司")
        private String accountName;
    }

    @Data
    public static class UserInfo {
        // 替换原有的用户信息字段，改为更详细的字段
        @Schema(description = "提供方", example = "普通用户用户名称")
        private String providerName;

        @Schema(description = "手机号")
        private String phoneNumber;

        @Schema(description = "数由器ID")
        private String routerId;

        @Schema(description = "数由器名称")
        private String routerName;

        @Schema(description = "账号状态")
        private AccountStatus accountStatus;

        @Schema(description = "有效期")
        @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private Date validityPeriod;
    }

    public static CompanyDetailResponse of(CompanyDTO company, User user) {
        CompanyDetailResponse response = new CompanyDetailResponse();
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        // 设置公司信息
        CompanyInfo companyInfo = BeanUtil.copyProperties(company, CompanyInfo.class);
//        if (company.getLegalRepresentativeIdType().equals("身份证")) {
//            companyInfo.setLegalRepresentativeIdNumberMasked(StringUtils.substring(company.getLegalRepresentativeIdNumber(), 0, 6) + "****" + StringUtils.substring(company.getLegalRepresentativeIdNumber(), -4));
//        } else
//            companyInfo.setLegalRepresentativeIdNumberMasked(StringUtils.repeat("*", company.getLegalRepresentativeIdNumber().length()));
//        companyInfo.setAuthorizationLetterLocalUrl(company.getAuthorizationLetter());
        companyInfo.setAuthorizationLetterRemoteUrl(JSONUtil.parseObj(company.getExt()).getStr("authorizationLetterRemoteUrl"));
        companyInfo.setBusinessLicenseLocalUrl(company.getBusinessLicense());
        companyInfo.setBusinessLicenseType(company.getBusinessLicenseType());
        companyInfo.setBusinessLicenseRemoteUrl(JSONUtil.parseObj(company.getExt()).getStr("businessLicenseRemoteUrl"));
        // 其他字段根据实际情况从company对象或其ext字段中获取
//        companyInfo.setDelegateIdEndExpiry(entries.getStr("delegateIdEndExpiry"));
//        companyInfo.setLegalRepresentativeIdStartExpiry(DateUtil.format(company.getLegalRepresentativeIdExpiry(), "yyyy-MM-dd"));
        companyInfo.setLegalRepresentativeIdEndExpiry(entries.getStr("legalRepresentativeIdValidityEndDate"));

        // 设置开户行和银行卡信息
        // 从 ext 字段中提取银行相关信息和新增的四个字段
        companyInfo.setBankName(entries.getStr("bankName"));
        companyInfo.setBankAccount(entries.getStr("bankAccount"));
        companyInfo.setFax(entries.getStr("fax"));
        companyInfo.setPostalCode(entries.getStr("postalCode"));
        companyInfo.setBankAddress(entries.getStr("bankAddress"));
        companyInfo.setAccountName(entries.getStr("accountName"));
        companyInfo.setPartyContactInfo(entries.getStr("partyContactInfo"));


        response.setCompanyInfo(companyInfo);

        // 用户信息设置保持不变
        UserInfo userInfo = new UserInfo();
        // 设置新增的字段
        userInfo.setProviderName(user.getAccount());
        userInfo.setPhoneNumber(user.getPhone());
        userInfo.setAccountStatus(AccountStatus.getStatus(user));
        userInfo.setValidityPeriod(user.getExt().getExpireDate());
        // 其他字段需要根据实际情况从相应的对象中获取
        // 这里仅作示例，实际实现可能需要从其他服务或对象中获取数据

        RegisterConnectorToHubRequest registerConnectorToHubRequest = entries.get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        if (ObjectUtil.isNotNull(registerConnectorToHubRequest)) {
            RegisterConnectorToHubRequest.BaseInfo baseInfo = registerConnectorToHubRequest.getBaseInfo();
            baseInfo.setIdentifyID(company.getNodeId());
//            baseInfo.setIdentityStatus(CompanyStatus.REVIEW_PASS.equals(company.getStatus()) ? "接入成功" : "");
            if (ObjectUtil.isNotNull(registerConnectorToHubRequest)) {
                userInfo.setRouterId(company.getNodeId());
                userInfo.setRouterName(registerConnectorToHubRequest.getBaseInfo().getConnectorName());
            }
            response.setRegisterConnectorInfo(registerConnectorToHubRequest);
        }

        response.setUserInfo(userInfo);

        return response;
    }
}