package com.ailpha.ailand.dataroute.endpoint.license;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.dbapp.licence.licsdkjava.helper.LocalConfigBO;
import com.dbapp.licence.licsdkjava.helper.SignHandler;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class Config {

    private final AiLandProperties aiLandProperties;

    @Bean
    public LocalConfigBO localConfigBO() {
        if (StringUtils.isEmpty(aiLandProperties.getNewLic().getProductName())) {
            throw new RuntimeException("初始化lic配置失败：产品名称不能为空，请检查配置");
        }
        return new LocalConfigBO(aiLandProperties.getNewLic().getLocalCacheDir(), aiLandProperties.getNewLic().getAdditional());
    }

    @Bean
    public SignHandler signHandler(LocalConfigBO localConfigBO) {
        return new SignHandler(localConfigBO);
    }


}
