package com.ailpha.ailand.dataroute.endpoint.dataAssetApproval.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/11/28
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataAssetIdsReq implements Serializable {
    @NotEmpty(message = "数据资产ID不能为空")
    @Schema(description = "数据资产ID")
    List<String> dataAssetIds;
    @NotNull(message = "数据资产类型不能为空")
    @Schema(description = "资产类型")
    AssetType type;
    @Schema(description = "是否通过审批")
    Boolean pass;
}
