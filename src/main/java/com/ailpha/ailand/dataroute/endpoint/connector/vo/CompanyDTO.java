package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * todo 等标准确定后 这里需要改造 删除一些废弃的字段
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyDTO implements Serializable {
    /**
     * 企业ID
     */
    Long id;
    /**
     * 接入主体ID
     */
    String thirdBusinessId;
    /**
     * 节点ID
     */
    String nodeId;
    /**
     * 租户schema
     */
    String schema;
    /**
     * 营业执照
     */
    private String businessLicense;
    private String businessLicenseType;

    /**
     * 接入主体类型
     */
    private AccessType accessType = AccessType.LEGAL_PERSON;

    // 法人相关字段
    /**
     * 企业名称
     */
    private String organizationName;
    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 法定代表人姓名
     */
    private String legalRepresentativeName;
    /**
     * 法定代表人证件类型
     */
    @Deprecated
    private String legalRepresentativeIdType;
    /**
     * 法定代表人证件号码
     */
    private String legalRepresentativeIdNumber;
    /**
     * 法定代表人证件有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Deprecated
    private Date legalRepresentativeIdExpiry;
    /**
     * 法定代表人实名认证等级
     */
    private String legalRepresentativeAuthLevel;
    /**
     * 认证方式
     */
    private String authMethod;
    /**
     * 注册地址
     */
    private String registrationAddress;
    /**
     * 行业类型
     */
    private String industryType;
    /**
     * 经营期限起始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessStartDate;
    /**
     * 经营期限截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessEndDate;

    /**
     * 有效期截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Deprecated
    private Date validityEndDate;
    /**
     * 是否删除
     */
    @Builder.Default
    private Boolean deleted = false;
    @Deprecated
    String authorizationLetter;
    String connectorName;
    /**
     * 经营范围
     */
    String businessScope;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮编
     */
    private String postalCode;

    /**
     * 银行地址
     */
    private String bankAddress;

    /**
     * 户名
     */
    private String accountName;

    String ext;

    Date createdTime;
}
