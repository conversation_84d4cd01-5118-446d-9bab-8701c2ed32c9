package com.ailpha.ailand.dataroute.endpoint.connector.vo;

import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompanyDTO implements Serializable {
    /**
     * 企业ID
     */
    Long id;
    String thirdBusinessId;
    /**
     * 节点ID
     */
    String nodeId;
    NodeDTO serviceNode;
    /**
     * 企业状态
     */
    CompanyStatus status;
    /**
     * 租户schema
     */
    String schema;
    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 接入主体类型
     */
    private AccessType accessType;

    // 法人相关字段
    /**
     * 企业名称
     */
    private String organizationName;
    /**
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 法定代表人姓名
     */
    private String legalRepresentativeName;
    /**
     * 法定代表人证件类型
     */
    private String legalRepresentativeIdType;
    /**
     * 法定代表人证件号码
     */
    private String legalRepresentativeIdNumber;
    /**
     * 法定代表人证件有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date legalRepresentativeIdExpiry;
    /**
     * 法定代表人实名认证等级
     */
    private String legalRepresentativeAuthLevel;
    /**
     * 认证方式
     */
    private String authMethod;
    /**
     * 注册地址
     */
    private String registrationAddress;
    /**
     * 行业类型
     */
    private String industryType;
    /**
     * 经营期限起始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessStartDate;
    /**
     * 经营期限截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date businessEndDate;

    // 经办人相关字段
    /**
     * 经办人姓名
     */
    private String delegateName;
    /**
     * 经办人证件类型
     */
    private String delegateIdType;
    /**
     * 经办人证件号码
     */
    private String delegateIdNumber;
    /**
     * 经办人证件有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityStartDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateIdValidityEndDate;
    /**
     * 经办人所属机构
     */
    private String delegateInstitution;
    /**
     * 经办人所属机构统一社会信用代码
     */
    private String delegateInstitutionCode;
    /**
     * 经办人联系方式
     */
    private String delegateContact;
    /**
     * 经办人电子邮箱
     */
    private String delegateEmail;
    /**
     * 经办人实名认证等级
     */
    private String delegateAuthLevel;
    /**
     * 经办人认证方式
     */
    private String delegateAuthMethod;
    /**
     * 经办人注册地址
     */
    private String delegateRegistrationAddress;
    /**
     * 经办人行业类型
     */
    private String delegateIndustryType;
    /**
     * 经办人任务范围
     */
    private String delegateTaskScope;
    /**
     * 授权开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthorizationStart;
    /**
     * 授权结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date delegateAuthorizationEnd;
    /**
     * 经办人备注
     */
    private String delegateRemarks;

    /**
     * 有效期截止日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validityEndDate;
    /**
     * 是否删除
     */
    private Boolean deleted = false;

    Date reviewTime;
    String authorizationLetter;
    String connectorName;

    String entityId;
    String entityCode;

    public boolean needReview() {
        return status.equals(CompanyStatus.INIT) || status.equals(CompanyStatus.NOT_REVIEW);
    }
}
