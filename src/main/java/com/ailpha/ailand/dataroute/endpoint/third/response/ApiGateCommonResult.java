package com.ailpha.ailand.dataroute.endpoint.third.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiGateCommonResult<T> implements Serializable {

    T data;

    /**
     * 业务代码
     */
    int code;

    String message;

    boolean success;

    public boolean isSuccess() {
        return 200 == code;
    }
}
