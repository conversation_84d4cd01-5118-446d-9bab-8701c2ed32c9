package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.SSEMessageRequest;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SendMessageRequest extends BaseRemoteRequest {
    List<SSEMessageRequest> sseMessageRequests;
}
