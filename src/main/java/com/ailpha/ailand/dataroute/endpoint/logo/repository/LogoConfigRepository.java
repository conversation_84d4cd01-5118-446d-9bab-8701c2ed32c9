package com.ailpha.ailand.dataroute.endpoint.logo.repository;

import com.ailpha.ailand.dataroute.endpoint.logo.entity.CustomLogo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

/**
 * <AUTHOR>
 * @date 2025/4/8 10:29
 */
public interface LogoConfigRepository extends JpaRepository<CustomLogo, Long>, QuerydslPredicateExecutor<CustomLogo> {



}
