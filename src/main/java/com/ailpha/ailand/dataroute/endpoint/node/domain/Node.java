package com.ailpha.ailand.dataroute.endpoint.node.domain;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "t_node")
public class Node {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "node_id", length = 64)
    private String nodeId;

    @Column(name = "node_name", length = 100)
    private String nodeName;

    @Column(name = "node_type", length = 50)
    private String nodeType;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private NodeStatus status;

    @Column(name = "access_time")
    private Date accessTime;

    // 已删除 company_id 字段
    String ext;
}