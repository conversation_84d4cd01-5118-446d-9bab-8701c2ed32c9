package com.ailpha.ailand.dataroute.endpoint.demand;

import cn.hutool.core.io.FileUtil;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.output.EndpointRemote;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import io.minio.*;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import retrofit2.Response;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class DemandService {

    private final MinioClient minioClient;
    private final List<String> INVALID_FILE_SUFFIX = List.of("doc", "docx", "pdf");
    private final RouterService routerService;
    private final EndpointRemote endpointRemote;

    public String uploadDemandSolutionFile(MultipartFile file) {
        String userId = LoginContextHolder.currentUser().getId();
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        String prefix = FileUtil.getPrefix(file.getOriginalFilename());
        if (!INVALID_FILE_SUFFIX.contains(suffix) || prefix.contains("../"))
            throw new RestfulApiException("非法文件类型");
        try {
            boolean bucketExists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(userId).build());
            if (!bucketExists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(userId).build());
            }
        } catch (Exception e) {
            log.error("上传文件失败 ", e);
            throw new RestfulApiException("上传文件失败：无法创建存储目录，请联系管理员");
        }

        try {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(userId)
                    .object(file.getOriginalFilename())
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
        } catch (Exception e) {
            log.error("上传文件失败 ", e);
            throw new RestfulApiException("上传文件失败：无法创建存储对象，请联系管理员");
        }
        return String.format("%s_%s_%s_%s", routerService.currentNode().getPlatformId(), LoginContextHolder.currentUser().getCompany().getNodeId(), userId, file.getOriginalFilename());
    }

    public void redirectToDownloadSolutionFile(String fileId, HttpServletResponse response) {
        response.reset();
        String[] args = StringUtils.split(fileId, "_");
        // 跨平台远程调用
        if (StringUtils.equals(args[0], routerService.currentNode().getPlatformId())) {
            downloadSolutionFile(fileId, response);
        } else {
            MDC.put(SystemConstants.ROUTE_ID, args[1]);
            Response<ResponseBody> download = endpointRemote.download(fileId);
            try (ResponseBody responseBody = download.body()) {
                Assert.isTrue(responseBody != null, "下载需求响应文件失败：获取数源方需求报价方案异常，请联系管理员");
                download(args[3], responseBody.byteStream(), response);
            }
        }
    }

    private void download(String filename, InputStream in, HttpServletResponse response) {
        try {
            ServletOutputStream outputStream = response.getOutputStream();
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
            byte[] bytes = new byte[1024];
            int len;
            while ((len = in.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        } catch (Exception e) {
            log.error("下载需求响应文件失败 ", e);
            throw new RestfulApiException("下载需求响应文件失败");
        }
    }

    public static void main(String[] args) {
        System.out.println(URLEncoder.encode("filename", StandardCharsets.UTF_8));
    }

    public void downloadSolutionFile(String fileId, HttpServletResponse response) {
        response.reset();
        String[] args = StringUtils.split(fileId, "_");
        try {
            InputStream inputStream = minioClient.getObject(GetObjectArgs.builder()
                    .bucket(args[2])
                    .object(args[3])
                    .build());
            download(args[3], inputStream, response);
        } catch (Exception e) {
            log.error("下载文件失败 ", e);
            throw new RestfulApiException("下载文件失败：无法连接存储服务器，请联系管理员");
        }
    }
}
