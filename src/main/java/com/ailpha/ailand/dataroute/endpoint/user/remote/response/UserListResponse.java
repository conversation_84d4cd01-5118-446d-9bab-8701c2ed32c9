package com.ailpha.ailand.dataroute.endpoint.user.remote.response;

import com.ailpha.ailand.dataroute.endpoint.user.enums.IdentityAuthStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UserListResponse {
    String id;
    @Schema(name = "用户名")
    String account;
    @Schema(name = "经办人姓名")
    String name;
    @Schema(name = "手机号")
    String mobile;
    @Schema(name = "邮箱")
    String email;
    @Schema(name = "终端访问地址")
    String endpointUrl;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    Date createTime;
    @Schema(name = "身份认证状态")
    IdentityAuthStatus authStatus;
    String authTime;
    @Schema(description = "有效期")
    Date expireDate;
}
