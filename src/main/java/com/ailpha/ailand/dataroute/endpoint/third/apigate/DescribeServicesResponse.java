package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DescribeServicesResponse extends GatewayResponse<DescribeServicesResponse.InvokeResultWrapper> {
    @Getter
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResultWrapper {
        InvokeResult invokeResult;

        public void setInvokeResult(String invokeResult) {
            this.invokeResult = JacksonUtils.json2pojo(invokeResult, InvokeResult.class);
        }
    }

    @Data
    @NoArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class InvokeResult {
        List<ServiceDescription> rows;

        Integer totalCount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ServiceDescription {
        @JsonProperty("api_total")
        Integer apiTotal;
        String auditLog;
        @JsonProperty("create_time")
        Long createTime;
        String desc;
        String discoverEnable;
        List<String> hosts;
        String id;
        String maskLog;
        String name;
        List<CreateServiceRequest.InvokeParam.PluginWrapper> plugins;
        @JsonProperty("sensitive_api_total")
        Integer sensitiveApiTotal;
        @JsonProperty("upgrade_time")
        Long updateTime;
        CreateServiceRequest.InvokeParam.UpStream upStream;
    }
}
