package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.interceptor.Sign;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.request.*;
import com.ailpha.ailand.dataroute.endpoint.third.response.DeliverySceneResponse;
import com.ailpha.ailand.dataroute.endpoint.third.response.PageResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 16:52
 */
@RetrofitClient(baseUrl = "http://127.0.0.1:8081")
@Sign(baseCapabilityType = BaseCapabilityType.TRADE_PLATFORM, tokenUrl = "/third/app/token")
public interface HubDeliverySceneRemote {

    /**
     * 新增
     *
     * @param sceneSaveReq req
     * @return
     */
    @POST("/api/deliveryScene/add")
    CommonResult<String> deliverySceneManagerAdd(@Body SceneSaveReq sceneSaveReq);

    /**
     * 归属当前用户场景交付列表
     *
     * @param sceneListReq req
     * @return
     */
    @POST("/api/deliveryScene/list")
    CommonResult<PageResponse<SceneListResp>> deliverySceneManagerList(@Body PageRequest<SceneListCenterReq> sceneListReq);


    /**
     * 场景交付详情
     *
     * @param id id
     * @return
     */
    @GET("/api/deliveryScene/detail")
    CommonResult<SceneDetailResp> sceneDetail(@Query("id") String id);


    /**
     * 用户可用数据资产
     *
     * @param req req
     * @return
     */
    @POST("/api/deliveryScene/assetApprove")
    CommonResult<PageResponse<DataAssetResp>> userDataAssetApprove(@Body PageRequest<UserDataAssetReq> req);

    /**
     * 根据订单ID列表查询场景信息列表
     *
     * @param orderIds 订单id列表
     */
    @POST("/api/deliveryScene/order/scene")
    CommonResult<List<OrderSceneRefResp>> orderNoRelateScene(@Body List<String> orderIds);

    /**
     * 更新绑定的内容信息
     *
     * @param req req
     * @return
     */
    @POST("/api/deliveryScene/relateApiID")
    CommonResult<Boolean> relateApi(@Body SceneAssetApiReq req);

    @POST("/api/deliveryScene/findBy")
    CommonResult<List<DeliverySceneResponse>> selectByCondition(@Body SelectDeliverySceneRequest request);

    /**
     * 根据apiId查询场景相关信息
     *
     * @param req
     * @return
     */
    @POST("/api/deliveryScene/querySceneByApiIdList")
    CommonResult<List<SceneApiIdRelateReq>> querySceneByApiIdList(@Body QuerySceneByApiIdReq req);


    /**
     * 删除场景
     *
     * @param req 场景id
     * @return true for success
     */
    @POST("/api/deliveryScene/deleteById")
    CommonResult<Boolean> deleteSceneById(@Body DeleteSceneByIdReq req);

    @POST("/api/deliveryScene/report/delivery")
    CommonResult<Boolean> reportDeliveryRecord(@Body DeliveryRecordRequest request);

    @POST("/api/deliveryScene/scene/update")
    CommonResult<Boolean> updateScene(@Body DeliverySceneUpdateRequest request);


    @POST("/api/deliveryScene/scene/assetOrder")
    CommonResult<SceneAssetResp> sceneAssetOrder(@Body DeliveryAssetOrderRequest request);

}
