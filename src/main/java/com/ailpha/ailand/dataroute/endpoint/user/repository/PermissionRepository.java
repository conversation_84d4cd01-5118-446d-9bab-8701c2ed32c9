package com.ailpha.ailand.dataroute.endpoint.user.repository;

import com.ailpha.ailand.dataroute.endpoint.user.domain.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import java.util.List;

public interface PermissionRepository extends JpaRepository<Permission, String>, QuerydslPredicateExecutor<Permission> {
    List<Permission> findByRoleIdIn(List<String> role);
}
