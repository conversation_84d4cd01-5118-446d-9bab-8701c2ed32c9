package com.ailpha.ailand.dataroute.endpoint.third.request;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeliveryType;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/11/18 09:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneApiIdRelateReq {

    String apiId;

    String dataAssetId;

    String sceneId;

    DeliveryType deliveryType;

    String orderId;

    SceneAssetApiReq.ExtData jsonExt;

    String ext;

    public SceneAssetApiReq.ExtData getJsonExt() {
        return StringUtils.isBlank(ext) ? null : JSONUtil.toBean(ext, SceneAssetApiReq.ExtData.class);
    }
}
