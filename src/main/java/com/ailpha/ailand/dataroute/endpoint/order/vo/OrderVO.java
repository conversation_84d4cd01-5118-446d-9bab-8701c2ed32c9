package com.ailpha.ailand.dataroute.endpoint.order.vo;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.order.util.OrderTimeUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/11/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderVO implements Serializable {

    @Schema(description = "订单编号")
    String orderId;

    @Schema(description = "资产ID")
    String assetId;

    @Schema(description = "资产名称")
    String assetName;

    @Schema(description = "交付方式")
    String deliveryMode;

    @Schema(description = "获益人用户ID")
    String beneficiaryId;

    @Schema(description = "获益人用户名")
    String beneficiaryUsername;

    @Schema(description = "获益人方企业名称")
    String beneficiaryEnterpriseName;

    @Schema(description = "获益人方企业性质")
    String beneficiaryEnterpriseProperty;

    @Schema(description = "审批人用户ID")
    String approverId;

    @Schema(description = "审批人用户名")
    String approverUsername;

    @Schema(description = "审批方连接器ID")
    String approverRouterId;

    @Schema(description = "审批方企业名称")
    String approverEnterpriseName;

    @Schema(description = "预付费、后付费")
    String chargingWay;

    @Schema(description = "计量方式：按次、按时间")
    String meteringWay;

    @Schema(description = "使用次数上限")
    BigInteger allowance;

    @Schema(description = "成功使用次数")
    BigInteger successfulUsage;

    @Schema(description = "失败使用次数")
    BigInteger unsuccessfulUsage;

    @Schema(description = "状态：APPLY（待审批）APPROVED（通过）REJECTED（拒绝）TERMINATED（已终止）COMPLETED（已完成）")
    String status;

    @Schema(description = "订单配置")
    OderRecordExtend orderConfig;

    /**
     * 资源类型
     */
    AssetType type;

    @Schema(description = "订单有效期")
    Date expireDate;

    @Schema(description = "审批通过时间")
//    @JsonFormat(pattern = "yyyy.MM.dd HH:mm:ss", timezone = "GMT+8")
    private Date approveTime;

    Date updateTime;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Schema(description = "创建时间")
    Date createTime;

    @Schema(description = "扩展字段json（api鉴权key等信息）")
    AssetBeneficiaryExtend beneficiaryExtend;


    Long approveTimeMill;

    @Schema(description = "资产信息")
    DataProductVO assetInfo;

    public Long getApproveTimeMill() {
        if (approveTime == null) {
            return null;
        } else {
            return approveTime.getTime();
        }
    }

    String usageTime;

    public String getUsageTime() {
        return OrderTimeUtil.formatUsageTime(status, approveTime, updateTime, expireDate);
    }
}
