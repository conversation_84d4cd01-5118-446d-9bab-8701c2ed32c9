package com.ailpha.ailand.dataroute.endpoint.user.schedule;


import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.ailpha.ailand.dataroute.endpoint.common.FunctionNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.pk.UUID64Generator;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RouterInfoReportRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.StandardBaseRequest;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LoginMonitorSchedule {

    @Resource
    private RouterManagerRemoteService routerManagerRemoteService;
    private static final Map<String, Long> LOGIN_CACHE = new ConcurrentHashMap<>();

    public static void addLoginRecord(String username) {
        LOGIN_CACHE.put(username, System.currentTimeMillis());
    }

    @Scheduled(fixedRate = 60 * 1000)
    public void processLoginRecords() {
        if (!LOGIN_CACHE.isEmpty()) {
            log.info("当前周期登录用户列表:{}", LOGIN_CACHE.keySet());
            infoReport(LOGIN_CACHE);
            LOGIN_CACHE.clear();
        }
    }

    public void infoReport(Map<String, Long> userMap) {
        if (userMap.isEmpty()) {
            return;
        }
        StandardBaseRequest<List<RouterInfoReportRequest>> request = new StandardBaseRequest<>();

        request.setSqNo(UUID64Generator.generate().toString());

        RouterInfoReportRequest reportRequest = new RouterInfoReportRequest();
        List<JSONObject> values = userMap.entrySet().stream().map(entry -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("identityId", StringUtils.split(entry.getKey(), "_")[1]);
            jsonObject.set("timestamp", entry.getValue());
            return jsonObject;
        }).collect(Collectors.toList());
        reportRequest.setType(1);
        reportRequest.setRemark("");
        reportRequest.setTimestamp(String.valueOf(System.currentTimeMillis()));
        reportRequest.setValue(values);

        request.setData(Collections.singletonList(reportRequest));
        String companyId = StringUtils.split(userMap.keySet().stream().findAny().get(), "_")[0];
        NodeDTO.HubInfo hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfoForEntity(Long.valueOf(companyId));
        request.setHubInfo(hubInfo);
        log.debug("用户信息上报:{}", request);
        FunctionNodeMetaData functionNodeMetaData = new FunctionNodeMetaData();
        functionNodeMetaData.setNodeId(StringUtils.split(userMap.keySet().iterator().next(), "_")[2]);
        CommonResult<Void> result = routerManagerRemoteService.infoReport(request, functionNodeMetaData.toBase64());
        if (!result.isSuccess()) {
            log.error("用户信息上报: {}", result);
        }
    }

}
