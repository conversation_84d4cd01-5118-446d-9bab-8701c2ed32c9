package com.ailpha.ailand.dataroute.endpoint.company.remote;

import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetEnterpriseInfoRequest extends BaseRemoteRequest {
    String identityId;
    String token;
}
