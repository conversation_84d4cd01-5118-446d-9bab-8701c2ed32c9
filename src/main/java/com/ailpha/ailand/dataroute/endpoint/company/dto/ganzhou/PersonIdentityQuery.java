package com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou;

import lombok.Data;

import java.io.Serializable;

@Data
public class PersonIdentityQuery implements Serializable {

    private static final long serialVersionUID = -1598451743195057957L;
    /**
     * 用户唯一标识
     */
    private String userName;

    /**
     * 主体主键ID
     */
    private String entityId;

    /**
     * 身份主键ID
     */
    private String identityId;

    /**
     * 身份唯一标识
     */
    private String identityCode;

    /**
     * 主体名称
     */
    private String entityName;

    /**
     * 主体状态:0-可用;1-不可用
     */
    private String entityStatus;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 证件类型
     */
    private String certType;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 证件有效期起始
     */
    private String validStartTime;

    /**
     * 证件有效期截止
     */
    private String validEndTime;

    /**
     * 身份状态 0-可用;1-不可用
     */
    private String identityStatus;

    /**
     * 实名等级
     */
    private String realnameAuthStatus;

    /**
     * 实名方式
     */
    private String realnameAuthMethod;

    /**
     * 实名时间
     */
    private String realnameAuthTime;
}