package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.ConnectorMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.request.BaseRemoteRequest;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Objects;

@Slf4j
@Component
@Setter
public class SignInterceptor extends BasePathMatchInterceptor {
    BaseCapabilityType baseCapabilityType;
    String tokenUrl;
    String[] getTokenExclude;
    private static final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        if (baseCapabilityType == BaseCapabilityType.NONE) {
            return new Response.Builder()
                    .code(500)
                    .protocol(Protocol.HTTP_2)
                    .message("基础能力 " + baseCapabilityType + " 未启用")
                    .request(chain.request())
                    .body(ResponseBody.create("非法的基础能力配置[" + baseCapabilityType.name() + "]，请联系管理员", MediaType.get("application/json")))
                    .build();
        }
        Request request = chain.request();

        // 目前只有连接器间请求需要功能节点信息
        new NodeDTO.HubInfo();
        NodeDTO.HubInfo hubInfo;
        // 目标连接器schema,作用：TenantFilter用于切换schema
        String targetSchema = "";
        String targetNodeId = "";
        // 当前连接器ID
        String nodeId = LoginContextHolder.isLogin() ? LoginContextHolder.currentUser().getCompany().getNodeId() : "";
        BaseCapabilityConfig capabilityConfig;
        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        String localCompanyId = null;
        if (LoginContextHolder.isLogin()) {
            UserDTO currentUser = LoginContextHolder.currentUser();
            localCompanyId = RoleEnums.SUPER_ADMIN.name().equals(currentUser.getRoleName()) ? null : String.valueOf(currentUser.getCompany().getId());
        }
        CompanyService companyService = SpringUtil.getBean(CompanyService.class);
        if (baseCapabilityType == BaseCapabilityType.END_POINT) {
            BaseRemoteRequest baseRemoteRequest = transformBody(request);
            Assert.isTrue(ObjectUtil.isNotNull(baseRemoteRequest), "跨连接器请求异常：缺少参数【baseRemoteRequest】");
            hubInfo = baseRemoteRequest.getHubInfo();

            capabilityConfig = new BaseCapabilityConfig();
            String baseUrl = "http://%s";

            Tuple2<Boolean, Company> localCompany = companyService.localCompanyByNodeId(baseRemoteRequest.getTargetNodeId());
            if (localCompany.first) {
                baseUrl = String.format(baseUrl, String.format("%s:%s", SpringUtil.getProperty("ailand.internal.ip"), SpringUtil.getProperty("server.port")));
                targetSchema = "tenant_" + localCompany.second.getId();
            } else {
                if (StringUtils.isEmpty(baseRemoteRequest.getTargetNodeId())) {
                    String metaData = request.header("metaData");
                    if (StringUtils.isNotEmpty(metaData)) {
                        ConnectorMetaData connectorMetaData = JSONUtil.toBean(new String(Base64.getDecoder().decode(metaData)), ConnectorMetaData.class);
                        baseRemoteRequest.setTargetNodeId(connectorMetaData.getTargetNodeId());
                    }
                }
                String headerHubInfo = Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(hubInfo).getBytes(StandardCharsets.UTF_8));
                DrClientInfoVO clientInfoVO = SpringUtil.getBean(RouterService.class).getByClientNo(baseRemoteRequest.getTargetNodeId(), headerHubInfo);
                if (baseRemoteRequest.getTargetCompanyId() == null)
                    targetSchema = "";
                else
                    targetSchema = "tenant_" + baseRemoteRequest.getTargetCompanyId();
                baseUrl = String.format(baseUrl, clientInfoVO.getClientIp());
            }
            capabilityConfig.setBaseUrl(baseUrl);
            // 这里是去枢纽鉴权 所以ak sk的值应该是枢纽的
            capabilityConfig.setAppKey(hubInfo.getAk());
            capabilityConfig.setAppSecret(hubInfo.getSk());
            targetNodeId = baseRemoteRequest.getTargetNodeId();

        } else {
            if (baseCapabilityManager.isCompanyCapability(baseCapabilityType) && ObjectUtils.isEmpty(localCompanyId)) {
                localCompanyId = request.header(SystemConstants.LOCAL_COMPANY_ID);
                // TODO：注释-非登录态接口调用前，需要拿到localCompanyId，获取基础能力管理配置
                Assert.isTrue(!ObjectUtil.isEmpty(localCompanyId), "企业级读取基础能力管理配置：缺少参数【LOCAL_COMPANY_ID】");
            }
            if (!baseCapabilityManager.platformEnable(localCompanyId, baseCapabilityType)) {
                return new Response.Builder()
                        .code(500)
                        .protocol(Protocol.HTTP_2)
                        .message("基础能力 " + baseCapabilityType + " 未启用")
                        .request(chain.request())
                        .body(ResponseBody.create("基础能力 " + baseCapabilityType.name() + " 未启用", MediaType.get("application/json")))
                        .build();
            }
            capabilityConfig = baseCapabilityManager.getCapabilityConfig(localCompanyId, baseCapabilityType);
            log.debug("基础能力 {}", capabilityConfig);
            if (StringUtils.isEmpty(capabilityConfig.getBaseUrl())) {
                log.warn("基础能力 {} 未正确配置: {}", baseCapabilityType, capabilityConfig);
                return new Response.Builder()
                        .code(400)
                        .protocol(Protocol.HTTP_2)
                        .message("基础能力 " + baseCapabilityType + " 接口地址未正确配置")
                        .request(chain.request())
                        .body(ResponseBody.create("基础能力 " + baseCapabilityType + " 接口地址未正确配置", MediaType.get("application/json")))
                        .build();
            }
            hubInfo = SpringUtil.getBean(CompanyService.class).getHubInfo();
        }
        URL url = URLUtil.url(capabilityConfig.getBaseUrl());

        String newUrl = parseUrl(url);

        Assert.isTrue(StringUtils.isNotEmpty(newUrl), "服务地址不允许为空");
        Request.Builder newRequest;
        if (Objects.requireNonNull(baseCapabilityType) == BaseCapabilityType.END_POINT) {
            boolean skipGetToken = Arrays.stream(getTokenExclude).anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()));
            newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request, baseCapabilityType, nodeId, hubInfo, skipGetToken, targetNodeId, targetSchema);
        } else {
            newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request, newUrl, baseCapabilityType, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());
        }
        HttpUrl httpUrl = request.url().newBuilder()
                .encodedPath(url.getPath() + request.url().encodedPath())
                .host(url.getHost())
                .port(url.getPort() == -1 ? 443 : url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        newRequest.url(httpUrl)
                .method(request.method(), request.body())
                .tag(request.tag());
        Response response = chain.proceed(newRequest.build());
        if (baseCapabilityType == BaseCapabilityType.END_POINT && (!request.url().encodedPath().equals("/third/data-asset/attach-file")
                && !request.url().encodedPath().equals("/third/user/preview/authorizationLetter"))) {
            boolean skipGetToken = Arrays.stream(getTokenExclude).anyMatch(u -> antPathMatcher.match(u, request.url().encodedPath()));
            return OpenApiHttpUtil.doInterceptForResp(chain, newRequest, response, skipGetToken, hubInfo.getCertificate(), baseCapabilityType, baseCapabilityType.name() + "_" + nodeId, nodeId, targetNodeId, hubInfo);
        } else
            return response;
    }


    private String parseUrl(URL url) {
        if (StringUtils.equals(url.getProtocol(), "http")) {
            return String.format("%s://%s:%s%s", url.getProtocol(), url.getHost(), url.getPort(), url.getPath()) + tokenUrl;
        } else if (StringUtils.equals(url.getProtocol(), "https")) {
            return String.format("%s://%s:%s%s", url.getProtocol(), url.getHost(), url.getPort() == -1 ? 443 : url.getPort(), url.getPath()) + tokenUrl;
        } else
            return "";
    }

    private BaseRemoteRequest transformBody(Request request) {
        if (request.body() == null) {
            // GET 请求文件下载 特殊处理
            if (StringUtils.equals(HttpMethod.GET.name(), request.method()) && request.url().encodedPath().contains("/transfer/data")) {
                String metaData = request.header("metaData");
                BaseRemoteRequest baseRemoteRequest = new BaseRemoteRequest();
                ConnectorMetaData connectorMetaData = JSONUtil.toBean(new String(Base64.getDecoder().decode(metaData)), ConnectorMetaData.class);
                baseRemoteRequest.setTargetNodeId(connectorMetaData.getTargetNodeId());
                return baseRemoteRequest;
            }
            return null;
        }
        try {
            String targetNodId = request.header("targetNodId");
            if (StringUtils.isNotEmpty(targetNodId) && request.url().encodedPath().equals("/third/transfer/process")) {
                BaseRemoteRequest baseRemoteRequest = new BaseRemoteRequest();
                baseRemoteRequest.setTargetNodeId(targetNodId);
                return baseRemoteRequest;
            }
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            return JSONUtil.toBean(buffer.readUtf8(), BaseRemoteRequest.class);
        } catch (IOException | JSONException e) {
            return null;
        }
    }
}
