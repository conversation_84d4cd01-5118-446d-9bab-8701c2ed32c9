package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityConfig;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityType;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.utils.OpenApiHttpUtil;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.service.CompanyService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ReginNodeResolutionRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ReginNodeResolution;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.exception.RestfulApiException;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Slf4j
@Component
@Setter
public class SignInterceptor extends BasePathMatchInterceptor {
    BaseCapabilityType baseCapabilityType;
    String tokenUrl;

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        NodeDTO.HubInfo hubInfo;
        if (LoginContextHolder.isLogin()) {
            hubInfo = LoginContextHolder.currentUser().getCompany().getServiceNode().getHubInfo();
        } else {
            hubInfo = JSONUtil.toBean(MDC.get("hubInfo"), NodeDTO.HubInfo.class);
        }
        BaseCapabilityConfig capabilityConfig;
        BaseCapabilityManager baseCapabilityManager = SpringUtil.getBean(BaseCapabilityManager.class);
        if (baseCapabilityType == BaseCapabilityType.END_POINT) {
            // 支持2种方式
            String routeId = findRouteId(request);
//            RouterService routerService = SpringUtil.getBean(RouterService.class);
//            DrClientInfoVO clientInfoVO = routerService.getByClientNo(routeId);

//            RouterManagerRemoteService routerRemoteService = SpringUtil.getBean(RouterManagerRemoteService.class);
//            ReginNodeResolutionRequest resolutionRequest = new ReginNodeResolutionRequest();
//            resolutionRequest.setConnectorId(routeId);
//            CommonResult<ReginNodeResolution> reginedNodeResolution = routerRemoteService.reginNodeResolution(resolutionRequest);
            String connectorDeploymentAddress = SpringUtil.getBean(NodeService.class).connectorDeploymentAddress(routeId);
            capabilityConfig = new BaseCapabilityConfig();
            String baseUrl = "http://%s";
            Tuple2<Boolean, Company> localCompany = SpringUtil.getBean(CompanyService.class).localCompany(routeId);
            if (localCompany.first) {
                baseUrl = String.format(baseUrl, SpringUtil.getProperty("ailand.internal.ip") + ":8080");
                MDC.put("X-Tenant-Schema", "tenant_" + localCompany.second.getId());
            } else
                baseUrl = String.format(baseUrl, connectorDeploymentAddress);
            capabilityConfig.setBaseUrl(baseUrl);
            // 这里是去枢纽鉴权 所以aksk的值应该是枢纽的
            capabilityConfig.setAppKey(hubInfo.getAk());
            capabilityConfig.setAppSecret(hubInfo.getSk());

        } else {
            if (!baseCapabilityManager.platformEnable(baseCapabilityType)) {
                return new Response.Builder()
                        .code(500)
                        .protocol(Protocol.HTTP_2)
                        .message("基础能力 " + baseCapabilityType + " 未启用")
                        .request(chain.request())
                        .body(ResponseBody.create("{\"message\":\"基础能力" + baseCapabilityType + " 未启用\"}", MediaType.get("application/json")))
                        .build();
            }
            capabilityConfig = baseCapabilityManager.getCapabilityConfig(baseCapabilityType);
            log.debug("基础能力 {}", capabilityConfig);
            if (StringUtils.isEmpty(capabilityConfig.getBaseUrl())) {
                log.warn("基础能力 {} 未正确配置: {}", baseCapabilityType, capabilityConfig);
                return new Response.Builder()
                        .code(400)
                        .protocol(Protocol.HTTP_2)
                        .message("基础能力 " + baseCapabilityType + " 接口地址未正确配置")
                        .request(chain.request())
                        .body(ResponseBody.create("{\"message\":\"基础能力" + baseCapabilityType + " 接口地址未正确配置\"}", MediaType.get("application/json")))
                        .build();
            }
        }
        URL url = URLUtil.url(capabilityConfig.getBaseUrl());

        String newUrl;
        if (baseCapabilityType == BaseCapabilityType.END_POINT) {
            URL shuHanUrl = URLUtil.url(hubInfo.getUrl());
            newUrl = parseUrl(shuHanUrl);
        } else {
            newUrl = parseUrl(url);
        }

        Assert.isTrue(StringUtils.isNotEmpty(newUrl), "服务地址不允许为空");
        Request.Builder newRequest = OpenApiHttpUtil.openApiHeadersForRetrofit(request,
                newUrl, baseCapabilityType, capabilityConfig.getAppKey(), capabilityConfig.getAppSecret());
        if (baseCapabilityType == BaseCapabilityType.END_POINT) {
            newRequest
                    .header("routerId", ObjectUtil.isNull(MDC.get(SystemConstants.ROUTE_ID)) ? "" : MDC.get(SystemConstants.ROUTE_ID))
                    .header("X-Tenant-Schema", ObjectUtil.isNull(MDC.get("X-Tenant-Schema")) ? "" : MDC.get("X-Tenant-Schema"));
        }
        HttpUrl httpUrl = request.url().newBuilder()
                .host(url.getHost())
                .port(url.getPort() == -1 ? 443 : url.getPort())
                .scheme(url.getProtocol())
                .build();
        if (log.isTraceEnabled())
            log.trace("重新包装的请求地址：{}", httpUrl);
        newRequest.url(httpUrl)
                .method(request.method(), request.body())
                .header("hubInfo", ObjectUtil.isNull(hubInfo) ? "" : Base64.getEncoder().encodeToString(JSONUtil.toJsonStr(hubInfo).getBytes(StandardCharsets.UTF_8)))
                .tag(request.tag());
        return chain.proceed(newRequest.build());
    }

    private String findRouteId(Request request) {
        String routeId = request.header(SystemConstants.ROUTE_ID);
        if (StringUtils.isNotBlank(routeId)) {
            return routeId;
        }

        // 上下文取
        routeId = MDC.get(SystemConstants.ROUTE_ID);
        if (StringUtils.isBlank(routeId)) {
            throw new RestfulApiException("未提供指定连接器id信息");
        }
        return routeId;
    }


    private String parseUrl(URL url) {
        if (StringUtils.equals(url.getProtocol(), "http")) {
            return String.format("%s://%s:%s", url.getProtocol(), url.getHost(), url.getPort()) + tokenUrl;
        } else if (StringUtils.equals(url.getProtocol(), "https")) {
            return String.format("%s://%s:%s", url.getProtocol(), url.getHost(), url.getPort() == -1 ? 443 : url.getPort()) + tokenUrl;
        } else
            return "";
    }
}
