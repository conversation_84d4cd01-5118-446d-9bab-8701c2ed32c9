package com.ailpha.ailand.dataroute.endpoint.dataasset.domain;

import com.ailpha.ailand.dataroute.endpoint.common.pk.UUID32;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Expression;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "t_data_resource")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResource {

    public static final Set<String> COULD_UPDATE_REGISTRATION_STATUS = Set.of("item_status1", "item_status2", "item_status3");

    @Id
    @UUID32
    String id;
    /**
     * 数据资源全局（连接器空间）唯一标识
     */
    @Column(name = "data_resource_platform_id")
    String dataResourcePlatformId;
    /**
     * 连接器ID
     */
    @Column(name = "platform_id")
    String platformId;
    /**
     * 连接器类型：0 标准型 1 全功能型
     */
    @Column(name = "platform_type")
    int platformType;
    /**
     * 数据资源名称
     */
    @Column(name = "data_resource_name")
    String dataResourceName;
    /**
     * 数据资源描述
     */
    @Column(name = "description")
    String description;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 数据接入方式：API,DATABASE,FILE,平台生成(MPC)
     */
    @Column(name = "source_type")
    SourceType sourceType;
    /**
     * 数据资源大小
     */
    Long capacity;
    /**
     * 敏感等级
     */
    @Column(name = "sensitive_level")
    String sensitiveLevel;
    /**
     * 更新频率：数源的更新迭代不代表接入平台的数据是否更新
     */
    @Column(name = "update_frequency")
    String updateFrequency;
    /**
     * 登记状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销
     */
    @Column(name = "item_status")
    String itemStatus;
    /**
     * 上下线状态
     *
     * @see com.ailpha.ailand.dataroute.endpoint.dataasset.enums.PushStatus
     */
    @Deprecated
    @Column(name = "push_status")
    String pushStatus;
    /**
     * 是否已删除
     */
    @Builder.Default
    @Column(name = "is_delete")
    Boolean isDelete = false;
    /**
     * 用户id
     */
    @Column(name = "user_id")
    String userId;
    /**
     * 用户名
     */
    String username;

    @Column(name = "create_time")
    Date createTime;

    @Column(name = "update_time")
    Date updateTime;

    /**
     * 资源提供方：默认填入连接器企业认证的企业类型
     * 数源单位：连接器企业认证的企业名称
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "provider", columnDefinition = "json")
    ProviderExt provider;

    /**
     * 数据资源扩展信息:
     * dataCoverage 数据覆盖范围
     * dataCoverageTimeStart 数据覆盖周期开始时间
     * dataCoverageTimeEnd 数据覆盖周期结束时间
     * apiQueryWay (接入方式为API)API查询方式,可用值:REALTIME,OFFLINE
     * dataType 数据类型(接入方式为FILE),可用值:STRUCTURED,UNSTRUCTURED,MODEL
     * debugDataSource 调试数据来源: {@link DebugDataSourceEnum}
     * 等信息
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "data_ext", columnDefinition = "json")
    DataResourceExt dataExt;

    public DataResource updateResourceFormat(String resourceFormat) {
        if (StringUtils.hasText(resourceFormat) && !resourceFormat.equals(this.getDataExt().getResourceFormat())) {
            this.getDataExt().setResourceFormat(resourceFormat);
        }
        return this;
    }

    public DataResource updateDataResourceNameCNTo(String dataResourceNameCN) {
        if (!org.apache.commons.lang3.StringUtils.equals(dataResourceNameCN, this.getDataExt().getAssetNameCN())) {
            this.getDataExt().setAssetNameCN(dataResourceNameCN);
        }
        return this;
    }

    /**
     * 数据资产扩展信息:
     * dataCoverage 数据覆盖范围
     * dataCoverageTimeStart 数据覆盖周期开始时间
     **/
    public DataResource updateIndustry(String industry, String industry1) {
        if (StringUtils.hasText(industry) && !industry.equals(this.getIndustry())) {
            this.setIndustry(industry);
        }
        if (StringUtils.hasText(industry1) && !industry1.equals(this.getDataExt().getIndustry1())) {
            this.getDataExt().setIndustry1(industry1);
        }
        return this;
    }

    public DataResource updateDataCoverageTime(String dataCoverageTimeStart, String dataCoverageTimeEnd) {
        if (StringUtils.hasText(dataCoverageTimeStart) && !dataCoverageTimeStart.equals(this.getDataExt().getDataCoverageTimeStart())) {
            this.getDataExt().setDataCoverageTimeStart(dataCoverageTimeStart);
        }
        if (StringUtils.hasText(dataCoverageTimeEnd) && !dataCoverageTimeEnd.equals(this.getDataExt().getDataCoverageTimeEnd())) {
            this.getDataExt().setDataCoverageTimeEnd(dataCoverageTimeEnd);
        }
        return this;
    }

    public DataResource updateRegionInfo(String region, String region1) {
        if (StringUtils.hasText(region) && !region.equals(this.getDataExt().getRegion())) {
            this.getDataExt().setRegion(region);
        }
        if (StringUtils.hasText(region1) && !region1.equals(this.getDataExt().getRegion1())) {
            this.getDataExt().setRegion1(region1);
        }
        return this;
    }

    public DataResource updatePersonalInformation(String personalInformation) {
        if (StringUtils.hasText(personalInformation) && !personalInformation.equals(this.getDataExt().getPersonalInformation())) {
            this.getDataExt().setPersonalInformation(personalInformation);
        }
        return this;
    }

    public DataResource updatetDescription(String description) {
        if (StringUtils.hasText(description) && !description.equals(this.getDescription())) {
            this.setDescription(description);
        }
        return this;
    }

    public DataResource updateSourceTo(String source) {
        if (StringUtils.hasText(source) && !source.equals(this.getDataExt().getSource())) {
            this.getDataExt().setSource(source);
        }
        return this;
    }

    /**
     * 更新数据类型
     *
     * @param dataType 新的数据类型
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataResource updateDataTypeTo(DataType dataType) {
        if (!Objects.equals(this.getDataExt().getDataType(), dataType)) {
            this.getDataExt().setDataType(dataType);
        }
        return this;
    }

    /**
     * 更新数据类型1
     *
     * @param dataType1 新的数据类型1
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataResource updateDataType1To(String dataType1) {
        if (!org.apache.commons.lang3.StringUtils.equals(dataType1, this.getDataExt().getDataType1())) {
            this.getDataExt().setDataType1(dataType1);
        }
        return this;
    }

    public DataResource updateOtherTo(String other) {
        if (other != null && !other.equals(this.getDataExt().getOther())) {
            this.getDataExt().setOther(other);
        }
        return this;
    }

    /**
     * 资源类型：1 数据库表 2 接口 3 文件 4 大数据 5 密态节点数据
     */
    public DataResource updateResourceTypeTo(String resourceType) {
        if (resourceType != null && !resourceType.equals(this.getDataExt().getResourceType())) {
            this.getDataExt().setOther(resourceType);
        }
        return this;
    }

    public DataResource updateUpdateFrequency(String updateFrequency) {
        if (StringUtils.hasText(updateFrequency) && !updateFrequency.equals(this.getUpdateFrequency())) {
            this.setUpdateFrequency(updateFrequency);
        }
        return this;
    }

    public DataResource updateSensitiveLevel(String sensitiveLevel) {
        if (StringUtils.hasText(sensitiveLevel) && !sensitiveLevel.equals(this.getSensitiveLevel())) {
            this.setSensitiveLevel(sensitiveLevel);
        }
        return this;
    }

    public DataResource updateDeliveryModesTo(List<DeliveryMode> deliveryModes) {
        boolean hasSameElements = false;
        if (deliveryModes != null && this.getDeliveryExt().getDeliveryModes() != null) {
            for (DeliveryMode mode : deliveryModes) {
                if (this.getDeliveryExt().getDeliveryModes().contains(mode)) {
                    hasSameElements = true;
                    break;
                }
            }
        }
        if (!CollectionUtils.isEmpty(deliveryModes) && !hasSameElements) {
            this.getDeliveryExt().setDeliveryModes(deliveryModes);
        }
        return this;
    }

    public DataResource updateLimitationsTo(String limitations) {
        if (StringUtils.hasText(limitations) && !limitations.equals(this.getDeliveryExt().getLimitations())) {
            this.getDeliveryExt().setLimitations(limitations);
        }
        return this;
    }

    public DataResource updateAuthorizeTo(String authorize) {
        if (StringUtils.hasText(authorize) && !authorize.equals(this.getDeliveryExt().getAuthorize())) {
            this.getDeliveryExt().setAuthorize(authorize);
        }
        return this;
    }

    public DataResource updateIsSecondaryProcessedTo(String isSecondaryProcessed) {
        if (StringUtils.hasText(isSecondaryProcessed) && !isSecondaryProcessed.equals(this.getDeliveryExt().getIsSecondaryProcessed())) {
            this.getDeliveryExt().setIsSecondaryProcessed(isSecondaryProcessed);
        }
        return this;
    }

    public DataResource updateResourceId(String resourceId) {
        if (StringUtils.hasText(resourceId) && !resourceId.equals(this.getDataExt().getResourceId())) {
            this.getDataExt().setResourceId(resourceId);
        }
        return this;
    }

    public DataResource updateLineageTo(String lineage) {
        if (StringUtils.hasText(lineage) && !lineage.equals(this.getDataExt().getLineage())) {
            this.getDataExt().setLineage(lineage);
        }
        return this;
    }

    /**
     * 更新数据结构
     *
     * @param dataSchema 新的数据结构列表
     * @return 更新后的 CatalogQueryDataProduct 实例
     */
    public DataResource updateDataSchemaTo(List<DataSchemaBO> dataSchema) {
        if (dataSchema == null) {
            return this;
        }
        if (!JacksonUtils.obj2jsonIgnoreNull(dataSchema).equals(JacksonUtils.obj2jsonIgnoreNull(this.getDataExt().getDataSchema()))) {
            this.getDataExt().setDataSchema(dataSchema);
        }
        return this;
    }


    public static class DataResourceExt extends DataAssetExt {
    }

    /**
     * 交付信息扩展字段：
     * deliveryModes 交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC
     * mpcPurpose
     * MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "delivery_ext", columnDefinition = "json")
    DataResourceDeliveryExt deliveryExt;

    public static class DataResourceDeliveryExt extends DataAssetDeliveryExt {
    }

    public static Specification<DataResource> dataResourceNameLike(Specification<DataResource> specification, String dataResourceName) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dataResourceName)) {
            Specification<DataResource> dataResourceNameLike = (root, query, cb) -> cb.like(root.get("dataResourceName"), "%" + dataResourceName + "%");
            specification = specification.and(dataResourceNameLike);
        }
        return specification;
    }

    public static Specification<DataResource> dataTypeIs(Specification<DataResource> specification, DataType dataType) {
        if (dataType != null) {
            Specification<DataResource> dataTypeIs = (root, query, cb) -> {
                Expression<String> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", String.class, root.get("dataExt"), cb.literal("dataType"));
                return cb.equal(jsonExtract, cb.literal(dataType.name()));
            };
            specification = specification.and(dataTypeIs);
        }
        return specification;
    }

    public static Specification<DataResource> userIdIs(Specification<DataResource> specification, String userId) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(userId)) {
            specification = specification.and((root, query, cb) -> cb.equal(root.get("userId"), userId));
        }
        return specification;
    }

    public static Specification<DataResource> itemStatusIs(Specification<DataResource> specification, String itemStatus) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(itemStatus)) {
            Specification<DataResource> itemStatusIs = (root, query, cb) -> cb.equal(root.get("itemStatus"), itemStatus);
            specification = specification.and(itemStatusIs);
        }
        return specification;
    }

    public static Specification<DataResource> itemStatusNot(Specification<DataResource> specification, String itemStatus) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(itemStatus)) {
            Specification<DataResource> itemStatusNot = (root, query, cb) -> cb.notEqual(root.get("itemStatus"), itemStatus);
            specification = specification.and(itemStatusNot);
        }
        return specification;
    }

    public static Specification<DataResource> registrationSubmitTimeBefore(Specification<DataResource> specification, Date registrationSubmitTime) {
        if (registrationSubmitTime != null) {
            Specification<DataResource> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationSubmitTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(registrationSubmitTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataResource> registrationSubmitTimeAfter(Specification<DataResource> specification, Date registrationSubmitTime) {
        if (registrationSubmitTime != null) {
            Specification<DataResource> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationSubmitTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(registrationSubmitTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataResource> registrationTimeBefore(Specification<DataResource> specification, Date registrationTime) {
        if (registrationTime != null) {
            Specification<DataResource> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationTime"));
                return cb.lessThan(jsonExtract.as(Long.class), cb.literal(registrationTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

    public static Specification<DataResource> registrationTimeAfter(Specification<DataResource> specification, Date registrationTime) {
        if (registrationTime != null) {
            Specification<DataResource> registrationTimeBefore = (root, query, cb) -> {
                Expression<Long> jsonExtract = cb.function("JSON_EXTRACT_PATH_TEXT", Long.class, root.get("dataExt"), cb.literal("registrationTime"));
                return cb.greaterThan(jsonExtract.as(Long.class), cb.literal(registrationTime.getTime()));
            };
            specification = specification.and(registrationTimeBefore);
        }
        return specification;
    }

}
