package com.ailpha.ailand.dataroute.endpoint.common.config;

import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistrar;
import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistry;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import okio.BufferedSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.Duration;

@Slf4j
@Component
public class CustomSourceOkHttpClientRegistrar implements SourceOkHttpClientRegistrar {

    @Value("${spring.profiles.active}")
    private String profile;

    @Override
    public void register(SourceOkHttpClientRegistry registry) {

        // add testSourceOkHttpClient
        registry.register("customOkHttpClient", new OkHttpClient.Builder()
                // ignore ssl
                .sslSocketFactory(createSSL(), createTrustAllManager())
                .connectTimeout(Duration.ofSeconds(600))
                .writeTimeout(Duration.ofSeconds(600))
                .readTimeout(Duration.ofSeconds(600))
                .hostnameVerifier((hostname, session) -> true)
                .addInterceptor(chain -> chain.proceed(chain.request()))
                .build());
    }

    private SSLSocketFactory createSSL() {
        SSLSocketFactory sslSocketFactory = null;
        try {

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{createTrustAllManager()}, new SecureRandom());
            sslSocketFactory = sslContext.getSocketFactory();
        } catch (Exception ignore) {
        }
        return sslSocketFactory;
    }

    public static X509TrustManager createTrustAllManager() {
        X509TrustManager tm = null;
        try {
            tm = new X509TrustManager() {
                public void checkClientTrusted(X509Certificate[] chain, String authType)
                        throws CertificateException {
                    //do nothing，接受任意客户端证书
                }

                public void checkServerTrusted(X509Certificate[] chain, String authType)
                        throws CertificateException {
                    //do nothing，接受任意服务端证书
                }

                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            };
        } catch (Exception e) {

        }
        return tm;
    }


    /**
     * 打印返回信息
     */
    private void recordResponse(Response response) {
        boolean flag = profile.startsWith("dev") || profile.startsWith("test");
        if (!flag) {
            return;
        }
        try {
            ResponseBody responseBody = response.body();
            if (responseBody != null) {
                BufferedSource source = responseBody.source();
                // 读取完整的响应体
                source.request(Long.MAX_VALUE);
                Buffer buffer = source.buffer();
                String responseBodyString = buffer.clone().readString(StandardCharsets.UTF_8);
                log.info("返回信息：{}", responseBodyString);
            }
        } catch (IOException e) {
            log.error("解析请求响应体异常：", e);
        }
    }

    /**
     * 打印请求信息
     */
    private void recordRequest(Interceptor.Chain chain) {
        boolean flag = profile.startsWith("dev") || profile.startsWith("test");
        if (!flag) {
            return;
        }
        try {
            RequestBody requestBody = chain.request().body();
            String body = null;
            if (requestBody != null) {
                Buffer buffer = new Buffer();
                requestBody.writeTo(buffer);
                body = buffer.readUtf8();
            }
            log.debug("本次发送body请求参数信息：{}", body);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

}
