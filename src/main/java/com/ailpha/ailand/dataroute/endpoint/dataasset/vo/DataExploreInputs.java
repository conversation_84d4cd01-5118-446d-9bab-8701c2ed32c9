package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

@Data
public class DataExploreInputs {
    String file;
    @Alias("prod_source_pdf")
    String prodSourcePdf;
    @Alias("industry_file")
    String industryFile;
    @Alias("return_logs")
    Boolean returnLogs = true;
    @Alias("enterprise_info")
    EnterpriseInfo enterpriseInfo;

    @Data
    public static class EnterpriseInfo {
        @Alias("enterprise_name")
        String enterpriseName;
        @Alias("enterprise_address")
        String enterpriseAddress;
        @Alias("industry_category")
        String industryCategory;
        @Alias("business_scope")
        String businessScope;
    }

}
