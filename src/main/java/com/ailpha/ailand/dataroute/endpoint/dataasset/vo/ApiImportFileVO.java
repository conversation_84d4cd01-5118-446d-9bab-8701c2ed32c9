package com.ailpha.ailand.dataroute.endpoint.dataasset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2023/9/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ApiImportFileVO implements Serializable {

    @Schema(description = "文件ID")
    String fileId;

    @Schema(description = "文件名称")
    String fileName;

    @Schema(description = "调试数据")
    List<List<String>> dataList;
}
