package com.ailpha.ailand.dataroute.endpoint.license;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2022/8/22 16:14
 * @description
 */
@Data
public class LicenceInfoDTO {

    /**
     * 合同编号
     */
    private String contractNumber;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 行业
     */
    private String industry;

    /**
     * 许可类型
     * 1 正式许可
     * 2 测试许可
     * 3 内部许可
     */
    private Integer licenseType;

    /**
     * 首次激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date firstActiveDate;

    /**
     * 产品过保时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productExpireTime;

    /**
     * 产品使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date productLiveTime;

    private String productName;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 产品识别码
     */
    private String productSn;

    private String sdkVersion;

    private String machineCode;


    /**
     * 隐藏功能模块id
     */
    private String hideMenuList;

    /**
     * 隐藏小菜单功能
     */
    private String hidePermsList;

    private Integer maxUserCount;

    private List<NewLicenceInfoDTO.UnitLicenceDTO> unitLicenseList;


    // >>> 旧证书字段不兼容字段

    private String licenseNo;

    private Integer months;

    private Date createDate;

    private String client;
}
