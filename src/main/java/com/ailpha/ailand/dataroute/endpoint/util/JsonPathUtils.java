package com.ailpha.ailand.dataroute.endpoint.util;

import com.ailpha.ailand.invoke.api.CommonException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonPathUtils {
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final Pattern ARRAY_PATTERN = Pattern.compile("(\\w+)\\[(\\d+)\\]");

    public static <T> T getValue(String json, String path, Class<T> valueType) {
        if (StringUtils.isBlank(json)) {
            throw new CommonException("解析json失败，json为空");
        }
        try {
            JsonNode node = mapper.readTree(json);

            String[] segments = path.split("\\.");

            for (String segment : segments) {
                Matcher matcher = ARRAY_PATTERN.matcher(segment);
                if (matcher.find()) {
                    String field = matcher.group(1);
                    int index = Integer.parseInt(matcher.group(2));
                    node = node.get(field).get(index);
                } else {
                    node = node.get(segment);
                }
                if (node == null) break;
            }

            if (node == null) {
                throw new CommonException("Path not found: " + path);
            }
            return mapper.treeToValue(node, valueType);
        } catch (Exception e) {
            throw new CommonException(e);
        }
    }

    public static String modifyJson(String jsonStr, String path, Object newValue) throws Exception {
        JsonNode root = mapper.readTree(jsonStr);
        modifyNode(root, path.split("\\."), 0, newValue);
        return mapper.writeValueAsString(root);
    }

    private static void modifyNode(JsonNode node, String[] pathParts, int index, Object newValue) {
        if (index >= pathParts.length) return;

        String currentKey = pathParts[index];

        if (node.isArray()) {
            ArrayNode arrayNode = (ArrayNode) node;
            int arrayIndex = Integer.parseInt(currentKey);
            if (index == pathParts.length - 1) {
                arrayNode.set(arrayIndex, mapper.valueToTree(newValue));
            } else {
                modifyNode(arrayNode.get(arrayIndex), pathParts, index + 1, newValue);
            }
        } else if (node.isObject()) {
            ObjectNode objectNode = (ObjectNode) node;
            if (index == pathParts.length - 1) {
                objectNode.put(currentKey, newValue.toString());
            } else {
                JsonNode nextNode = objectNode.has(currentKey) ? objectNode.get(currentKey) : mapper.createObjectNode();
                objectNode.set(currentKey, nextNode);
                modifyNode(nextNode, pathParts, index + 1, newValue);
            }
        }
    }

    public static void main(String[] args) throws Exception {
        // 数组JSON
        String arrayJson = "[{\"data\":{\"message\":\"old\"}}, {\"other\":123}]";
        System.out.println(modifyJson(arrayJson, "0.data.message", "new"));

        // 对象JSON
        String objectJson = "[\n" +
                "    {\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"data\": {\n" +
                "                    \"message\": \"old\"\n" +
                "                }\n" +
                "            },\n" +
                "            {\n" +
                "                \"other\": 123\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    {\n" +
                "        \"other\": 123\n" +
                "    }\n" +
                "]";
        System.out.println(modifyJson(objectJson, "0.list.0.data.message", "new"));
    }
}