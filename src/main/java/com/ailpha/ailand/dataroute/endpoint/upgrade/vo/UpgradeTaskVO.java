package com.ailpha.ailand.dataroute.endpoint.upgrade.vo;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeModule;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/9
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpgradeTaskVO implements Serializable {

    @Schema(description = "ID")
    private String id;

    @Schema(description = "模块：DATA_ROUTE（连接器）")
    private UpgradeModule module;

    @Schema(description = "升级前包ID")
    private String beforePackageId;

    @Schema(description = "升级前版本")
    private String beforeVersion;

    @Schema(description = "升级后包ID")
    private String afterPackageId;

    @Schema(description = "升级后包名称")
    private String afterPackageName;

    @Schema(description = "升级后版本")
    private String afterVersion;

    @Schema(description = "MD5")
    private String md5;

    @Schema(description = "来源：DATA_ROUTE（连接器）")
    private UpgradeSource source;

    @Schema(description = "是否立即升级")
    private Boolean immediately;

    @Schema(description = "升级时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date upgradeTime;

    @Schema(description = "升级状态：WAIT（待升级）UPGRADING（升级中）SUCCESS（升级成功）FAILURE（升级失败）INVALID（已失效）")
    private UpgradeStatus status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
