package com.ailpha.ailand.dataroute.endpoint.upgrade.vo.request;

import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeSource;
import com.ailpha.ailand.dataroute.endpoint.common.enums.UpgradeStatus;
import com.dbapp.rest.request.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * 2025/6/9
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UpgradeTaskListReq extends Page implements Serializable {

    @Schema(description = "来源：DATA_ROUTE（连接器）")
    private UpgradeSource source;

    @Schema(description = "升级状态：WAIT（待升级）UPGRADING（升级中）SUCCESS（升级成功）FAILURE（升级失败）INVALID（已失效）")
    private UpgradeStatus status;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "升级时间-区间开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "升级时间-区间结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
