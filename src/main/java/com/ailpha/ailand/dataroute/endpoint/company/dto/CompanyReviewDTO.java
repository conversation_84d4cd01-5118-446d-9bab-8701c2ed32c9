package com.ailpha.ailand.dataroute.endpoint.company.dto;

import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CompanyReviewDTO {
    @NotNull(message = "企业ID不能为空")
    private Long id;
    
    @NotNull(message = "审核状态不能为空")
    private CompanyStatus status;
    
    private String remarks;
    
    private String refuseReason;
}