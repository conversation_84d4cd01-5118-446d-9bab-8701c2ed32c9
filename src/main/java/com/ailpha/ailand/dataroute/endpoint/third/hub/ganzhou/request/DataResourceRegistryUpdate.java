package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.ResourceItem;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataResourceRegistryUpdate {
    /**
     * 资源名称
     */
    String resourceName;
    /**
     * 来源平台内部标识
     */
    String outerResourceId;
    /**
     * 资源唯一编码
     */
    String resourceCode;
    /**
     * 平台id
     */
    String platformId;
    /**
     * 数据类型：1 个人 2 企业 3 公共
     */
    String dataType;
    /**
     * 数据资源类型
     */
    String resourceType;
    /**
     * 行业分类
     */
    String industry;
    /**
     * 存储容量
     */
    String capacity;
    /**
     * 联系人
     */
    String contacter;
    /**
     * 联系方式
     */
    String contractInformation;
    /**
     * 资源摘要
     */
    String resourceAbstract;
    /**
     * 资源格式
     */
    String resourceFormat;
    /**
     * 数据来源 01 收集取得 02 原始取得 03 交易取得 04 其他
     */
    String dataSource;
    /**
     * 是否涉及个人信息： 0 否 1 是
     */
    String personalInformation;
    /**
     * 其他
     */
    String others;
    /**
     * 使用限制
     */
    String limitations;
    /**
     * 授权使用：0 否 1 是
     */
    String authorize;
    /**
     * 查询资源列表
     */
    List<ResourceItemAddCmd> resourceItemList;
}
