package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.biz.api.collector.ApiImportTestVO;
import com.ailpha.ailand.biz.api.constants.BodyTypeEnum;
import com.ailpha.ailand.biz.api.constants.MethodEnum;
import com.ailpha.ailand.biz.api.dataset.ParamsBO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import com.dbapp.rest.exception.RestfulApiException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "数据资产交付-文件下载")
@RequiredArgsConstructor
@RequestMapping("data-asset/api")
public class DataAssetDeliverAsApiController {

    private final DataAssetService dataAssetService;

    @PostMapping("{assetType}/{deliverId}/{companyId}/{dataAssetId}")
    @Operation(summary = "api资产调用获取")
    public String dataResourceApi(@RequestParam(required = false) String sequenceNum,
                                  @RequestParam(required = false) String productId,
                                  @RequestParam(required = false) String ctrlInstructionId,
                                  @RequestParam(required = false) String targetConnectorId,
                                  @RequestParam(required = false) String issuerConnectorId,
                                  @RequestParam(required = false) String connectorProof,
                                  @RequestParam(required = false) String bodyHash,
                                  @RequestParam(required = false) boolean dispatch,
                                  @PathVariable("assetType") AssetType assetType,
                                  @PathVariable("deliverId") String deliverId,
                                  @PathVariable("companyId") String companyId,
                                  @PathVariable("dataAssetId") String dataAssetId,
                                  @RequestBody ApiImportTestVO apiImportTestVO
    ) {
        if (StringUtils.isNotBlank(sequenceNum)) {
            log.debug("需方 API 接口请求信息 sequenceNum:{}  productId:{}  ctrlInstructionId:{}  targetConnectorId:{}  issuerConnectorId:{}  connectorProof:{}  bodyHash:{}",
                    sequenceNum, productId, ctrlInstructionId, targetConnectorId, issuerConnectorId, connectorProof, bodyHash);
        }
        checkParam(apiImportTestVO);
        return dataAssetService.callDataApiProcess(assetType, deliverId, companyId, dataAssetId, apiImportTestVO, dispatch);
    }

    private void checkParam(ApiImportTestVO apiImportTestVO) {
        Assert.isTrue(apiImportTestVO != null, "api调用接口参数有误，请根据调用文档规范请求");

        MethodEnum method = apiImportTestVO.getMethod();
        Assert.isTrue(method != null, "不支持的http请求方法 【method=" + apiImportTestVO.getMethod() + "】");

        BodyTypeEnum bodyType = apiImportTestVO.getBodyType();
        Assert.isTrue(bodyType != null, "不支持的http请求Body类型 【bodyType=" + apiImportTestVO.getMethod() + "】");

        List<ParamsBO> headers = apiImportTestVO.getHeaders();
        if (CollectionUtils.isEmpty(headers)) {
            throw new RestfulApiException("缺少apiKey鉴权信息");
        }

        boolean apiKeyHeader = headers.stream().anyMatch(paramsBO -> paramsBO.getKey().equalsIgnoreCase("APIKEY"));
        if (!apiKeyHeader) {
            throw new RestfulApiException("缺少apiKey鉴权信息");
        }
    }

}
