package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品登记审批列表筛选条件")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductListQuery {
    @Schema(description = "资产类型")
    AssetType type;
    @Schema(description = "数据产品名称")
    String productName;
    @Schema(description = "数据类型：结构化数据、非结构化数据、模型")
    DataType dataType;
    @Schema(description = "接入方式")
    SourceType accessWay;
    @Schema(description = "审批状态: item_status0 暂存 item_status1 待审批 item_status2 通过 item_status3 拒绝 item_status4 登记撤销")
    String itemStatus;
    @JsonIgnore
    DataAssetPrepareStatus dataAssetPrepareStatus;
    @Schema(description = "上下架状态: 0 默认状态 1 待审批 2 通过 3 拒绝 4 已撤销 5 业务节点待审批 6 业务节点通过 7 业务节点拒绝")
    String publishStatus;
    @Schema(description = "登记提交时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationSubmitTimeStart;
    @Schema(description = "登记提交时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationSubmitTimeEnd;
    @Schema(description = "登记时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationTimeStart;
    @Schema(description = "登记时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date registrationTimeEnd;
    @Schema(description = "上架提交时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date publishSubmitTimeStart;
    @Schema(description = "上架提交时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date publishSubmitTimeEnd;
    @Schema(description = "上架时间起始 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date publishTimeStart;
    @Schema(description = "上架时间结束 yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date publishTimeEnd;
    @Schema(description = "交付场景")
    DeliveryMode deliveryMode;
    @Schema(description = "交付方式")
    String deliveryMethod;
    @JsonIgnore
    String routeId;
    @JsonIgnore
    String userId;
    @JsonIgnore
    String userName;

    @Schema(description = "是否只查询当前用户的数据资产 MPC隐私求交有这个限制")
    @Builder.Default
    Boolean currentUser = false;
    @Builder.Default
    @JsonProperty("$page")
    long num = 1;
    @Builder.Default
    @JsonProperty("$size")
    long size = 10;
}
