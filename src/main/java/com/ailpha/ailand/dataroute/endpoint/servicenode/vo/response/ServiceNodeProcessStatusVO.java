package com.ailpha.ailand.dataroute.endpoint.servicenode.vo.response;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.RegionSyncListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * 2025/7/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ServiceNodeProcessStatusVO implements Serializable {

    @Schema(description = "业务节点登记名称")
    String entryName;

    @Schema(description = "业务节点标识编码（业务节点ID）")
    String serviceNodeId;

    @Schema(description = "业务功能类型：1－应用侧基础设施，2－数据交易类，3－数据开发利用类，4－公共数据授权运营平台类，5－公共服务平台类")
    String type;

    @Schema(description = "业务功能类型描述")
    String typeDescription;

    @Schema(description = "业务节点IP地址")
    String ip;

    @Schema(description = "业务节点域名")
    String domainName;

    @NotEmpty(message = "业务节点接口地址不能为空")
    @Schema(description = "业务节点接口地址")
    String apiUrl;

    @Schema(description = "业务功能简介")
    String introduction;

    @Schema(description = "业务节点版本")
    String version;

    @Schema(description = "备注")
    String reserveNotes;

    @Schema(description = "所属法人或其他组织名称")
    String enterpriseName;

    @Schema(description = "所属法人或其他组织身份标识码")
    String enterpriseIdentityId;

    @Schema(description = "审核状态：0：待审核，1：通过，2：拒绝")
    String processStatus;

    @Schema(description = "审核时间（13位时间戳）")
    private String processTime;

    public String getTypeDescription() {
        return RegionSyncListVO.getTypeDescription(this.type);
    }
}
