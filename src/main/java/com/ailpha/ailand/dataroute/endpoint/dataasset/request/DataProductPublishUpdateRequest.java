package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品上架请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublishUpdateRequest {
    @Schema(description = "数据标识")
    String productId;
    // 上架基本信息
    @Schema(description = "发布业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;
    // 定价信息
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价 单位为分，19.99 元表示为 1999")
    Integer price;

    @Schema(description = "交付方式说明")
    String deliveryInfo;

    @Schema(description = "接入数据的更新方式：ONCE -> 单次 SCHEDULE -> 定时 MANUAL -> 手动")
    UpdateWay updateWay;
    @Schema(description = "更新类型", example = "0 -> 按天执行, 1 -> 按周执行, 2 -> 按月执行, 3 -> 单次执行 4 -> 按小时执行, 5 -> 多次执行")
    SchedulerPeriodEnum updateFreq;
    @Schema(description = "选中的日期，星期一到星期日为1-7，月份日期1-31")
    Integer selectDate;
    @Schema(description = "几点执行，默认0, 范围 0-23")
    @Range(min = 0, max = 23)
    Integer selectHour;
}
