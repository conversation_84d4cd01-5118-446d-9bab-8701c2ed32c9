package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品上架请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublishUpdateRequest {
    @Schema(description = "数据标识")
    String productId;
    // 上架基本信息
    @Schema(description = "发布业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;
    // 定价信息
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价")
    String price;
}
