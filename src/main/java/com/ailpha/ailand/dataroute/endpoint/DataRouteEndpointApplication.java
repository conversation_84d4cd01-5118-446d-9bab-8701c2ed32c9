package com.ailpha.ailand.dataroute.endpoint;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableAsync
@EnableScheduling
@SpringBootApplication
@RetrofitScan(basePackages = "com.ailpha.ailand.dataroute.endpoint")
public class DataRouteEndpointApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataRouteEndpointApplication.class, args);
    }

}
