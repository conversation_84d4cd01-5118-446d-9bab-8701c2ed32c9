package com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataAssetRevokeVM {
    @Schema(description = "数据唯一标识")
    @NotBlank(message = "数据唯一标识为空")
    private String registrationId;
}
