package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.ailpha.ailand.dataroute.endpoint.common.utils.PinYin4JUtil;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.dbapp.rest.response.ApiResponse;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.QueryResults;
import com.querydsl.jpa.impl.JPAQuery;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024/12/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformAppKeyService {

    private final EntityManager entityManager;

    private final PlatformKeySecretRepository platformKeySecretRepository;

    private final ExternalKeySecretRepository externalKeySecretRepository;

    /**
     * 接口授权管理-创建appKey
     */
    public PlatformAppKeySecret interfaceCreate(String appKey, String platformName, PlatformType platformType) {
        Assert.isTrue(!nameRepeatCheck(platformName), "平台名称 " + platformName + " 已存在!");
        appKey = appKey == null ? String.format("%s%s", PinYin4JUtil.getPinYinHeadChar(platformName).toUpperCase(), UuidUtils.uuid32()) : appKey;
        return platformKeySecretRepository.saveAndFlush(PlatformAppKeySecret.builder()
                .platformName(platformName)
                .appKey(appKey)
                .platformType(platformType)
                .appSecret(UUID.randomUUID().toString())
                .createTime(new Date())
                .build());
    }

    public boolean nameRepeatCheck(String appName) {
        return platformKeySecretRepository.exists(Example.of(PlatformAppKeySecret.builder().platformName(appName).build()));
    }

    public PlatformAppKeySecret getByNameOrCreateOne(String appKey, String appName, PlatformType platformType) {
        Optional<PlatformAppKeySecret> one = platformKeySecretRepository.getFirstByPlatformName(appName);
        return one.orElseGet(() -> interfaceCreate(appKey, appName, platformType));
    }

    public List<PlatformAppKeySecret> getByName(String appName) {
        return platformKeySecretRepository.getAllByPlatformName(appName);
    }

    /**
     * 接口授权管理-删除appKey
     */
    public boolean interfaceDelete(Long id) {
        platformKeySecretRepository.deleteById(id);
        return true;
    }

    /**
     * 接口授权管理-编辑appKey
     */
    public boolean interfaceUpdate(Long id, String platformName, PlatformType platformType) {
        Assert.isTrue(!nameRepeatCheck(platformName), "平台名称 " + platformName + " 已存在!");
        platformKeySecretRepository.findById(id).ifPresent(platformKeySecret -> {
            platformKeySecret.setPlatformName(platformName);
            platformKeySecret.setPlatformType(platformType);
            platformKeySecret.setUpdateTime(new Date());
            platformKeySecretRepository.saveAndFlush(platformKeySecret);
        });
        return true;
    }

    /**
     * 接口授权管理-重置appSecret
     */
    public boolean interfaceResetAppSecret(Long id) {
        platformKeySecretRepository.findById(id).ifPresent(platformKeySecret -> {
            String appSecret = UUID.randomUUID().toString();
            platformKeySecret.setAppSecret(appSecret);
            platformKeySecret.setUpdateTime(new Date());
            platformKeySecretRepository.saveAndFlush(platformKeySecret);
        });
        return true;
    }

    /**
     * 接口授权管理-appKey列表
     */
    public SuccessResponse<List<PlatformAppKeySecret>> interfaceList(PlatformAppKeyPageReq platformAppKeyPageReq) {
        long offset = (platformAppKeyPageReq.getNum() - 1) * platformAppKeyPageReq.getSize();

        QPlatformAppKeySecret qas = QPlatformAppKeySecret.platformAppKeySecret;
        JPAQuery<PlatformAppKeySecret> jpaQuery = new JPAQuery<>(entityManager);
        jpaQuery = jpaQuery.from(qas);
        if (StringUtils.hasText(platformAppKeyPageReq.getPlatformName())) {
            jpaQuery = jpaQuery.where(qas.platformName.like("%" + platformAppKeyPageReq.getPlatformName() + "%"));
        }
        QueryResults<PlatformAppKeySecret> results = jpaQuery
                .orderBy(qas.createTime.desc())
                .offset(offset).limit(platformAppKeyPageReq.getSize()).fetchResults();

        return ApiResponse.success(results.getResults()).page(platformAppKeyPageReq).total(results.getTotal()).build();
    }

    /**
     * 外部接口管理-创建appKey
     */
    public String externalCreate(String platformId, String platformDomain, PlatformType platformType, String appKey, String appSecret) {
        if (PlatformType.DATA_ROUTE_HUB.equals(platformType)) {
            List<String> platformIdExists = externalKeySecretRepository.findByPlatformType(platformType).stream().map(ExternalPlatformAppKeySecret::getPlatformId).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(platformIdExists)) {
                for (String platformIdExist : platformIdExists) {
                    Assert.isTrue(ObjectUtils.isEmpty(platformIdExist), "连接器枢纽已存在，请重新选择平台类型");
                }
            }
        }
        List<String> platformIdExists = externalKeySecretRepository.findByPlatformId(platformId).stream().map(ExternalPlatformAppKeySecret::getPlatformId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(platformIdExists)) {
            for (String platformIdExist : platformIdExists) {
                Assert.isTrue(ObjectUtils.isEmpty(platformIdExist), "平台ID已存在，请重新填写平台ID");
            }
        }
        externalKeySecretRepository.saveAndFlush(ExternalPlatformAppKeySecret.builder()
                .platformId(platformId)
                .platformDomain(platformDomain)
                .platformType(platformType)
                .appKey(appKey)
                .appSecret(appSecret)
                .createTime(new Date())
                .build());
        return appKey;
    }

    /**
     * 外部接口管理-删除appKey
     */
    public boolean externalDelete(Long id) {
        externalKeySecretRepository.deleteById(id);
        return true;
    }

    /**
     * 外部接口管理-编辑appKey
     */
    public boolean externalUpdate(Long id, String platformId, String platformDomain, PlatformType platformType, String appKey, String appSecret) {
        if (PlatformType.DATA_ROUTE_HUB.equals(platformType)) {
            List<String> platformIdExists = externalKeySecretRepository.findByPlatformType(platformType)
                    .stream().filter(e -> e.getId() != null && !e.getId().equals(id))
                    .map(ExternalPlatformAppKeySecret::getPlatformId).collect(Collectors.toList());

            if (!ObjectUtils.isEmpty(platformIdExists)) {
                for (String platformIdExist : platformIdExists) {
                    Assert.isTrue(ObjectUtils.isEmpty(platformIdExist), "连接器枢纽已存在，请重新选择平台类型");
                }
            }
        }
        List<String> platformIdExists = externalKeySecretRepository.findByPlatformId(platformId)
                .stream().filter(e -> e.getId() != null && !e.getId().equals(id))
                .map(ExternalPlatformAppKeySecret::getPlatformId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(platformIdExists)) {
            for (String platformIdExist : platformIdExists) {
                Assert.isTrue(ObjectUtils.isEmpty(platformIdExist), "平台ID已存在，请重新填写平台ID");
            }
        }

        externalKeySecretRepository.findById(id).ifPresent(platformKeySecret -> {
            platformKeySecret.setPlatformId(platformId);
            platformKeySecret.setPlatformDomain(platformDomain);
            platformKeySecret.setPlatformType(platformType);
            platformKeySecret.setAppKey(appKey);
            platformKeySecret.setAppSecret(appSecret);
            platformKeySecret.setUpdateTime(new Date());
            externalKeySecretRepository.saveAndFlush(platformKeySecret);
        });
        return true;
    }

    /**
     * 外部接口管理-appKey列表
     */
    public SuccessResponse<List<ExternalPlatformAppKeySecret>> externalList(ExternalPlatformAppKeyPageReq externalPlatformAppKeyPageReq) {
        long offset = (externalPlatformAppKeyPageReq.getNum() - 1) * externalPlatformAppKeyPageReq.getSize();

        QExternalPlatformAppKeySecret qas = QExternalPlatformAppKeySecret.externalPlatformAppKeySecret;
        JPAQuery<ExternalPlatformAppKeySecret> jpaQuery = new JPAQuery<>(entityManager);
        jpaQuery = jpaQuery.from(qas);
        if (StringUtils.hasText(externalPlatformAppKeyPageReq.getPlatformId())) {
            jpaQuery = jpaQuery.where(qas.platformId.like("%" + externalPlatformAppKeyPageReq.getPlatformId() + "%"));
        }
        QueryResults<ExternalPlatformAppKeySecret> results = jpaQuery
                .orderBy(qas.createTime.desc())
                .offset(offset).limit(externalPlatformAppKeyPageReq.getSize()).fetchResults();

        return ApiResponse.success(results.getResults()).page(externalPlatformAppKeyPageReq).total(results.getTotal()).build();
    }
}
