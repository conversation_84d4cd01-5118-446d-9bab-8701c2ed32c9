package com.ailpha.ailand.dataroute.endpoint.common.config;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import sun.security.x509.X500Name;

import java.io.FileNotFoundException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.security.*;
import java.security.cert.Certificate;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPrivateCrtKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class KeyStoreFactory {
    char[] password;
    KeyStore keyStore;
    final Object lock = new Object();
    Path keyStorePath;
    String keyStoreType;

    static final String KEY_TYPE = "RSA";
    static final String CERTIFICATE_SIGNATURE_ALGORITHM = "SHA256WithRSA";
    static final int KEY_BITS = 2048;

    private void init() {
        if (!keyStorePath.getParent().toFile().exists()) {
            try {

                Files.createDirectories(keyStorePath.getParent());
            } catch (Exception ignore) {

            }
        }
    }

    public KeyStoreFactory(char[] password, Path keyStorePath, String keyStoreType) {
        this.password = password;
        this.keyStorePath = keyStorePath;
        this.keyStoreType = keyStoreType;
    }

    public KeyPair getKeyPair(String alias, char[] password, String keyStoreType) {
        try {
            synchronized (this.lock) {
                if (keyStore == null) {
                    synchronized (this.lock) {
                        keyStore = KeyStore.getInstance(Optional.ofNullable(keyStoreType).orElse("PKCS12"));
                        keyStore.load(Files.newInputStream(keyStorePath), this.password);
                    }
                }
            }
            RSAPrivateCrtKey key = (RSAPrivateCrtKey) keyStore.getKey(alias, password);
            RSAPublicKeySpec spec = new RSAPublicKeySpec(key.getModulus(), key.getPublicExponent());
            PublicKey publicKey = KeyFactory.getInstance("RSA").generatePublic(spec);
            return new KeyPair(publicKey, key);
        } catch (FileNotFoundException | NoSuchFileException e) {
            keyStore = null;
            this.generateKeyStore(alias, password, keyStoreType);
            return this.getKeyPair(alias,password,keyStoreType);
        } catch (Exception e) {
            throw new IllegalStateException("cannot load keys from store path: " + keyStorePath, e);
        }
    }

    private void generateKeyStore(String alias, char[] password, String keyStoreType) {
        try {
            synchronized (lock) {
                if (keyStore == null) {
                    synchronized (lock) {
                        keyStore = KeyStore.getInstance(Optional.ofNullable(keyStoreType).orElse("keyStoreType"));
                        keyStore.load(null, null);

                        CertificateAndKeyGenerate certificateAndKeyGenerate = new CertificateAndKeyGenerate(KEY_TYPE, CERTIFICATE_SIGNATURE_ALGORITHM);
                        certificateAndKeyGenerate.generate(KEY_BITS);

                        PrivateKey privateKey = certificateAndKeyGenerate.getPrivateKey();

                        X509Certificate selfCertificate = certificateAndKeyGenerate.getSelfCertificate(new X500Name("cn=DAS-security,ou=Data Route"), new Date(), TimeUnit.DAYS.toSeconds(3650));

                        keyStore.setKeyEntry(alias, privateKey, password, new Certificate[]{selfCertificate});
                        keyStore.store(Files.newOutputStream(keyStorePath), password);
                    }
                }
            }
        } catch (Exception e) {
            throw new IllegalStateException("Cannot load keys from store: " + keyStorePath, e);
        }
    }
}
