package com.ailpha.ailand.dataroute.endpoint.user.vo.loginSetting;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class LoginSettingVO {
    //锁定时间区间(单位:秒)
    @Schema(description = "锁定时间区间(单位:秒)")
    private Integer lockPeriod = 60;

    //时间内运行允许最大登陆失败次数
    @Schema(description = "时间内运行允许最大登陆失败次数")
    private Integer maxFailNum = 3;

    //禁止登陆时间(单位:秒)
    @Schema(description = "禁止登陆时间(单位:秒)")
    private Integer unlockTime = 600;
}
