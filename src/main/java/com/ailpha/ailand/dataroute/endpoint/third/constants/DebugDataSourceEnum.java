package com.ailpha.ailand.dataroute.endpoint.third.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @since 2020-04-09 15:31
 **/
public enum DebugDataSourceEnum {

    GENERATE_FROM_EXECUTOR("generateFromExecutor", "前置机生成(脱敏)"),

    SOURCE_SAMPLING("sourceSampling", "源数据采样"),

    EXTRA_UPLOAD("extraUpload", "文件上传上传"),

    NONE("none", "无调试数据");

    private final String code;

    @Getter
    private final String label;

    DebugDataSourceEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    @JsonCreator
    public static DebugDataSourceEnum getItem(String code) {
        for (DebugDataSourceEnum item : values()) {
            if (item.getCode().equalsIgnoreCase(code)) {
                return item;
            }
        }
        return null;
    }

}
