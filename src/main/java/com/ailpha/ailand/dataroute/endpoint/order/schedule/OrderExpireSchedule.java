package com.ailpha.ailand.dataroute.endpoint.order.schedule;

import cn.hutool.core.collection.CollectionUtil;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.OrderClassify;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.service.DeliveryService;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.domain.QOrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.remote.request.TradingStrategyDelivery;
import com.ailpha.ailand.dataroute.endpoint.order.repository.OrderRecordRepository;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @description: 订单有效期检查
 * @date 2025/2/25 15:29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderExpireSchedule {

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final CompanyRepository companyRepository;

    private final OrderManagerService orderManagerService;

    private final OrderRecordRepository orderRecordRepository;

    private final JPAQueryFactory queryFactory;

    private final DeliveryService deliveryService;

    private final ServiceNodeRemoteService nodeRemoteService;

    @Scheduled(fixedDelay = 2, timeUnit = TimeUnit.MINUTES)
    public void expireOrder() {
        if (BUSY.get()) {
            return;
        }

        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        companyRepository.findAll().forEach(company -> {
            if (StringUtils.isEmpty(company.getNodeId()))
                return;
            try {
                BUSY.set(true);
                log.info("expire order check ....");
                // 买方轮询 指定routeID， 状态：审批通过、待审核， expire 过期时间不为 null， expire ＜ 当前时间

                AsyncManager.getInstance().executeFuture(() -> {
                    TenantContext.setCurrentTenant("tenant_" + company.getId());
                    checkExpired(company.getNodeId());
                    return true;
                });

            } finally {
                BUSY.set(false);
            }
        });

    }

    private void checkExpired(String nodeId) {
        QOrderApprovalRecord orderApprovalRecord = QOrderApprovalRecord.orderApprovalRecord;
        List<OrderApprovalRecord> approvalRecordList = queryFactory.selectFrom(orderApprovalRecord)
                .where(orderApprovalRecord.status.in("APPROVED").and(orderApprovalRecord.classify.eq(OrderClassify.NORMAL))
                        .and(orderApprovalRecord.beneficiaryRouterId.eq(nodeId))
                        .and(orderApprovalRecord.expireDate.before(new Date())))
                .orderBy(orderApprovalRecord.pullTime.desc())
                .offset(0)
                .limit(50L)
                .fetch();

        if (CollectionUtil.isEmpty(approvalRecordList)) {
            log.info("未检测到过期订单...");
            return;
        }

        // 过期的订单
        for (OrderApprovalRecord approvalRecord : approvalRecordList) {
            log.info("检测到过期订单: {}", approvalRecord);
            changeOrder2Expire(approvalRecord);
        }
    }

    private void changeOrder2Expire(OrderApprovalRecord approvalRecord) {
        // step 1、修改状态
        log.info("订单【{}】已过期", approvalRecord.getId());
        approvalRecord.setStatus("COMPLETED");
        approvalRecord.setUpdateTime(new Date());
        orderRecordRepository.save(approvalRecord);

        // step 2、完成场景交付
        deliveryService.completeByOrderId(approvalRecord.getId());


        // step 3、通知业务流通平台
        OderRecordExtend oderConfig = null;
        try {
            oderConfig = approvalRecord.getExtend();
            String serviceNodeId = oderConfig.getServiceNodeId();
            log.info("通知流通利用平台【{}】过期修改订单状态：{}", serviceNodeId, approvalRecord.getId());

            TradingStrategyDelivery strategyDelivery = new TradingStrategyDelivery();
            strategyDelivery.setTradingStrategyCode(oderConfig.getTradingStrategyCode());
            strategyDelivery.setDeliveryStatus(3);
            strategyDelivery.setUrl(oderConfig.getServiceNodeUrl());
            strategyDelivery.setCurrentNodeId(approvalRecord.getBeneficiaryRouterId());

            nodeRemoteService.deliveryStatusReport(strategyDelivery);
        } catch (Exception e) {
            log.error("订单过期同步业务节点异常:{}", e.getMessage(), e);
        }

        // 合约失效
        try {
            String localCompanyId = oderConfig.getBeneficiaryCompanyId();
            orderManagerService.terminalContract(localCompanyId, Collections.singletonList(approvalRecord.getId()), null);
        } catch (Exception e) {
            log.warn("修改订单关联合约【{}】异常", approvalRecord.getId());
        }
    }

}
