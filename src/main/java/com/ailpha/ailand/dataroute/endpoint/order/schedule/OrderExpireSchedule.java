package com.ailpha.ailand.dataroute.endpoint.order.schedule;

import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataProductService;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderApprovalRecord;
import com.ailpha.ailand.dataroute.endpoint.order.domain.OrderRecordDTO;
import com.ailpha.ailand.dataroute.endpoint.order.service.OrderManagerService;
import com.ailpha.ailand.dataroute.endpoint.order.vo.OderRecordExtend;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SystemConstants;
import com.ailpha.ailand.dataroute.endpoint.third.output.HubOrderRemote;
import com.ailpha.ailand.dataroute.endpoint.third.request.OrderRecordsReq;
import com.ailpha.ailand.dataroute.endpoint.third.response.OrderRecordsResp;
import com.dbapp.rest.response.SuccessResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @description: 订单有效期检查
 * @date 2025/2/25 15:29
 */
@Slf4j
// 赣州没有交易中心，不做定时任务过期扫描
//@Component
@RequiredArgsConstructor
public class OrderExpireSchedule {

    private static final AtomicBoolean BUSY = new AtomicBoolean(false);

    private final CompanyRepository companyRepository;

    private final HubOrderRemote hubOrderRemote;

    private final OrderManagerService orderManagerService;

    private final DataProductService dataProductService;

    @Scheduled(fixedDelay = 5, timeUnit = TimeUnit.MINUTES)
    public void expireOrder() {
        if (BUSY.get()) {
            return;
        }

        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        companyRepository.findByStatusAndDeletedFalse(CompanyStatus.REVIEW_PASS).forEach(company -> {
            try {
                BUSY.set(true);
                log.info("expire order check ....");
                // 买方轮询 指定routeID， 状态：审批通过、待审核， expire 过期时间不为 null， expire ＜ 当前时间
                OrderRecordsReq orderRecordsReq = new OrderRecordsReq();
                orderRecordsReq.setRouterId(company.getNodeId());
                orderRecordsReq.setPage(1L);
                orderRecordsReq.setSize(20L);
                final SuccessResponse<OrderRecordsResp> expiredOrderPage = hubOrderRemote.expiredOrderPage(orderRecordsReq);
                if (!expiredOrderPage.isSuccess() || expiredOrderPage.getTotal() == null || expiredOrderPage.getData() == null) {
                    return;
                }

                // 过期的订单
                final List<OrderRecordDTO> list = expiredOrderPage.getData().getData();
                for (OrderRecordDTO orderRecordDTO : list) {
                    String status = orderRecordDTO.getStatus();
                    switch (status) {
                        case "APPLY" -> orderInvalid(orderRecordDTO);
                        case "APPROVED" -> changeOrder2Expire(orderRecordDTO);
                    }
                }

            } finally {
                BUSY.set(false);
            }
        });

    }

    private void orderInvalid(OrderRecordDTO orderRecordDTO) {
        try {
            log.info("订单【{}】有效期内未审批, 置为失效", orderRecordDTO.getOrderId());
            SuccessResponse<OrderApprovalRecord> successResponse = hubOrderRemote.selectOrderApprovalRecordWhereId(orderRecordDTO.getOrderId());
            final OrderApprovalRecord approvalRecord = successResponse.getData();
            if (successResponse.isSuccess() && approvalRecord != null) {
                approvalRecord.setStatus("INVALID");
                hubOrderRemote.updateOrderApprovalRecord(approvalRecord);
            } else {
                log.warn("订单【{}】有效期内未审批置为失效异常", orderRecordDTO.getOrderId());
            }
        } catch (Exception e) {
            log.error("订单【{}】有效期内未审批置为失效异常", orderRecordDTO.getOrderId(), e);
        }
    }

    private void changeOrder2Expire(OrderRecordDTO orderRecordDTO) {
        // 修改状态
        OrderApprovalRecord approvalRecord = null;
        try {
            log.info("订单【{}】已过期", orderRecordDTO.getOrderId());
            SuccessResponse<OrderApprovalRecord> successResponse = hubOrderRemote.selectOrderApprovalRecordWhereId(orderRecordDTO.getOrderId());
            approvalRecord = successResponse.getData();
            if (successResponse.isSuccess() && approvalRecord != null) {
                approvalRecord.setStatus("COMPLETED");
                // 修改状态、完成场景交付
                approvalRecord.setChangeStatus(true);
                hubOrderRemote.updateOrderApprovalRecord(approvalRecord);
            } else {
                log.warn("修改订单【{}】已过期异常", orderRecordDTO.getOrderId());
            }
        } catch (Exception e) {
            log.error("修改订单【{}】已过期异常", orderRecordDTO.getOrderId(), e);
        }

        // 合约失效
        try {
            assert approvalRecord != null;
            OderRecordExtend oderConfig = JacksonUtils.json2pojo(approvalRecord.getExtend(), OderRecordExtend.class);
            MDC.put(SystemConstants.COMPANY_ID, oderConfig.getBeneficiaryCompanyId());
            orderManagerService.terminalContract(Collections.singletonList(orderRecordDTO.getOrderId()), null);
        } catch (Exception e) {
            log.warn("修改订单关联合约【{}】异常", orderRecordDTO.getOrderId());
        }
    }

}
