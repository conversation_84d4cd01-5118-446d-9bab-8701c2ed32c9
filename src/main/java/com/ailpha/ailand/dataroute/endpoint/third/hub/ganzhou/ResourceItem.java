package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ResourceItem {
    /**
     * 主键
     */
    String id;
    String parentId;
    /**
     * 数据资源标识码
     */
    String resourceId;
    String resourceType;
    String code;
    Integer level;
    String fieldType;
    String filedLength;
    String filedDesc;
    String primaryFlag;
    String nullFlag;
    String paramType;
    String dataType;
    String createTime;
    String updateTime;
}
