package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateConvertUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.RuntimeUtils;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.WeekEnums;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataUpdateTaskLogRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataUpdateTaskRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.repository.DataProductRepository;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.DataAssetPrepareSchedule;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz.DataUpdateCronJobParams;
import com.ailpha.ailand.dataroute.endpoint.dataasset.schedule.quartz.QuartzSchedulerManager;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataUpdateTaskService;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogPageRequest;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataUpdateTaskLogVO;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.repository.TenantRepository;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;

import javax.annotation.PostConstruct;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据接入任务服务层实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn("quartzSchedulerManager")
public class DataUpdateTaskServiceImpl implements DataUpdateTaskService {

    private final DataUpdateTaskRepository taskRepository;
    private final DataUpdateTaskLogRepository taskLogRepository;

    private final DataProductRepository dataProductRepository;

    private final QuartzSchedulerManager quartzSchedulerManager;

    private final JPAQueryFactory jpaQueryFactory;

    private final AiLandProperties aiLandProperties;

    private final TenantRepository tenantRepository;
    private final CompanyRepository companyRepository;

    private final FilesStorageServiceImpl filesStorageService;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat(
            "yyyy-MM-dd HH:mm:ss");

    private final Object lock = new Object();

    @PostConstruct
    private void recoverTasks() {

        Set<Company> companySet = tenantRepository.findAll().stream().map(t -> {
                    try {
                        Optional<Company> companyReference = companyRepository.findById(Long.valueOf(StringUtils.substringAfter(t.getSchemaName(), "_")));
                        return companyReference.orElse(null);
                    } catch (Exception e) {
                        return null;
                    }
                }).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        companySet.forEach(c -> {
            try {
                TenantContext.setCurrentTenant("tenant_" + c.getId());

                // 程序异常退出时，修改处于执行中的任务状态为失败，并打印日志信息
                List<DataUpdateTaskLog> dataUpdateTaskLogList = taskLogRepository.findByTaskStatus(DataUpdateStatus.SYNCING);
                if (!dataUpdateTaskLogList.isEmpty()) {
                    dataUpdateTaskLogList.forEach(t -> {
                        Optional<DataUpdateTask> optionalDataUpdateTask = taskRepository.findById(t.getTaskId());
                        if (optionalDataUpdateTask.isEmpty()) {
                            log.error("任务历史 [{}] 关联的任务 [{}] 不存在", t.getId(), t.getTaskId());
                            return;
                        }
                        DataUpdateTask dataUpdateTask = optionalDataUpdateTask.get();
                        log.error("The update task failed due to an abnormal system restart. [{}]", t.getId());
                        taskLogRepository.updateTaskStatus(t.getId(), DataUpdateStatus.FAILED);
                        taskLogRepository.updateEndTime(t.getId(), new Date());
                        taskLogStatistics(t.getId(), dataUpdateTask.getTaskLogNum());
                        taskLogRepository.updateLog(t.getId(), grepTaskLog(t.getId()));
                    });
                }

                // 恢复定时任务
                List<DataUpdateTask> dataUpdateScheduleTaskList = getScheduleUpdateTasks();

                if (!dataUpdateScheduleTaskList.isEmpty()) {
                    dataUpdateScheduleTaskList.forEach(dataUpdateTask -> {
                        DataProduct dataProduct = dataProductRepository.getReferenceById(dataUpdateTask.getDataProductId());

                        // 构建crontask DataUpdateCronJobParams
                        DataUpdateCronJobParams dataUpdateCronJobParams = new DataUpdateCronJobParams();
                        dataUpdateCronJobParams.setDataUpdateTask(dataUpdateTask);
                        dataUpdateCronJobParams.setCompanyId(dataProduct.getProvider().getCompany().getId());
                        dataUpdateCronJobParams.setDataAssetPrepareSchedule(SpringUtil.getBean(DataAssetPrepareSchedule.class));
                        try {
                            quartzSchedulerManager.submitCronJob(dataUpdateCronJobParams, false);
                        } catch (Exception e) {
                            log.error("数据产品定时更新任务 {} 恢复失败, reason: {}", JSONUtil.toJsonStr(dataUpdateTask), e.getMessage());
                            return;
                        }
                        log.info("成功恢复定时任务：{}", JSONUtil.toJsonStr(dataUpdateTask));
                    });
                }
                TenantContext.clear();
            } catch (Exception e) {
                log.warn("恢复企业 {} 数据产品定时更新任务失败", c, e);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createTask(DataUpdateTask task) {
        task.setId(UUID.randomUUID().toString());
        if (UpdateWay.SCHEDULE.equals(task.getDataUpdateType())) {
            task.setCronExpression(dataUpdateBaseInfo2CronExpression(task.getBaseInfo()));
        }
        task.setCreateTime(new Date());

        taskRepository.saveAndFlush(task);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataUpdateTask updateTask(DataUpdateTask task) {
        DataUpdateTask existingTask = getTask(task.getId());

        existingTask.setDataProductId(task.getDataProductId());
        existingTask.setDataUpdateType(task.getDataUpdateType());
        existingTask.setCronExpression(dataUpdateBaseInfo2CronExpression(task.getBaseInfo()));
        existingTask.setExt(task.getExt());
        existingTask.setBaseInfo(task.getBaseInfo());
        existingTask.setUpdateTime(new Date());

        return taskRepository.saveAndFlush(existingTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTask(String taskId) {
        taskRepository.deleteById(taskId);
    }

    @Override
    public DataUpdateTask getTask(String taskId) {
        DataUpdateTask dataUpdateTask = taskRepository.findById(taskId).orElse(null);
        Assert.notNull(dataUpdateTask, "更新任务不存在");
        return dataUpdateTask;
    }

    @Override
    public DataUpdateTask getTaskByDataProductId(String dataProductId) {
        DataUpdateTask dataUpdateTask = taskRepository.findByDataProductId(dataProductId);
        Assert.isTrue(!Objects.isNull(dataUpdateTask), "数据产品更新任务不存在");
        return dataUpdateTask;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public void executeTask(String taskId) {
//        log.debug("手动执行数据产品更新任务-executeTask");
        DataUpdateTask task = getTask(taskId);

        switch (task.getDataUpdateType()) {
            case ONCE, MANUAL:
                executeTask(task);
                break;
            case SCHEDULE:
                submitScheduledTask(task);
                break;
            default:
                throw new IllegalStateException("Unsupported task scheduler type: " + task.getDataUpdateType());
        }
    }

    @Override
    public synchronized Boolean checkLatestTaskLogStatus(String taskLogId) {
        if (StringUtils.isBlank(taskLogId)) {
            return true;
        }
        DataUpdateTaskLog dataUpdateTaskLog = getTaskLog(taskLogId);
        return DataUpdateStatus.SUCCESS.equals(dataUpdateTaskLog.getTaskStatus()) || DataUpdateStatus.FAILED.equals(dataUpdateTaskLog.getTaskStatus());
    }

    @Override
    public SuccessResponse<List<DataUpdateTaskLogVO>> getTaskHistory(DataUpdateTaskLogPageRequest dataUpdateTaskLogPageRequest) {
        log.debug("查询历史请求：{}", JSONUtil.toJsonStr(dataUpdateTaskLogPageRequest));
        Date startDate = dataUpdateTaskLogPageRequest.getStartDate();
        Date endDate = dataUpdateTaskLogPageRequest.getEndDate();
        UpdateWay updateWay = dataUpdateTaskLogPageRequest.getUpdateWay();
        DataUpdateStatus dataUpdateStatus = dataUpdateTaskLogPageRequest.getUpdateStatus();
        String dataProductId = dataUpdateTaskLogPageRequest.getDataAssetId();

        DataUpdateTask dataUpdateTask = getTaskByDataProductId(dataProductId);

        QDataUpdateTaskLog qDataUpdateTaskLog = QDataUpdateTaskLog.dataUpdateTaskLog;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        // 基础条件，某个任务下的记录
        booleanBuilder.and(qDataUpdateTaskLog.taskId.eq(dataUpdateTask.getId()));

        // 根据更新方式查询
        if (!Objects.isNull(updateWay)) {
            booleanBuilder.and(qDataUpdateTaskLog.dataUpdateType.eq(updateWay));
        }

        // 根据任务状态查询
        if (!Objects.isNull(dataUpdateStatus)) {
            booleanBuilder.and(qDataUpdateTaskLog.taskStatus.eq(dataUpdateStatus));
        }

        // 根据时间范围查询
        if (startDate != null) {
            booleanBuilder.and(qDataUpdateTaskLog.createTime.goe(startDate));
        }

        if (endDate != null) {
            // 将endDate设置为当天的23:59:59
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            Date endOfDay = calendar.getTime();
            booleanBuilder.and(qDataUpdateTaskLog.createTime.loe(endOfDay));
        }

        Long total = jpaQueryFactory.select(qDataUpdateTaskLog.countDistinct()).from(qDataUpdateTaskLog)
                .where(booleanBuilder).fetch().getFirst();

        List<DataUpdateTaskLogVO> dataUpdateTaskLogVOList = jpaQueryFactory
                .select(qDataUpdateTaskLog)
                .from(qDataUpdateTaskLog)
                .where(booleanBuilder)
                .orderBy(qDataUpdateTaskLog.createTime.desc())
                .offset((dataUpdateTaskLogPageRequest.getNum() - 1) * dataUpdateTaskLogPageRequest.getSize())
                .limit(dataUpdateTaskLogPageRequest.getSize())
                .fetch()
                .stream()
                .map(tuple -> {
                    DataUpdateTaskLogVO dataUpdateTaskLogVO = new DataUpdateTaskLogVO();
                    dataUpdateTaskLogVO.setUpdateTime(tuple.getCreateTime());
                    dataUpdateTaskLogVO.setUpdateStatus(tuple.getTaskStatus());
                    dataUpdateTaskLogVO.setTaskLog(tuple.getLog());
                    dataUpdateTaskLogVO.setUpdateWay(tuple.getDataUpdateType());

                    return dataUpdateTaskLogVO;
                }).collect(Collectors.toList());

        return SuccessResponse.success(dataUpdateTaskLogVOList).total(total).build();
    }

    @Override
    public DataUpdateTaskLog getTaskLog(String taskLogId) {
        return taskLogRepository.findById(taskLogId).orElseThrow(() -> new RestfulApiException("任务记录不存在"));
    }

    /**
     * 直接执行任务
     *
     * @param task 任务信息
     */
//    @Transactional(rollbackFor = Exception.class, readOnly = true)
    public void executeTask(DataUpdateTask task) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(task.getDataProductId());
        synchronized (this.lock) {
            // 检查最新的执行任务是否正在执行中
            Assert.isTrue(checkLatestTaskLogStatus(task.getLatestTaskLogId()), "数据产品的更新任务正在执行中，无法创建新任务");
            // 单次任务，不会在详情页展示，只会存在一条执行历史
            // 每执行一次，初始化一条任务历史记录
            DataUpdateTaskLog taskLog = new DataUpdateTaskLog();
            taskLog.setId(UUID.randomUUID().toString());
            taskLog.setTaskId(task.getId());
            taskLog.setTaskStatus(DataUpdateStatus.CREATED);
            taskLog.setCreateTime(new Date());
            taskLog.setDataUpdateType(task.getDataUpdateType());
            taskLogRepository.saveAndFlush(taskLog);

            // 存储最新的执行记录ID
            task.setLatestTaskLogId(taskLog.getId());
            // 任务执行次数+1
            int taskNums;
            if (Objects.isNull(task.getTaskLogNum())) {
                taskNums = 0;
            } else {
                taskNums = task.getTaskLogNum();
            }
            task.setTaskLogNum(taskNums + 1);
            task.setUpdateTime(new Date());
            taskRepository.saveAndFlush(task);
        }
        log.debug("执行更新任务 {}", JSONUtil.toJsonStr(task));
        String taskId = task.getId();
        Long companyId = dataProduct.getProvider().getCompany().getId();
        ThreadUtil.execAsync(() -> {
            log.debug("开始执行异步更新任务");
            executeAsyncTask(taskId, companyId);
        });
    }

    /**
     * 执行异步任务
     *
     * @param taskId
     * @param companyId
     */
//    @Async
    public void executeAsyncTask(String taskId, Long companyId) {
//        log.debug("开始执行异步更新任务");
        TenantContext.setCurrentTenant("tenant_" + companyId);
        DataUpdateTask task = this.getTask(taskId);
        DataProduct dataProduct = dataProductRepository.getReferenceById(task.getDataProductId());

        try {
            // 执行数据产品预处理
            DataUpdateTaskLog taskLog = getTaskLog(task.getLatestTaskLogId());
            log.info("Start executing data product update task [{}]", taskLog.getId());
            log.info("Update task type: {} [{}]", task.getDataUpdateType(), taskLog.getId());
            SpringUtil.getBean(DataAssetPrepareSchedule.class).prepareDataProduct(dataProduct);
        } catch (Exception e) {
            log.error("Failed to execute the data product update task [{}] error: {}", task.getId(), e.getMessage());
        } finally {
            TenantContext.clear();
        }
    }

    /**
     * 提交定时任务
     *
     * @param task 任务信息
     */
//    @Transactional(rollbackFor = Exception.class)
    public void submitScheduledTask(DataUpdateTask task) {
        DataProduct dataProduct = dataProductRepository.getReferenceById(task.getDataProductId());

        // 构建crontask DataUpdateCronJobParams
        DataUpdateCronJobParams dataUpdateCronJobParams = new DataUpdateCronJobParams();
        dataUpdateCronJobParams.setDataUpdateTask(task);
        dataUpdateCronJobParams.setCompanyId(dataProduct.getProvider().getCompany().getId());
        dataUpdateCronJobParams.setDataAssetPrepareSchedule(SpringUtil.getBean(DataAssetPrepareSchedule.class));

        // 提交定时任务
        quartzSchedulerManager.submitCronJob(dataUpdateCronJobParams, true);

        log.info("提交数据产品：{}, 定时更新任务：{}", task.getDataProductId(), task.getId());
    }

    /**
     * 根据起始时间和周期计算cron表达式
     *
     * @param dataUpdateBaseInfo
     * @return
     */
    public String dataUpdateBaseInfo2CronExpression(DataUpdateTaskBaseInfo dataUpdateBaseInfo) {
        if (!UpdateWay.SCHEDULE.equals(dataUpdateBaseInfo.getUpdateWay())) {
            return null;
        }

        long startTime = dataUpdateBaseInfo.getStartTime().getTime();
        long minute = getCurrentMinuteOfHour(startTime);
        long hour = getCurrentHourOfDay(startTime);
        // 2月没有30号的bug
        long day = getDayOfMonth(dataUpdateBaseInfo.getStartTime());
        WeekEnums week = WeekEnums.getByDate(dataUpdateBaseInfo.getStartTime());

        if (dataUpdateBaseInfo.getSchedulerPeriod() == null) {
            return null;
        }

        switch (dataUpdateBaseInfo.getSchedulerPeriod()) {
            case HOUR:
                // 针对小时任务重新设置表达式时间(提前10s)，确保不能自动执行一次
                long preTime = System.currentTimeMillis() - 10000;
                long second = preTime / 1000 % 60;
                minute = getCurrentMinuteOfHour(preTime);
                hour = dataUpdateBaseInfo.getIntervalHour();
                return String.format("%d %d 0/%d * * ?", second, minute, hour);
            case DAY:
                // 每天跑一次
                return String.format("1 %d %d * * ?", minute, hour);
            case WEEK:
                // 每周跑一次
                return String.format("1 %d %d ? * %s", minute, hour, week.getAbbr());
            case MONTH:
                // 每月跑一次
                return String.format("1 %d %d %d * ?", minute, hour, day);
            default:
                return null;
        }

    }

    private Long getCurrentMinuteOfHour(long time) {
        return time % 3600000 / 60000;
    }

    private static Integer getCurrentHourOfDay(Long ctime) {
        DateTime dateTime = new DateTime(ctime);
        return dateTime.hourOfDay().get();
    }

    private Integer getDayOfMonth(Date date) {
        DateTime dateTime = new DateTime(date);
        return dateTime.dayOfMonth().get();
    }

    /**
     * 根据定时信息，构建 DataUpdateTaskBaseInfo
     *
     * @param updateWay
     * @param updateFreq
     * @param selectDate
     * @param selectHour
     * @return
     */
    public DataUpdateTaskBaseInfo buildDataUpdateBaseInfo(UpdateWay updateWay, SchedulerPeriodEnum updateFreq, Integer selectDate, Integer selectHour) {

        // for test
        if (updateWay == null) {
            updateWay = aiLandProperties.getUpdateWay();
        }

        if (updateFreq == null) {
            updateFreq = aiLandProperties.getUpdateFreq();
        }

        if (selectDate == null) {
            selectDate = aiLandProperties.getSelectDate();
        }

        if (selectHour == null) {
            selectHour = aiLandProperties.getSelectHour();
        }

        DataUpdateTaskBaseInfo dataUpdateBaseInfo = new DataUpdateTaskBaseInfo();

        dataUpdateBaseInfo.setUpdateWay(updateWay);
        dataUpdateBaseInfo.setSchedulerPeriod(updateFreq);

        // 按小时更新的任务无需校正 startTime
        if (SchedulerPeriodEnum.HOUR == updateFreq) {
            dataUpdateBaseInfo.setIntervalHour(selectHour);
            dataUpdateBaseInfo.setStartTime(new Date());
            return dataUpdateBaseInfo;
        }
        List<Integer> schedulerPeriodValue = Arrays.asList(selectDate);

        DateTime dateTime = new DateTime();
        switch (updateFreq) {
            case DAY:
                break;
            case WEEK:
                List<Integer> weekList = schedulerPeriodValue;
                if (0 == weekList.size()) {
                    log.warn("getSchedulerPeriod can not calculate week option ...");
                    break;
                }
                dateTime = dateTime.plusDays(weekList.get(0) - dateTime.getDayOfWeek());
                break;
            case MONTH:
                List<Integer> monthList = schedulerPeriodValue;
                if (0 == monthList.size()) {
                    log.warn("getSchedulerPeriod can not calculate month option ...");
                }
                dateTime = dateTime.plusDays(monthList.get(0) - dateTime.getDayOfMonth());
                break;
            default:
                log.warn("getSchedulerPeriod failed ...");
                break;
        }

        String schedulerTime = selectHour.toString();
        String startTime = DateConvertUtils.format(dateTime.toDate(), DateConvertUtils.DATE_FORMAT) + " "
                + schedulerTime + ":00:00";
        dataUpdateBaseInfo.setStartTime(DateConvertUtils.parse(startTime, DateConvertUtils.DATE_TIME_FORMAT));

        return dataUpdateBaseInfo;
    }

    /**
     * 更新任务信息
     *
     * @param dataUpdateTask
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void saveAndFlushDataUpdateTask(DataUpdateTask dataUpdateTask) {
        taskRepository.saveAndFlush(dataUpdateTask);
    }

    /**
     * 更新任务记录信息
     *
     * @param dataUpdateTaskLog
     */
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void saveAndFlushDataUpdateTaskLog(DataUpdateTaskLog dataUpdateTaskLog) {
        taskLogRepository.saveAndFlush(dataUpdateTaskLog);
    }

    @Override
    public List<DataUpdateTask> getScheduleUpdateTasks() {
        return taskRepository.findByDataUpdateType(UpdateWay.SCHEDULE);
    }

    @Value("${logging.file.path}")
    private String logDir;

    @Value("${spring.application.name}")
    private String logFileName;

    @Override
    public String grepTaskLog(String taskLogId) {
        String logPath = StringUtils.join(new String[]{System.getProperty("user.dir"), logDir}, File.separator);
        String logFile = StringUtils.join(new String[]{
                logPath, logFileName}, File.separator) + ".log";

        log.debug("logFile: {}", logFile);

        Path logDiskPath = filesStorageService.getRootPath()
                .resolve("data-product-update-log")
                .resolve(String.format("log-%s", taskLogId));

        if (!logDiskPath.getParent().toFile().exists()) {
            try {
                Files.createDirectories(logDiskPath.getParent());
            } catch (IOException ignore) {
            }
        }

        try {
            String grepCmd = "grep -a '\\[".concat(taskLogId).concat("\\]' ").concat(logFile).concat(" > ").concat(logDiskPath.toFile().getAbsolutePath());
            //将日志根据关键字 [jobId] 输出到 version_path 文件中
            RuntimeUtils.exec(grepCmd);
            // sed -i 's/\[16312593525199267e6748754452a9031040f0f4d42df\]//g' test.log
            String sedCmd = "sed -i 's/\\[".concat(taskLogId).concat("\\]//g' ").concat(logDiskPath.toFile().getAbsolutePath());
            RuntimeUtils.exec(sedCmd);
        } catch (Exception e) {
            log.warn(e.getMessage());
        }

        String logContent = "";
        try {
            logContent = Files.readString(logDiskPath);
        } catch (Exception e) {
            log.error("读取数据产品更新任务日志文件失败, error: {}", e.getMessage());
        }

        log.debug("logContent: {}", logContent);

        return logContent;
    }

    @Override
    public void taskLogStatistics(String logId, Integer total) {
        DataUpdateTaskLog dataUpdateTaskLog = getTaskLog(logId);
        log.info("Update task start time: {} [{}]", dateFormat.format(dataUpdateTaskLog.getStartTime()), dataUpdateTaskLog.getId());
        log.info("Update task end time: {} [{}]", dateFormat.format(dataUpdateTaskLog.getEndTime()), dataUpdateTaskLog.getId());

        long duration = dataUpdateTaskLog.getEndTime().getTime() - dataUpdateTaskLog.getStartTime().getTime();
        log.info("Update task duration: {}s [{}]", duration / 1000.0, dataUpdateTaskLog.getId());
        log.info("Total number of task executions: {} [{}]", total, dataUpdateTaskLog.getId());
        log.info("Final status of the task: {} [{}]", dataUpdateTaskLog.getTaskStatus(), dataUpdateTaskLog.getId());
    }

    @Override
    public List<DataUpdateStatus> getDataUpdateTaskStatus(String taskId) {
        List<DataUpdateTaskLog> dataUpdateTaskLogList = taskLogRepository.findByTaskId(taskId);
        List<DataUpdateStatus> dataUpdateStatusList = new ArrayList<>();
        if (dataUpdateTaskLogList != null) {
            dataUpdateTaskLogList.forEach(taskLog -> {
                dataUpdateStatusList.add(taskLog.getTaskStatus());
            });
        }

        return dataUpdateStatusList;
    }

    @Override
    public void triggerTask(String taskId) {
        DataUpdateTask task = getTask(taskId);
        Assert.isTrue(checkLatestTaskLogStatus(task.getLatestTaskLogId()), "数据产品的更新任务正在执行中，无法创建新任务");
        quartzSchedulerManager.triggerJob(taskId);
    }
}