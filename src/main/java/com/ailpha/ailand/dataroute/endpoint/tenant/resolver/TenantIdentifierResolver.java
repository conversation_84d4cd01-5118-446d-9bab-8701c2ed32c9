package com.ailpha.ailand.dataroute.endpoint.tenant.resolver;

import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class TenantIdentifierResolver implements CurrentTenantIdentifierResolver<String> {

    public static final String DEFAULT_TENANT = "data_route_public";

    @Override
    public String resolveCurrentTenantIdentifier() {
        String tenant = TenantContext.getCurrentTenant();
//        if (log.isDebugEnabled())
//            log.debug("当前租户schema: {}", tenant != null ? tenant : DEFAULT_TENANT);
        return tenant != null ? tenant : DEFAULT_TENANT;
    }

    @Override
    public boolean validateExistingCurrentSessions() {
        return true;
    }
}