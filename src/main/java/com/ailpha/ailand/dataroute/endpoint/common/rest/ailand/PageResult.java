package com.ailpha.ailand.dataroute.endpoint.common.rest.ailand;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class PageResult<T> extends CommonResult<List<T>> implements IPageResult<T> {

    private long total;

    /**
     * 如果需要前端分页，需要返回这个字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer page;

    /**
     * 如果需要前端分页，需要返回这个字段
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer size;

    public PageResult(List<T> data, long total) {
        super(data);
        this.total = total;
    }

    public PageResult() {
    }

    public PageResult(int code, String message, List<T> data, boolean success) {
        super(code, message, data, success);
    }

    public PageResult(int code, String message, List<T> data, boolean success, long total, int page, int size) {
        super(code, message, data, success);
        this.total = total;
        this.page = page;
        this.size = size;
    }

    public PageResult(CommonResult<List<T>> commonResult, long total, int page, int size) {
        super(commonResult.getCode(), commonResult.getMessage(), commonResult.getData(), commonResult.isSuccess());
        this.total = total;
        this.page = page;
        this.size = size;
    }

    public PageResult(CommonResult<List<T>> commonResult, long total, IPage iPage) {
        super(commonResult.getCode(), commonResult.getMessage(), commonResult.getData(), commonResult.isSuccess());
        this.total = total;
        this.page = iPage.getPageNo();
        this.size = iPage.getPageSize();
    }

}
