package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.common.interceptor.AiSortInterceptor;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortColumnResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortDataSourceResp;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortResponse;
import com.ailpha.ailand.dataroute.endpoint.third.response.AiSortTablesResp;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import retrofit2.http.GET;
import retrofit2.http.Query;

import java.util.List;

/**
 * <AUTHOR>
 * 2025/3/3
 */
@RetrofitClient(baseUrl = "http://127.0.0.1:8081", sourceOkHttpClient = "customOkHttpClient")
@Intercept(handler = AiSortInterceptor.class)
public interface AiSortRemote {

    /**
     * 数据目录–获取表分页
     *
     * @param dbName        数据库名
     * @param dbType        数据库类型
     * @param pageIndex     当前页码
     * @param pageSize      每页大小
     * @param sourceName    数据源名称–除数据源分类外用
     * @param tableNameDesc 表名/表注释–新版接口入参
     * @param userId        数据源所属用户id
     * @param userName      数据源所属用户名称
     * @param combing       是否梳理
     * @param sortField     排序字段名称
     * @param sortOrder     排序类型（asc;desc）
     */
    @GET("/asset/api/data/tables")
    AiSortResponse<AiSortTablesResp> tables(@Query("dbName") String dbName, @Query("dbType") String dbType, @Query("pageIndex") String pageIndex, @Query("pageSize") String pageSize, @Query("sourceName") String sourceName, @Query("tableNameDesc") String tableNameDesc, @Query("userId") String userId, @Query("userName") String userName, @Query("combing") Boolean combing, @Query("sortField") String sortField, @Query("sortOrder") String sortOrder);

    /**
     * 数据目录–根据表id获取字段列表
     *
     * @param comment comment
     * @param id      表id
     * @param name    name
     */
    @GET("/asset/api/data/columns")
    AiSortResponse<List<AiSortColumnResp>> columns(@Query("comment") String comment, @Query("id") Long id, @Query("name") String name);

    /**
     * 数据源管理-根据ID获取数据源详情
     *
     * @param id id
     */
    @GET("/asset/api/dataSource/getById")
    AiSortResponse<AiSortDataSourceResp> getById(@Query("id") Long id);
}
