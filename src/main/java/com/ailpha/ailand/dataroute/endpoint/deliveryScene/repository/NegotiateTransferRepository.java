package com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository;

import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.NegotiateTransfer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:14
 */
@Repository
public interface NegotiateTransferRepository extends JpaRepository<NegotiateTransfer, String>, JpaSpecificationExecutor<NegotiateTransfer> {


}
