package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductCatalogQuery {
    /**
     * 平台 ID
     */
    String platformId;
    /**
     * 来源平台内部标识
     */
    String outerProductId;
    /**
     * 产品类型（附录字典项 pord_type）
     */
    String productType;
    /**
     * 产品名称
     */
    String productName;
    /**
     * 行业分类，详见附录《行业代码表》
     */
    String industry;
    /**
     * 地域分类，详见附录《全国区划表》
     */
    String productRegion;
    /**
     * 交付方式：01-文件传输,02-数据流传输,03-API传输
     */
    String deliveryMethod;
    /**
     * 数据主体：01-个人信息,02-企业数据,03-公共数据
     */
    String dataSubject;
    /**
     * 主体 id
     */
    String entityId;
    /**
     * 主体编码
     */
    String entityCode;
    /**
     * 唯一编码
     */
    String productCode;
    /**
     * 产品状态 01 待登记，02 登记，03 撤销
     */
    String productStatus;
    /**
     * 分页大小
     */
    Integer pageSize;
    /**
     * 当前页数
     */
    Integer pageNum;
}
