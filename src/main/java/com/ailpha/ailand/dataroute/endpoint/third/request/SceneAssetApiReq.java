package com.ailpha.ailand.dataroute.endpoint.third.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/18 17:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SceneAssetApiReq {


    @Schema(description = "场景ID")
    String sceneId;

    List<DataAssetApi> dataAssetApiList;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class DataAssetApi {

        /**
         * 数据资产iD
         */
        String dataAssetId;

        /**
         * apiID
         */
        String apiId;

        String orderId;

        String apiKey;

        String ext;
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class ExtData {
        String dataProductName;

        String fileExtend;

        String platformId;

        String userId;

        String routeId;

        String dataProductPlatformId;

        String buyerCompanyId;

        String sellerCompanyId;

        String deliveryType;
    }

}
