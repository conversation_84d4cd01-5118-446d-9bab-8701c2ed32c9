package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ImportLicenseResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.ImportLicenseRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

@RetrofitClient(baseUrl = "${ailand.license-server.base-url}")
public interface LicenseRemoteService {

    @GET(value = "/api/v1/license/init")
    CommonResult<NodeResponse> initRoute(@Query("type") String type);

    @POST("/api/v1/license/import")
    CommonResult<ImportLicenseResponse> importLicense(@Body ImportLicenseRequest request);
}
