package com.ailpha.ailand.dataroute.endpoint.connector.remote;

import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ImportLicenseResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.ImportLicenseRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeResponse;
import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import retrofit2.http.*;

@RetrofitClient(baseUrl = "${ailand.license-server.base-url}")
public interface LicenseRemoteService {

    @GET(value = "/api/v1/license/init/{id}")
    CommonResult<NodeResponse> initRoute(@Path("id") String id);

    @POST("/api/v1/license/import")
    CommonResult<ImportLicenseResponse> importLicense(@Body ImportLicenseRequest request);

    @POST("/api/v1/license/save/root/cert")
    CommonResult<Void> saveRootCert(@Body SaveRootCertRequest request);

    @POST("/api/v1/license/save/pem")
    CommonResult<Void> savePem(@Body SavePemRequest request);

    // 校验客户端证书与根证书是否匹配
    @POST("/api/v1/license/verify/platform/cert")
    CommonResult<Void> verifyLicense(@Body VerifyPlatformCertRequest request);

    @POST("/api/v1/license/import")
    CommonResult<Void> importLicense(@Body ImportCertRequest request);

    // 解密随机数
    @POST("/api/v1/license/verify/nonce/sign")
    CommonResult<String> decryptNonce(@Body DecryptNonceRequest request);

    // 对随机数进行签名
    @POST("/api/v1/license/sign")
    CommonResult<SignResponse> encryptNonce(@Body EncryptNonceRequest request);
}
