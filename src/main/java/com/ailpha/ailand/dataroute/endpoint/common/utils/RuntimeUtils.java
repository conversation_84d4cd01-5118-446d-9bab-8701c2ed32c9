package com.ailpha.ailand.dataroute.endpoint.common.utils;

import com.dbapp.rest.exception.RestfulApiException;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: jackie.liu
 * @Date: 2021/9/3 2:41 下午
 * @Desc: 运行工具类
 */
@Slf4j
public class RuntimeUtils {
    public static void exec(String command) {
        if (!CommandSecurityFilter.validateSafety(command)) {
            throw new RestfulApiException("command 指令包含危险字符，已终止执行");
        }
        String[] cmd = new String[3];
        cmd[0] = "/bin/sh";
        cmd[1] = "-c";
        cmd[2] = command;
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(cmd);
            process.waitFor();
        } catch (Exception e) {
            log.warn("执行指令 {} 失败", command, e);
        } finally {
            if (process != null)
                process.destroy();
        }
    }

    public static List<String> execute(String command) {
        List<String> returnList = new ArrayList<>();
        String[] cmd = new String[3];
        cmd[0] = "/bin/sh";
        cmd[1] = "-c";
        cmd[2] = command;
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(cmd);
            BufferedReader input = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                returnList.add(line);
            }
            input.close();
            process.waitFor();
        } catch (Exception e) {
            log.warn("执行指令 {} 失败", command, e);
        } finally {
            if (process != null)
                process.destroy();
        }

        return returnList;
    }
}
