package com.ailpha.ailand.dataroute.endpoint.third.request;

import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/12/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MPCDataset implements Serializable {
    /**
     * 资产类型
     */
    AssetType assetType;
    /**
     * 资产ID
     */
    String assetId;
    /**
     * 资产名称
     */
    String datasetName;
    /**
     * 资产用途
     */
    List<MPCPurpose> purposes;
    /**
     * dataSchema
     */
    List<MPCDataSchemasVO> datasourceSchemas;
    /**
     * 数据集数据来源类型: FILE（文件）DATABASE（数据库）API_IMPORT（API接口导入）
     */
    String dataSourceType;
    /**
     * 原始数据文件路径
     */
    String filePath;
    /**
     * 业务领域
     */
    String businessArea;
    /**
     * 创建用户ID
     */
    String creatorId;
    /**
     * 连接器ID
     */
    String routeId;

    /**
     * mpc 那边有个上传IP
     */
    String createIp;
    /**
     * 扩展字段
     */
    Extend extendObj;

    /**
     * 扩展字段
     */
    String extend;

    public String getExtend() {
        return JSONUtil.toJsonStr(extendObj);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class Extend implements Serializable {
        /**
         * 数据接入方式：API、数据库、文件
         */
        SourceType source;
    }
}
