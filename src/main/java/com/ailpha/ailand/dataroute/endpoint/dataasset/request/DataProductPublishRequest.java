package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.APIQueryWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DeliveryMode;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.SourceType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品上架请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublishRequest {
    @Schema(description = "数据标识")
    String id;
    // 上架基本信息
    @Schema(description = "发布业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;
    // 定价信息
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价")
    String price;
//    @Schema(description = "交付方式：数据交付方式的编码（01表示文件传输，02表示数据流传输，03表示API传输）")
//    String deliveryMethod;
    @Schema(description = "交付方式：API接口、文件下载、TEE_ONLINE、TEE_OFFLINE、MPC")
    List<DeliveryMode> deliveryModes;
    // >>> 接入配置
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像")
    String dataType1;
    @Schema(description = "数据接入方式")
    SourceType accessWay;
    @Schema(description = "API查询方式")
    APIQueryWay apiQueryWay;
    @Schema(description = "MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）")
    List<MPCPurpose> mpcPurpose;

    @NotNull
    @Schema(description = "调试数据来源")
    DebugDataSourceEnum debugDataSource;
    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "临时调试文件id")
    String tempDebugFileId;
    @Schema(description = "调试数据分隔符")
    String separator;
    @Schema(description = "调试数据是否包含表头 1:是")
    Integer hasHeader;
    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;
    @Schema(description = "文件数据资产元数据")
    FileSourceMetadata fileSourceMetadata;
    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
    @Schema(description = "AiSort元数据")
    AiSortMetadata aiSortMetadata;

    @Schema(description = "绑定交易所插件")
    List<Long> exchangePluginIds;
    @Schema(description = "绑定数字证书插件")
    List<Long> certificatePluginIds;

    @Schema(description = "平台生成(MPC) 结果集(openapiId)")
    String mpcOpenAPIId;

    @Schema(description = "是否改写响应体")
    Boolean extractResponse;
}
