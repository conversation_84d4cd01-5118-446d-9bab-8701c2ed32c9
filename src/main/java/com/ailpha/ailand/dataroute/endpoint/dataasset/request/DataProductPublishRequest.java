package com.ailpha.ailand.dataroute.endpoint.dataasset.request;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.*;
import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.update.UpdateWay;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.MPCPurpose;
import com.ailpha.ailand.dataroute.endpoint.dataasset.enums.TEEPurpose;
import com.ailpha.ailand.dataroute.endpoint.third.constants.DebugDataSourceEnum;
import com.ailpha.ailand.dataroute.endpoint.third.constants.SchedulerPeriodEnum;
import com.ailpha.ailand.dataroute.endpoint.third.hub.shuhan.ServiceNodeApplyListVO;
import com.ailpha.ailand.dataroute.endpoint.third.response.DataSchemaBO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据产品上架请求")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductPublishRequest {
    @Schema(description = "数据标识")
    String id;
    // 上架基本信息
    @Schema(description = "发布业务节点")
    List<ServiceNodeApplyListVO> serviceNodes;
    // 定价信息
    @Schema(description = """
            计费方式: 01：一次性计费
            02：按次计费
            03：按时间计费""")
    String billingMethod;
    @Schema(description = """
            购买单位: 011：一次性
            021：次
            031:天
            032:月
            033:年
            041:MB
            042:GB
            043:TB""")
    String purchaseUnit;
    @Schema(description = "单价 单位为分，19.99 元表示为 1999")
    Integer price;

    @Schema(description = "交付方式说明 形式为文件 Base64 编码后的字符串")
    String deliveryInfo;

    @Schema(description = "交付方式：API, FILE_DOWNLOAD, TEE, MPC")
    List<DeliveryMode> deliveryModes;
    // >>> 接入配置
    @Schema(description = "数据类型 结构化 非结构化")
    DataType dataType;
    @Schema(description = "数据类型 结构化对应数据集；非结构化对应文本、图像、模型")
    String dataType1;
    @Schema(description = "文件后缀名 .tar.gz, .zip .csv")
    String datasetFileType = ".csv";
    @Schema(description = "数据接入方式")
    SourceType accessWay;
    @Schema(description = "API查询方式")
    APIQueryWay apiQueryWay;
    @Schema(description = "MPC用途：PRIVATE_INFORMATION_RETRIEVAL（匿踪查询）、PRIVATE_SET_INTERSECTION（隐私求交）、CIPHER_TEXT_COMPUTE（密文计算）")
    List<MPCPurpose> mpcPurpose;
    @Schema(description = "TEE用途：TEE_ONLINE, // TEE 在线计算\n" +
            "    TEE_MODEL_PREDICT, // TEE 模型预测\n" +
            "    TEE_MODEL_OPTIMIZE, // TEE 模型精调\n" +
            "    TEE_OFFLINE // TEE 离线计算")
    List<TEEPurpose> teePurpose;
    @Schema(description = "接入数据的更新方式：ONCE -> 单次 SCHEDULE -> 定时 MANUAL -> 手动")
    UpdateWay updateWay;
    @Schema(description = "更新类型", example = "0 -> 按天执行, 1 -> 按周执行, 2 -> 按月执行, 3 -> 单次执行 4 -> 按小时执行, 5 -> 多次执行")
    SchedulerPeriodEnum updateFreq;
    @Schema(description = "选中的日期，星期一到星期日为1-7，月份日期1-31")
    Integer selectDate;
    @Schema(description = "几点执行，默认0, 范围 0-23")
    @Range(min = 0, max = 23)
    Integer selectHour;

    // dataType1 为 模型 时
    @Schema(description = "是否是大模型")
    Boolean isLLM;
    @Schema(description = "模型参数")
    ModelMetadata modelMetadata;

    @NotNull
    @Schema(description = "调试数据来源")
    DebugDataSourceEnum debugDataSource;
    @Schema(description = "数据结构")
    List<DataSchemaBO> dataSchema;

    @Schema(description = "BROWSER_UPLOAD 浏览器页面上传 SERVER_FILE_PATH 服务器文件路径")
    FileSourceEnum fileSource;
    @Schema(description = "数据资产文件(文件/图像/模型)临时id，页面上传接口返回的或者服务器路径校验接口返回的")
    String fileId;

    @Schema(description = "临时调试文件id")
    String tempDebugFileId;
    @Schema(description = "调试数据分隔符")
    String separator;
    @Schema(description = "调试数据是否包含表头 1:是")
    Integer hasHeader;
    @Schema(description = "API数据资产元数据")
    APISourceMetadata apiSourceMetadata;
    @Schema(description = "文件数据资产元数据")
    FileSourceMetadata fileSourceMetadata;
    @Schema(description = "数据库数据资产元数据")
    DatabaseSourceMetadata databaseSourceMetadata;
    @Schema(description = "AiSort元数据")
    AiSortMetadata aiSortMetadata;

    @Schema(description = "绑定交易所插件")
    List<Long> exchangePluginIds;
    @Schema(description = "绑定数字证书插件")
    List<Long> certificatePluginIds;

    @Schema(description = "平台生成(MPC) 结果集(openapiId)")
    String mpcOpenAPIId;

    @Schema(description = "是否改写响应体")
    Boolean extractResponse;

}
