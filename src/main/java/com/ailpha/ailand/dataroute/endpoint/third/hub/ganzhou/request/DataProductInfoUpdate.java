package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request;

import lombok.*;
import lombok.experimental.FieldDefaults;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DataProductInfoUpdate extends DataProductInfoRegist {
    /**
     * 32
     * 产品唯一编码
     */
    String productCode;
}


