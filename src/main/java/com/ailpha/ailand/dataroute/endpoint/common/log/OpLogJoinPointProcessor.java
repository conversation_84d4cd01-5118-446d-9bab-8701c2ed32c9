package com.ailpha.ailand.dataroute.endpoint.common.log;

import cn.hutool.core.util.ObjectUtil;
import com.ailpha.ailand.dataroute.endpoint.user.domain.OpLogRecord;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserDTO;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.repository.LogRepository;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.dbapp.rest.response.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;

@Component
@RequiredArgsConstructor
@Slf4j
public class OpLogJoinPointProcessor {

    private final LogRepository logRepository;

    public Object process(ProceedingJoinPoint jp) throws Throwable {

        try {
            Object result;
            try {
                result = jp.proceed();
            } catch (Exception ce) {
                //单独记录commonException
                OPLogContext.putResult(false);
                OPLogContext.putMessage(ce.getMessage());
                OPLogContext.openSwitch();
                processOPLog(jp);
                throw ce;
            }

            //某些返回值的类单独封装了判断是否成功的方法
            if (result instanceof ApiResponse<?>) {
                OPLogContext.putResult(((ApiResponse<Object>) result).isSuccess());
            }

            //执行opLog插入操作
            processOPLog(jp);

            return result;
        } finally {
            // 清除threadLocal，防止内存泄漏
            if (OPLogContext.getLocal().get() != null) {
                OPLogContext.getLocal().remove();
            }
        }
    }

    private void processOPLog(ProceedingJoinPoint joinPoint) throws ClassNotFoundException {
        String targetName = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();
        Object[] arguments = joinPoint.getArgs();
        Class<?> targetClass = Class.forName(targetName);
        Method[] methods = targetClass.getMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class<?>[] clazzs = method.getParameterTypes();
                if (clazzs.length == arguments.length) {
                    OpLog opLog = method.getAnnotation(OpLog.class);
                    if (opLog == null) {
                        return;
                    }
                    Boolean opSwitch = OPLogContext.getSwitch();
                    boolean case1 = opLog.needSwitch() && !Boolean.TRUE.equals(opSwitch);
                    boolean case2 = !opLog.needSwitch() && Boolean.FALSE.equals(opSwitch);
                    if (case1 || case2) {
                        return;
                    }
                    OpType type = OPLogContext.getOpType();
                    OpModule module = OPLogContext.getOpModule();
                    String message = OPLogContext.getOpMessage();
                    Boolean result = OPLogContext.getOpResult();
                    String account = OPLogContext.getOpAccount();
                    //如果context没有指定值，则从注解里面拿
//                    String describe = opLog.describe().trim();
                    result = result == null || result;
                    //OpType，OpModule目前不支持在注解里面写
                    type = type == null ? InternalOpType.OTHER : type;
                    module = module == null ? InternalOpModule.OTHER : module;
                    message = StringUtils.isBlank(message) ? opLog.message() : message;
                    message = StringUtils.isBlank(message) ? type.bizName() : message;
                    message = result && StringUtils.isNotBlank(opLog.messageSuffix()) ? message + "，" + opLog.messageSuffix() : message;
                    //操作人身份解析由上层实现
                    UserDTO iUserDTO = LoginContextHolder.currentUser();
                    if (iUserDTO != null && StringUtils.isBlank(account)) {
                        account = iUserDTO.getUsername();
                    }
//                    account = "test";
                    if (StringUtils.isBlank(account)) {
                        // quick fix: bugID 190474
                        return;
                    }
                    OpLogRecord opLogRecord = new OpLogRecord();
                    opLogRecord.setUserId(LoginContextHolder.currentUser().getId());
                    opLogRecord.setMessage(this.analyzeParams(message).length() > 50 ? StringUtils.substring(this.analyzeParams(message), 0, 50) : this.analyzeParams(message));
                    opLogRecord.setOpModule(module.bizName());
                    opLogRecord.setOpType(type.bizName());
                    opLogRecord.setSuccess(result);
                    opLogRecord.setDescription("");
                    opLogRecord.setUsername(account);
                    opLogRecord.setCreateTime(new Date());
                    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
                    String ipAddress = request.getHeader("X-Forwarded-For");
                    if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                        ipAddress = request.getHeader("Proxy-Client-IP");
                    }
                    if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                        ipAddress = request.getHeader("WL-Proxy-Client-IP");
                    }
                    if (ipAddress == null || ipAddress.isEmpty() || "unknown".equalsIgnoreCase(ipAddress)) {
                        ipAddress = request.getRemoteAddr();
                    }
                    opLogRecord.setIp(ipAddress);
                    logRepository.saveAndFlush(opLogRecord);
                    if (LoginContextHolder.isLogin() && !LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN)) {
                        saveToPublicSchema(opLogRecord);
                    }
                }
            }
        }
    }

    @Async
    public void saveToPublicSchema(OpLogRecord opLogRecord) {
        logRepository.saveAndFlush(opLogRecord);
        log.info("操作日志写入主库成功");
    }

    private String analyzeParams(String str) {
        if (str == null) {
            return null;
        }
        ArrayList<String> list = new ArrayList<>();
        String[] split1 = str.split("\\{");
        for (String aSplit1 : split1) {
            if (aSplit1.contains("}")) {
                String[] split = aSplit1.split("\\}");
                list.add(split[0]);
            }
        }
        for (String params : list) {
            Object obj = OPLogContext.get(params);
            if (obj != null) {
                str = str.replace("{" + params + "}", obj.toString());
            }
        }
        return str;
    }
}
