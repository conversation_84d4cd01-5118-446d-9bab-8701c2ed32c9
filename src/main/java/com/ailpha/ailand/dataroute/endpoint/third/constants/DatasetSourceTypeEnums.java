package com.ailpha.ailand.dataroute.endpoint.third.constants;


import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * <AUTHOR>
 * @date 2020-09-21 16:41
 * @since
 */
public enum DatasetSourceTypeEnums {

    FILE(1, "文件"),

    DATABASE(3, "数据库"),

    API_IMPORT(5, "API接口导入/离线"),

    API_IMPORT_ONLINE(7, "API接口导入/在线");

    private final Integer type;

    private final String desc;

    private DatasetSourceTypeEnums(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    @JsonCreator
    public static DatasetSourceTypeEnums getItem(String name) {
        for (DatasetSourceTypeEnums item : values()) {
            if (item.name().equals(name)) {
                return item;
            }
        }
        return null;
    }

    public static DatasetSourceTypeEnums getEnum(Integer type) {
        for (DatasetSourceTypeEnums item : values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return null;
    }

}
