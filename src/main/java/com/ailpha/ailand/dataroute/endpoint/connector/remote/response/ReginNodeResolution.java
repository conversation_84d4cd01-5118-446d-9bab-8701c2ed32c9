package com.ailpha.ailand.dataroute.endpoint.connector.remote.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class ReginNodeResolution implements Serializable {

    private static final long serialVersionUID = -2329553019642919995L;
    /**
     * 执行结果状态
     * length = 2
     */
    private String changeFlag;

    /**
     * 接入连接器所属区域/行业功能节点标识
     * length = 32
     */
    private String regionNodeId;

    /**
     * 接入连接器所有者主体身份标识
     * length = 18
     */
    private String connectorOwnerId;

    /**
     * 接入连接器物理部署地址
     */
    private String connectorDeploymentAddress;

    /**
     * 接入连接器地址A
     * length = 15
     */
    private String connectorAddressA;

    /**
     * 接入连接器地址AAAA
     * length = 39
     */
    private String connectorAddressAAAA;

    /**
     * 接入连接器接入方式说明
     */
    private String connectorAccessMethod;

    /**
     * 备注
     */
    private String changeNotes;

    /**
     * 供应商名
     */
    private String supplier;

    /**
     * 产品版本
     */
    private String productVersion;

    /**
     * mac地址
     */
    private String macAddr;
}
