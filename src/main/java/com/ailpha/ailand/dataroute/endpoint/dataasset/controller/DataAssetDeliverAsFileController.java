package com.ailpha.ailand.dataroute.endpoint.dataasset.controller;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import com.ailpha.ailand.dataroute.endpoint.dataasset.service.DataAssetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "数据资产交付-文件下载")
@RequiredArgsConstructor
@RequestMapping("data-asset/download")
public class DataAssetDeliverAsFileController {

    private final DataAssetService dataAssetService;

    @GetMapping("{assetType}/{deliverId}/{companyId}/{dataResourceId}")
    @Operation(summary = "下载数据资源文件")
    public void downloadDataResourceFile(@PathVariable("assetType") AssetType assetType,
                                         @PathVariable("deliverId") String deliverId,
                                         @PathVariable("companyId") String companyId,
                                         @PathVariable("dataResourceId") String dataAssetId,
                                         @RequestParam(required = false) boolean dispatch,
                                         @RequestParam String accessKey, @RequestParam String secretKey,
                                         HttpServletResponse response) {
        dataAssetService.download(assetType, deliverId, companyId, dataAssetId, accessKey, secretKey, dispatch, response);
    }

}
