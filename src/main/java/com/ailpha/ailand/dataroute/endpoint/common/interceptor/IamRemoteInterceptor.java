package com.ailpha.ailand.dataroute.endpoint.common.interceptor;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.rest.iam.CommResult;
import com.ailpha.ailand.dataroute.endpoint.user.remote.IamRemoteService;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.ClientTokenRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.ClientTokenResponse;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.ehcache.Cache;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.IOException;

/**
 * 数由空间远程服务拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IamRemoteInterceptor extends BasePathMatchInterceptor {

    private final Cache<String, String> userCache;
    private final AiLandProperties aiLandProperties;

    /**
     * 1.动态url
     * 2.同一设置ak sk
     */
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        IamRemoteService iamRemoteService = SpringUtil.getBean(IamRemoteService.class);
        Request request = chain.request();
        String APP_TOKEN = "app_token:%s";
        String appTokenKey = String.format(APP_TOKEN, aiLandProperties.getIamServer().getClientId());
        String appToken = userCache.get(appTokenKey);
        if (StringUtils.isEmpty(appToken)) {
            CommResult<ClientTokenResponse> appTokenResult = iamRemoteService.appToken(ClientTokenRequest.builder()
                    .clientId(aiLandProperties.getIamServer().getClientId())
                    .clientSecret(aiLandProperties.getIamServer().getClientSecret())
                    .build());
            Assert.isTrue(appTokenResult.isSuccess(), "获取appToken异常:" + appTokenResult.getMsg());
            appToken = appTokenResult.getContent().getToken();
            userCache.put(appTokenKey, appToken);
            ThreadUtil.execAsync(() -> {
                try {
                    ThreadUtil.safeSleep(appTokenResult.getContent().getExpired());
                } finally {
                    if (log.isTraceEnabled())
                        log.trace("iam app token [{}] is expired,and then do remove", appTokenKey);
                    userCache.remove(appTokenKey);
                }
            });

        }
        Response response = chain.proceed(request.newBuilder().header("appToken", appToken).build());
        return response.newBuilder().build();
//        return chain.proceed(request);
    }
}
