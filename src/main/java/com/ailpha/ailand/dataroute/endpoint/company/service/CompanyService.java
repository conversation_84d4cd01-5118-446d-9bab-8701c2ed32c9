package com.ailpha.ailand.dataroute.endpoint.company.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.common.ServiceNodeMetaData;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.common.manager.AsyncManager;
import com.ailpha.ailand.dataroute.endpoint.common.rest.shuhan.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple4;
import com.ailpha.ailand.dataroute.endpoint.common.utils.ServletUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.UuidUtils;
import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.controller.GenerateCSRResponse;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.QCompany;
import com.ailpha.ailand.dataroute.endpoint.company.dto.*;
import com.ailpha.ailand.dataroute.endpoint.company.mapstruct.CompanyMapper;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyRemoteService;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyVerifyRequest;
import com.ailpha.ailand.dataroute.endpoint.company.remote.GetEnterpriseInfoRequest;
import com.ailpha.ailand.dataroute.endpoint.company.remote.UpdateCompanyLegalInfoReq;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.EnterpriseInfoResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.ImportCertRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.LicenseRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.response.ConnectorChangeResponse;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.entity.ServiceNodeInfo;
import com.ailpha.ailand.dataroute.endpoint.servicenode.remote.ServiceNodeRemoteService;
import com.ailpha.ailand.dataroute.endpoint.servicenode.repository.ServiceNodeRepository;
import com.ailpha.ailand.dataroute.endpoint.tenant.contants.TenantConstant;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.tenant.service.TenantService;
import com.ailpha.ailand.dataroute.endpoint.third.response.CompanyInfoResp;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUser;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUserRole;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.domain.UserRole;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.dataroute.endpoint.user.vo.loginSetting.LoginSetting;
import com.ailpha.ailand.utils.safe.RSAUtil;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn("FlywayConfig")
public class CompanyService {
    private final CompanyRepository companyRepository;
    private final CompanyRemoteService companyRemoteService;
    private final UserService userService;
    private final CompanyMapper companyMapper;
    private final ServiceNodeRepository serviceNodeRepository;

    // 这里默认支持最大两千企业注册
    private final Cache<String, Set<Company>> GLOBAL_COMPANY_CACHE = new LRUCache<>(2000);
    private final org.ehcache.Cache<Long, CompanyDTO> companyCache;

    @PostConstruct
    private void initCompanyLocalCache() {
        Set<Company> companySet = companyRepository.findAll().stream().filter(c -> StringUtils.isNotEmpty(c.getNodeId())).collect(Collectors.toSet());
        GLOBAL_COMPANY_CACHE.put("global_company_cache", companySet);
        if (log.isDebugEnabled())
            log.debug("全局企业缓存数据初始化成功");
    }

    /**
     * @param nodeId 连接器ID
     * @return true 是当前平台注册的企业 false：非当前平台
     */
    public Tuple2<Boolean, Company> localCompanyByNodeId(String nodeId) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        Company company = globalCompanyCache.stream().filter(c -> StringUtils.equals(nodeId, c.getNodeId())).findFirst().orElse(null);
        return new Tuple2<>(ObjectUtil.isNotNull(company), company);
    }

    public Boolean localExistCompanyByRouteId(String routeId) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        return globalCompanyCache.stream().anyMatch(c -> StringUtils.equals(routeId, c.getNodeId()));
    }

    public CompanyDTO localCompany(Long id) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        if (globalCompanyCache == null) {
            return detail(id);
        }
        return globalCompanyCache.stream().filter(c -> c.getId().equals(id)).findFirst().map(x -> {
            try {
                return detail(x.getId());
            } catch (Exception e) {
                log.warn(e.getMessage());
                return null;
            }
        }).orElse(null);
    }

    public void addCompanyCache(Company company) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache") == null ? CollectionUtil.newHashSet() : GLOBAL_COMPANY_CACHE.get("global_company_cache");
        globalCompanyCache.add(company);
        GLOBAL_COMPANY_CACHE.put("global_company_cache", globalCompanyCache);
    }

    /**
     * 更新企业缓存
     *
     * @param company 新的企业数据
     */
    public void updateCompanyCache(Company company) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        // 先移除可能存在的旧数据（基于nodeId）
        globalCompanyCache.removeIf(c -> Objects.equals(c.getId(), company.getId())
                || (StringUtils.isNotEmpty(company.getNodeId()) && StringUtils.equals(c.getNodeId(), company.getNodeId())));
        // 添加新数据
        globalCompanyCache.add(company);
        GLOBAL_COMPANY_CACHE.put("global_company_cache", globalCompanyCache);
        if (log.isDebugEnabled()) {
            log.debug("企业【{}】缓存更新成功", company.getId());
        }
    }


    @Async
    public void updatePublicSchema(Company company) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        companyRepository.saveAndFlush(company);
        if (log.isDebugEnabled())
            log.debug("企业【{}】信息 更新主库成功", company.getId());
    }

    @Async
    public void updateTenantSchema(Company company) {
        TenantContext.setCurrentTenant("tenant_" + company.getId());
        companyRepository.saveAndFlush(company);
        if (log.isDebugEnabled())
            log.debug("企业【{}】信息 更新企业租户库成功", company.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public Company apply(CompanyApplyDTO dto) {
//        if (CollectionUtil.isNotEmpty(dto.getRegisterConnectorInfo().getBaseInfo().getConnectorIpList())) {
//            List<String> connectorIpList = new ArrayList<>(List.of(detectLocalIp()));
//            connectorIpList.addAll(dto.getRegisterConnectorInfo().getBaseInfo().getConnectorIpList());
//            dto.getRegisterConnectorInfo().getBaseInfo().setConnectorIpList(connectorIpList);
//        } else {
//            dto.getRegisterConnectorInfo().getBaseInfo().setConnectorIpList(List.of(detectLocalIp()));
//        }
        CompanyVerifyRequest verifyRequest = companyMapper.toVerifyRequest(dto);
        CommonResult<Boolean> verify = companyRemoteService.companyVerify(verifyRequest);
        Assert.isTrue(verify.isSuccess(), "提交企业认证信息失败：" + verify.getMessage());

        Company company = companyRepository.findById(LoginContextHolder.currentUser().getCompany().getId())
                .orElseThrow(() -> new RestfulApiException("企业不存在"));

//        if ((ObjectUtil.equals(company.getStatus(), CompanyStatus.NOT_REVIEW) || ObjectUtil.equals(company.getStatus(), CompanyStatus.REVIEW_PASS))
//                && companyRepository.existsByCreditCodeAndDeletedFalse(dto.getCreditCode())) {
//            throw new RestfulApiException("统一社会信用代码已存在");
//        }

//        company.setStatus(CompanyStatus.NOT_REVIEW);
        companyMapper.updateCompanyFromDTO(dto, company);
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("registerConnectorInfo", dto.getRegisterConnectorInfo());
        entries.set("businessLicenseRemoteUrl", dto.getBusinessLicenseRemoteUrl());
        entries.set("authorizationLetterRemoteUrl", dto.getAuthorizationLetterRemoteUrl());
        entries.set("partyContactInfo", dto.getPartyContactInfo());

        // 添加开户行和银行账号信息到 ext 字段
        entries.set("bankName", dto.getBankName());
        entries.set("bankAccount", dto.getBankAccount());

        // 添加新增的四个字段到 ext 字段
        entries.set("fax", dto.getFax());
        entries.set("postalCode", dto.getPostalCode());
        entries.set("bankAddress", dto.getBankAddress());
        entries.set("accountName", dto.getAccountName());

        company.setUpdatedBy(LoginContextHolder.currentUser().getId());
        company.setUpdatedTime(new Date());
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        return company;
    }

    public Page<Company> list(int page, int size) {
        return companyRepository.findAll(
                PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdTime")));
    }

    public NodeDTO.HubInfo getHubInfo(Long id) {
        Company company = companyRepository.findById(id).orElseThrow(() -> new RestfulApiException("接入主体不存在"));
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "functionNodeUrl", ""));
        hubInfo.setServiceNodeUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "serviceNodeUrl", ""));
        hubInfo.setCertificate(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "crt", ""));
        hubInfo.setKeyId(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "keyId", ""));
        return hubInfo;
    }

    public NodeDTO.HubInfo getHubInfoForEntity(Long id) {
        Company company = companyRepository.findById(id).orElseThrow(() -> new RestfulApiException("接入主体不存在"));
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "functionNodeUrl", ""));
        hubInfo.setServiceNodeUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "serviceNodeUrl", ""));
        hubInfo.setCertificate(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "entityCrt", ""));
        hubInfo.setKeyId(company.getCompanyId());
        return hubInfo;
    }

    public NodeDTO.HubInfo getHubInfoForEntity() {
        Company company = companyRepository.findAll().stream().filter(c -> StringUtils.isNotEmpty(c.getNodeId())).toList().getFirst();
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "functionNodeUrl", ""));
        hubInfo.setServiceNodeUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "serviceNodeUrl", ""));
        hubInfo.setCertificate(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "entityCrt", ""));
        hubInfo.setKeyId(company.getCompanyId());
        return hubInfo;
    }

    public NodeDTO.HubInfo getHubInfo() {
        Company company = companyRepository.findAll().stream().filter(c -> StringUtils.isNotEmpty(c.getNodeId())).toList().getFirst();
        NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
        hubInfo.setUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "functionNodeUrl", ""));
        hubInfo.setServiceNodeUrl(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "serviceNodeUrl", ""));
        hubInfo.setCertificate(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "crt", ""));
        hubInfo.setKeyId(JSONUtil.getByPath(JSONUtil.parse(company.getExt()), "keyId", ""));
        return hubInfo;
    }

    public CompanyDTO detail(Long id) {
        CompanyDTO companyDTO = companyCache.get(id);
        if (companyDTO == null) {
            // 如果状态是待审核，调用远程接口查询审批状态；如果是审批拒绝或审批通过，更新本地数据库状态
            Company company = companyRepository.findByIdAndDeletedFalse(id).orElseThrow(() -> new RestfulApiException("接入主体不存在"));
            CompanyDTO detail = companyMapper.toDetail(company, enterpriseInfo(id));
            companyCache.put(company.getId(), detail);
            return detail;
        } else
            return companyDTO;

    }

    public Company getFirst() {
        return companyRepository.findAll().stream().filter(c -> StringUtils.isNotEmpty(c.getNodeId())).toList().getFirst();
    }

    private final ServiceNodeRemoteService serviceNodeRemoteService;

    public EnterpriseInfoResponse enterpriseInfo(Long id) {
        Company company = companyRepository.findByIdAndDeletedFalse(id).orElseThrow(() -> new RestfulApiException("接入主体不存在"));
        NodeDTO.HubInfo hubInfo = getHubInfo(company.getId());
        GetEnterpriseInfoRequest request = GetEnterpriseInfoRequest.builder().identityId(company.getCompanyId()).build();
        request.setHubInfo(hubInfo);
//        MetaData metaData = new MetaData();
//        metaData.setNodeId(company.getNodeId());
//        CommonResult<EnterpriseInfoResponse> enterpriseInfo = companyRemoteService.getEnterpriseInfo(request, metaData.toBase64());
//        Assert.isTrue(enterpriseInfo.isSuccess(), "法人或其他组织身份信息查询接口异常：" + enterpriseInfo.getMessage());
//        return enterpriseInfo.getData();
//        return mockEnterpriseInfo();
        ServiceNodeMetaData metaData = new ServiceNodeMetaData();
        metaData.setNodeId(company.getNodeId());
        metaData.setUrl(hubInfo.getServiceNodeUrl());
        CommonResult<EnterpriseInfoResponse> enterpriseInfo = serviceNodeRemoteService.getEnterpriseInfo(request, metaData.toBase64());
        Assert.isTrue(enterpriseInfo.isSuccess(), "法人或其他组织身份信息查询接口异常：" + enterpriseInfo.getMessage());
        return enterpriseInfo.getData();
    }

    public EnterpriseInfoResponse mockEnterpriseInfo() {
        EnterpriseInfoResponse response = new EnterpriseInfoResponse();

        // 构建基础信息
        EnterpriseInfoResponse.BaseInfo baseInfo = new EnterpriseInfoResponse.BaseInfo();
        baseInfo.setIdentityId("91330108MA22222224");
        baseInfo.setEnterpriseName("模拟企业有限公司");
        baseInfo.setEnterpriseCode("91330100MA28A1234X");
        baseInfo.setEnterpriseType("有限责任公司");
        baseInfo.setOperatingPeriodBegin(DateUtil.format(new Date(), "yyyy-MM-dd"));
        baseInfo.setOperatingPeriodEnd(DateUtil.format(new Date(), "yyyy-MM-dd"));
        baseInfo.setAuthType("电子营业执照");
        baseInfo.setAuthStatus("已认证");
        baseInfo.setLegalPerson("张三");
        baseInfo.setLegalPersonCertno("330100199001011234");
        baseInfo.setLegalPersonAuthLevel("3");
        baseInfo.setIdentityStatus("1");
        baseInfo.setAuthTime("2024-01-01 10:00:00");

        // 构建扩展信息
        EnterpriseInfoResponse.ExtendInfo extendInfo = new EnterpriseInfoResponse.ExtendInfo();
        extendInfo.setEnterpriseAddress("浙江省杭州市西湖区文三路123号");
        extendInfo.setRegAmount("1000000");
        extendInfo.setRegDate(DateUtil.format(new Date(), "yyyy-MM-dd"));
        extendInfo.setBusinessScope("软件开发；信息技术咨询服务；数据处理和存储服务");
        extendInfo.setIndustryCategory("软件和信息技术服务业");
        extendInfo.setBusinessLicense("data:image/*;base64,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");
        extendInfo.setBusinessLicenseType("png");
        extendInfo.setLegalPersonPhone("***********");
        extendInfo.setLegalPersonEmail("<EMAIL>");
        extendInfo.setLegalPersonStatus("1");

        response.setBaseInfo(baseInfo);
        response.setExtendInfo(extendInfo);

        return response;
    }

    public static void main(String[] args) {
        String s = "D:\\test.png";
        try {
            byte[] bytes = FileUtil.readBytes(s);
            String base64 = cn.hutool.core.codec.Base64.encode(bytes);
            System.out.println(base64);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void afterReviewPass(Company company) {
        syncConnectorInfo(company);
//        registerToHub(company);
        SpringUtil.getBean(UserService.class).reloadUserDetails();
        // 更新企业缓存
        updateCompanyCache(company);
    }

    public final RouterManagerRemoteService routerManagerRemoteService;

    private void syncConnectorInfo(Company company) {
        RegisterConnectorToHubRequest registerConnectorToHubRequest =
                JSONUtil.parseObj(company.getExt()).get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        RegisterConnectorToHubRequest.BaseInfo baseInfo1 = registerConnectorToHubRequest.getBaseInfo();
//        baseInfo1.setClientNo(routerService.currentNode().getPlatformId());
//        NodeDTO node = nodeService.getNode(company.getServiceNodeId());
//        baseInfo1.setCertificateNo(node.getHubInfo().getCertificateNo());
        registerConnectorToHubRequest.setBaseInfo(baseInfo1);
        CommonResult<ConnectorChangeResponse> result = routerManagerRemoteService.activate(registerConnectorToHubRequest);
        Assert.isTrue(result.isSuccess(), "接入连接器地址上报接口异常：" + result.getMessage());
        company.setNodeId(result.getData().getIdentityID());
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        RegisterConnectorToHubRequest newInfo = entries.get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        RegisterConnectorToHubRequest.BaseInfo baseInfo = newInfo.getBaseInfo();
        baseInfo.setIdentifyID(result.getData().getIdentityID());
        newInfo.setBaseInfo(baseInfo);
        companyRepository.saveAndFlush(company);
    }


    private final RouterService routerService;
    private final NodeService nodeService;

//    private void registerToHub(Company company) {
//        ActivateRouterRequest activateRouterRequest = new ActivateRouterRequest();
//        activateRouterRequest.setClientNo(LoginContextHolder.currentUser().getCompany().getNodeId());
//        activateRouterRequest.setStatus(RouteStatus.activated);
//        NodeDTO node = nodeService.getNode(company.getServiceNodeId());
//        activateRouterRequest.setClientIp(node.getConnectorInfo().getExposeUrl());
//        activateRouterRequest.setClientNo(company.getNodeId());
//        activateRouterRequest.setCompanyId(company.getId());
//        activateRouterRequest.setCompanyCode(company.getCreditCode());
//        dataHubRemoteService.activate(activateRouterRequest);
//        log.info("终端注册基础服务平台 = {}", activateRouterRequest);
//    }

    public String getSchema(String nodeId) {
        Company company = companyRepository.findByNodeId(nodeId);
        return "tenant_" + company.getId();
    }

    private final JPAQueryFactory jpaQueryFactory;

    @Deprecated
    // todo 需要确定方案
    public SuccessResponse<List<CompanyListResponse>> list(CompanyPageRequest request) {
        QCompany company = QCompany.company;
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        BooleanBuilder booleanBuilder = new BooleanBuilder();
//
//        // 基础条件：未删除
        booleanBuilder.and(company.deleted.eq(false))
                .and(userRole.roleId.eq(RoleEnums.COMPANY_ADMIN.name()).or(userRole.roleId.isNull()));
//
//        // 根据组织名称查询
//        if (StringUtils.isNotEmpty(request.getName())) {
//            booleanBuilder.and(company.organizationName.contains(request.getName()));
//        }
//
//        // 根据统一信用代码查询
//        if (StringUtils.isNotEmpty(request.getCreditCode())) {
//            booleanBuilder.and(company.creditCode.contains(request.getCreditCode()));
//        }
//
//        // 根据认证状态查询
//        if (request.getStatus() != null) {
//            booleanBuilder.and(company.status.eq(request.getStatus()));
//        }
//
//        if (request.getAccountStatus() != null) {
//            switch (request.getAccountStatus()) {
//                case disable -> booleanBuilder.and(user.deleted.isTrue().or(user.enabled.isFalse()));
//                case expired ->
//                        booleanBuilder.and(user.ext.expireDate.isNotNull().and(user.ext.expireDate.before(DateUtil.date())));
//                case register_pass -> booleanBuilder.and(user.deleted.isFalse().and(user.enabled.isTrue()));
//            }
//        }
//
//        // 根据法人查询
//        if (StringUtils.isNotEmpty(request.getLegalPerson())) {
//            booleanBuilder.and(company.legalRepresentativeName.contains(request.getLegalPerson()));
//        }
//
//        // 根据经办人查询
//        if (StringUtils.isNotEmpty(request.getOperator())) {
//            booleanBuilder.and(company.delegateName.contains(request.getOperator()));
//        }
//
//        // 根据账号查询
//        if (StringUtils.isNotEmpty(request.getAccount())) {
//            booleanBuilder.and(user.account.contains(request.getAccount()));
//        }
//
        Long total = jpaQueryFactory.select(company.countDistinct()).from(company)
                .leftJoin(user).on(user.companyId.eq(company.id))
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(booleanBuilder).fetch().getFirst();

        // 构建查询
        List<CompanyListResponse> companyListResponses = jpaQueryFactory
                .select(company, user)
                .from(company)
                .leftJoin(user).on(user.companyId.eq(company.id))
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(booleanBuilder)
                .orderBy(company.createdTime.desc())
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .fetch()
                .stream()
                .map(tuple -> {
                    CompanyListResponse response = new CompanyListResponse();
                    Company c = tuple.get(company);
                    if (c == null) {
                        throw new RestfulApiException("企业信息不能为空");
                    }
                    response.setId(c.getId());
                    if (StringUtils.isNotEmpty(c.getCompanyId()) && StringUtils.isNotEmpty(c.getNodeId())) {
                        response.setStatus(CompanyStatus.COMPLETE);
                    } else {
                        response.setStatus(CompanyStatus.INIT);
                        JSONObject entries = JSONUtil.parseObj(c.getExt());
                        response.setKeyId(entries.getStr("keyId"));
                        response.setCsr(entries.getStr("csr"));
                        return response;
                    }
                    User u = tuple.get(user);

                    response.setEnterpriseInfo(enterpriseInfo(c.getId()));
                    response.setConnectorInfo(routerService.regionNodeResolution(c.getNodeId(), getHubInfo(c.getId())));
                    // 设置企业信息 TODO 这里查缓存
//                    response.setName(c.getOrganizationName());
//                    response.setStatus(c.getStatus());
//                    response.setCreditCode(c.getCreditCode());
//                    response.setBusinessLicense(c.getBusinessLicense());
//                    response.setAgentPerson(c.getDelegateName());
//                    response.setRegistrationAddress(ObjectUtil.equals(c.getAccessType(), AccessType.LEGAL_PERSON) ? c.getRegistrationAddress() : c.getDelegateRegistrationAddress());
//                    response.setLegalPerson(c.getLegalRepresentativeName());
                    response.setAccountStatus(AccountStatus.getStatus(u));
                    // 设置关联用户信息
                    if (u != null) {
                        response.setAccount(u.getRealName());
                        response.setLoginUrl(u.getExt().getUrl());
                    }
                    return response;
                })
                .collect(Collectors.toList());
        return SuccessResponse.success(companyListResponses).total(total).build();
//        return SuccessResponse.success(null).build();
    }

    // 重置企业用户密码
    public String resetPassword(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        User user = userService.findCompanyAdminByCompanyId(company.getId());
        // 重置用户密码
        Tuple2<String, User> resetPwd = userService.resetPwd(user.getId());
        userService.updateSchema(resetPwd.second);
        return resetPwd.first;

    }

    public Company disable(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("enabled", false);
        companyRepository.saveAndFlush(company);
        // 禁用企业用户
        List<User> users = userService.disableCompanyUsers(company.getId());
        users.forEach(u -> {
            userService.updateSchema(u);
            // todo 如果后面存储在数据库中 这里会有问题 需要和上面的操作放在一起
            // 销毁用户session
            ThreadUtil.execAsync(() -> userService.destroyUserSession(u.getId(), id));
        });
        return company;
    }

    public Company enable(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("enabled", false);
        companyRepository.saveAndFlush(company);
        // 禁用企业用户
        List<User> users = userService.enableCompanyUsers(company.getId());
        users.forEach(userService::updateSchema);
        return company;
    }


    // 编辑企业信息
    @Transactional(rollbackFor = Exception.class)
    public void edit(UpdateCompanyRequest request) {
        switch (LoginContextHolder.currentUserRole()) {
            case SUPER_ADMIN -> {
                // 更新企业用户有效期
                List<User> user = userService.updateAccessEntityUserExpireDate(request.getId(), request.getAdminExpireDays());
                user.forEach(userService::updateSchema);
            }
            case COMPANY_ADMIN -> {
                Company company = companyRepository.findById(request.getId()).orElseThrow(() -> new RestfulApiException("接入主体不存在"));
                JSONObject entries = JSONUtil.parseObj(company.getExt());
                if (ObjectUtil.isNotNull(request.getRegisterConnectorInfo())) {
                    RegisterConnectorToHubRequest registerConnectorInfo = request.getRegisterConnectorInfo();
                    RegisterConnectorToHubRequest.BaseInfo baseInfo = registerConnectorInfo.getBaseInfo();
//                    baseInfo.setClientNo(routerService.currentNode().getPlatformId());
                    baseInfo.setEnterpriseName(request.getRegisterEntityInfo().getOrganizationName());
//                    baseInfo.setCertificateNo(LoginContextHolder.currentUser().getCompany().getServiceNode().getHubInfo().getCertificateNo());
                    if (CollectionUtil.isEmpty(baseInfo.getConnectorIpList())) {
//                        baseInfo.setConnectorIpList(List.of(detectLocalIp()));
                    }
                    registerConnectorInfo.setBaseInfo(baseInfo);
                    CommonResult<ConnectorChangeResponse> result = routerManagerRemoteService.activate(registerConnectorInfo);
                    Assert.isTrue(result.isSuccess(), "接入连接器地址上报接口异常：" + result.getMessage());
                    entries.set("registerConnectorInfo", registerConnectorInfo);
                    company.setExt(entries.toString());
                    companyRepository.saveAndFlush(company);
                    ThreadUtil.execAsync(() -> updatePublicSchema(company));
                    updateCompanyCache(company);
                }
                if (ObjectUtil.isNotNull(request.getRegisterEntityInfo())) {
                    // 保存开户行和银行卡信息到 ext 字段
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getBankName())) {
                        entries.set("bankName", request.getRegisterEntityInfo().getBankName());
                    }
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getBankAccount())) {
                        entries.set("bankAccount", request.getRegisterEntityInfo().getBankAccount());
                    }

                    // 添加对传真、邮编、银行地址和户名四个字段的处理
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getFax())) {
                        entries.set("fax", request.getRegisterEntityInfo().getFax());
                    }
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getPostalCode())) {
                        entries.set("postalCode", request.getRegisterEntityInfo().getPostalCode());
                    }
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getBankAddress())) {
                        entries.set("bankAddress", request.getRegisterEntityInfo().getBankAddress());
                    }
                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getAccountName())) {
                        entries.set("accountName", request.getRegisterEntityInfo().getAccountName());
                    }

                    if (StringUtils.isNotEmpty(request.getRegisterEntityInfo().getPartyContactInfo()))
                        entries.set("partyContactInfo", request.getRegisterEntityInfo().getPartyContactInfo());

                    company.setExt(entries.toString());
                    UpdateCompanyLegalInfoReq updateReq = companyMapper.toUpdateReq(request.getRegisterEntityInfo());
                    CommonResult<String> result = companyRemoteService.edit(updateReq);
                    Assert.isTrue(result.isSuccess(), "调用基础支持平台【编辑接入主体认证信息】接口异常：" + result.getMessage());
//                    companyMapper.convertToCompany(request.getRegisterEntityInfo(), company);
//                    companyRepository.saveAndFlush(company);
//                    ThreadUtil.execAsync(() -> updatePublicSchema(company));
//                    updateCompanyCache(company);
                    //TODO 编辑过后需要把缓存失效
                }
                userService.reloadUserDetails();
            }
            default -> throw new RestfulApiException("无权限");
        }
    }

    @Deprecated
    public Company add(AddCompanyRequest request) {
        // todo 校验社会统一信用代码是否存在
        // 初始化一个空的企业，并且
        Company company = new Company();
        company.setId(System.currentTimeMillis());
        company.setNodeId("");
//        company.setStatus(CompanyStatus.INIT);
//        company.setAccessType(AccessType.LEGAL_PERSON);
//        company.setOrganizationName(request.getOrganizationName());
//        company.setCreditCode(request.getCreditCode());
//        company.setLegalRepresentativeName("");
//        company.setLegalRepresentativeIdType("");
//        company.setLegalRepresentativeIdNumber("");
//        company.setIsPermanent(true);
//        company.setValidityEndDate(null);
        company.setCompanyId("");
//        company.setServiceNodeId(request.getServiceNodeId());
        company.setCreatedTime(new Date());
        JSONObject entries = new JSONObject();
        entries.set("enabled", true);
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        addCompanyCache(company);
        String loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "tenant_" + company.getId() + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        FileUtil.writeUtf8String(JSONUtil.toJsonStr(new LoginSetting()), loginSettingKey);
        return company;
    }

    public boolean initCompanySchema(Long companyId) {
        Company company = companyRepository.findById(companyId).orElseThrow(() -> new RestfulApiException("企业不存在"));
        companyRepository.saveAndFlush(company);
        return true;
    }

    @Transactional
    // 在 CompanyService 接口中添加新方法
    public Tuple2<CompanyDTO, User> getCompanyDetail(Long id) {
        if (ObjectUtil.equals(LoginContextHolder.currentUserRole(), RoleEnums.COMPANY_ADMIN)) {
            id = LoginContextHolder.currentUser().getCompany().getId();
        }
        User user = userService.findCompanyAdminByCompanyId(id);
        return new Tuple2<>(detail(id), user);
    }

    @Async
    public void updateCompanySchema(Company company) {
        TenantContext.setCurrentTenant("tenant_" + company.getId());
        companyRepository.saveAndFlush(company);
    }

    private final FilesStorageServiceImpl filesStorageService;
    @Value("${ailand.endpoint.ip}")
    private String endpointIpPort;

    public Tuple2<String, String> uploadFile(MultipartFile file) {
        String fileName;
        okhttp3.RequestBody requestBody;
        try {
            fileName = URLEncoder.encode(Objects.requireNonNull(file.getOriginalFilename()), StandardCharsets.UTF_8);
            requestBody = okhttp3.RequestBody.create(MediaType.parse("multipart/form-data"), file.getBytes());
        } catch (Exception e) {
            log.error("解析文件异常 ", e);
            throw new RestfulApiException("解析文件异常：" + e.getMessage());
        }

        MultipartBody.Part part = MultipartBody.Part.createFormData("file", fileName, requestBody);
        CommonResult<String> uploadFile = companyRemoteService.uploadFile(part);
        Assert.isTrue(uploadFile.isSuccess(), "上传文件异常：" + uploadFile.getMessage());
        String originalFilename = file.getOriginalFilename();
        assert originalFilename != null;
        String newFileName = String.format("%s%s%s", "bl_", System.currentTimeMillis(), originalFilename.contains(".") ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".png");
        Path path = Paths.get(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "company", newFileName);
        filesStorageService.save(file, path);

        return new Tuple2<>(newFileName, uploadFile.getData());
    }

    private final AiLandProperties aiLandProperties;

    /**
     * 注册接入主体管理员账号
     *
     * @return 是否允许注册接入主体
     */
    public Boolean registerUser() {
        boolean share = ObjectUtil.equals(aiLandProperties.getDeploy().getMode(), DeployMode.share);
        long count = companyRepository.count();
        return share || count == 0;
    }

    public Company getCompanyByNodeId(String nodeId) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        return companyRepository.findByNodeId(nodeId);
    }

    public CompanyInfoResp getCompanyInfoByNodeId(String nodeId) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        Company company = companyRepository.findByNodeId(nodeId);
        if (company == null) {
            return null;
        }
        return AsyncManager.getInstance().executeFuture(() -> {
            TenantContext.setCurrentTenant("tenant_" + company.getId());
            EnterpriseInfoResponse enterpriseInfoResponse = enterpriseInfo(company.getId());
            CompanyInfoResp companyInfoResp = companyMapper.toCompanyInfoResp(enterpriseInfoResponse);
            companyInfoResp.setId(company.getId());
            return companyInfoResp;
        });
    }

    public void preview(String type, Long id) throws IOException {
        String filename;
        HttpServletResponse response = ServletUtils.getResponse();
        String contentType = "application/octet-stream";
        switch (type) {
            case "authorizationLetter" -> {
                // 兼容一下历史数据
                if (id != null) {
                    Company company = companyRepository.findById(id).orElseThrow(() -> new RestfulApiException("企业不存在"));
                    // todo 这里后续需要改成远端服务器地址
                    filename = "company.getAuthorizationLetter()";
                } else
                    filename = LoginContextHolder.currentUser().getCompany().getAuthorizationLetter();
                if (StringUtils.contains(filename, "http")) {
                    filename = StringUtils.substringAfterLast(filename, "/");
                }
            }
            case "businessLicense" -> {
                contentType = "image/png";
                if (id != null)
                    // todo 这里后续需要改成远端服务器地址
                    filename = "companyRepository.findById(id).orElseThrow(() -> new RestfulApiException(企业不存在)).getBusinessLicense()";
                else
                    filename = LoginContextHolder.currentUser().getCompany().getBusinessLicense();
                if (StringUtils.contains(filename, "http")) {
                    filename = StringUtils.substringAfterLast(filename, "/");
                }
            }
            default -> throw new RestfulApiException("暂不支持该类型");
        }
        if (StringUtils.isEmpty(filename))
            return;
        Path attachFilePath = Paths.get(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "company", filename);
        try (InputStream inputStream = Files.newInputStream(attachFilePath)) {
            ServletOutputStream outputStream = response.getOutputStream();
            response.reset();
            response.setContentType(contentType);
            response.setCharacterEncoding("utf-8");
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
            byte[] bytes = new byte[2048];
            int len;
            while ((len = inputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            outputStream.close();
        }
    }

    private final TenantService tenantService;
    private final LicenseRemoteService licenseRemoteService;

    public GenerateCSRResponse generateCSR() {
        String id = UuidUtils.uuid32();
        String certificateCSR = licenseRemoteService.initRoute(id).getData().getCertificateCSR();
        Company company = new Company();
        company.setId(System.currentTimeMillis());
        JSONObject entries = new JSONObject();
        entries.set("keyId", id);
        entries.set("csr", certificateCSR);
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        return GenerateCSRResponse.builder().csr(certificateCSR).id(id).companyId(company.getId()).build();
    }


    //    @Transactional(rollbackFor = Exception.class)
    public ConnectorRegisterResponse companyInit(IdentityFileDTO identityInfo) {
        if (StringUtils.equals(identityInfo.getType(), "file")) {
            String filepath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + identityInfo.getFileId() + FileUtil.FILE_SEPARATOR + "crt";
            File[] crts = FileUtil.ls(filepath);
            if (crts.length > 0) {
                identityInfo.setCrt(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "crt", ""));
                identityInfo.setNodeId(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "nodeId", ""));
                identityInfo.setEntityId(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "entityId", ""));
                identityInfo.setFunctionNodeUrl(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "functionNodeUrl", ""));
                identityInfo.setServiceNodeUrl(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "serviceNodeUrl", ""));
                identityInfo.setEntityCrt(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "entityCrt", ""));
                identityInfo.setEntityPrivateKey(JSONUtil.getByPath(JSONUtil.parse(FileUtil.readUtf8String(crts[0])), "entityPrivateKey", ""));
            }
        }
        // 检查连接器是否重复注册
        boolean existsed = companyRepository.existsByNodeId(identityInfo.getNodeId());
        Assert.isTrue(!existsed, "连接器已注册，请勿重复注册");
        ImportCertRequest.ConnectorIdentity connectorIdentity = new ImportCertRequest.ConnectorIdentity();
        connectorIdentity.setKeyId(identityInfo.getId());
        connectorIdentity.setRouteCertificate(identityInfo.getCrt());
        ImportCertRequest.SubjectIdentity subjectIdentity = new ImportCertRequest.SubjectIdentity();
        subjectIdentity.setCertificate(identityInfo.getEntityCrt());
        subjectIdentity.setPrivateKey(identityInfo.getEntityPrivateKey());
        subjectIdentity.setKeyId(identityInfo.getEntityId());
        com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult<Void> verifyLicense =
                licenseRemoteService.importLicense(ImportCertRequest.builder().connectorIdentity(connectorIdentity).subjectIdentity(subjectIdentity).build());
        Assert.isTrue(verifyLicense.isSuccess(), "校验crt文件异常：" + verifyLicense.getMessage());
        Company company = companyRepository.findById(identityInfo.getCompanyId()).orElseThrow(() -> new RestfulApiException("接入主体ID错误"));
        company.setNodeId(identityInfo.getNodeId());
        company.setCompanyId(identityInfo.getEntityId());
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("crt", identityInfo.getCrt());
        String loginUrl = String.format("%s/data-route/#/login?schema=%s", endpointIpPort, TenantConstant.TENANT_SCHEMA_PREFIX + company.getId());
        entries.set("loginUrl", loginUrl);
        entries.set("functionNodeUrl", identityInfo.getFunctionNodeUrl());
        entries.set("serviceNodeUrl", identityInfo.getServiceNodeUrl());
        entries.set("keyId", identityInfo.getId());
        entries.set("entityCrt", identityInfo.getEntityCrt());
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        addCompanyCache(company);
        String loginSettingKey = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + "loginSetting" + FileUtil.FILE_SEPARATOR + "tenant_" + company.getId() + FileUtil.FILE_SEPARATOR + "loginSetting.json";
        FileUtil.writeUtf8String(JSONUtil.toJsonStr(new LoginSetting()), loginSettingKey);

        tenantService.addTenantSchema("tenant_" + company.getId());
        updateCompanySchema(company);
        // 初始化一个管理员用户
        Tuple2<String, String> companyUserInit = companyUserInit(company);
        // 绑定业务节点
        ThreadUtil.execAsync(() -> {
            serviceNodeRepository.saveAndFlush(ServiceNodeInfo.builder().id(UUID.randomUUID().toString()).serviceNodeId(identityInfo.getServiceNodeId()).apiUrl(identityInfo.getServiceNodeUrl()).build());
        });

        return ConnectorRegisterResponse.builder().loginUrl(loginUrl).username(companyUserInit.second).password(companyUserInit.first).build();
    }

    private final KeyPair keyPair;

    public Tuple2<String, String> companyUserInit(Company company) {
        EnterpriseInfoResponse enterpriseInfo = enterpriseInfo(company.getId());
        // 2. 构建用户请求
        AddUserRequest addUserRequest = new AddUserRequest();
        EnterpriseInfoResponse.BaseInfo baseInfo = enterpriseInfo.getBaseInfo();
        addUserRequest.setAccount(baseInfo.getEnterpriseCode());
        addUserRequest.setName(baseInfo.getEnterpriseName());
        addUserRequest.setMobile(enterpriseInfo.getExtendInfo().getLegalPersonPhone());
        addUserRequest.setEmail("");
        try {
            addUserRequest.setPassword(Base64.getEncoder().encodeToString(RSAUtil.encryptLargeDataByPublicKey(("2wsxVFR".getBytes(StandardCharsets.UTF_8)), (RSAPublicKey) keyPair.getPublic())));
        } catch (Exception e) {
            log.error("初始化密码异常 ", e);
            throw new RestfulApiException("初始化企业管理员密码异常");
        }

        // 3. 设置企业相关信息
        addUserRequest.setLocalCompanyId(String.valueOf(company.getId()));
        addUserRequest.setLoginUrl(String.format("%s/data-route/#/login?schema=%s", endpointIpPort, TenantConstant.TENANT_SCHEMA_PREFIX + company.getId()));
        addUserRequest.setExpireDate(null);
        addUserRequest.setCompanyAdmin(true);
        addUserRequest.setTargetCompanyId(company.getId());
        addUserRequest.setEntityId(company.getCompanyId());
        addUserRequest.setNodeId(company.getNodeId());

        Tuple4<String, User, UserRole, Boolean> addUser = userService.addUser(addUserRequest);

        ThreadUtil.execAsync(() -> {
            userService.updateSchema(addUser.second, addUser.third);
            SpringUtil.getBean(CompanyService.class).updateTenantSchema(company);
        });

        return new Tuple2<>(addUser.first, addUser.second.getAccount());
    }

    public String uploadCrt(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName) || fileName.contains("../")) {
            throw new RestfulApiException("非法的文件名称");
        }
        String fileId = String.valueOf(System.currentTimeMillis());
        String filepath = aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + fileId + FileUtil.FILE_SEPARATOR + "crt" + FileUtil.FILE_SEPARATOR + fileName;
        File newFile = FileUtil.touch(filepath);
        try {
            file.transferTo(newFile);
        } catch (IOException e) {
            log.error("保存license文件异常 ", e);
            throw new RestfulApiException("保存crt文件异常");
        }
        return fileId;
    }
}