package com.ailpha.ailand.dataroute.endpoint.company.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.biz.api.constants.Constants;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.enums.DeployMode;
import com.ailpha.ailand.dataroute.endpoint.common.rest.ailand.CommonResult;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import com.ailpha.ailand.dataroute.endpoint.common.tuple.Tuple2;
import com.ailpha.ailand.dataroute.endpoint.company.AccountStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.company.domain.Company;
import com.ailpha.ailand.dataroute.endpoint.company.domain.CompanyStatus;
import com.ailpha.ailand.dataroute.endpoint.company.domain.QCompany;
import com.ailpha.ailand.dataroute.endpoint.company.dto.*;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.LegalIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.OperatorIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.company.dto.ganzhou.PersonIdentityQuery;
import com.ailpha.ailand.dataroute.endpoint.company.mapstruct.CompanyMapper;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyRemoteService;
import com.ailpha.ailand.dataroute.endpoint.company.remote.CompanyVerifyRequest;
import com.ailpha.ailand.dataroute.endpoint.company.remote.GetCompanyVerifyResultResponse;
import com.ailpha.ailand.dataroute.endpoint.company.repository.CompanyRepository;
import com.ailpha.ailand.dataroute.endpoint.connector.RouteStatus;
import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DataHubRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.QueryIdentityRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.RouterManagerRemoteService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.ActivateRouterRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.request.RegisterConnectorToHubRequest;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.node.service.NodeService;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.tenant.resolver.TenantIdentifierResolver;
import com.ailpha.ailand.dataroute.endpoint.tenant.service.TenantService;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUser;
import com.ailpha.ailand.dataroute.endpoint.user.domain.QUserRole;
import com.ailpha.ailand.dataroute.endpoint.user.domain.User;
import com.ailpha.ailand.dataroute.endpoint.user.enums.RoleEnums;
import com.ailpha.ailand.dataroute.endpoint.user.repository.UserRepository;
import com.ailpha.ailand.dataroute.endpoint.user.security.LoginContextHolder;
import com.ailpha.ailand.dataroute.endpoint.user.service.UserService;
import com.ailpha.ailand.invoke.api.tuple.Tuple3;
import com.dbapp.rest.exception.RestfulApiException;
import com.dbapp.rest.response.SuccessResponse;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn("FlywayConfig")
public class CompanyService {
    private final CompanyRepository companyRepository;
    private final CompanyRemoteService companyRemoteService;
    private final UserService userService;
    private final CompanyMapper companyMapper;

    // 这里默认支持最大两千企业注册
    private final Cache<String, Set<Company>> GLOBAL_COMPANY_CACHE = new LRUCache<>(2000);

    @PostConstruct
    private void initCompanyLocalCache() {
        Set<Company> companySet = companyRepository.findAll().stream().filter(c -> StringUtils.isNotEmpty(c.getNodeId())).collect(Collectors.toSet());
        GLOBAL_COMPANY_CACHE.put("global_company_cache", companySet);
        if (log.isDebugEnabled())
            log.debug("全局企业缓存数据初始化成功");
    }

    /**
     * @param nodeId 连接器ID
     * @return true 是当前平台注册的企业 false：非当前平台
     */
    public Tuple2<Boolean, Company> localCompany(String nodeId) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        Company company = globalCompanyCache.stream().filter(c -> StringUtils.equals(nodeId, c.getNodeId())).findFirst().orElse(null);
        return new Tuple2<>(ObjectUtil.isNotNull(company), company);
    }

    public void addCompanyCache(Company company) {
        Set<Company> globalCompanyCache = GLOBAL_COMPANY_CACHE.get("global_company_cache");
        globalCompanyCache.add(company);
        GLOBAL_COMPANY_CACHE.put("global_company_cache", globalCompanyCache);
    }


    @Async
    public void updatePublicSchema(Company company) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        companyRepository.saveAndFlush(company);
        if (log.isDebugEnabled())
            log.debug("企业【{}】信息 更新主库成功", company.getOrganizationName());
    }

    @Async
    public void updateTenantSchema(Company company) {
        TenantContext.setCurrentTenant("tenant_" + company.getId());
        companyRepository.saveAndFlush(company);
        if (log.isDebugEnabled())
            log.debug("企业【{}】信息 更新企业租户库成功", company.getOrganizationName());
    }

    @Transactional(rollbackFor = Exception.class)
    public Company apply(CompanyApplyDTO dto) {
        CompanyVerifyRequest verifyRequest = companyMapper.toVerifyRequest(dto);
        CommonResult<Boolean> verify = companyRemoteService.companyVerify(verifyRequest);
        Assert.isTrue(verify.isSuccess(), "提交企业认证信息失败：" + verify.getMsg());

        Company company = companyRepository.findById(LoginContextHolder.currentUser().getCompany().getId())
                .orElseThrow(() -> new RestfulApiException("企业不存在"));

//        if ((ObjectUtil.equals(company.getStatus(), CompanyStatus.NOT_REVIEW) || ObjectUtil.equals(company.getStatus(), CompanyStatus.REVIEW_PASS))
//                && companyRepository.existsByCreditCodeAndDeletedFalse(dto.getCreditCode())) {
//            throw new RestfulApiException("统一社会信用代码已存在");
//        }

        company.setStatus(CompanyStatus.NOT_REVIEW);
        companyMapper.updateCompanyFromDTO(dto, company);
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("registerConnectorInfo", dto.getRegisterConnectorInfo());
        entries.set("businessLicenseRemoteUrl", dto.getBusinessLicenseRemoteUrl());
        entries.set("authorizationLetterRemoteUrl", dto.getAuthorizationLetterRemoteUrl());
        company.setUpdatedBy(LoginContextHolder.currentUser().getId());
        company.setUpdatedTime(new Date());
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        return company;
    }

    public NodeDTO getNodeInfo(Long id) {
        Company company = companyRepository.findById(id).orElseThrow(() -> new RestfulApiException("企业不存在"));
        return nodeService.getNode(company.getServiceNodeId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void review(CompanyReviewDTO dto) {
        Company company = companyRepository.findById(dto.getId())
                .orElseThrow(() -> new RestfulApiException("企业不存在"));

        if (company.getStatus() != CompanyStatus.NOT_REVIEW) {
            throw new RestfulApiException("企业状态不正确");
        }

        company.setStatus(dto.getStatus());
        company.setReviewUserId(LoginContextHolder.currentUser().getId());
        company.setReviewTime(new Date());
        company.setReviewRemarks(dto.getRemarks());
        company.setRefuseReason(dto.getRefuseReason());

        companyRepository.save(company);
    }

    public Page<Company> list(int page, int size) {
        return companyRepository.findAll(
                PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createdTime")));
    }

    @Transactional
    public Tuple2<Company, Boolean> detail(Long id) {
        // 如果状态是待审核，调用远程接口查询审批状态；如果是审批拒绝或审批通过，更新本地数据库状态
        // 审核通过 更新nodeId
        boolean updateCompany = false;
        Company company = companyRepository.findByIdAndDeletedFalse(id).orElseThrow(() -> new RestfulApiException("企业不存在"));
        if (ObjectUtil.equals(company.getStatus(), CompanyStatus.NOT_REVIEW)) {
            if (!LoginContextHolder.isLogin() || LoginContextHolder.currentUserRole().contains(RoleEnums.SUPER_ADMIN))
                return new Tuple2<>(company, updateCompany);
            NodeDTO node = nodeService.getNode(company.getServiceNodeId());
            MDC.put("hubInfo", JSONUtil.toJsonStr(node.getHubInfo()));
            CommonResult<GetCompanyVerifyResultResponse> companyVerifyInfo = companyRemoteService.getCompanyVerifyInfo(company.getCreditCode());
            Assert.isTrue(companyVerifyInfo.isSuccess(), "查询企业认证结果异常：" + companyVerifyInfo.getMsg());
            if (StringUtils.equals(companyVerifyInfo.getData().getVerifyStatus(), "认证通过")) {
                company.setReviewTime(new Date());
                company.setStatus(CompanyStatus.REVIEW_PASS);
                company.setCompanyId(companyVerifyInfo.getData().getThirdBusinessId());
                afterReviewPass(company);
                updateCompany = true;
            } else if (StringUtils.equals(companyVerifyInfo.getData().getVerifyStatus(), "认证不通过")) {
                company.setStatus(CompanyStatus.REVIEW_REFUSED);
                company.setRefuseReason(companyVerifyInfo.getData().getVerifyResultDesc());
                company.setReviewRemarks("");
                company.setReviewTime(new Date());
                updateCompany = true;
            }
            companyRepository.saveAndFlush(company);
        }
        return new Tuple2<>(company, updateCompany);
    }

    private void afterReviewPass(Company company) {
        syncConnectorInfo(company);
        registerToHub(company);
        SpringUtil.getBean(UserService.class).reloadUserDetails();
    }

    public final RouterManagerRemoteService routerManagerRemoteService;

    private void syncConnectorInfo(Company company) {
        RegisterConnectorToHubRequest registerConnectorToHubRequest =
                JSONUtil.parseObj(company.getExt()).get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        RegisterConnectorToHubRequest.BaseInfo baseInfo1 = registerConnectorToHubRequest.getBaseInfo();
        baseInfo1.setClientNo(routerService.currentNode().getPlatformId());
        NodeDTO node = nodeService.getNode(company.getServiceNodeId());
        baseInfo1.setCertificateNo(node.getHubInfo().getCertificateNo());
        registerConnectorToHubRequest.setBaseInfo(baseInfo1);
//        CommonResult<ConnectorChangeResponse> result = routerManagerRemoteService.activate(registerConnectorToHubRequest);
//        Assert.isTrue(result.isSuccess(), "接入连接器地址上报接口异常：" + result.getMsg());
//        company.setNodeId(result.getData().getIdentityID());
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        RegisterConnectorToHubRequest newInfo = entries.get("registerConnectorInfo", RegisterConnectorToHubRequest.class);
        RegisterConnectorToHubRequest.BaseInfo baseInfo = newInfo.getBaseInfo();
//        baseInfo.setIdentifyID(result.getData().getIdentityID());
        newInfo.setBaseInfo(baseInfo);
        companyRepository.saveAndFlush(company);
    }

    public CompanyDTO getFirstCompany() {
        return companyMapper.toDTO(companyRepository.findAll().getFirst());
    }

    private final DataHubRemoteService dataHubRemoteService;
    private final RouterService routerService;
    private final NodeService nodeService;

    private void registerToHub(Company company) {
        ActivateRouterRequest activateRouterRequest = new ActivateRouterRequest();
        activateRouterRequest.setClientNo(LoginContextHolder.currentUser().getCompany().getNodeId());
        activateRouterRequest.setStatus(RouteStatus.activated);
        NodeDTO node = nodeService.getNode(company.getServiceNodeId());
        activateRouterRequest.setClientIp(node.getConnectorInfo().getExposeUrl());
        activateRouterRequest.setClientNo(company.getNodeId());
        activateRouterRequest.setCompanyId(Long.valueOf(company.getCompanyId()));
        activateRouterRequest.setCompanyCode(company.getCreditCode());
        dataHubRemoteService.activate(activateRouterRequest);
        log.info("终端注册基础服务平台 = {}", activateRouterRequest);
    }

    public String getSchema(String nodeId) {
        Company company = companyRepository.findByNodeId(nodeId);
        return "tenant_" + company.getId();
    }

    private final JPAQueryFactory jpaQueryFactory;

    public SuccessResponse<List<CompanyListResponse>> list(CompanyPageRequest request) {
        QCompany company = QCompany.company;
        QUser user = QUser.user;
        QUserRole userRole = QUserRole.userRole;
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        // 基础条件：未删除
        booleanBuilder.and(company.deleted.eq(false)).and(userRole.roleId.eq(RoleEnums.COMPANY_ADMIN.name()));

        // 根据组织名称查询
        if (StringUtils.isNotEmpty(request.getName())) {
            booleanBuilder.and(company.organizationName.contains(request.getName()));
        }

        // 根据统一信用代码查询
        if (StringUtils.isNotEmpty(request.getCreditCode())) {
            booleanBuilder.and(company.creditCode.contains(request.getCreditCode()));
        }

        // 根据认证状态查询
        if (request.getStatus() != null) {
            booleanBuilder.and(company.status.eq(request.getStatus()));
        }

        if (request.getAccountStatus() != null) {
            switch (request.getAccountStatus()) {
                case disable -> booleanBuilder.and(user.deleted.isTrue().or(user.enabled.isFalse()));
                case expired ->
                        booleanBuilder.and(user.ext.expireDate.isNotNull().and(user.ext.expireDate.before(DateUtil.date())));
                case register_pass -> booleanBuilder.and(user.deleted.isFalse().and(user.enabled.isTrue()));
            }
        }

        // 根据法人查询
        if (StringUtils.isNotEmpty(request.getLegalPerson())) {
            booleanBuilder.and(company.legalRepresentativeName.contains(request.getLegalPerson()));
        }

        // 根据经办人查询
        if (StringUtils.isNotEmpty(request.getOperator())) {
            booleanBuilder.and(company.delegateName.contains(request.getOperator()));
        }

        // 根据账号查询
        if (StringUtils.isNotEmpty(request.getAccount())) {
            booleanBuilder.and(user.account.contains(request.getAccount()));
        }

        Long total = jpaQueryFactory.select(company.countDistinct()).from(company)
                .leftJoin(user).on(user.companyId.eq(company.id))
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(booleanBuilder).fetch().getFirst();

        // 构建查询
        List<CompanyListResponse> companyListResponses = jpaQueryFactory
                .select(company, user)
                .from(company)
                .leftJoin(user).on(user.companyId.eq(company.id))
                .leftJoin(userRole).on(user.id.eq(userRole.userId))
                .where(booleanBuilder)
                .orderBy(company.createdTime.desc())
                .offset((request.getNum() - 1) * request.getSize())
                .limit(request.getSize())
                .fetch()
                .stream()
                .map(tuple -> {
                    Company c = tuple.get(company);
                    if (c == null) {
                        throw new RestfulApiException("企业信息不能为空");
                    }
                    User u = tuple.get(user);
                    CompanyListResponse response = new CompanyListResponse();
                    // 设置企业信息
                    response.setName(c.getOrganizationName());
                    response.setStatus(c.getStatus());
                    response.setCreditCode(c.getCreditCode());
                    response.setId(c.getId());
                    response.setBusinessLicense(c.getBusinessLicense());
                    response.setAgentPerson(c.getDelegateName());
                    response.setRegistrationAddress(ObjectUtil.equals(c.getAccessType(), AccessType.LEGAL_PERSON) ? c.getRegistrationAddress() : c.getDelegateRegistrationAddress());
                    response.setLegalPerson(c.getLegalRepresentativeName());
                    response.setAccountStatus(AccountStatus.getStatus(u));
                    // 设置关联用户信息
                    if (u != null) {
                        response.setAccount(u.getRealName());
                        response.setLoginUrl(u.getExt().getUrl());
                    }
                    return response;
                })
                .collect(Collectors.toList());
        return SuccessResponse.success(companyListResponses).total(total).build();
    }

    // 重置企业用户密码
    public String resetPassword(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        User user = userService.findCompanyAdminByCompanyId(company.getId());
        // 重置用户密码
        Tuple2<String, User> resetPwd = userService.resetPwd(user.getId());
        userService.updateSchema(resetPwd.second);
        return resetPwd.first;

    }

    public Company disable(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("enabled", false);
        companyRepository.saveAndFlush(company);
        // 禁用企业用户
        List<User> users = userService.disableCompanyUsers(company.getId());
        users.forEach(u -> {
            userService.updateSchema(u);
            // todo 如果后面存储在数据库中 这里会有问题 需要和上面的操作放在一起
            // 销毁用户session
            userService.destroyUserSession(u.getId(), id);
        });
        return company;
    }

    public Company enable(Long id) {
        Company company = companyRepository.findById(id)
                .orElseThrow(() -> new RestfulApiException("企业不存在"));
        JSONObject entries = JSONUtil.parseObj(company.getExt());
        entries.set("enabled", false);
        companyRepository.saveAndFlush(company);
        // 禁用企业用户
        List<User> users = userService.enableCompanyUsers(company.getId());
        users.forEach(userService::updateSchema);
        return company;
    }


    // 编辑企业信息
    public void edit(UpdateCompanyRequest request) {
        // 更新企业用户有效期
        List<User> user = userService.updateCompanyAdminExpireDays(request.getId(), request.getAdminExpireDays());
        user.forEach(userService::updateSchema);
    }

    private final TenantService tenantService;

    public void initCompany(Long serviceNodeId, String nodeId) {
        Company company = new Company();
        company.setId(System.currentTimeMillis());
        company.setNodeId(nodeId);
        company.setAccessType(AccessType.LEGAL_PERSON);
        company.setStatus(CompanyStatus.REVIEW_PASS);
        company.setCompanyId("");
        company.setIsPermanent(true);
        company.setValidityEndDate(null);
        company.setServiceNodeId(serviceNodeId);
        companyRepository.saveAndFlush(company);
        addCompanyCache(company);
        tenantService.addTenantSchema("tenant_" + company.getId());
        ThreadUtil.execAsync(() -> updateCompanySchema(company));
    }

    public Company add(AddCompanyRequest request) {
        // todo 校验社会统一信用代码是否存在
        // 初始化一个空的企业，并且
        Company company = new Company();
        company.setId(System.currentTimeMillis());
        company.setNodeId("");
        company.setStatus(CompanyStatus.INIT);
        company.setAccessType(AccessType.LEGAL_PERSON);
        company.setOrganizationName(request.getOrganizationName());
        company.setCreditCode(request.getCreditCode());
        company.setLegalRepresentativeName("");
        company.setLegalRepresentativeIdType("");
        company.setLegalRepresentativeIdNumber("");
        company.setIsPermanent(true);
        company.setValidityEndDate(null);
        company.setCompanyId("");
        company.setServiceNodeId(request.getServiceNodeId());
        company.setCreatedTime(new Date());
        JSONObject entries = new JSONObject();
        entries.set("enabled", true);
        company.setExt(entries.toString());
        companyRepository.saveAndFlush(company);
        addCompanyCache(company);
        return company;
    }

    public boolean initCompanySchema(Long companyId) {
        Company company = companyRepository.findById(companyId).orElseThrow(() -> new RestfulApiException("企业不存在"));
        companyRepository.saveAndFlush(company);
        return true;
    }

    private final UserRepository userRepository;

    @Transactional
    // 在 CompanyService 接口中添加新方法
    public Tuple3<Company, User, Boolean> getCompanyDetail(Long id) {
        if (LoginContextHolder.currentUserRole().contains(RoleEnums.COMPANY_ADMIN)) {
            id = LoginContextHolder.currentUser().getCompany().getId();
        }
        Tuple2<Company, Boolean> detail = detail(id);
        User user = userService.findCompanyAdminByCompanyId(id);
        return new Tuple3<>(detail.first, user, detail.second);
    }

    @Async
    public void updateCompanySchema(Company company) {
        TenantContext.setCurrentTenant("tenant_" + company.getId());
        companyRepository.saveAndFlush(company);
        log.info("企业租户库【{}】更新成功", "tenant_" + company.getId());
    }

    private final FilesStorageServiceImpl filesStorageService;
    @Value("${ailand.endpoint.ip}")
    private String endpointIpPort;

    public Tuple2<String, String> uploadFile(MultipartFile file) {
        String fileName;
        okhttp3.RequestBody requestBody;
        try {
            fileName = URLEncoder.encode(Objects.requireNonNull(file.getOriginalFilename()), StandardCharsets.UTF_8);
            requestBody = okhttp3.RequestBody.create(MediaType.parse("multipart/form-data"), file.getBytes());
        } catch (Exception e) {
            log.error("解析文件异常 ", e);
            throw new RestfulApiException("解析文件异常：" + e.getMessage());
        }

        MultipartBody.Part part = MultipartBody.Part.createFormData("file", fileName, requestBody);
        CommonResult<String> uploadFile = companyRemoteService.uploadFile(part);
        Assert.isTrue(uploadFile.isSuccess(), "上传文件异常：" + uploadFile.getMsg());
        String originalFilename = file.getOriginalFilename();
        assert originalFilename != null;
        String newFileName = String.format("%s%s%s", "bl_", System.currentTimeMillis(), originalFilename.contains(".") ? originalFilename.substring(originalFilename.lastIndexOf(".")) : ".png");
        Path path = Paths.get(Constants.PUBLIC_PATH, newFileName);
        filesStorageService.save(file, path);
        String nginxUrl = String.format("%s%s/%s", endpointIpPort, Constants.NGINX_PUBLIC_PATH, newFileName);

        return new Tuple2<>(nginxUrl, uploadFile.getData());
    }

    private final AiLandProperties aiLandProperties;

    public Boolean registerUser() {
        boolean share = ObjectUtil.equals(aiLandProperties.getDeploy().getMode(), DeployMode.share);
        long count = companyRepository.count();
        return share || count == 0;
    }

    public Company getCompanyByNodeId(String nodeId) {
        TenantContext.setCurrentTenant(TenantIdentifierResolver.DEFAULT_TENANT);
        return companyRepository.findByNodeId(nodeId);
    }

    public void updateCompanyInfo(String username, String accessToken) {
        QueryIdentityRequest request = new QueryIdentityRequest();
        request.setAccessToken(accessToken);
        Company company = companyRepository.findAll().getFirst();
        if (StringUtils.startsWith(username, "ep") || StringUtils.startsWith(username, "cs")) {
            com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<LegalIdentityQuery> legalIdentityQueryCommonResult =
                    routerManagerRemoteService.queryLegalIdentity(request);
            LegalIdentityQuery legalIdentityQuery = legalIdentityQueryCommonResult.getData();
            company.setAccessType(AccessType.LEGAL_PERSON);
            company.setOrganizationName(legalIdentityQuery.getEntityName());
            company.setCreditCode(legalIdentityQuery.getSocialCode());
            company.setBusinessStartDate(DateUtil.parse(legalIdentityQuery.getEntityValidStartTime()));
            company.setBusinessEndDate(DateUtil.parse(legalIdentityQuery.getEntityValidEndTime()));
            company.setLegalRepresentativeName(legalIdentityQuery.getLegalName());
            company.setLegalRepresentativeIdType(legalIdentityQuery.getLegalCertType());
            company.setLegalRepresentativeIdNumber(legalIdentityQuery.getLegalCertNo());
            company.setIdentityStatus(legalIdentityQuery.getEntityStatus());
            company.setLegalRepresentativeAuthLevel(legalIdentityQuery.getRealnameAuthStatus());
            company.setAuthMethod(legalIdentityQuery.getRealnameAuthMethod());
            company.setAuthDate(legalIdentityQuery.getRealnameAuthTime());
            JSONObject entries = JSONUtil.parseObj(company.getExt());
            if (entries.isEmpty())
                entries = new JSONObject();
            entries.set("entityId", legalIdentityQuery.getEntityId());
            entries.set("entityCode", legalIdentityQuery.getEntityCode());
            company.setExt(entries.toString());
            User user = userRepository.findByAccount(username);
            User.Ext ext = user.getExt();
            ext.setIdentityId(legalIdentityQuery.getIdentityId());
            ext.setIdentityCode(legalIdentityQuery.getIdentityCode());
            user.setExt(ext);
            userRepository.saveAndFlush(user);
            ThreadUtil.execAsync(() -> userService.updatePublic(user));

        } else if (StringUtils.startsWith(username, "ot") || StringUtils.startsWith(username, "shuhan")) {
            com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<OperatorIdentityQuery> legalIdentityQueryCommonResult =
                    routerManagerRemoteService.operatorIdentityQuery(request);
            OperatorIdentityQuery operatorIdentityQuery = legalIdentityQueryCommonResult.getData();
            company.setAccessType(AccessType.AGENT_PERSON);
            company.setOrganizationName(operatorIdentityQuery.getEntityName());
            company.setCreditCode(operatorIdentityQuery.getSocialCode());
            company.setBusinessStartDate(DateUtil.parse(operatorIdentityQuery.getEntityValidStartTime()));
            company.setBusinessEndDate(DateUtil.parse(operatorIdentityQuery.getEntityValidEndTime()));
            company.setLegalRepresentativeName(operatorIdentityQuery.getLegalName());
            company.setLegalRepresentativeIdType(operatorIdentityQuery.getLegalCertType());
            company.setLegalRepresentativeIdNumber(operatorIdentityQuery.getLegalCertNo());
            company.setIdentityStatus(operatorIdentityQuery.getEntityStatus());
            company.setDelegateAuthLevel(operatorIdentityQuery.getRealnameAuthStatus());
            company.setAuthMethod(operatorIdentityQuery.getRealnameAuthMethod());
            company.setAuthDate(operatorIdentityQuery.getRealnameAuthTime());
            company.setDelegateIdNumber(operatorIdentityQuery.getCertNo());
            company.setDelegateIdType(operatorIdentityQuery.getCertType());
            company.setDelegateName(operatorIdentityQuery.getName());
            company.setDelegateContact(operatorIdentityQuery.getPhone());
            company.setDelegateIdExpiry(DateUtil.parse(operatorIdentityQuery.getValidStartTime()));
            JSONObject entries = JSONUtil.parseObj(company.getExt());
            entries.set("delegateIdEndExpiry", operatorIdentityQuery.getValidEndTime());
            company.setCompanyId(operatorIdentityQuery.getEntityId());
            entries.set("entityId", operatorIdentityQuery.getEntityId());
            entries.set("entityCode", operatorIdentityQuery.getEntityCode());
            company.setExt(entries.toString());
            User user = userRepository.findByAccount(username);
            User.Ext ext = user.getExt();
            ext.setIdentityId(operatorIdentityQuery.getIdentityId());
            ext.setIdentityCode(operatorIdentityQuery.getIdentityCode());
            user.setExt(ext);
            userRepository.saveAndFlush(user);
            ThreadUtil.execAsync(() -> userService.updatePublic(user));
        } else {
            com.ailpha.ailand.dataroute.endpoint.common.rest.ganzhou.CommonResult<PersonIdentityQuery> legalIdentityQueryCommonResult =
                    routerManagerRemoteService.personIdentityQuery(request);
            PersonIdentityQuery personIdentityQuery = legalIdentityQueryCommonResult.getData();
            company.setAccessType(AccessType.PERSON);
            company.setOrganizationName(personIdentityQuery.getEntityName());
            company.setIdentityStatus(personIdentityQuery.getEntityStatus());
            company.setIdentityStatus(personIdentityQuery.getEntityStatus());
        }
        companyRepository.saveAndFlush(company);
        ThreadUtil.execAsync(() -> updatePublicSchema(company));

    }
}