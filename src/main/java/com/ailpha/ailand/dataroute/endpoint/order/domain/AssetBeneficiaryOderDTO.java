package com.ailpha.ailand.dataroute.endpoint.order.domain;

import com.ailpha.ailand.dataroute.endpoint.dataasset.domain.AssetType;
import lombok.*;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <AUTHOR>
 * 2024/11/18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AssetBeneficiaryOderDTO implements Serializable {
    /**
     * 资产获益人信息
     * <p>
     * 资产获益人关联表ID
     */
    String id;

    AssetType type;

    /**
     * 资产ID
     */
    String assetId;
    /**
     * 获益人用户ID
     */
    String beneficiaryId;
    /**
     * 订单ID
     */
    String orderId;
    /**
     * 获益方扩展字段json（api鉴权key等信息）
     */
    String beneficiaryExtend;
    /**
     * 订单扩展字段json
     */
    String extend;
    /**
     * 资产获益人关联表创建时间
     */
    Date relCreateTime;

    /**
     * 以下是订单信息
     * <p>
     * 获益人用户名
     */
    String beneficiaryUsername;
    /**
     * 获益方连接器ID
     */
    String beneficiaryRouterId;
    /**
     * 获益方企业名称
     */
    String beneficiaryEnterpriseName;
    /**
     * 获益人方企业性质
     */
    String beneficiaryEnterpriseProperty;
    /**
     * 审批人用户ID
     */
    String approverId;
    /**
     * 审批人用户名
     */
    String approverUsername;
    /**
     * 审批方连接器ID
     */
    String approverRouterId;
    /**
     * 审批方企业名称
     */
    String approverEnterpriseName;
    /**
     * 资产名称
     */
    String assetName;
    /**
     * 交付方式
     */
    String deliveryMode;
    /**
     * 计费方式：预付费、后付费
     */
    String chargingWay;
    /**
     * 计量方式：按次、按时间
     */
    String meteringWay;
    /**
     * 使用次数上限
     */
    BigInteger allowance;
    /**
     * 成功使用次数
     */
    BigInteger successfulUsage;
    /**
     * 失败使用次数
     */
    BigInteger unsuccessfulUsage;
    /**
     * 状态：APPLY（待审批）APPROVED（通过）REJECTED（拒绝）TERMINATED（已终止）COMPLETED（已完成）
     */
    String status;
    /**
     * 有效期
     */
    Date orderExpireDate;
    /**
     * 创建时间
     */
    Date orderCreateTime;
}
