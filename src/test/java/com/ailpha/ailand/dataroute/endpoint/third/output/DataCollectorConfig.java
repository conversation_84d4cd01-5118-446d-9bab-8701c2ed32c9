package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class DataCollectorConfig {

    @Bean
    public AiLandProperties aiLandProperties() {
        AiLandProperties aiLandProperties = new AiLandProperties();
        AiLandProperties.ExecutorServer executorServer = new AiLandProperties.ExecutorServer();
        aiLandProperties.setExecutorServer(executorServer);
        return aiLandProperties;
    }
}
