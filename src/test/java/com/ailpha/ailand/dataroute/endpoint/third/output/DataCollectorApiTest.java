package com.ailpha.ailand.dataroute.endpoint.third.output;

import com.ailpha.ailand.biz.api.collector.DatasourceCheckQuery;
import com.ailpha.ailand.biz.api.collector.DatasourceColumnResponse;
import com.ailpha.ailand.biz.api.collector.DatasourceJoinFieldDebugDataResponse;
import com.ailpha.ailand.collector.api.source.DataCollectSourceTypeEnum;
import com.ailpha.ailand.dataroute.endpoint.common.service.FilesStorageServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;

@AutoConfigureWebTestClient
@ContextConfiguration(classes = {DataCollectorConfig.class})
@SpringBootTest
@Import({DataCollectorApi.class, FilesStorageServiceImpl.class})
class DataCollectorApiTest {

    @Autowired
    DataCollectorApi dataCollectorApi;

    @Autowired
    FilesStorageServiceImpl filesStorageService;

    @BeforeEach
    void setUp() {
    }

    @Test
    void checkConn() {
        dataCollectorApi.checkConn(DatasourceCheckQuery.builder()
                .datasourceType(DataCollectSourceTypeEnum.MYSQL.getType())
                .table("t_oplog")
                .jdbcUrl("******************************************")
                .username("root")
                .password("O%0boNnBg7fITxlE")
                .build());
    }

    @Test
    void checkTable() {
        dataCollectorApi.checkTable(DatasourceCheckQuery.builder()
                .datasourceType(DataCollectSourceTypeEnum.MYSQL.getType())
                .table("t_oplog")
                .jdbcUrl("******************************************")
                .username("root")
                .password("O%0boNnBg7fITxlE")
                .build());
    }

    @Test
    void debugData() {
        DatasourceJoinFieldDebugDataResponse debugDataResponse = dataCollectorApi.debugData(DatasourceCheckQuery.builder()
                .datasourceType(DataCollectSourceTypeEnum.MYSQL.getType())
                .table("t_oplog")
                .jdbcUrl("******************************************")
                .username("root")
                .password("O%0boNnBg7fITxlE")
                .joinFieldSql("username")
                .samplingRowCount(10)
                .build());
        System.out.println(debugDataResponse.getValues());
    }

    @Test
    void fetchTableColumn() {
        DatasourceColumnResponse columnResponse = dataCollectorApi.fetchTableColumn(DatasourceCheckQuery.builder()
                .datasourceType(DataCollectSourceTypeEnum.MYSQL.getType())
                .table("t_oplog")
                .jdbcUrl("******************************************")
                .username("root")
                .password("O%0boNnBg7fITxlE")
                .build());
        System.out.println(columnResponse);
    }

    @Test
    void asyncSubmitJob() {
//        DataItem dataItem = JacksonUtils.json2pojo(dataItemJson, DataItem.class);
//        DataAssetMapper dataAssetMapper = new DataAssetMapperImpl();
//        DataAsset dataAsset = dataAssetMapper.dataItemToDataAsset(dataItem);
//        Path dataAssetFilePath = filesStorageService.getRootPath()
//                .resolve("dataAsset")
//                .resolve(dataAsset.getUserId())
//                .resolve(dataAsset.getAssetId() + ".csv");
//        dataCollectorApi.asyncSubmitJob(DataCollectorJobParam.builder()
//                .dataAssetId(dataAsset.getAssetId())
//                .datasourceType(DatasourceType.valueOf(dataAsset.getExtraData().getDatabaseSourceMetadata().getDatasourceType()))
//                .jdbcUrl(dataAsset.getExtraData().getDatabaseSourceMetadata().getJdbcUrl())
//                .username(dataAsset.getExtraData().getDatabaseSourceMetadata().getUsername())
//                .password(dataAsset.getExtraData().getDatabaseSourceMetadata().getPassword())
//                .columns(dataAsset.getExtraData().getDataSchema().stream().map(DataSchemaBO::getFieldName).toList())
//                .columnAlias(dataAsset.getExtraData().getDataSchema().stream()
//                        .map(dataSchemaBO -> StringUtils.isNotBlank(dataSchemaBO.getName()) ? dataSchemaBO.getName() : dataSchemaBO.getFieldName()).toList())
//                .tableName(dataAsset.getExtraData().getDatabaseSourceMetadata().getTableName())
//                .filepath(dataAssetFilePath.toFile().getAbsolutePath())
//                .conditions(
//                        dataAsset.getExtraData().getDatabaseSourceMetadata().getPartitionColumns() != null ?
//                                dataAsset.getExtraData().getDatabaseSourceMetadata().getPartitionColumns().stream()
//                                        .filter(partitionColumnBO -> Boolean.TRUE.equals(partitionColumnBO.getSelected()))
//                                        .map(partitionColumnBO -> PartitionQueryConditionDTO.builder()
//                                                .colName(partitionColumnBO.getColumn())
//                                                .value(partitionColumnBO.getColumnValue())
//                                                .build())
//                                        .toList()
//                                : null
//                )
//                .syncRule(dataAsset.getExtraData().getDatabaseSourceMetadata().getSyncRule())
//                .callbackUrl(String.format("http://%s:%s%s/%s", "127.0.0.1", "8080", "/callback/executor", dataAsset.getAssetId()))
//                .build());
    }

    private static final String dataItemJson = "{\n" +
            "    \"id\": \"1861966421014114305\",\n" +
            "    \"createTime\": \"2024-11-28 10:52:43\",\n" +
            "    \"createUser\": \"0\",\n" +
            "    \"updateTime\": \"2024-11-28 10:52:43\",\n" +
            "    \"updateUser\": \"0\",\n" +
            "    \"itemCode\": \"ZYc94453bfc3024b0e9e0dc56f662d6fa3\",\n" +
            "    \"itemNum\": null,\n" +
            "    \"name\": \"test112802\",\n" +
            "    \"provider\": \"事业单位\",\n" +
            "    \"sourceOrg\": \"企业210新\",\n" +
            "    \"accessWay\": {\n" +
            "      \"key\": null,\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"industryClassify\": {\n" +
            "      \"key\": \"industryClassify6\",\n" +
            "      \"data\": \"农、林、牧、渔业\"\n" +
            "    },\n" +
            "    \"dataRange\": {\n" +
            "      \"key\": null,\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"safeLevel\": {\n" +
            "      \"key\": \"safeLevel3\",\n" +
            "      \"data\": \"较敏感\"\n" +
            "    },\n" +
            "    \"coverCycleStart\": null,\n" +
            "    \"coverCycleEnd\": null,\n" +
            "    \"updateFrequency\": {\n" +
            "      \"key\": null,\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"tag\": [],\n" +
            "    \"describeMessage\": \"\",\n" +
            "    \"dataAddr\": null,\n" +
            "    \"testDataAddr\": null,\n" +
            "    \"itemStatus\": {\n" +
            "      \"key\": \"item_status1\",\n" +
            "      \"data\": \"审批中\"\n" +
            "    },\n" +
            "    \"catalogNumber\": \"\",\n" +
            "    \"classifyCode\": {\n" +
            "      \"key\": null,\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"nickName\": null,\n" +
            "    \"sort\": null,\n" +
            "    \"dataSource\": {\n" +
            "      \"key\": \"DATABASE\",\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"dataResItemColumnList\": [],\n" +
            "    \"remarks\": null,\n" +
            "    \"autoUpdateDay\": null,\n" +
            "    \"sourceOrgName\": null,\n" +
            "    \"inCartFlag\": null,\n" +
            "    \"operateList\": null,\n" +
            "    \"remindDay\": null,\n" +
            "    \"itemType\": {\n" +
            "      \"key\": \"STRUCTURED\",\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"targetObject\": {\n" +
            "      \"key\": null,\n" +
            "      \"data\": null\n" +
            "    },\n" +
            "    \"uniqeKey\": null,\n" +
            "    \"pushStatus\": {\n" +
            "      \"key\": \"pushstatus_not_push\",\n" +
            "      \"data\": \"未发布\"\n" +
            "    },\n" +
            "    \"findDataKey\": null,\n" +
            "    \"completeFlag\": null,\n" +
            "    \"clientId\": \"f0693fcabd956e547bd753a527a45b65db041d6ae3aa45249e98729be4fc2917\",\n" +
            "    \"userName\": \"luva\",\n" +
            "    \"userId\": \"1861953906477658114\",\n" +
            "    \"dataAccessMode\": null,\n" +
            "    \"extraData\": \"{\\\"dataAssetPrepareStatus\\\":\\\"CREATED\\\",\\\"apiQueryWay\\\":null,\\\"deliveryModes\\\":[\\\"FILE_DOWNLOAD\\\"],\\\"debugDataSource\\\":\\\"none\\\",\\\"debugDataPath\\\":null,\\\"dataSchema\\\":[{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"id\\\",\\\"type\\\":\\\"BIGINT\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"username\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"操作用户名\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"module\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"操作模块\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"op_type\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"操作类型\\\",\\\"allowQuery\\\":false,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"optime\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"操作时间\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"op_result\\\",\\\"type\\\":\\\"INT\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"操作成功与否,true/false或者是code错误码之类\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"op_desc\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"描述\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"message\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"消息(用于存放异常，警告，提醒等消息)\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"ip\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"\\\",\\\"allowQuery\\\":true,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false},{\\\"apiOnline\\\":false,\\\"name\\\":null,\\\"fieldName\\\":\\\"tenant_id\\\",\\\"type\\\":\\\"STRING\\\",\\\"sensitiveLevel\\\":null,\\\"comment\\\":\\\"租户id\\\",\\\"allowQuery\\\":false,\\\"authorization\\\":false,\\\"update\\\":false,\\\"create\\\":false}],\\\"gatewayServiceRouteId\\\":null,\\\"apiSourceMetadata\\\":null,\\\"fileSourceMetadata\\\":{\\\"dataAssetFilePath\\\":null,\\\"dataAssetFileHash\\\":null},\\\"databaseSourceMetadata\\\":{\\\"columns\\\":null,\\\"name\\\":\\\"t_oplog\\\",\\\"desc\\\":null,\\\"jdbcUrl\\\":\\\"******************************************\\\",\\\"username\\\":\\\"root\\\",\\\"tableName\\\":\\\"t_oplog\\\",\\\"password\\\":\\\"O%0boNnBg7fITxlE\\\",\\\"datasourceType\\\":\\\"mysql\\\",\\\"access_id\\\":null,\\\"access_key\\\":null,\\\"project_name\\\":null,\\\"hasKerberos\\\":false,\\\"keytabFilePath\\\":null,\\\"kerberosPrincipal\\\":null,\\\"kerberosConfPath\\\":null,\\\"partitionColumns\\\":[],\\\"syncRule\\\":null}}\",\n" +
            "    \"companyCode\": \"RTY123451234567\",\n" +
            "    \"companyName\": \"企业210新\",\n" +
            "    \"isDelete\": false\n" +
            "  }";
}