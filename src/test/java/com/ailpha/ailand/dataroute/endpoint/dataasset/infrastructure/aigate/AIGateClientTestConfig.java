package com.ailpha.ailand.dataroute.endpoint.dataasset.infrastructure.aigate;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.web.client.RestClientBuilderConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class AIGateClientTestConfig {

    @Bean
    @ConditionalOnMissingBean(RestClientBuilderConfigurer.class)
    public RestClientBuilderConfigurer restClientBuilderConfigurer() {
        return new RestClientBuilderConfigurer();
    }
}
