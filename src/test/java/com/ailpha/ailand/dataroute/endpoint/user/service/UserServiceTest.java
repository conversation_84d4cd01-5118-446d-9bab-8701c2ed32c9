package com.ailpha.ailand.dataroute.endpoint.user.service;

import com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication;
import com.ailpha.ailand.dataroute.endpoint.tenant.context.TenantContext;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.AddUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.request.UpdateUserRequest;
import com.ailpha.ailand.dataroute.endpoint.user.remote.response.UserListResponse;
import com.ailpha.ailand.dataroute.endpoint.user.vo.UserPageRequest;
import com.dbapp.rest.response.SuccessResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.util.Assert;

import java.util.List;

@Slf4j
@ActiveProfiles({"local"})
@SpringBootTest(classes = DataRouteEndpointApplication.class)
class UserServiceTest {
    @Autowired
    UserService userService;


    @Test
    void userDetail() {
    }

    @Test
    void userList() {
        UserPageRequest request = new UserPageRequest();
        SuccessResponse<List<UserListResponse>> listSuccessResponse = userService.userList(request);
        log.info("users={}", listSuccessResponse.getData());
    }

    @Test
    public void addUser() {
        AddUserRequest addUserRequest = new AddUserRequest();
        addUserRequest.setName("test1");
        addUserRequest.setPassword("U3bGlrd8c9Vf6oSX4deXx/uJh1cS1chlX5NebFMkotQHtZj1RrzyEq7z2Ya3fU+bNG9YTQU0XunGLTxz5OoRSx04p0koNFk4zBsPXyfhH0Rz0NlnUUrnV9Me35r35BHB7gEK5ISoz/6MGzBzVeFva0qfam8ao0ON5KEDDBcR2Tl4c248h/OGO0n43qfCXqQq28kzWjPVjxrUeVykBYspblxjLmLx+glq6Fa+vR3OCZ48magYHFV7tgBKs8izKu8hlccv3rYpfes+nGRhktF7vY0nNfI6Q1BaqAYI6egO4vWLI1S1KUDMHxVoNoePjjrmcm0ANwJinZxGAhI8Zp1CGw==");
        addUserRequest.setMobile("**********");
        addUserRequest.setEmail("<EMAIL>");
        addUserRequest.setAccount("admin1");

    }

    @Test
    void checkNameRepeat() {
        Boolean checkNameRepeat = userService.checkNameRepeat("1866794882161471490", "121112");
        Assert.isTrue(checkNameRepeat, "账号重名了");
    }

    @Test
    void updateUser() {
        UpdateUserRequest updateUserRequest = new UpdateUserRequest();
        updateUserRequest.setId("1866794882161471490");
        updateUserRequest.setName("112233");
//        Boolean updateUser = userService.updateUser(updateUserRequest);
//        log.info("updateUser = {}", updateUser);
    }

    @Test
    void deleteUser() {
        Boolean deleteUser = userService.deleteUser("1866794882161471490");
        log.info("deleteUser = {}", deleteUser);
    }
}