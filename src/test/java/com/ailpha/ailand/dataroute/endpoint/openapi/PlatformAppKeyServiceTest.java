package com.ailpha.ailand.dataroute.endpoint.openapi;

import com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.AIGateClient;
import com.ailpha.ailand.dataroute.endpoint.third.config.AIGateConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource("classpath:com/ailpha/ailand/dataroute/endpoint/openapi/application.properties")
@ContextConfiguration(classes = {PlatformAppKeyConfig.class})
@EnableConfigurationProperties(value = AIGateConfig.class)
@Import({AIGateClient.class, RestClientConfig.class})
class PlatformAppKeyServiceTest {

}