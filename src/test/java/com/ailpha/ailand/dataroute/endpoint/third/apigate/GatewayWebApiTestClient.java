package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.base.BaseCapabilityManager;
import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.NodeInfoResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;

import static com.ailpha.ailand.dataroute.endpoint.connector.RouterService.ROUTER_FILENAME;

@Component
class GatewayWebApiTestClient extends GatewayWebApi {

    private final AiLandProperties aiLandProperties;

    public GatewayWebApiTestClient(AiLandProperties aiLandProperties, BaseCapabilityManager baseCapabilityManager, ObjectMapper objectMapper) {
        super(baseCapabilityManager, null);
        this.aiLandProperties = aiLandProperties;
    }

    public NodeInfoResponse currentNode() {
        String nodeInfo = FileUtil.readString(aiLandProperties.getFileStorage().getBasePath() + FileUtil.FILE_SEPARATOR + ROUTER_FILENAME, Charset.defaultCharset());
        //        nodeInfoResponse.setCompany(currentCompany());
        return JSONUtil.toBean(nodeInfo, NodeInfoResponse.class);
    }

    @Override
    protected String getRouterId() {
        return currentNode().getPlatformId();
    }
}
