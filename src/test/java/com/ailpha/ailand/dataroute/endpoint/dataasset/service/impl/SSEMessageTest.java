package com.ailpha.ailand.dataroute.endpoint.dataasset.service.impl;

import com.ailpha.ailand.dataroute.endpoint.connector.RouterService;
import com.ailpha.ailand.dataroute.endpoint.connector.remote.DrClientInfoVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @author: sunsas.yu
 * @date: 2024/11/21 19:44
 * @Description:
 */
@ActiveProfiles({"local"})
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class SSEMessageTest {

    @Autowired
    private RouterService routerService;

    @Test
    public void testGetClient(){
        DrClientInfoVO clientInfo = routerService.getByClientNo("c986e9d7177ca64c7d9ae3ac78a0d2f19156c95804019185406f896654ae8661");
        System.out.println(clientInfo);
    }
}
