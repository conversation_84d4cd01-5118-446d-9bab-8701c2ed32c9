package com.ailpha.ailand.dataroute.endpoint.tenant.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ActiveProfiles({"local"})
@SpringBootTest(classes = DataRouteEndpointApplication.class)
class TenantServiceTest {
    @Autowired
    TenantService tenantService;

    @Test
    void initializeTenantSchemaTest() {
        tenantService.initTenantSchema("tenant_1");
    }

    @Test
    void createTenantTest() {
        tenantService.addTenantSchema("tenant_2");
    }


}