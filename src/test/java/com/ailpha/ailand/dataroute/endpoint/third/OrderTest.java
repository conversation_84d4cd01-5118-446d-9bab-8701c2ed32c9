package com.ailpha.ailand.dataroute.endpoint.third;

import com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication;
import com.ailpha.ailand.dataroute.endpoint.order.schedule.OrderSyncSchedule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * @author: sunsas.yu
 * @date: 2024/11/21 19:44
 * @Description:
 */
@Slf4j
@ActiveProfiles({"local"})
@SpringBootTest(classes = DataRouteEndpointApplication.class)
public class OrderTest {

    @Autowired
    private OrderSyncSchedule orderSyncSchedule;

    @Test
    public void testSync(){
        System.out.println("orderSyncSchedule = " + orderSyncSchedule);
        orderSyncSchedule.syncOrder();
    }
}
