package com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ailpha.ailand.dataroute.endpoint.company.domain.AccessType;
import com.ailpha.ailand.dataroute.endpoint.connector.vo.CompanyDTO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.request.DataAssetQuery;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataProductVO;
import com.ailpha.ailand.dataroute.endpoint.dataasset.vo.DataResourceVO;
import com.ailpha.ailand.dataroute.endpoint.node.dto.NodeDTO;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataProductInfoRegist;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.DataResourceRegistry;
import com.ailpha.ailand.dataroute.endpoint.third.hub.ganzhou.request.ResourceItemAddCmd;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static com.ailpha.ailand.dataroute.endpoint.dataasset.domain.DataProduct.deliveryModeMapping;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@Import({HubGanzhouApiClientTest.GanzhouApiClient.class})
//@EnableConfigurationProperties(value = {HubGanZhouConfig.class})
@ContextConfiguration(classes = {HubGanZhouConfig.class})
class HubGanzhouApiClientTest {

    @Component(value = "ganzhouApiClient")
    public static class GanzhouApiClient extends HubGanzhouApiClient {

        public GanzhouApiClient() {
            super(new ObjectMapper());
        }

        @Override
        protected NodeDTO.HubInfo getHubInfo() {
            NodeDTO.HubInfo hubInfo = new NodeDTO.HubInfo();
            hubInfo.setUrl("http://218.64.194.49:8081/prod-api/data-base");
            hubInfo.setAk("587ba74a8462a25a28133012zQnNpP31");
            hubInfo.setSk("skuFgHdpvc0dOicn");
            return hubInfo;
        }
    }

    @Autowired
    HubGanzhouApiClient ganzhouApiClient;

    private static final String PLATFORM_ID = "53beda0d-799e-4b26-84ce-8da54b05f7c6";

    @Test
    void allMarketDataResource() {
        ganzhouApiClient.allMarketDataResource(DataAssetQuery.builder()
                .routeId(PLATFORM_ID)
                .num(1)
                .size(10)
                .build());
    }

    @Test
    void allMarketDataProduct() {
        ganzhouApiClient.allMarketDataProduct(DataAssetQuery.builder()
                .routeId(PLATFORM_ID)
                .num(1)
                .size(10)
                .build());
    }

    @Test
    void getProduct() {
        ganzhouApiClient.getProductInfo("619124192181993267223607C5Xtn0wd5");
    }
    @Test
    void getResource() {
        ganzhouApiClient.getResourceInfo("7191420021231735193833011iR8tdTL3");
    }

    @Test
    void dataProductInfoRegist() {
        DataProductVO product = JSONUtil.toBean("""
                {
                        "id": "b7641334fe274146a99ed3803101603d",
                        "dataProductPlatformId": "69133010866230119576440TSBH5TUBA",
                        "platformId": "3ad20253d69d4163b98800a39731b70a",
                        "userId": "e1bac37003714e148052afc69cf6c152",
                        "provider": {
                            "phone": "***********",
                            "email": "<EMAIL>",
                            "routerId": "591330108662301195764408VB2Z4MH0",
                            "routerName": "node_电信测试one",
                            "userId": "e1bac37003714e148052afc69cf6c152",
                            "userIdShuhan": "1913514495427248130",
                            "username": "ltq239",
                            "company": {
                                "id": 1745052236949,
                                "thirdBusinessId": "1913496963932782593",
                                "nodeId": "591330108662301195764408VB2Z4MH0",
                                "serviceNode": {
                                    "id": 2,
                                    "nodeId": "58a0af47-6112-4a47-9ffe-e982de777507",
                                    "nodeName": "功能节点6",
                                    "nodeType": "全域功能节点",
                                    "status": "ACTIVE",
                                    "accessTime": "2025-04-19 16:41:45",
                                    "hubInfo": {
                                        "url": "https://10.0.0.5:4036",
                                        "authType": "sign",
                                        "publicKey": null,
                                        "ak": "raBNZDBKNzCnuHoY",
                                        "sk": "tppSWf1VtFdEeoNK6LhTRCePrMwNnRGq",
                                        "certificateNo": "11362790560308141312"
                                    },
                                    "connectorInfo": {
                                        "exposeUrl": "10.128.0.11:8080"
                                    }
                                },
                                "status": "REVIEW_PASS",
                                "schema": "tenant_1745052236949",
                                "businessLicense": "https://125.122.153.239:4443/_data-route/public/bl_1745052301117.png",
                                "accessType": "LEGAL_PERSON",
                                "organizationName": "电信测试one",
                                "creditCode": "913301111111111111",
                                "legalRepresentativeName": "法人239",
                                "legalRepresentativeIdType": "身份证",
                                "legalRepresentativeIdNumber": "220772199901214522",
                                "legalRepresentativeIdExpiry": "2025-04-30",
                                "legalRepresentativeAuthLevel": "1",
                                "authMethod": "0",
                                "registrationAddress": "",
                                "industryType": null,
                                "businessStartDate": "2025-04-01",
                                "businessEndDate": "2025-04-30",
                                "delegateName": "",
                                "delegateIdType": "身份证",
                                "delegateIdNumber": "",
                                "delegateIdExpiry": null,
                                "delegateInstitution": null,
                                "delegateInstitutionCode": null,
                                "delegateContact": "***********",
                                "delegateEmail": "",
                                "delegateAuthLevel": "1",
                                "delegateAuthMethod": null,
                                "delegateRegistrationAddress": "",
                                "delegateIndustryType": null,
                                "delegateTaskScope": null,
                                "delegateAuthorizationStart": null,
                                "delegateAuthorizationEnd": null,
                                "delegateRemarks": "",
                                "validityEndDate": null,
                                "deleted": false,
                                "reviewTime": "2025-04-19T08:45:36.602+00:00",
                                "authorizationLetter": "",
                                "connectorName": "1"
                            }
                        },
                        "dataProductName": "省内异地用户",
                        "dataProductNameCN": "",
                        "type": "02",
                        "dataCoverageTimeStart": "2025-08-24",
                        "dataCoverageTimeEnd": "2025-08-24",
                        "industry": "I6311",
                        "industry1": "{\\"industryCode\\":[\\"industry\\",\\"I\\",\\"I63\\",\\"I631\\",\\"I6311\\"],\\"industryName\\":\\"国标行业/信息传输、软件和信息技术服务业/电信、广播电视和卫星传输服务/电信/固定电信服务\\"}",
                        "region": null,
                        "region1": null,
                        "personalInformation": "1",
                        "description": "近半年省内异地活跃天数大于80%。",
                        "deliveryModes": [
                            "API"
                        ],
                        "deliveryMethod": null,
                        "limitations": "10000",
                        "authorize": "1",
                        "source": "",
                        "dataSize": null,
                        "updateFrequency": "2",
                        "isSecondaryProcessed": "1",
                        "registrationTime": 1746512278623,
                        "registrationUpdateTime": null,
                        "publishTime": 1746512302558,
                        "publishUpdateTime": 1746512308276,
                        "resourceId": "69133010866230119576440TSBH5TUBA",
                        "lineage": "{\\"cells\\":[{\\"shape\\":\\"edge\\",\\"attrs\\":{\\"line\\":{\\"stroke\\":\\"#8C8C8C\\",\\"strokeWidth\\":1,\\"targetMarker\\":{\\"name\\":\\"classic\\",\\"size\\":8},\\"strokeDasharray\\":5}},\\"id\\":\\"9ba4f7de-cee3-4c92-8aaf-ccb25bc7dd66\\",\\"connector\\":{\\"name\\":\\"curveConnector\\"},\\"defaultLabel\\":{\\"markup\\":[{\\"tagName\\":\\"rect\\",\\"selector\\":\\"body\\"},{\\"tagName\\":\\"text\\",\\"selector\\":\\"label\\"}],\\"attrs\\":{\\"label\\":{\\"fontSize\\":14,\\"textAnchor\\":\\"middle\\",\\"textVerticalAnchor\\":\\"middle\\",\\"pointerEvents\\":\\"none\\",\\"fill\\":\\"#1F5AFF\\"}},\\"position\\":{\\"distance\\":0.5}},\\"router\\":{\\"name\\":\\"orth\\"},\\"zIndex\\":0,\\"source\\":{\\"cell\\":\\"d4b45550-920e-4d1d-a2cd-cd0566c19f3e\\",\\"port\\":\\"f21e4eef-9074-4ef7-be1d-f24e2b7e9b8c\\"},\\"target\\":{\\"cell\\":\\"260a25c9-a872-461c-ba93-e0b263dedf6d\\",\\"port\\":\\"e7b34e13-0065-445e-9eef-5e583c3925cc\\"}},{\\"shape\\":\\"edge\\",\\"attrs\\":{\\"line\\":{\\"stroke\\":\\"#8C8C8C\\",\\"strokeWidth\\":1,\\"targetMarker\\":{\\"name\\":\\"classic\\",\\"size\\":8},\\"strokeDasharray\\":5}},\\"id\\":\\"a21dcec2-6d99-45f0-8b8b-aaebc3352551\\",\\"connector\\":{\\"name\\":\\"curveConnector\\"},\\"defaultLabel\\":{\\"markup\\":[{\\"tagName\\":\\"rect\\",\\"selector\\":\\"body\\"},{\\"tagName\\":\\"text\\",\\"selector\\":\\"label\\"}],\\"attrs\\":{\\"label\\":{\\"fontSize\\":14,\\"textAnchor\\":\\"middle\\",\\"textVerticalAnchor\\":\\"middle\\",\\"pointerEvents\\":\\"none\\",\\"fill\\":\\"#1F5AFF\\"}},\\"position\\":{\\"distance\\":0.5}},\\"router\\":{\\"name\\":\\"orth\\"},\\"zIndex\\":0,\\"source\\":{\\"cell\\":\\"f0aace16-b3aa-4254-bb5d-45615289a238\\",\\"port\\":\\"d9939353-e60b-4c9d-98c6-7de1a4d66e36\\"},\\"target\\":{\\"cell\\":\\"260a25c9-a872-461c-ba93-e0b263dedf6d\\",\\"port\\":\\"e7b34e13-0065-445e-9eef-5e583c3925cc\\"}},{\\"shape\\":\\"edge\\",\\"attrs\\":{\\"line\\":{\\"stroke\\":\\"#8C8C8C\\",\\"strokeWidth\\":1,\\"targetMarker\\":{\\"name\\":\\"classic\\",\\"size\\":8},\\"strokeDasharray\\":5}},\\"id\\":\\"db67f017-cef8-4489-85f3-754f88bf5eca\\",\\"connector\\":{\\"name\\":\\"curveConnector\\"},\\"defaultLabel\\":{\\"markup\\":[{\\"tagName\\":\\"rect\\",\\"selector\\":\\"body\\"},{\\"tagName\\":\\"text\\",\\"selector\\":\\"label\\"}],\\"attrs\\":{\\"label\\":{\\"fontSize\\":14,\\"textAnchor\\":\\"middle\\",\\"textVerticalAnchor\\":\\"middle\\",\\"pointerEvents\\":\\"none\\",\\"fill\\":\\"#1F5AFF\\"}},\\"position\\":{\\"distance\\":0.5}},\\"router\\":{\\"name\\":\\"orth\\"},\\"zIndex\\":0,\\"source\\":{\\"cell\\":\\"260a25c9-a872-461c-ba93-e0b263dedf6d\\",\\"port\\":\\"af599c9a-2cd3-4f93-92f4-79a71ccfe93f\\"},\\"target\\":{\\"cell\\":\\"59c65173-b67b-47dd-a62b-f4966f1afb89\\",\\"port\\":\\"d43131ff-e583-48c2-94d6-e6711eac6de9\\"}},{\\"position\\":{\\"x\\":80,\\"y\\":80},\\"size\\":{\\"width\\":123,\\"height\\":58},\\"attrs\\":{\\"imgBg\\":{\\"fill\\":\\"#1F5AFF\\"},\\"logo\\":{\\"xlink:href\\":\\"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='10px'%20height='9.99888368px'%20viewBox='0%200%2010%209.99888368'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e开始数据%3c/title%3e%3cg%20id='页面-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='数据资产管理-数据产品录入-产品血缘'%20transform='translate(-221.000000,%20-231.000000)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组-7备份-3'%20transform='translate(217.000000,%20220.000000)'%3e%3cg%20id='开始数据'%20transform='translate(4.000000,%2011.000000)'%3e%3cpath%20d='M8.5699933,0%20C9.35588301,0%209.99888368,0.64300067%2010,1.42889038%20L10,8.5699933%20C10,9.35588301%209.35699933,9.99888368%208.57110962,9.99888368%20L7.5027908,9.99888368%20C7.30408573,9.99888368%207.14333557,9.83813351%207.14333557,9.64054476%20C7.14333557,9.44295602%207.30408573,9.28220585%207.5027908,9.28220585%20C7.51618665,9.28220585%207.5295825,9.28332217%207.54297834,9.28443849%20L8.57445858,9.28443849%20C8.96740344,9.28443849%209.28890377,8.95400759%209.28890377,8.56106274%20L9.28890377,2.85554811%20L0.73453896,2.85554811%20C0.727841036,2.85554811%200.720026792,2.85443179%200.713328868,2.85443179%20L0.713328868,8.56106274%20C0.713328868,8.95400759%201.0348292,9.28443849%201.42777406,9.28443849%20L4.61040411,9.28220585%20C4.8158071,9.28220585%204.97655727,9.44295602%204.97655727,9.64054476%20C4.97655727,9.83813351%204.81692342,9.99888368%204.61821835,9.99888368%20L1.42889038,9.99888368%20C0.64300067,9.99888368%200,9.35588301%200,8.5699933%20L0,1.42889038%20C0,0.64300067%200.64300067,0%201.42889038,0%20L8.5699933,0%20Z%20M6.05715562,9.28220585%20C6.25567726,9.28220585%206.41661085,9.44263965%206.41661085,9.64054476%20C6.41661085,9.83844988%206.25567726,9.99888368%206.05715562,9.99888368%20C5.85863397,9.99888368%205.69770038,9.83844988%205.69770038,9.64054476%20C5.69770038,9.44263965%205.85863397,9.28220585%206.05715562,9.28220585%20Z%20M4.22304086,4.67180174%20C4.3011833,4.67180174%204.37262782,4.70640768%204.4206296,4.76222371%20L6.29381558,5.85398526%20C6.37307435,5.89975441%206.42665774,5.98682742%206.42665774,6.08506363%20C6.42665774,6.19223041%206.36414378,6.2837687%206.27483813,6.32507256%20L4.35811565,7.44139317%20C4.31792811,7.4648359%204.27215896,7.47823175%204.22304086,7.47823175%20C4.07791918,7.47823175%203.95958919,7.35990176%203.95958919,7.21254744%20C3.95958919,7.20250056%203.95958919,7.19356999%203.96070551,7.18463943%20L3.95624023,4.95423086%20L3.95958919,4.93748605%20C3.95958919,4.79124805%204.07680286,4.67180174%204.22304086,4.67180174%20Z%20M1.15539183,0.765795937%20C0.896405448,0.872962715%200.713328868,1.12860013%200.713328868,1.42554142%20L0.713328868,2.14445189%20L0.723933914,2.14347511%20L1.9502121,2.14333557%20L1.15539183,0.765795937%20Z%20M6.79504354,0.711096227%20L5.7803081,0.711096227%20L6.60638535,2.14333557%20L7.62112079,2.14333557%20L6.79504354,0.711096227%20Z%20M4.9240902,0.711096227%20L3.91047109,0.711096227%20L4.73654834,2.14333557%20L5.75016745,2.14333557%20L4.9240902,0.711096227%20Z%20M3.05313686,0.711096227%20L1.98035276,0.711096227%20L2.80754633,2.14333557%20L3.87921411,2.14333557%20L3.05313686,0.711096227%20Z%20M8.57445858,0.711096227%20L7.65237776,0.711096227%20L8.47845501,2.14333557%20L9.28890377,2.14333557%20L9.28890377,1.42554142%20C9.28890377,1.03259656%208.96740344,0.711096227%208.57445858,0.711096227%20Z'%20id='形状结合'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\\"},\\"nodeName\\":{\\"text\\":\\"节点名称\\"},\\"typeName\\":{\\"text\\":\\"开始数据\\"}},\\"shape\\":\\"start-canvas-node\\",\\"ports\\":{\\"groups\\":{\\"input\\":{\\"position\\":\\"left\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}},\\"output\\":{\\"position\\":\\"right\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}}},\\"items\\":[{\\"group\\":\\"output\\",\\"id\\":\\"f21e4eef-9074-4ef7-be1d-f24e2b7e9b8c\\"}]},\\"id\\":\\"d4b45550-920e-4d1d-a2cd-cd0566c19f3e\\",\\"zIndex\\":1},{\\"position\\":{\\"x\\":80,\\"y\\":240},\\"size\\":{\\"width\\":123,\\"height\\":58},\\"attrs\\":{\\"imgBg\\":{\\"fill\\":\\"#1F5AFF\\"},\\"logo\\":{\\"xlink:href\\":\\"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='10px'%20height='9.99888368px'%20viewBox='0%200%2010%209.99888368'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e开始数据%3c/title%3e%3cg%20id='页面-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='数据资产管理-数据产品录入-产品血缘'%20transform='translate(-221.000000,%20-231.000000)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组-7备份-3'%20transform='translate(217.000000,%20220.000000)'%3e%3cg%20id='开始数据'%20transform='translate(4.000000,%2011.000000)'%3e%3cpath%20d='M8.5699933,0%20C9.35588301,0%209.99888368,0.64300067%2010,1.42889038%20L10,8.5699933%20C10,9.35588301%209.35699933,9.99888368%208.57110962,9.99888368%20L7.5027908,9.99888368%20C7.30408573,9.99888368%207.14333557,9.83813351%207.14333557,9.64054476%20C7.14333557,9.44295602%207.30408573,9.28220585%207.5027908,9.28220585%20C7.51618665,9.28220585%207.5295825,9.28332217%207.54297834,9.28443849%20L8.57445858,9.28443849%20C8.96740344,9.28443849%209.28890377,8.95400759%209.28890377,8.56106274%20L9.28890377,2.85554811%20L0.73453896,2.85554811%20C0.727841036,2.85554811%200.720026792,2.85443179%200.713328868,2.85443179%20L0.713328868,8.56106274%20C0.713328868,8.95400759%201.0348292,9.28443849%201.42777406,9.28443849%20L4.61040411,9.28220585%20C4.8158071,9.28220585%204.97655727,9.44295602%204.97655727,9.64054476%20C4.97655727,9.83813351%204.81692342,9.99888368%204.61821835,9.99888368%20L1.42889038,9.99888368%20C0.64300067,9.99888368%200,9.35588301%200,8.5699933%20L0,1.42889038%20C0,0.64300067%200.64300067,0%201.42889038,0%20L8.5699933,0%20Z%20M6.05715562,9.28220585%20C6.25567726,9.28220585%206.41661085,9.44263965%206.41661085,9.64054476%20C6.41661085,9.83844988%206.25567726,9.99888368%206.05715562,9.99888368%20C5.85863397,9.99888368%205.69770038,9.83844988%205.69770038,9.64054476%20C5.69770038,9.44263965%205.85863397,9.28220585%206.05715562,9.28220585%20Z%20M4.22304086,4.67180174%20C4.3011833,4.67180174%204.37262782,4.70640768%204.4206296,4.76222371%20L6.29381558,5.85398526%20C6.37307435,5.89975441%206.42665774,5.98682742%206.42665774,6.08506363%20C6.42665774,6.19223041%206.36414378,6.2837687%206.27483813,6.32507256%20L4.35811565,7.44139317%20C4.31792811,7.4648359%204.27215896,7.47823175%204.22304086,7.47823175%20C4.07791918,7.47823175%203.95958919,7.35990176%203.95958919,7.21254744%20C3.95958919,7.20250056%203.95958919,7.19356999%203.96070551,7.18463943%20L3.95624023,4.95423086%20L3.95958919,4.93748605%20C3.95958919,4.79124805%204.07680286,4.67180174%204.22304086,4.67180174%20Z%20M1.15539183,0.765795937%20C0.896405448,0.872962715%200.713328868,1.12860013%200.713328868,1.42554142%20L0.713328868,2.14445189%20L0.723933914,2.14347511%20L1.9502121,2.14333557%20L1.15539183,0.765795937%20Z%20M6.79504354,0.711096227%20L5.7803081,0.711096227%20L6.60638535,2.14333557%20L7.62112079,2.14333557%20L6.79504354,0.711096227%20Z%20M4.9240902,0.711096227%20L3.91047109,0.711096227%20L4.73654834,2.14333557%20L5.75016745,2.14333557%20L4.9240902,0.711096227%20Z%20M3.05313686,0.711096227%20L1.98035276,0.711096227%20L2.80754633,2.14333557%20L3.87921411,2.14333557%20L3.05313686,0.711096227%20Z%20M8.57445858,0.711096227%20L7.65237776,0.711096227%20L8.47845501,2.14333557%20L9.28890377,2.14333557%20L9.28890377,1.42554142%20C9.28890377,1.03259656%208.96740344,0.711096227%208.57445858,0.711096227%20Z'%20id='形状结合'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\\"},\\"nodeName\\":{\\"text\\":\\"节点名称\\"},\\"typeName\\":{\\"text\\":\\"开始数据\\"}},\\"shape\\":\\"start-canvas-node\\",\\"ports\\":{\\"groups\\":{\\"input\\":{\\"position\\":\\"left\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}},\\"output\\":{\\"position\\":\\"right\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}}},\\"items\\":[{\\"group\\":\\"output\\",\\"id\\":\\"d9939353-e60b-4c9d-98c6-7de1a4d66e36\\"}]},\\"id\\":\\"f0aace16-b3aa-4254-bb5d-45615289a238\\",\\"zIndex\\":2},{\\"position\\":{\\"x\\":330,\\"y\\":160},\\"size\\":{\\"width\\":123,\\"height\\":58},\\"attrs\\":{\\"imgBg\\":{\\"fill\\":\\"#1E8FF8\\"},\\"logo\\":{\\"xlink:href\\":\\"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='10px'%20height='10px'%20viewBox='0%200%2010%2010'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e作业%3c/title%3e%3cg%20id='页面-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='数据资产管理-数据产品录入-产品血缘'%20transform='translate(-221.000000,%20-393.000000)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组-7备份-8'%20transform='translate(217.000000,%20382.000000)'%3e%3cg%20id='作业'%20transform='translate(4.000000,%2011.000000)'%3e%3cpath%20d='M8.58048162,0.0633713581%20C9.3409379,0.0633713581%2010,0.671736383%2010,1.48288974%20L10,8.58048162%20C10,9.3409379%209.3409379,10%208.58048162,10%20L1.48288974,10%20C0.722433463,10%200.0633713581,9.3409379%200.0633713581,8.58048162%20L0.0633713581,3.35868187%20C0.0633713581,3.15589354%200.266159711,2.95310521%200.468948041,2.9531052%20C0.671736372,2.95310519%200.874524714,3.15589353%200.874524714,3.35868187%20L0.874524714,8.58048162%20C0.874524725,8.93536122%201.12801015,9.18884664%201.48288974,9.18884664%20L8.58048162,9.18884664%20C8.93536122,9.18884663%209.18884664,8.93536121%209.18884664,8.58048162%20L9.18884664,1.48288974%20C9.18884663,1.12801014%208.93536121,0.874524714%208.58048162,0.874524714%20L3.46007604,0.874524714%20C3.25728771,0.874524725%203.05449938,0.671736383%203.05449937,0.468948041%20C3.05449936,0.2661597%203.2572877,0.0633713581%203.46007604,0.0633713581%20Z%20M7.87072243,7.11026616%20C8.07351078,7.11026616%208.27629912,7.3130545%208.27629912,7.51584284%20C8.27629912,7.76932826%208.12420786,7.92141951%207.87072243,7.92141951%20L2.09125475,7.92141951%20C1.88846641,7.92141951%201.68567807,7.71863118%201.68567807,7.51584284%20C1.68567807,7.3130545%201.88846641,7.11026616%202.09125475,7.11026616%20Z%20M7.87072243,5.23447402%20C8.07351078,5.23447402%208.27629912,5.43726236%208.27629912,5.6400507%20C8.27629912,5.84283904%208.12420786,6.04562738%207.87072243,6.04562738%20L6.2991128,6.04562738%20C6.09632446,6.04562738%205.89353612,5.84283904%205.89353612,5.6400507%20C5.89353612,5.43726236%206.09632446,5.23447402%206.2991128,5.23447402%20Z%20M0.114068438,0.114068438%20C0.266159689,-0.0380228127%200.519645121,-0.0380228127%200.671736372,0.114068438%20L4.77820025,4.22053232%20C4.9302915,4.37262357%204.9302915,4.626109%204.77820025,4.77820025%20C4.57541192,4.9302915%204.32192649,4.9302915%204.16983524,4.77820025%20L0.114068438,0.671736372%20C-0.0380228127,0.519645121%20-0.0380228127,0.266159689%200.114068438,0.114068438%20Z'%20id='形状结合'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\\"},\\"nodeName\\":{\\"text\\":\\"节点名称\\"},\\"typeName\\":{\\"text\\":\\"作业\\"}},\\"shape\\":\\"canvas-node\\",\\"ports\\":{\\"groups\\":{\\"input\\":{\\"position\\":\\"left\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}},\\"output\\":{\\"position\\":\\"right\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}}},\\"items\\":[{\\"group\\":\\"input\\",\\"id\\":\\"e7b34e13-0065-445e-9eef-5e583c3925cc\\"},{\\"group\\":\\"output\\",\\"id\\":\\"af599c9a-2cd3-4f93-92f4-79a71ccfe93f\\"}]},\\"id\\":\\"260a25c9-a872-461c-ba93-e0b263dedf6d\\",\\"zIndex\\":3},{\\"position\\":{\\"x\\":580,\\"y\\":160},\\"size\\":{\\"width\\":123,\\"height\\":58},\\"attrs\\":{\\"imgBg\\":{\\"fill\\":\\"#F16407\\"},\\"logo\\":{\\"xlink:href\\":\\"data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='UTF-8'?%3e%3csvg%20width='10px'%20height='10px'%20viewBox='0%200%2010%2010'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20xmlns:xlink='http://www.w3.org/1999/xlink'%3e%3ctitle%3e最终%3c/title%3e%3cg%20id='页面-1'%20stroke='none'%20stroke-width='1'%20fill='none'%20fill-rule='evenodd'%3e%3cg%20id='数据资产管理-数据产品录入-产品血缘'%20transform='translate(-221.000000,%20-339.000000)'%20fill='%23FFFFFF'%20fill-rule='nonzero'%3e%3cg%20id='编组-7备份-7'%20transform='translate(217.000000,%20328.000000)'%3e%3cg%20id='最终'%20transform='translate(4.000000,%2011.000000)'%3e%3cpath%20d='M9.08867478,0.434805428%20L7.60000475,0.434805428%20C7.44272086,0.176090782%207.15365664,0%206.81998492,0%20L3.17997946,0%20C2.84630774,0%202.55771845,0.175688774%202.39995963,0.434805428%20L0.905044317,0.434805428%20C0.405207573,0.435285605%200.000284956099,0.822810395%200,1.30097688%20L0,9.13382855%20C0,9.61125802%200.406335524,10%200.905436131,10%20L9.09452825,10%20C9.59428188,9.99929649%209.99921637,9.61191686%2010,9.13382855%20L10,1.30097688%20C9.99804093,0.821191195%209.59016189,0.433621737%209.08867478,0.434805428%20Z%20M6.81821582,0.874010613%20L6.81998492,1.73918821%20L3.18183167,1.73478845%20L3.18183167,0.874010613%20L3.17997946,0.869499187%20L6.81821582,0.874010613%20Z%20M0.909093068,9.13382855%20L0.905044317,1.30430462%20L2.27273861,1.30430462%20L2.27273861,1.73478845%20C2.27273861,2.21657301%202.67954906,2.60879907%203.17997946,2.60879907%20L6.81998492,2.60879907%20C7.32264748,2.60654335%207.72851995,2.21557916%207.72726139,1.73478845%20L7.72726139,1.30430462%20L9.09090693,1.30097688%20L9.09452825,9.13046731%20L0.909093068,9.13382855%20Z%20M7.16321454,4.0635441%20C6.97790997,3.90134494%206.69037739,3.91369553%206.52086413,4.09089183%20L4.36544432,6.34472878%20L3.48316088,5.3991607%20C3.31597476,5.21999902%203.02863215,5.20439886%202.84140412,5.36437582%20C2.65409298,5.52431927%202.63782673,5.79918124%202.80504847,5.97823125%20L4.0222622,7.28261404%20C4.107939,7.37439475%204.23009018,7.42714717%204.358641,7.42788419%20L4.36139557,7.42788419%20C4.48868784,7.42788419%204.6104947,7.3764718%204.69680077,7.28653362%20L7.19182888,4.67792439%20C7.36124716,4.50060525%207.34840039,4.22555343%207.16321454,4.0635441%20L7.16321454,4.0635441%20Z'%20id='形状'%3e%3c/path%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/g%3e%3c/svg%3e\\"},\\"nodeName\\":{\\"text\\":\\"节点名称\\"},\\"typeName\\":{\\"text\\":\\"最终数据\\"}},\\"shape\\":\\"end-canvas-node\\",\\"ports\\":{\\"groups\\":{\\"input\\":{\\"position\\":\\"left\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}},\\"output\\":{\\"position\\":\\"right\\",\\"attrs\\":{\\"circle\\":{\\"r\\":4,\\"magnet\\":true,\\"stroke\\":\\"#1F5AFF\\",\\"strokeWidth\\":1,\\"fill\\":\\"#fff\\",\\"style\\":{\\"visibility\\":\\"hidden\\"}}}}},\\"items\\":[{\\"group\\":\\"input\\",\\"id\\":\\"d43131ff-e583-48c2-94d6-e6711eac6de9\\"}]},\\"id\\":\\"59c65173-b67b-47dd-a62b-f4966f1afb89\\",\\"zIndex\\":4}]}",
                        "other": "",
                        "qualificationDoc": {
                            "dataSampleAttach": "",
                            "complianceAndLegalStatementAttach": "a6b4e7f6adec4d0bbebec98cbb68e7fd.pdf",
                            "dataSourceStatementAttach": "3e46dcdd8e0749c2baf7689a052f397c.pdf",
                            "safeLevelAttach": "",
                            "evaluationReportAttach": "",
                            "complianceSelfCheckManualAttach": "",
                            "otherAttach": ""
                        },
                        "serviceNodes": [
                            {
                                "serviceNodeId": "49133010866230119576440HV7OJNXS3",
                                "serviceNodeName": "tee",
                                "serviceNodeLocation": "浙江杭州",
                                "pushStatus": "ONLINE"
                            }
                        ],
                        "billingMethod": "02",
                        "purchaseUnit": "包月",
                        "price": "1月500元",
                        "dataType": "STRUCTURED",
                        "dataType1": "数据集",
                        "accessWay": "API",
                        "apiQueryWay": "REALTIME",
                        "mpcPurpose": [],
                        "debugDataSource": "none",
                        "debugDataPath": null,
                        "dataSchema": [],
                        "separator": ",",
                        "hasHeader": 1,
                        "apiSourceMetadata": {
                            "url": "http://10.0.0.26:7201/api/random-strings",
                            "method": "GET",
                            "params": [],
                            "body": "",
                            "bodyType": "NONE",
                            "headers": [],
                            "response": [],
                            "responseEcho": "{\\"responseData\\":[{\\"uuid\\":\\"a2e0c709-9bd2-4941-86ef-71c264e05986\\",\\"field\\":\\"根节点\\",\\"type\\":\\"ARRAY\\",\\"desc\\":\\"\\",\\"sensitiveLevel\\":null,\\"id\\":false,\\"children\\":[{\\"uuid\\":\\"14d64774-cb4a-49a1-9b59-3d15c00080d3\\",\\"field\\":\\"ITEMS\\",\\"type\\":\\"STRING\\",\\"desc\\":\\"\\",\\"sensitiveLevel\\":null,\\"id\\":false,\\"children\\":[],\\"level\\":2,\\"isRoot\\":false,\\"isItems\\":true,\\"disabled\\":false,\\"showChildren\\":true,\\"path\\":[{\\"dataPath\\":\\"根节点\\",\\"dataType\\":\\"ARRAY\\",\\"isItems\\":false},{\\"dataPath\\":\\"ITEMS\\",\\"dataType\\":\\"STRING\\",\\"isItems\\":true}]}],\\"level\\":1,\\"isRoot\\":true,\\"isItems\\":false,\\"disabled\\":true,\\"showChildren\\":true,\\"path\\":[{\\"dataPath\\":\\"根节点\\",\\"dataType\\":\\"ARRAY\\",\\"isItems\\":false}]}],\\"checkedRowKeysList\\":[],\\"primaryKeyNodes\\":[]}",
                            "dataPath": [],
                            "datasetId": null,
                            "isBatchParams": false,
                            "batchParamsFileId": "",
                            "batchParamsUrl": null,
                            "batchParamsPath": null,
                            "localBatchParamsPath": null,
                            "isAuth": false,
                            "strategy": null
                        },
                        "fileSourceMetadata": {
                            "dataAssetFileId": "",
                            "debugDataPath": null,
                            "dataAssetFilePath": null,
                            "dataAssetFileHash": null
                        },
                        "databaseSourceMetadata": {
                            "tableName": null,
                            "name": null,
                            "desc": null,
                            "jdbcUrl": null,
                            "username": null,
                            "password": null,
                            "datasourceType": null,
                            "access_id": null,
                            "access_key": null,
                            "project_name": null,
                            "hasKerberos": false,
                            "keytabFilePath": null,
                            "kerberosPrincipal": null,
                            "kerberosConfPath": null,
                            "syncRule": null,
                            "partitionColumns": null
                        },
                        "aiSortMetadata": {
                            "sourceId": null,
                            "id": null,
                            "tableName": "",
                            "tableDesc": "",
                            "schemaName": ""
                        },
                        "exchangePluginIds": null,
                        "certificatePluginIds": null,
                        "mpcOpenAPIId": null,
                        "extractResponse": null,
                        "dataList": [],
                        "gatewayServiceRouteId": "gWOMBxlPRcawHK9wAfBGYg",
                        "prepareStatus": "AVAILABLE",
                        "publishStatus": "2",
                        "itemStatus": "item_status2"
                    }""", DataProductVO.class);
        JSONObject others = new JSONObject();
        others.set("assetId", product.getId());
        others.set("productNameCN", product.getDataProductNameCN());
        others.set("industry1", product.getIndustry1());
        others.set("region1", product.getRegion1());
        others.set("deliveryModes", product.getDeliveryModes());
        CompanyDTO company = product.getProvider().getCompany();
        others.set("clientPlatformUniqueNo", company.getNodeId());
        others.set("userId", product.getUserId());
        others.set("username", product.getProvider().getUsername());
        others.set("organizationName", company.getOrganizationName());
        others.set("routerId", company.getNodeId());
        others.set("platformId", product.getPlatformId());
        others.set("companyId", company.getId());
        others.set("deliveryModes", product.getDeliveryModes());
        others.set("billingMethod", product.getBillingMethod());
        others.set("purchaseUnit", product.getPurchaseUnit());
        others.set("price", product.getPrice());

        ganzhouApiClient.dataProductInfoRegist(DataProductInfoRegist.builder()
                .outerProductId(product.getId())
                .productType(product.getType())
                .productName(product.getDataProductName())
                .validStartTime(product.getDataCoverageTimeStart())
                .validEndTime(product.getDataCoverageTimeEnd())
                .industry(product.getIndustry())
                .productRegion(product.getRegion())
                .personalInformation(product.getPersonalInformation())
                .description(product.getDescription())
                .deliveryMethod(product.getDeliveryMethod() != null ? product.getDeliveryMethod() : (
                        !CollectionUtils.isEmpty(product.getDeliveryModes()) ? deliveryModeMapping.apply(product.getDeliveryModes().getFirst()) : null
                ))
                .limitations(product.getLimitations())
                .authorize(product.getAuthorize())
                .dataSubject("01")
                .dataSize("1GB")
                .updateFrequency(product.getUpdateFrequency())
                .others(JSONUtil.toJsonStr(others))
                .providerName(company.getOrganizationName())
                .providerType(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "02" : "03")
                .entityInformation(JSONUtil.toJsonStr(company))
                .identityId(company.getCreditCode())
                .providerDesc(company.getAccessType().getDesc())
                .operatorName(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeName() : company.getDelegateName())
                .operatorTelephone(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? "" : company.getDelegateContact())
                .operatorIdCard(AccessType.LEGAL_PERSON.equals(company.getAccessType()) ? company.getLegalRepresentativeIdNumber() : company.getDelegateIdNumber())
                .commission(company.getAuthorizationLetter())
                .dataSample(product.getQualificationDoc().getDataSampleAttach())
                .complianceAndLegalStatement(product.getQualificationDoc().getComplianceAndLegalStatementAttach())
                .dataSourceStatement(product.getQualificationDoc().getDataSourceStatementAttach())
                .safeLevel(product.getQualificationDoc().getSafeLevelAttach())
                .evaluationReport(product.getQualificationDoc().getEvaluationReportAttach())
                .entityId(product.getUserId())
                .entityCode(company.getNodeId().substring(1, 19))
                .platformId(product.getPlatformId())
                .resourceCodes(JSONUtil.toJsonStr(List.of(product.getId())))
                .dpe(product.getProvider().getCompany().getNodeId())
                .build());
    }

    @Test
    void dataProductInfoUpdate() {
    }

    @Test
    void dataProductInfoRevoke() {
    }

    @Test
    void dataProductPublish() {
    }

    @Test
    void dataProductUnpublish() {
    }

    @Test
    void dataResourceRegistry() {
        DataResourceVO dataResource = JSONUtil.toBean(
                """
                {
                        "id": "14945a13f2824dd9a97ac466e84d4a26",
                        "dataResourcePlatformId": "79133010866230119576440JR7A14ZMB",
                        "userId": "e1bac37003714e148052afc69cf6c152",
                        "provider": {
                            "phone": "***********",
                            "email": "<EMAIL>",
                            "routerId": "591330108662301195764408VB2Z4MH0",
                            "routerName": "R_电信测试one",
                            "userId": null,
                            "userIdShuhan": "1913514495427248130",
                            "username": null,
                            "company": {
                                "id": 1745052236949,
                                "thirdBusinessId": "1913496963932782593",
                                "nodeId": "591330108662301195764408VB2Z4MH0",
                                "serviceNode": {
                                    "id": 2,
                                    "nodeId": "58a0af47-6112-4a47-9ffe-e982de777507",
                                    "nodeName": "功能节点6",
                                    "nodeType": "全域功能节点",
                                    "status": "ACTIVE",
                                    "accessTime": "2025-04-19 16:41:45",
                                    "hubInfo": {
                                        "url": "https://10.0.0.5:4036",
                                        "authType": "sign",
                                        "publicKey": null,
                                        "ak": "raBNZDBKNzCnuHoY",
                                        "sk": "tppSWf1VtFdEeoNK6LhTRCePrMwNnRGq",
                                        "certificateNo": "11362790560308141312"
                                    },
                                    "connectorInfo": {
                                        "exposeUrl": "10.128.0.11:8080"
                                    }
                                },
                                "status": "REVIEW_PASS",
                                "schema": "tenant_1745052236949",
                                "businessLicense": "https://125.122.153.239:4443/_data-route/public/bl_1745052301117.png",
                                "accessType": "LEGAL_PERSON",
                                "organizationName": "电信测试one",
                                "creditCode": "913301111111111111",
                                "legalRepresentativeName": "法人239",
                                "legalRepresentativeIdType": "身份证",
                                "legalRepresentativeIdNumber": "220772199901214522",
                                "legalRepresentativeIdExpiry": "2025-04-30",
                                "legalRepresentativeAuthLevel": "1",
                                "authMethod": "0",
                                "registrationAddress": "",
                                "industryType": null,
                                "businessStartDate": "2025-04-01",
                                "businessEndDate": "2025-04-30",
                                "delegateName": "",
                                "delegateIdType": "身份证",
                                "delegateIdNumber": "",
                                "delegateIdExpiry": null,
                                "delegateInstitution": null,
                                "delegateInstitutionCode": null,
                                "delegateContact": "***********",
                                "delegateEmail": "",
                                "delegateAuthLevel": "1",
                                "delegateAuthMethod": null,
                                "delegateRegistrationAddress": "",
                                "delegateIndustryType": null,
                                "delegateTaskScope": null,
                                "delegateAuthorizationStart": null,
                                "delegateAuthorizationEnd": null,
                                "delegateRemarks": "",
                                "validityEndDate": null,
                                "deleted": false,
                                "reviewTime": "2025-04-19T08:45:36.602+00:00",
                                "authorizationLetter": "",
                                "connectorName": "1"
                            }
                        },
                        "dataResourceName": "luvatest_R_51201",
                        "resourceFormat": "数据集",
                        "dataResourceNameCN": null,
                        "industry": "I6311",
                        "industry1": "{\\"industryCode\\":[\\"industry\\",\\"I\\",\\"I63\\",\\"I631\\",\\"I6311\\"],\\"industryName\\":\\"国标行业/信息传输、软件和信息技术服务业/电信、广播电视和卫星传输服务/电信/固定电信服务\\"}",
                        "region": null,
                        "region1": null,
                        "personalInformation": "1",
                        "description": "用户行为数据（如通话记录、流量使用、消费习惯）用于客户画像与精准营销。",
                        "source": "01",
                        "registrationTime": 1746498977992,
                        "registrationUpdateTime": null,
                        "dataType": "STRUCTURED",
                        "dataType1": "数据集",
                        "other": null,
                        "resourceId": "79133010866230119576440JR7A14ZMB",
                        "dataSchema": [
                            {
                                "apiOnline": null,
                                "name": null,
                                "fieldName": "id",
                                "type": "STRING",
                                "sensitiveLevel": null,
                                "comment": "",
                                "classifyName": null,
                                "level": null,
                                "isSensitive": null,
                                "combingExplain": null,
                                "allowQuery": true,
                                "authorization": false,
                                "update": false,
                                "create": false,
                                "originalColumnName": null,
                                "originalDataType": null,
                                "id": false
                            },
                            {
                                "apiOnline": null,
                                "name": null,
                                "fieldName": "phone",
                                "type": "STRING",
                                "sensitiveLevel": null,
                                "comment": "",
                                "classifyName": null,
                                "level": null,
                                "isSensitive": null,
                                "combingExplain": null,
                                "allowQuery": true,
                                "authorization": false,
                                "update": false,
                                "create": false,
                                "originalColumnName": null,
                                "originalDataType": null,
                                "id": false
                            },
                            {
                                "apiOnline": null,
                                "name": null,
                                "fieldName": "name",
                                "type": "STRING",
                                "sensitiveLevel": null,
                                "comment": "",
                                "classifyName": null,
                                "level": null,
                                "isSensitive": null,
                                "combingExplain": null,
                                "allowQuery": true,
                                "authorization": false,
                                "update": false,
                                "create": false,
                                "originalColumnName": null,
                                "originalDataType": null,
                                "id": false
                            },
                            {
                                "apiOnline": null,
                                "name": null,
                                "fieldName": "tel_num",
                                "type": "STRING",
                                "sensitiveLevel": null,
                                "comment": "",
                                "classifyName": null,
                                "level": null,
                                "isSensitive": null,
                                "combingExplain": null,
                                "allowQuery": true,
                                "authorization": false,
                                "update": false,
                                "create": false,
                                "originalColumnName": null,
                                "originalDataType": null,
                                "id": false
                            },
                            {
                                "apiOnline": null,
                                "name": null,
                                "fieldName": "area",
                                "type": "STRING",
                                "sensitiveLevel": null,
                                "comment": "",
                                "classifyName": null,
                                "level": null,
                                "isSensitive": null,
                                "combingExplain": null,
                                "allowQuery": true,
                                "authorization": false,
                                "update": false,
                                "create": false,
                                "originalColumnName": null,
                                "originalDataType": null,
                                "id": false
                            }
                        ],
                        "itemStatus": "item_status2"
                    }""", DataResourceVO.class);
        JSONObject others = new JSONObject();
        others.set("assetId", dataResource.getId());
        others.set("resourceNameCN", dataResource.getDataResourceNameCN());
        others.set("industry1", dataResource.getIndustry1());
        others.set("region1", dataResource.getRegion1());
        others.set("clientPlatformUniqueNo", dataResource.getProvider().getCompany().getNodeId());
        others.set("userId", dataResource.getUserId());
        others.set("username", dataResource.getProvider().getUsername());
        others.set("organizationName", dataResource.getProvider().getCompany().getOrganizationName());
        others.set("routerId", dataResource.getProvider().getCompany().getNodeId());
        others.set("platformId", dataResource.getDataResourcePlatformId());
        others.set("companyId", dataResource.getProvider().getCompany().getId());
        others.set("company", dataResource.getProvider().getCompany());
        String dataResourceInfo = ganzhouApiClient.dataResourceRegistry(DataResourceRegistry.builder()
                .resourceName(dataResource.getDataResourceName())
                .outerResourceId(dataResource.getId())
                .dataType("01")
                .resourceType("2")
                .industry(dataResource.getIndustry())
                .resourceOwner(dataResource.getProvider().getCompany().getOrganizationName())
                .resourceOwnerId(dataResource.getProvider().getCompany().getNodeId())
                .resourceOwnerCode(dataResource.getProvider().getCompany().getNodeId())
                .capacity("1GB")
                .contacter(dataResource.getProvider().getCompany().getOrganizationName())
                .contactInformation(dataResource.getProvider().getCompany().getDelegateContact())
                .resourceAbstract(dataResource.getDescription())
                .dataSource(dataResource.getSource())
                .personalInformation(dataResource.getPersonalInformation())
                .others(JSONUtil.toJsonStr(others))
                .limitations("使用限制")
                .authorize("0")
                .dpe(PLATFORM_ID)
                .resourceItemList(dataResource.getDataSchema() == null ? null : JSONUtil.toJsonStr(dataResource.getDataSchema().stream()
                        .map(dataSchemaBO -> ResourceItemAddCmd.builder()
                                .code(dataSchemaBO.getFieldName())
                                .name(dataSchemaBO.getName())
                                .fieldType(dataSchemaBO.getType())
                                .primaryFlag(dataSchemaBO.isId() ? "1" : "0")
                                .fieldDesc(dataSchemaBO.getComment())
//                                .level(dataSchemaBO.getLevel())
                                .build())
                        .toList()))
                .build());
    }

    @Test
    void dataResourceRegistryUpdate() {
    }

    @Test
    void dataResourceRegistryRevoke() {
    }
}