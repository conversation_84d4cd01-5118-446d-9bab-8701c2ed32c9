package com.ailpha.ailand.dataroute.endpoint.dataasset.infrastructure.aigate;

import com.ailpha.ailand.dataroute.endpoint.common.config.RestClientConfig;
import com.ailpha.ailand.dataroute.endpoint.third.aigate.AIGateClient;
import com.ailpha.ailand.dataroute.endpoint.third.config.AIGateConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest
@TestPropertySource("classpath:application.properties")
@ContextConfiguration(classes = {AIGateClientTestConfig.class})
@EnableConfigurationProperties(value = AIGateConfig.class)
@Import({AIGateClient.class, RestClientConfig.class})
class AIGateClientTest {
    @Autowired
    private AIGateClient aigateClient;

    @Value("${data-route.aigate.base-url}")
    String baseUrl;

    @Test
    void createAsset() {
        System.out.println(baseUrl);
//        String jdbcUrl = "jdbc:postgresql://**********:3306/ailand-oauth2";
//        String cleanURI = jdbcUrl.substring(5);
//        URI uri = URI.create(cleanURI);
//        AIGateResponse<CreateAssetByRouterResponse> assetByRouterResponse = aigateClient.createAsset(CreateAssetByRouterRequest.builder()
//                .ip(uri.getHost())
//                .port(String.valueOf(uri.getPort()))
//                .dbType(DbType.MYSQL)
//                .build());
//        System.out.println(assetByRouterResponse);
    }
}