package com.ailpha.ailand.dataroute.endpoint.user.service;

import com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.domain.NegotiateTransfer;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.mapstruct.NegotiateMapstruct;
import com.ailpha.ailand.dataroute.endpoint.deliveryScene.repository.NegotiateTransferRepository;
import com.ailpha.ailand.dataroute.endpoint.order.schedule.OrderSyncSchedule;
import com.ailpha.ailand.dataroute.endpoint.third.response.NegotiateDataTransferDTO;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.Cache;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Slf4j
@ActiveProfiles({"local205"})
@SpringBootTest(classes = DataRouteEndpointApplication.class)
public class UserCacheTest {

    @Autowired
    private Cache<String, String> userCache;


    @Autowired
    private OrderSyncSchedule orderSyncSchedule;

    @Autowired
    private NegotiateTransferRepository negotiateTransferRepository;

    @Autowired
    private NegotiateMapstruct negotiateMapstruct;

    @Test
    void userCacheTest() {
//        List<NegotiateTransfer> all = negotiateTransferRepository.findAll();
//        System.out.println("all = " + all);
        NegotiateDataTransferDTO transferDTO = new NegotiateDataTransferDTO();
        transferDTO.setNodeId("serviceNodeId");
        transferDTO.setTradingStrategyCode("2");
        transferDTO.setTradingStrategyName("3");
        transferDTO.setCtrlInstructionId("4");
        transferDTO.setTransactionExecutionStrategy("5");
        transferDTO.setTransferMode("2");


        NegotiateTransfer negotiateTransfer = negotiateMapstruct.negotiateDataTransferTo(transferDTO);
        System.out.println("negotiateTransfer = " + negotiateTransfer);

    }

}
