package com.ailpha.ailand.dataroute.endpoint.user.service;

import com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.Cache;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@Slf4j
@ActiveProfiles({"r1"})
@SpringBootTest(classes = DataRouteEndpointApplication.class)
public class UserCacheTest {

    @Autowired
    private Cache<String, String> userCache;

    @Test
    void userCacheTest() {
        userCache.put("123", "456");
        String o = userCache.get("123");
        log.info("user cache = {}", o);
    }

}
