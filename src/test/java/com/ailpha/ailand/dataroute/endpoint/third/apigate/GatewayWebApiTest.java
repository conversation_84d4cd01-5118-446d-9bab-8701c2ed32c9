package com.ailpha.ailand.dataroute.endpoint.third.apigate;

import com.ailpha.ailand.dataroute.endpoint.common.config.AiLandProperties;
import com.ailpha.ailand.dataroute.endpoint.common.utils.DateTimeUtils;
import com.ailpha.ailand.dataroute.endpoint.common.utils.JacksonUtils;
import com.ailpha.ailand.dataroute.endpoint.third.config.GatewayConfig;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ContextConfiguration;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.ailpha.ailand.dataroute.endpoint.third.apigate.CreateServiceRequest.InvokeParam.DEFAULT_PLUGINS;
import static org.assertj.core.api.Assertions.assertThat;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest
@ContextConfiguration(classes = {GatewayWebApiConfig.class})
//@TestPropertySource("classpath:application.properties")
@Import({GatewayWebApiTestClient.class})
@EnableConfigurationProperties(value = {AiLandProperties.class, GatewayConfig.class})
class GatewayWebApiTest {

    @Autowired
    GatewayWebApiTestClient gatewayWebApi;

    private static String API_SERVICE_NAME = "luva";
    private static String API_ROUTE_NAME = "luva";
    private static String CONSUMER_NAME = "luva";

    @BeforeAll
    static void setUpServiceNameAndApiName() {
        String date = DateTimeUtils.formatDate(new Date(), "yyyyMMdd");
        API_SERVICE_NAME = API_SERVICE_NAME + date;
        API_ROUTE_NAME = API_SERVICE_NAME + date + ((int) (Math.random() * 10));
        CONSUMER_NAME = CONSUMER_NAME + DateTimeUtils.curTime(date);
    }

    @Test
    @Order(1)
    public void describeServiceByServiceName() {
        DescribeServicesResponse describeServicesResponse = gatewayWebApi.describeServices("API_SERVICE_NAME");
        System.out.println(JacksonUtils.obj2json(describeServicesResponse));
        assertThat(describeServicesResponse.success).isTrue();
        assertThat(describeServicesResponse.data.getInvokeResult().getRows().size()).isEqualTo(0);
    }

    @Test
    @Order(2)
    void createService() throws URISyntaxException {
        CreateServiceRequest.InvokeParam.UpStream upStream = CreateServiceRequest.InvokeParam.UpStream.builder().build();
        URI uri = new URI("http://***********:8080/data-asset");
        upStream.addNode(uri.getAuthority(), 1);
        upStream.setScheme(uri.getScheme());
        CreateServiceRequest.InvokeParam invokeParam = CreateServiceRequest.InvokeParam.builder()
                .name(API_SERVICE_NAME)
                .desc("test service for " + API_SERVICE_NAME)
//                .hosts(List.of("baidu.com"))
                .plugins(DEFAULT_PLUGINS)
                .upstream(upStream)
                .build();

        CreateServiceRequest createServiceRequest = CreateServiceRequest.builder()
                .invokeParam(invokeParam)
                .build();
        /**
         * {
         *     "create_time": 1732253507449,
         *     "desc": "test11222222a",
         *     "hosts": [
         *         "hello.world"
         *     ],
         *     "id": "bfLhVEOmSQW3MrKDSAYJPA",
         *     "name": "test11222222a",
         *     "plugins": {
         *         "key-auth": {
         *             "header": "apikey",
         *             "query": "apikey",
         *             "hide_credentials": false
         *         },
         *         "consumer-restriction": {
         *             "whitelist": [
         *                 "△"
         *             ],
         *             "rejected_code": 401,
         *             "type": "consumer_name",
         *             "rejected_msg": "Authorization Invalid.Please Contact The Administrator To Enable Permissions "
         *         }
         *     },
         *     "update_time": 1732253507449,
         *     "upstream": {
         *         "keepalive_pool": {
         *             "idle_timeout": 60,
         *             "requests": 1000,
         *             "size": 320
         *         },
         *         "nodes": {
         *             "***********:9200": 1
         *         },
         *         "pass_host": "pass",
         *         "retries": 3,
         *         "retry_timeout": 5,
         *         "scheme": "http",
         *         "timeout": {
         *             "connect": 6,
         *             "read": 6,
         *             "send": 6
         *         },
         *         "type": "roundrobin"
         *     }
         * }
         */
        System.out.println(JacksonUtils.obj2json(createServiceRequest));
        CreateServiceResponse createServiceResponse = gatewayWebApi.createService(createServiceRequest);
        System.out.println(JacksonUtils.obj2json(createServiceResponse));
        // "message":"service name exists"
//        assertThat(createServiceResponse.code).isEqualTo("appErr.CreateService.10000");
    }

    @Test
    @Order(11)
    public void describeRouteByRouteName() {
        DescribeRoutesResponse describeRoutesResponse = gatewayWebApi.describeRoutes(null);
        System.out.println(JacksonUtils.obj2json(describeRoutesResponse));
        assertThat(describeRoutesResponse.success).isTrue();
        assertThat(describeRoutesResponse.data.getInvokeResult().getRows().size()).isEqualTo(0);
    }

    @Test
    @Order(12)
    void createRoute() throws URISyntaxException {
        DescribeServicesResponse describeServicesResponse = gatewayWebApi.describeServices(API_SERVICE_NAME);
        DescribeServicesResponse.ServiceDescription serviceDescription = describeServicesResponse.getData().getInvokeResult().getRows().getFirst();
        URI uri = new URI("http://***********:8080/data-asset");
        /**
         * {"invokeParam":"{\"name\":\"test24111701\",\"desc\":\"描述\",\"priority\":0,\"methods\":[\"GET\",\"POST\"],\"plugins\":{},\"labels\":{\"API_VERSION\":\"V1\"},\"service_name\":\"luva\",\"uri\":\"/test24111701\",\"vars\":[[\"http_route-id\",\"==\",\"route-1000\"]],\"service_id\":\"Q8Uy0Z4tSHiWQBKSvWA6dw\"}"}
         *{
         *     "name": "test24111701",
         *     "desc": "描述",
         *     "priority": 0,
         *     "methods": [
         *         "GET",
         *         "POST"
         *     ],
         *     "plugins": {},
         *     "labels": {
         *         "API_VERSION": "V1"
         *     },
         *     "service_name": "luva",
         *     "uri": "/test24111701",
         *     "vars": [
         *         [
         *             "http_route-id",
         *             "==",
         *             "route-1000"
         *         ]
         *     ],
         *     "service_id": "Q8Uy0Z4tSHiWQBKSvWA6dw"
         * }
         */
        CreateRouteResponse createRouteResponse = gatewayWebApi.createRoute(
                CreateRouteRequest.builder()
                        .invokeParam(CreateRouteRequest.InvokeParam.builder()
                                .serviceId(serviceDescription.getId())
                                .serviceName(serviceDescription.getName())
                                .name(API_ROUTE_NAME)
                                .uri(uri.getPath())
                                .methods(List.of("GET", "POST"))
                                .labels(Map.of("API_VERSION", "V1", "test", "hello"))
                                .vars(List.of(List.of("http_route-id", "==", "route-1000")))
                                .build())
                        .build());
        ROUTE_ID = createRouteResponse.getData().getInvokeResult().getId();
        System.out.println(JacksonUtils.obj2json(createRouteResponse));
    }

    private static String ROUTE_ID = "";

    @Test
    @Order(14)
    void createConsumer() {
        CreateConsumerResponse createConsumerResponse = gatewayWebApi.createConsumer(CreateConsumerRequest.builder()
                .invokeParam(CreateConsumerRequest.InvokeParam.builder()
                        .username(CONSUMER_NAME)
                        .plugins(CreateConsumerRequest.PluginWrapper.builder()
                                .keyAuth(CreateConsumerRequest.KeyAuth.builder()
                                        .key(UUID.randomUUID().toString())
                                        .build())
                                .build())
                        .build())
                .build());
        System.out.println(JacksonUtils.obj2json(createConsumerResponse));
    }

    @Test
    @Order(15)
    void createAuthzConfig() {
        DescribeRouteResponse describeRouteResponse = gatewayWebApi.describeRoute(DescribeRouteRequest.builder()
                .id("rh6v21kcTqWxSZ2XgH9JOw")
                .build());
        System.out.println(JacksonUtils.obj2json(describeRouteResponse));
        DescribeRouteResponse.InvokeResult result = describeRouteResponse.getData().getInvokeResult();
        CreateAuthzConfigRequest.InvokeParam invokeParam = new CreateAuthzConfigRequest.InvokeParam();
        invokeParam.addAllowed(CreateAuthzConfigRequest.Allowed.builder()
                .serviceId(result.getServiceId())
                .routers(List.of(result.getId()))
                .dataSource(List.of(CreateAuthzConfigRequest.DataSource.builder()
                        .serviceId(result.getServiceId())
                        .key(result.getId())
                        .value(result.getName())
                        .build()))
                .build());
        CreateAuthzConfigResponse authzConfig = gatewayWebApi.createAuthzConfig(CreateAuthzConfigRequest.builder()
                .username(CONSUMER_NAME)
                .invokeParam(invokeParam)
                .build());
        System.out.println(JacksonUtils.obj2json(authzConfig));
    }

    @Test
    @Order(16)
    void describeAuthzConfig() {
        DescribeAuthzConfigResponse describeAuthzConfigResponse = gatewayWebApi.describeAuthzConfig(DescribeAuthzConfigRequest.builder()
                .username("zt")
                .build());
        System.out.println(JacksonUtils.obj2json(describeAuthzConfigResponse));
    }

    @Test
    @Order(17)
    void describeConsumer() {
        DescribeConsumerResponse describeConsumerResponse = gatewayWebApi.describeConsumer(DescribeConsumerRequest.builder()
                .username("zt")
                .build());
        System.out.println(JacksonUtils.obj2json(describeConsumerResponse));
    }

    @Test
    @Order(18)
    void describeRoute() {
        DescribeRouteResponse describeConsumerResponse = gatewayWebApi.describeRoute(DescribeRouteRequest.builder()
                .id("rh6v21kcTqWxSZ2XgH9JOw")
                .build());
        System.out.println(JacksonUtils.obj2json(describeConsumerResponse));
    }

    @Test
    @Order(19)
    void describeService() {
        DescribeServicesResponse describeConsumerResponse = gatewayWebApi.describeServices("", 1, 10);
        System.out.println(JacksonUtils.obj2json(describeConsumerResponse));
    }
}
