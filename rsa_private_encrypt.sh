#!/bin/bash

# ==================== 可配置参数 ====================
# 需根据实际环境修改以下路径
PRIVATE_KEY_FILE="./rsaPrivateKey.txt"  # 私钥文件路径（默认当前目录）
ENCRYPT_FILE="./encrypt.txt"            # 加密结果保存路径（默认当前目录encrypt.txt）
# ====================================================

# 清理临时文件的函数（确保退出时执行）
cleanup() {
    rm -f "$TEMP_PEM" "$INPUT_TEMP" "$OUTPUT_TEMP"
}
trap cleanup EXIT

# 参数校验（仅需1个参数：待加密字符串）
if [ $# -ne 1 ]; then
    echo "使用方法: $0 <待加密字符串>"
    echo "示例: $0 '需要加密的敏感信息'"
    exit 1
fi

# 定义临时文件
TEMP_PEM=$(mktemp)       # 带PEM头的私钥临时文件
INPUT_TEMP=$(mktemp)     # 待加密字符串临时文件
OUTPUT_TEMP=$(mktemp)    # 加密二进制结果临时文件
INPUT_STR="$1"           # 从命令行获取待加密字符串

# 检查私钥文件是否存在
if [ ! -f "$PRIVATE_KEY_FILE" ]; then
    echo "错误: 私钥文件 $PRIVATE_KEY_FILE 不存在"
    exit 1
fi

# 修复私钥PEM格式（添加头尾）
{
    echo "-----BEGIN RSA PRIVATE KEY-----"
    cat "$PRIVATE_KEY_FILE"
    echo "-----END RSA PRIVATE KEY-----"
} > "$TEMP_PEM" || { echo "错误: 私钥文件读取失败"; exit 1; }

# 准备输入数据（-n避免换行符）
echo -n "$INPUT_STR" > "$INPUT_TEMP" || { echo "错误: 输入数据写入临时文件失败"; exit 1; }

# 执行RSA私钥加密（签名模式）
if ! openssl rsautl -sign -inkey "$TEMP_PEM" -in "$INPUT_TEMP" -out "$OUTPUT_TEMP"; then
    echo "加密失败，可能原因："
    echo "1. 私钥格式不正确（非RSA私钥）"
    echo "2. 输入字符串过长（2048位密钥最大支持245字节）"
    echo "3. openssl工具异常"
    exit 1
fi

# 获取Base64结果
base64_result=$(base64 "$OUTPUT_TEMP" | tr -d '\n')

# 输出到控制台
echo "加密结果（Base64）: "
echo "$base64_result"

# 输出到内置文件（自动创建目录）
output_dir=$(dirname "$ENCRYPT_FILE")
if [ ! -d "$output_dir" ]; then
    mkdir -p "$output_dir" || { echo "错误: 输出目录 $output_dir 创建失败"; exit 1; }
fi
echo "$base64_result" > "$ENCRYPT_FILE" || { echo "错误: 加密文件 $ENCRYPT_FILE 写入失败"; exit 1; }
echo "已保存到加密文件: $ENCRYPT_FILE"
    
