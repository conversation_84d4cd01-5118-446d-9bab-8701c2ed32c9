# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Building the Project
```bash
# Build the project with Maven
mvn clean compile

# Package the application
mvn clean package

# Run with specific profile (dev is default)
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### Testing
```bash
# Run tests (currently skipped in configuration)
mvn test

# Security dependency check
mvn dependency-check:check
```

### Running the Application
```bash
# Development mode
./start.sh --spring.profiles.active=dev

# Stop the application
./stop.sh
```

## Code Architecture

### Core Package Structure
- `com.ailpha.ailand.dataroute.endpoint` - Main application package
- `base/` - Base capability management and API gateway responses
- `common/` - Cross-cutting concerns (config, filters, interceptors, utils)
- `dataasset/` - Data asset management (delivery, products, resources)
- `connector/` - Router and connection management
- `openapi/` - External API platform integration
- `company/` - Company/organization management
- `user/` - User authentication and management

### Key Technologies
- **Spring Boot 3.3.10** with Java 21
- **Spring Security** for authentication/authorization
- **Spring Data JPA** with QueryDSL for database operations
- **Retrofit** for HTTP client integrations
- **Knife4j** for API documentation
- **Flyway** for database migrations
- **MapStruct** for object mapping
- **Lombok** for boilerplate reduction

### Database Configuration
- Primary database: HighgoDB (PostgreSQL-compatible)
- Connection pooling: HikariCP
- Schema management: Flyway migrations in `src/main/resources/db/migration/`
- Multi-tenant support with separate schemas

### Security Architecture
- Spring Security with custom authentication providers
- JWT/session-based authentication
- CSRF protection with whitelisted APIs
- Role-based access control (RBAC)
- API key/secret authentication for external platforms

### Key Domain Models
- **DataAsset** - Core data asset management
- **Company** - Organization/tenant management
- **DataProduct** - Published data products
- **DataResource** - Raw data resources
- **DeliveryScene** - Data delivery scenarios

### Configuration Profiles
- `dev` - Development environment (default)
- `test` - Testing environment
- `prod` - Production environment

### External Integrations
- **MinIO** - File storage and management
- **Router Agent** - Data routing services
- **IAM Server** - Identity and access management
- **License Server** - License validation

### Common Development Patterns
- Use MapStruct for DTO/Entity conversions
- QueryDSL for type-safe database queries
- Retrofit for external service calls
- Spring Security for endpoint protection
- Custom aspects for logging and monitoring

## Environment Variables and Configuration
- Database credentials encrypted with Jasypt
- Profile-specific configurations in `application-{profile}.yaml`
- Default encryption password: `ailand@VFR_`
- Key management through custom KeyStore configuration