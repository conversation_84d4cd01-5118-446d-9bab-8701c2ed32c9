# 菜单管理

## 项目资料

需求prd链接：

技术方案链接：

计划发布日期：

PD人员：xxx

开发人员：rick.zhou

测试人员：xxx

## 需求价值

动态控制用户菜单权限，提高用户管理效率

## 需求分析

1.  支持超管用户（super\_admin）控制接入主体管理员（company\_admin）可见菜单
    
2.  支持接入主体管理员用户（company\_admin）控制普通用户（TRADER）可见菜单
    

## 应用服务

| 服务域 | 服务接口 | 服务方法 | 方法流程描述 | 校验约束 |
| --- | --- | --- | --- | --- |
| 用户模块 | 菜单管理 | 用户列表查询 | 列表字段：主体名称、用户名称<br>*   如果当前登陆人是超管，查询所有角色为 company\_admin 的用户<br>    <br>*   如果当前登陆人是接入主体管理员，则查询所有普通用户列表 |  |
|  |  | 用户详情 | 展示用户基本信息，以及菜单权限信息 |  |
|  |  | 查询菜单权限 | 查询属于该用户的菜单权限列表，其中通过 enabled 字段表示该菜单权限是否被启用 |  |
|  |  | 菜单权限变更 | 用户菜单权限修改，只需要接收启用的菜单权限 | 所有参数必填<br>1.  用户 id<br>    <br>2.  菜单 id（数组） |

## 改造方案

### 后端工程

现有工程中已存在基于 RABC 实现的权限控制方案，包括 User、Role、Menu、Permisson，现在缺少对 Permission 表的 curd 操作

1.  新增Permission 的 curd 业务代码，其中接口权限限制 trader 用户调用
    
2.  超管可以控制所有接入主体菜单权限，接入主体管理员可以控制当前主体下的所有用户，即超管变更完菜单权限，需要同步接入主体租户库，接入主体变更完用户菜单权限，只需要变更当前库数据
    

### 前端工程

1.  新增菜单管理页面，只允许角色为SUPER\_ADMIN 和COMPANY\_ADMIN 可见
    
    1.  超管查询所有主体列表，接入主体管理员查询所有用户列表
        
2.  列表中支持查询详情
    
    1.  根据菜单权限接口返回数据，展示树形可勾选，根据 enabled 判断是否选中
        
3.  列表支持编辑
    

1.  编辑时调用**查询菜单权限接口**展示菜单树

2.  调用**菜单权限变更** 时只需要把选中的菜单传给接口

## 接口文档

### 1. 用户列表查询接口

#### 接口签名及描述
* 签名：SuccessResponse<List<UserListResponse>> com.ailpha.ailand.dataroute.endpoint.user.controller.MenuManagementController.queryUserList(MenuManagementRequest request)
* 描述：用户列表查询。如果当前登陆人是超管，查询所有角色为company_admin的用户；如果当前登陆人是接入主体管理员，则查询所有普通用户列表

#### 接口入参
```json
{
  "request": {
    "id": "主键ID",
    "userId": "用户ID",
    "menuIds": ["菜单ID1", "菜单ID2"],
    "createTime": "2025-01-01 12:34:56",
    "updateTime": "2025-01-01 12:34:56",
    "pageNo": 1,
    "pageSize": 20
  }
}
```

#### 接口出参
```json
{
  "success": true,
  "errorCode": "",
  "errorMessage": "",
  "data": [
    {
      "id": "用户ID",
      "companyName": "主体名称",
      "userName": "用户名称",
      "createTime": "2025-01-01 12:34:56",
      "updateTime": "2025-01-01 12:34:56"
    }
  ]
}
```

### 2. 用户详情接口

#### 接口签名及描述
* 签名：SuccessResponse<UserDetailDTO> com.ailpha.ailand.dataroute.endpoint.user.controller.MenuManagementController.getUserDetail(String userId)
* 描述：用户详情。展示用户基本信息，以及菜单权限信息

#### 接口入参
```json
{
  "userId": "用户ID"
}
```

#### 接口出参
```json
{
  "success": true,
  "errorCode": "",
  "errorMessage": "",
  "data": {
    "id": "用户ID",
    "userName": "用户基本信息",
    "companyName": "主体名称",
    "menuPermissions": [
      {
        "id": "权限ID",
        "menuId": "菜单ID",
        "menuName": "菜单名称",
        "enabled": true,
        "createTime": "2025-01-01 12:34:56",
        "updateTime": "2025-01-01 12:34:56"
      }
    ],
    "createTime": "2025-01-01 12:34:56",
    "updateTime": "2025-01-01 12:34:56"
  }
}
```

### 3. 查询菜单权限接口

#### 接口签名及描述
* 签名：SuccessResponse<List<MenuPermissionDTO>> com.ailpha.ailand.dataroute.endpoint.user.controller.MenuManagementController.queryMenuPermissions(String userId)
* 描述：查询菜单权限。查询属于该用户的菜单权限列表，其中通过enabled字段表示该菜单权限是否被启用

#### 接口入参
```json
{
  "userId": "用户ID"
}
```

#### 接口出参
```json
{
  "success": true,
  "errorCode": "",
  "errorMessage": "",
  "data": [
    {
      "id": "权限ID",
      "menuId": "菜单ID",
      "menuName": "菜单名称",
      "enabled": true,
      "createTime": "2025-01-01 12:34:56",
      "updateTime": "2025-01-01 12:34:56"
    }
  ]
}
```

### 4. 菜单权限变更接口

#### 接口签名及描述
* 签名：SuccessResponse<Boolean> com.ailpha.ailand.dataroute.endpoint.user.controller.MenuManagementController.updateMenuPermissions(MenuManagementRequest request)
* 描述：菜单权限变更。用户菜单权限修改，只需要接收启用的菜单权限

#### 接口入参
```json
{
  "request": {
    "id": "主键ID",
    "userId": "用户ID",
    "menuIds": ["菜单ID1", "菜单ID2"],
    "createTime": "2025-01-01 12:34:56",
    "updateTime": "2025-01-01 12:34:56",
    "pageNo": 1,
    "pageSize": 20
  }
}
```

#### 接口出参
```json
{
  "success": true,
  "errorCode": "",
  "errorMessage": "",
  "data": true
}
```