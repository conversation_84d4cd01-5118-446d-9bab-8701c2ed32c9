<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ailpha.ailand.dataroute</groupId>
    <artifactId>data-route-endpoint</artifactId>
    <version>DAS-SMPC-DR-V1.0R25C12</version>
    <name>data-route-endpoint</name>
    <description>data-route-endpoint</description>
    <properties>
        <java.version>21</java.version>
        <app.fully.qualified.MainClass>com.ailpha.ailand.dataroute.endpoint.DataRouteEndpointApplication
        </app.fully.qualified.MainClass>
        <app.MainClass>DataRouteEndpointApplication</app.MainClass>
        <dependency-check.skip>true</dependency-check.skip>
        <cyclonedx.skip>true</cyclonedx.skip>
        <spring.boot.version>3.3.10</spring.boot.version>
        <io.netty.version>4.1.119.Final</io.netty.version>
        <org.mapstruct.version>1.6.3</org.mapstruct.version>
        <lombok.version>1.18.36</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <retrofit.version>3.1.3</retrofit.version>
        <highgoDB.version>6.2.4</highgoDB.version>
        <hutool.version>5.8.36</hutool.version>
        <flyway.version>10.20.1</flyway.version>
        <querydsl.version>5.1.0</querydsl.version>
        <alibaba.druid.version>1.2.23</alibaba.druid.version>
        <org.bouncycastle.version>1.79</org.bouncycastle.version>
        <google.guava.version>33.3.1-jre</google.guava.version>
        <minio.version>8.5.17</minio.version>
        <google.protobuf-java.version>4.28.3</google.protobuf-java.version>
        <ailand.data-collector.version>DAS-SMPC-V3.4R24C20_110102</ailand.data-collector.version>
        <ehcache.version>3.10.8</ehcache.version>
        <jasypt.version>3.0.5</jasypt.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.github.ulisesbocchio/jasypt-spring-boot-starter -->
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
            <version>${jasypt.version}</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-jcache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>${ehcache.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ailpha.ailand.oam</groupId>
            <artifactId>ailand-kms-adapter</artifactId>
            <version>DAS-SMPC-V3.4R24C10_FINAL</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio-admin</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-cache</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- Spring Security Test -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dbapp.licence</groupId>
            <artifactId>lic-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.13.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpg-jdk18on</artifactId>
            <version>${org.bouncycastle.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
            <version>${org.bouncycastle.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${alibaba.druid.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>${flyway.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-database-postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-jpa</artifactId>
            <classifier>jakarta</classifier>
            <version>${querydsl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.querydsl</groupId>
            <artifactId>querydsl-apt</artifactId>
            <scope>provided</scope>
            <classifier>jakarta</classifier>
            <version>${querydsl.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.4.3</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- Spring Data JPA for Database Access -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>com.highgo</groupId>
            <artifactId>HgdbJdbc</artifactId>
            <version>${highgoDB.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>${retrofit.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-webflux</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; Spring Security for Authentication and Authorization &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            artifactId>spring-boot-starter-security</artifactId>-->
        <!--        </dependency>-->
        <!--redis 依赖 -->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-data-redis</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.integration</groupId>-->
        <!--            <artifactId>spring-integration-redis</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.17.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.jiangxincode</groupId>
            <artifactId>cpdetector</artifactId>
            <version>1.0.10</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.3.1</version>
        </dependency>

        <!-- https://scc.das-security.cn/#/docs/specifications?position=das-rest-sdk%2525E6%25258E%2525A5%2525E5%252585%2525A5%2525E6%25258C%252587%2525E5%25258D%252597 -->
        <dependency>
            <groupId>com.dbapp.rest</groupId>
            <artifactId>das-rest-sdk</artifactId>
            <version>1.0.0-jdk17</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-openfeign</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
            <version>4.5.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${io.netty.version}</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${org.bouncycastle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${google.protobuf-java.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <finalName>data-route-endpoint</finalName>
        <plugins>
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.4.2</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>${app.fully.qualified.MainClass}</mainClass>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib</classpathPrefix>
                        </manifest>
                    </archive>
                    <excludes>
                        <exclude>docker/**</exclude>
                        <exclude>*.sh</exclude>
                        <exclude>*.properties</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                        <arg>--add-exports</arg>
                        <arg>java.base/sun.security.x509=ALL-UNNAMED</arg>
                        <arg>--add-exports</arg>
                        <arg>java.security.jgss/sun.security.krb5.internal.ktab=ALL-UNNAMED</arg>
                    </compilerArgs>
                    <annotationProcessorPaths combine.children="append">
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                        <path>
                            <groupId>com.querydsl</groupId>
                            <artifactId>querydsl-apt</artifactId>
                            <version>${querydsl.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <includes>
                        <include>**/*Test*.java</include><!--run test classes -->
                    </includes>
                    <skip>true</skip><!--skip run test classes -->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.7.1</version>
                <configuration>
                    <descriptors>
                        <descriptor>assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>release</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <attach>false</attach>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- mvn dependency-check:check -->
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>8.2.1</version>
                <configuration>
                    <skipProvidedScope>true</skipProvidedScope>
                    <skipRuntimeScope>true</skipRuntimeScope>
                    <skipTestScope>${dependency-check.skip}</skipTestScope>
                    <scanSet>
                        <fileSet>
                            <directory>${project.basedir}</directory>
                            <excludes>
                                <include>package.json</include>
                                <include>package-lock.json</include>
                            </excludes>
                        </fileSet>
                    </scanSet>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.cyclonedx</groupId>
                <artifactId>cyclonedx-maven-plugin</artifactId>
                <version>2.9.0</version>
                <configuration>
                    <skip>${cyclonedx.skip}</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <app.env>dev</app.env>
                <app.name>${project.build.finalName}</app.name>
                <app.mainClass>${app.fully.qualified.MainClass}</app.mainClass>
                <app.mainClass-short>${app.MainClass}</app.mainClass-short>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencyManagement>
                <dependencies>
                    <!-- license sdk -->
                    <dependency>
                        <groupId>com.dbapp.licence</groupId>
                        <artifactId>lic-sdk-java</artifactId>
                        <version>1.0.12-RC1</version>
                    </dependency>
                </dependencies>
            </dependencyManagement>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <app.env>test</app.env>
                <app.name>${project.build.finalName}</app.name>
                <app.mainClass>${app.fully.qualified.MainClass}</app.mainClass>
                <app.mainClass-short>${app.MainClass}</app.mainClass-short>
            </properties>
            <dependencyManagement>
                <dependencies>
                    <!-- license sdk -->
                    <dependency>
                        <groupId>com.dbapp.licence</groupId>
                        <artifactId>lic-sdk-java</artifactId>
                        <version>1.0.12-RC1</version>
                    </dependency>
                </dependencies>
            </dependencyManagement>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <app.env>prod</app.env>
                <app.name>${project.build.finalName}</app.name>
                <app.mainClass>${app.fully.qualified.MainClass}</app.mainClass>
                <app.mainClass-short>${app.MainClass}</app.mainClass-short>
            </properties>
            <dependencyManagement>
                <dependencies>
                    <!-- license sdk -->
                    <dependency>
                        <groupId>com.dbapp.licence</groupId>
                        <artifactId>lic-sdk-java</artifactId>
                        <version>1.0.12-Final</version>
                    </dependency>
                </dependencies>
            </dependencyManagement>
        </profile>
    </profiles>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>maven-releases</name>
            <url>https://ci.das-security.cn/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshots</name>
            <url>https://ci.das-security.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
